package config

type ProductTimer struct {
	Enable      bool                 `json:"enable" mapstruct:"enable" yaml:"enabled"`               // 是否启用
	Spec        string               `json:"spec" mapstruct:"spec" yaml:"spec"`                      // 定时规则
	WithSeconds bool                 `json:"withSeconds" mapstruct:"withSeconds" yaml:"withSeconds"` // 是否精确到秒
	Details     []ProductTimerDetail `json:"details" mapstruct:"details" yaml:"details"`             // 详情
}

type ProductTimerDetail struct {
	TableName    string `json:"tableName" mapstruct:"tableName" yaml:"tableName"`          // 表名
	CompareField string `json:"compareField" mapstruct:"compareField" yaml:"compareField"` // 比较的时间字段
	InterVal     string `json:"interVal" mapstruct:"interVal" yaml:"interVal"`             // 时间间隔
}

type OrderUnpaidTimer struct {
	Enable      bool                     `json:"enable" mapstruct:"enable" yaml:"enabled"`
	Spec        string                   `json:"spec" mapstruct:"spec" yaml:"spec"`                      // 定时规则
	WithSeconds bool                     `json:"withSeconds" mapstruct:"withSeconds" yaml:"withSeconds"` // 是否精确到秒
	Details     []OrderUnpaidTimerDetail `json:"details" mapstruct:"details" yaml:"details"`
}

type OrderUnpaidTimerDetail struct {
	TableName    string `json:"tableName" mapstruct:"tableName" yaml:"tableName"`          // 表名
	CompareField string `json:"compareField" mapstruct:"compareField" yaml:"compareField"` // 比较的时间字段
	InterVal     string `json:"interVal" mapstruct:"interVal" yaml:"interVal"`             // 时间间隔
}
