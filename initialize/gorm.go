package initialize

import (
	"os"

	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/account"
	"github.com/OSQianXing/guanpu-server/model/example"
	"github.com/OSQianXing/guanpu-server/model/system"

	"github.com/OSQianXing/guanpu-server/model/app"
	"github.com/OSQianXing/guanpu-server/model/apprelease"
	"github.com/OSQianXing/guanpu-server/model/autoJob"
	"github.com/OSQianXing/guanpu-server/model/common"
	"github.com/OSQianXing/guanpu-server/model/finance"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/pay"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/model/warehouse"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"

	"github.com/OSQianXing/guanpu-server/model/franchisees/distribution"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "mysql":
		return GormMysql()
	case "pgsql":
		return GormPgSql()
	case "oracle":
		return GormOracle()
	case "mssql":
		return GormMssql()
	default:
		return GormMysql()
	}
}

func RegisterTables() {
	db := global.GVA_DB
	err := db.AutoMigrate(

		system.SysApi{},
		system.SysUser{},
		system.SysBaseMenu{},
		system.JwtBlacklist{},
		system.SysAuthority{},
		system.SysDictionary{},
		system.SysOperationRecord{},
		system.SysAutoCodeHistory{},
		system.SysDictionaryDetail{},
		system.SysBaseMenuParameter{},
		system.SysBaseMenuBtn{},
		system.SysAuthorityBtn{},
		system.SysAutoCode{},
		system.SysChatGptOption{},
		system.Tenant{},
		system.TenantAppConfig{},
		system.UserTenantRelation{},
		system.SuperAdminOperationLog{},

		example.ExaFile{},
		example.ExaCustomer{},
		example.ExaFileChunk{},
		example.ExaFileUploadAndDownload{},
		franchisees.Franchisee{},
		franchisees.FranchiseeCategory{},
		franchisees.RechargeRecord{},
		products.Product{},
		products.ProductBrand{},
		products.ProductCategory{},
		products.ProductManufacturer{},
		common.City{},
		orders.Order{},
		orders.OrderGoods{},
		orders.OrderDelivery{},
		orders.OrderReturn{},
		orders.OrderShipMethod{},
		app.FranchiseeAddress{},
		app.Cart{},
		app.Product{},
		app.Franchisee{},
		app.ProductCategory{},
		products.GiftRule{},
		common.LogisticsTemplate{},
		apprelease.AppRelease{},
		account.ExclusiveAccount{},
		products.SpecialMall{},
		warehouse.CloudWarehouse{},
		warehouse.CloudWarehouseRecord{},
		warehouse.SubCloudWarehouse{},
		app.CloudWarehouse{},
		autoJob.ProductJob{},
		finance.FranchiseePerformance{},
		app.FranchiseePerformance{},
		distribution.DistributionConfig{},
		franchisees.FranchiseeStandby{},
		franchisees.FranchiseeApprove{},
		franchisees.FranchiseeWxInfo{},
		orders.OrderRemark{},
		finance.FranchiseePerformanceMonth{},
		bigwarehouse.AreaCode{},
		bigwarehouse.BigWarehouse{},
		bigwarehouse.BigWarehouseCoverage{},
		bigwarehouse.BigWarehouseGoods{},
		bigwarehouse.BigWarehouseManager{},
		bigwarehouse.BigWarehouseAllocation{},
		bigwarehouse.BigWarehouseOrder{},
		bigwarehouse.BigWarehouseStock{},
		bigwarehouse.BigWarehouseRecord{},
		pay.OrderOnlinePay{},
		pay.OrderOnlinePayRecord{},
		pay.OnlinePayDefaultPolicy{},
		pay.HyAllotOrderCallback{},
		pay.HyAllotOrderCallbackRecord{},
		pay.HyAllotMerchant{},
		pay.HyAllotOrder{},
		pay.HyAllotOrderRecord{},
		finance.GeneratedInvestmentPerformanceFranchiseeConfig{},
	)
	if err != nil {
		global.GVA_LOG.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}
	global.GVA_LOG.Info("register table success")
	
	// 注册租户插件
	err = db.Use(&tenant.TenantPlugin{})
	if err != nil {
		global.GVA_LOG.Error("register tenant plugin failed", zap.Error(err))
		os.Exit(0)
	}
	global.GVA_LOG.Info("register tenant plugin success")
}
