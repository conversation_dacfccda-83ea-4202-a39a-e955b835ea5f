package initialize

import (
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/gorm/clause"
	"time"

	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/service/finance"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/robfig/cron/v3"

	"github.com/OSQianXing/guanpu-server/global"
)

func Timer() {

	if !global.GVA_CONFIG.Timer.Start {
		return
	}
	// for i := range global.GVA_CONFIG.Timer.Detail {
	//	go func(detail config.Detail) {
	//		var option []cron.Option
	//		if global.GVA_CONFIG.Timer.WithSeconds {
	//			option = append(option, cron.WithSeconds())
	//		}
	//		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", global.GVA_CONFIG.Timer.Spec, func() {
	//			err := utils.ClearTable(global.GVA_DB, detail.TableName, detail.CompareField, detail.Interval)
	//			if err != nil {
	//				fmt.Println("timer error:", err)
	//			}
	//		}, option...)
	//		if err != nil {
	//			fmt.Println("add timer error:", err)
	//		}
	//	}(global.GVA_CONFIG.Timer.Detail[i])
	// }

	// 标记确认收货任务
	go func() {
		var option []cron.Option
		if global.GVA_CONFIG.Timer.WithSeconds {
			option = append(option, cron.WithSeconds())
		}

		ago := time.Now().AddDate(0, 0, -global.GVA_CONFIG.Timer.ConfirmDeliveryDay).Truncate(24 * time.Hour)
		_, err := global.GVA_Timer.AddTaskByFunc("ConfirmDelivery", global.GVA_CONFIG.Timer.Spec, func() {
			// job执行需抢锁，防止多个job同时执行
			locker := utils.NewDistributedLockEngine("job:confirm-delivery")
			locker.Timeout(1800)
			lockSuccess, err := locker.Lock()
			if err != nil || !lockSuccess {
				global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
				return
			}
			defer func() {
				_ = locker.UnLock()
			}()

			var ods []*orders.OrderDelivery
			if err := global.GVA_DB.Where("updated_at <  ?", ago).Where("status in (?)", []types.DeliveryStatus{types.DeliveryStatusDelivery, types.DeliveryStatusPartReturned}).Find(&ods).Error; err != nil {
				fmt.Println("OrderDelivery 查询异常:", err)
			}

			for _, v := range ods {
				err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
					if err := tx.Model(&orders.OrderDelivery{}).Where("id = ?", v.ID).Updates(map[string]interface{}{
						"status": types.DeliveryConfirm,
					}).Error; err != nil {
						return err
					}

					// 更新订单状态（全部确认收货则更新订单状态为 已完成）
					var count int64
					if err := tx.Model(&orders.OrderDelivery{}).Where("order_no = ?", v.OrderNo).Where("status != ?", types.DeliveryConfirm).Count(&count).Error; err != nil {
						return err
					}

					var o orders.Order
					if count == 0 {
						if err := tx.Model(&o).Clauses(clause.Returning{}).Where("order_no = ?", v.OrderNo).Where("status = ?", types.OrderDelivery).Updates(map[string]interface{}{
							"status": types.OrderCompleted,
						}).Error; err != nil {
							return err
						}

						jsonExtra, _ := json.Marshal(orders.OrderRecordExtra{
							Req:    v,
							Result: o,
							Stage:  types.OrderCompleted.StringEn(),
						})
						// 记录订单操作
						orderRecord := &orders.OrderRecord{
							OrderID:     o.ID,
							OrderNo:     v.OrderNo,
							OrderStatus: types.OrderCompleted,
							OperatorID:  0,
							Extra:       jsonExtra,
							Remark:      "",
						}
						if err := tx.Create(orderRecord).Error; err != nil {
							return err
						}
					}

					return nil
				})

				if err != nil {
					global.GVA_LOG.Error("确认收货异常!", zap.Error(err), zap.String("deliveryNo", v.DeliveryNo))
					continue
				}
			}
		}, option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}
	}()

	// 加盟商业绩统计,每天统计前一天每个加盟商的业绩
	ctx := context.Background()
	utils.Go(ctx, func(ctx context.Context) {
		var option []cron.Option
		if global.GVA_CONFIG.Timer.WithSeconds {
			option = append(option, cron.WithSeconds())
		}
		_, err := global.GVA_Timer.AddTaskByJob("FranchiseePerformance", global.GVA_CONFIG.Timer.FranchiseePerformance.Spec, finance.NewPerformance())
		if err != nil {
			fmt.Println("add timer error:", err)
		}
	})

}
