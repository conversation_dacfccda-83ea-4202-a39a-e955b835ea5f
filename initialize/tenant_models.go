package initialize

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/products"
	"go.uber.org/zap"
)

// RegisterTenantModels 注册所有租户模型
// 这个函数应该在应用启动时调用，用于自动检测和注册包含TenantID字段的模型
func RegisterTenantModels() {
	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("开始注册租户模型...")
	}

	// 注册订单相关模型
	registerOrderModels()

	// 注册产品相关模型
	registerProductModels()

	// 注册加盟商相关模型
	registerFranchiseeModels()

	// 输出注册结果
	if global.GVA_LOG != nil {
		autoDetectedTables := global.GetAutoDetectedTenantTables()
		global.GVA_LOG.Info("租户模型注册完成",
			zap.Int("autoDetectedCount", len(autoDetectedTables)),
			zap.Any("autoDetectedTables", autoDetectedTables))
	}
}

// registerOrderModels 注册订单相关模型
func registerOrderModels() {
	// 注册订单模型
	global.RegisterTenantModel(orders.Order{})

	// 注册订单商品模型
	global.RegisterTenantModel(orders.OrderGoods{})

	// 注册订单配送模型
	global.RegisterTenantModel(orders.OrderDelivery{})

	// 注册订单退货模型
	global.RegisterTenantModel(orders.OrderReturn{})

	// 注册订单备注模型
	global.RegisterTenantModel(orders.OrderRemark{})

	// 注册订单记录模型
	global.RegisterTenantModel(orders.OrderRecord{})
}

// registerProductModels 注册产品相关模型
func registerProductModels() {
	// 注册产品模型
	global.RegisterTenantModel(products.Product{})

	// 注册产品品牌模型
	global.RegisterTenantModel(products.ProductBrand{})

	// 注册产品分类模型
	global.RegisterTenantModel(products.ProductCategory{})
}

// registerFranchiseeModels 注册加盟商相关模型
func registerFranchiseeModels() {
	// 注册加盟商模型
	global.RegisterTenantModel(franchisees.Franchisee{})

	// 注册加盟商分类模型
	global.RegisterTenantModel(franchisees.FranchiseeCategory{})

	// 注册加盟商账户模型
	global.RegisterTenantModel(franchisees.FranchiseeAccount{})

	// 注册充值记录模型
	global.RegisterTenantModel(franchisees.RechargeRecord{})
}

// ValidateTenantModels 验证租户模型注册情况（用于调试）
func ValidateTenantModels() {
	if global.GVA_LOG == nil {
		return
	}

	// 测试一些已知的表名
	testTables := []string{
		"order",
		"order_goods",
		"products",
		"franchisees",
		"sys_users", // 这个不应该是租户表
	}

	global.GVA_LOG.Info("验证租户表检测结果:")
	for _, tableName := range testTables {
		isTenantTable := global.IsTenantTable(tableName)
		global.GVA_LOG.Info("表检测结果",
			zap.String("tableName", tableName),
			zap.Bool("isTenantTable", isTenantTable))
	}
}
