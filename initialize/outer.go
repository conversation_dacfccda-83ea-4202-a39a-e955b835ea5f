package initialize

import (
	"time"

	"github.com/songzhibin97/gkit/cache/local_cache"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/utils"
	"go.uber.org/zap"
)

func OtherInit() {
	// Initialize JWT expiration time
	dr, err := utils.ParseDuration(global.GVA_CONFIG.JWT.ExpiresTime)
	if err != nil {
		if global.GVA_LOG != nil {
			global.GVA_LOG.Error("Failed to parse JWT expiration time", zap.Error(err))
		}
		dr = 7 * 24 * time.Hour // Default to 7 days if parsing fails
	}

	// Initialize JWT buffer time
	bufferTime, err := utils.ParseDuration(global.GVA_CONFIG.JWT.BufferTime)
	if err != nil {
		if global.GVA_LOG != nil {
			global.GVA_LOG.Error("Failed to parse JWT buffer time", zap.Error(err))
		}
		bufferTime = 1 * time.Hour // Default to 1 hour if parsing fails
	}

	// Initialize cache with parsed or default duration
	global.BlackCache = local_cache.NewCache(
		local_cache.SetDefaultExpire(dr),
	)

	if global.GVA_LOG != nil {
		global.GVA_LOG.Info("Other initialization completed",
			zap.Duration("jwt_expiration", dr),
			zap.Duration("jwt_buffer", bufferTime))
	}
}
