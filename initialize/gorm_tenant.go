package initialize

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/system"
	"go.uber.org/zap"
)

// InitTenantTables 初始化租户相关表
func InitTenantTables() {
	db := global.GVA_DB
	err := db.AutoMigrate(
		&system.Tenant{},
		&system.TenantAppConfig{},
		&system.UserTenantRelation{},
	)
	if err != nil {
		global.GVA_LOG.Error("初始化租户表失败!", zap.Error(err))
		panic(err)
	}
	global.GVA_LOG.Info("租户表初始化成功!")
}

// CreateDefaultTenant 创建默认租户
func CreateDefaultTenant() {
	db := global.GVA_DB
	
	// 检查是否已存在默认租户
	var count int64
	db.Model(&system.Tenant{}).Where("code = ?", "default").Count(&count)
	if count > 0 {
		global.GVA_LOG.Info("默认租户已存在，跳过创建")
		return
	}
	
	// 创建默认租户
	status := true
	defaultTenant := system.Tenant{
		Name:           "默认租户",
		Code:           "default",
		Logo:           "",
		PrimaryColor:   "#409EFF",
		SecondaryColor: "#909399",
		Status:         &status,
		ContactName:    "系统管理员",
		ContactPhone:   "",
	}
	
	err := db.Create(&defaultTenant).Error
	if err != nil {
		global.GVA_LOG.Error("创建默认租户失败!", zap.Error(err))
		return
	}
	
	// 创建默认租户应用配置
	defaultAppConfig := system.TenantAppConfig{
		TenantID:       defaultTenant.ID,
		AppName:        "管铺系统",
		AppLogo:        "",
		LoginBgImage:   "",
		HomePageConfig: "{}",
	}
	
	err = db.Create(&defaultAppConfig).Error
	if err != nil {
		global.GVA_LOG.Error("创建默认租户应用配置失败!", zap.Error(err))
		return
	}
	
	global.GVA_LOG.Info("默认租户创建成功!", zap.Uint("tenantId", defaultTenant.ID))
}