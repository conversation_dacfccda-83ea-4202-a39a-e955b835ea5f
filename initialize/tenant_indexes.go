package initialize

import (
	"github.com/OSQianXing/guanpu-server/global"
	"go.uber.org/zap"
)

// CreateTenantIndexes 创建租户相关的数据库索引
func CreateTenantIndexes() {
	if global.GVA_DB == nil {
		global.GVA_LOG.Error("数据库连接未初始化，跳过索引创建")
		return
	}

	global.GVA_LOG.Info("开始创建租户相关数据库索引...")

	// 创建订单表索引
	createOrderIndexes()
	
	// 创建订单商品表索引
	createOrderGoodsIndexes()
	
	// 创建产品表索引
	createProductIndexes()
	
	// 创建加盟商表索引
	createFranchiseeIndexes()
	
	// 创建订单配送表索引
	createOrderDeliveryIndexes()

	global.GVA_LOG.Info("租户相关数据库索引创建完成")
}

// createOrderIndexes 创建订单表索引
func createOrderIndexes() {
	indexes := []struct {
		name string
		sql  string
	}{
		{
			name: "idx_order_tenant_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_tenant_created ON `order` (tenant_id, created_at DESC)",
		},
		{
			name: "idx_order_tenant_status_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_tenant_status_created ON `order` (tenant_id, status, created_at DESC)",
		},
		{
			name: "idx_order_tenant_user_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_tenant_user_created ON `order` (tenant_id, franchisee_id, created_at DESC)",
		},
		{
			name: "idx_order_tenant_no",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_tenant_no ON `order` (tenant_id, order_no)",
		},
		{
			name: "idx_order_tenant_updated",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_tenant_updated ON `order` (tenant_id, updated_at DESC)",
		},
	}

	createIndexes("order", indexes)
}

// createOrderGoodsIndexes 创建订单商品表索引
func createOrderGoodsIndexes() {
	indexes := []struct {
		name string
		sql  string
	}{
		{
			name: "idx_order_goods_tenant_order",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_goods_tenant_order ON order_goods (tenant_id, order_id)",
		},
		{
			name: "idx_order_goods_tenant_product",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_goods_tenant_product ON order_goods (tenant_id, product_id)",
		},
		{
			name: "idx_order_goods_tenant_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_goods_tenant_created ON order_goods (tenant_id, created_at DESC)",
		},
	}

	createIndexes("order_goods", indexes)
}

// createProductIndexes 创建产品表索引
func createProductIndexes() {
	indexes := []struct {
		name string
		sql  string
	}{
		{
			name: "idx_products_tenant_category_status",
			sql:  "CREATE INDEX IF NOT EXISTS idx_products_tenant_category_status ON products (tenant_id, category_id, status)",
		},
		{
			name: "idx_products_tenant_brand_status",
			sql:  "CREATE INDEX IF NOT EXISTS idx_products_tenant_brand_status ON products (tenant_id, brand_id, status)",
		},
		{
			name: "idx_products_tenant_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_products_tenant_created ON products (tenant_id, created_at DESC)",
		},
		{
			name: "idx_products_tenant_name",
			sql:  "CREATE INDEX IF NOT EXISTS idx_products_tenant_name ON products (tenant_id, name(50))", // 限制name字段长度
		},
	}

	createIndexes("products", indexes)
}

// createFranchiseeIndexes 创建加盟商表索引
func createFranchiseeIndexes() {
	indexes := []struct {
		name string
		sql  string
	}{
		{
			name: "idx_franchisees_tenant_status_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_franchisees_tenant_status_created ON franchisees (tenant_id, status, created_at DESC)",
		},
		{
			name: "idx_franchisees_tenant_region",
			sql:  "CREATE INDEX IF NOT EXISTS idx_franchisees_tenant_region ON franchisees (tenant_id, province, city)",
		},
		{
			name: "idx_franchisees_tenant_category",
			sql:  "CREATE INDEX IF NOT EXISTS idx_franchisees_tenant_category ON franchisees (tenant_id, category_id)",
		},
	}

	createIndexes("franchisees", indexes)
}

// createOrderDeliveryIndexes 创建订单配送表索引
func createOrderDeliveryIndexes() {
	indexes := []struct {
		name string
		sql  string
	}{
		{
			name: "idx_order_delivery_tenant_order_no",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_delivery_tenant_order_no ON order_delivery (tenant_id, order_no)",
		},
		{
			name: "idx_order_delivery_tenant_status_created",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_delivery_tenant_status_created ON order_delivery (tenant_id, status, created_at DESC)",
		},
		{
			name: "idx_order_delivery_tenant_deliverer",
			sql:  "CREATE INDEX IF NOT EXISTS idx_order_delivery_tenant_deliverer ON order_delivery (tenant_id, deliverer_id)",
		},
	}

	createIndexes("order_delivery", indexes)
}

// createIndexes 执行索引创建
func createIndexes(tableName string, indexes []struct {
	name string
	sql  string
}) {
	for _, index := range indexes {
		// 检查索引是否已存在
		if indexExists(tableName, index.name) {
			global.GVA_LOG.Debug("索引已存在，跳过创建",
				zap.String("table", tableName),
				zap.String("index", index.name))
			continue
		}

		// 创建索引
		err := global.GVA_DB.Exec(index.sql).Error
		if err != nil {
			global.GVA_LOG.Error("创建索引失败",
				zap.String("table", tableName),
				zap.String("index", index.name),
				zap.String("sql", index.sql),
				zap.Error(err))
		} else {
			global.GVA_LOG.Info("索引创建成功",
				zap.String("table", tableName),
				zap.String("index", index.name))
		}
	}
}

// indexExists 检查索引是否存在
func indexExists(tableName, indexName string) bool {
	var count int64
	
	// 查询索引是否存在
	err := global.GVA_DB.Raw(`
		SELECT COUNT(*) 
		FROM information_schema.statistics 
		WHERE table_schema = DATABASE() 
			AND table_name = ? 
			AND index_name = ?
	`, tableName, indexName).Scan(&count).Error
	
	if err != nil {
		global.GVA_LOG.Warn("检查索引存在性失败",
			zap.String("table", tableName),
			zap.String("index", indexName),
			zap.Error(err))
		return false
	}
	
	return count > 0
}

// AnalyzeIndexUsage 分析索引使用情况
func AnalyzeIndexUsage() {
	if global.GVA_DB == nil {
		return
	}

	global.GVA_LOG.Info("开始分析索引使用情况...")

	// 查询租户相关表的索引信息
	tables := []string{"order", "order_goods", "products", "franchisees", "order_delivery"}
	
	for _, tableName := range tables {
		analyzeTableIndexes(tableName)
	}
}

// analyzeTableIndexes 分析单个表的索引
func analyzeTableIndexes(tableName string) {
	var indexes []struct {
		IndexName  string `json:"index_name"`
		ColumnName string `json:"column_name"`
		SeqInIndex int    `json:"seq_in_index"`
		Cardinality int64 `json:"cardinality"`
	}

	err := global.GVA_DB.Raw(`
		SELECT 
			INDEX_NAME as index_name,
			COLUMN_NAME as column_name,
			SEQ_IN_INDEX as seq_in_index,
			CARDINALITY as cardinality
		FROM information_schema.STATISTICS 
		WHERE TABLE_SCHEMA = DATABASE() 
			AND TABLE_NAME = ?
			AND INDEX_NAME LIKE 'idx_%tenant%'
		ORDER BY INDEX_NAME, SEQ_IN_INDEX
	`, tableName).Scan(&indexes).Error

	if err != nil {
		global.GVA_LOG.Error("分析表索引失败",
			zap.String("table", tableName),
			zap.Error(err))
		return
	}

	if len(indexes) > 0 {
		global.GVA_LOG.Info("表索引分析结果",
			zap.String("table", tableName),
			zap.Int("index_count", len(indexes)),
			zap.Any("indexes", indexes))
	} else {
		global.GVA_LOG.Warn("表缺少租户相关索引",
			zap.String("table", tableName))
	}
}

// OptimizeTableStats 优化表统计信息
func OptimizeTableStats() {
	if global.GVA_DB == nil {
		return
	}

	global.GVA_LOG.Info("开始优化表统计信息...")

	tables := []string{"order", "order_goods", "products", "franchisees", "order_delivery"}
	
	for _, tableName := range tables {
		// 分析表
		err := global.GVA_DB.Exec("ANALYZE TABLE " + tableName).Error
		if err != nil {
			global.GVA_LOG.Error("分析表失败",
				zap.String("table", tableName),
				zap.Error(err))
		} else {
			global.GVA_LOG.Info("表分析完成",
				zap.String("table", tableName))
		}
	}
}
