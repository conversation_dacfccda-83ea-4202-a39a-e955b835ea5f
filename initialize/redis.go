package initialize

import (
	"context"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// Redis initializes the Redis client with retries and proper error handling
func Redis() {
	var client *redis.Client

	// Get Redis configuration
	redisCfg := global.GVA_CONFIG.Redis
	if redisCfg.Addr == "" {
		if global.GVA_LOG != nil {
			global.GVA_LOG.Error("Redis address not configured")
		}
		return
	}

	// Configure Redis client
	options := &redis.Options{
		Addr:         redisCfg.Addr,
		Password:     redisCfg.Password,
		DB:           redisCfg.DB,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 5,
		MaxRetries:   3,
	}

	// Create Redis client
	client = redis.NewClient(options)
	global.GVA_REDIS = client // Set immediately to ensure it's never nil

	// Attempt to connect with retries
	maxRetries := 3
	retryDelay := time.Second
	var lastError error

	for i := 0; i < maxRetries; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		pong, err := client.Ping(ctx).Result()
		cancel()

		if err == nil {
			if global.GVA_LOG != nil {
				global.GVA_LOG.Info("Redis connection established",
					zap.String("address", redisCfg.Addr),
					zap.Int("database", redisCfg.DB),
					zap.String("ping", pong))
			}
			return
		}

		lastError = err
		if global.GVA_LOG != nil {
			global.GVA_LOG.Warn("Redis connection attempt failed",
				zap.Int("attempt", i+1),
				zap.Int("max_attempts", maxRetries),
				zap.Error(err))
		}

		if i < maxRetries-1 {
			time.Sleep(retryDelay)
			retryDelay *= 2 // Exponential backoff
		}
	}

	// All retries failed
	if global.GVA_LOG != nil {
		global.GVA_LOG.Error("Failed to connect to Redis after retries",
			zap.Int("attempts", maxRetries),
			zap.Error(lastError))
	}
}

// CloseRedis gracefully closes the Redis connection
func CloseRedis() {
	if global.GVA_REDIS != nil {
		if err := global.GVA_REDIS.Close(); err != nil {
			if global.GVA_LOG != nil {
				global.GVA_LOG.Error("Error closing Redis connection", zap.Error(err))
			}
		}
	}
}
