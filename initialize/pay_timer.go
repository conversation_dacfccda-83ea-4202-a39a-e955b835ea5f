package initialize

import (
	"sync"

	"github.com/OSQianXing/guanpu-server/config"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"go.uber.org/zap"
)

const (
	OrderTableName = "order"
)

// PayAutoJob
// TODO: refer to product_timer.go ,implement the pay auto job, the logic is below:
//  1. from
//  2. from order_online_pay table get the online pay orders which not paid yet and order create time is more than 30 minutes
//  3. update order table set the order status to closed
func UnPayedOrderClose() {
	global.GVA_LOG.Info("unpaid order auto job init", zap.Any("global.GVA_CONFIG.UnPayedOrderClose", global.GVA_CONFIG.OrderUnpaidJobTimer))
	if global.GVA_CONFIG.OrderUnpaidJobTimer.Enable {
		var wg sync.WaitGroup
		for _, k := range global.GVA_CONFIG.OrderUnpaidJobTimer.Details {
			wg.Add(1)
			go func(d config.OrderUnpaidTimerDetail) {
				defer wg.Done()
				global.GVA_LOG.Info("UnPayedOrderClose auto job thread start", zap.Any("Detail", d))
				cronId, err := global.GVA_Timer.AddTaskByFunc("UnPayedOrderClose", global.GVA_CONFIG.OrderUnpaidJobTimer.Spec, func() {
					if global.GVA_DB == nil {
						global.GVA_LOG.Error("global.GVA_DB is nil")
						return
					}
					locker := utils.NewDistributedLockEngine("jobOrderUnpaidJobTimer:" + d.TableName + ":" + d.CompareField)
					locker.Timeout(1800)
					lockSuccess, err := locker.Lock()
					if err != nil || !lockSuccess {
						global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
						return
					}
					defer func() {
						_ = locker.UnLock()
					}()
					global.GVA_LOG.Info("UnPayedOrderClose auto job lock success")
					service.ServiceGroupApp.OnlinePayServiceGroup.OnlinePayUnpaidOrderClose(d)
				})
				if err != nil {
					global.GVA_LOG.Error(err.Error())
				}
				global.GVA_LOG.Info("product auto job init done", zap.Any("cronId", cronId))
			}(k)
		}
		wg.Wait()
	}
}
