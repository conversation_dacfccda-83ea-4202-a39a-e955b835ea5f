package initialize

import (
	"github.com/OSQianXing/guanpu-server/config"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/service"
	autoJobService "github.com/OSQianXing/guanpu-server/service/autoJob"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

func ProductAutoJob() {
	global.GVA_LOG.Info("product auto job init", zap.Any("global.GVA_CONFIG.ProductAutoJob", global.GVA_CONFIG.ProductAutoJobTimer))
	if global.GVA_CONFIG.ProductAutoJobTimer.Enable {
		var option []cron.Option
		if global.GVA_CONFIG.ProductAutoJobTimer.WithSeconds {
			option = append(option, cron.WithSeconds())
		}
		// productAutoJobInstance := new(autoJobService.ProductJobService)
		for _, k := range global.GVA_CONFIG.ProductAutoJobTimer.Details {
			global.GVA_LOG.Info("product auto job init", zap.Any("k", k))
			if k.TableName == autoJobService.ProductJobTableName {
				go func(d config.ProductTimerDetail) {
					global.GVA_LOG.Info("product auto job thread start", zap.Any("Detail", d))
					cronId, err := global.GVA_Timer.AddTaskByFunc("product_job", global.GVA_CONFIG.ProductAutoJobTimer.Spec, func() {
						// job执行需抢锁，防止多个job同时执行
						global.GVA_LOG.Info("product auto job func run")
						locker := utils.NewDistributedLockEngine("job:" + d.TableName + ":" + d.CompareField)
						locker.Timeout(1800)
						lockSuccess, err := locker.Lock()
						if err != nil || !lockSuccess {
							global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
							return
						}
						defer func() {
							_ = locker.UnLock()
						}()
						global.GVA_LOG.Info("product auto job lock success")
						service.ServiceGroupApp.AutoJobServiceGroup.ProductJobScheduler()
					}, option...)
					if err != nil {
						global.GVA_LOG.Error(err.Error())
					}
					global.GVA_LOG.Info("product auto job init done", zap.Any("cronId", cronId))
				}(k)
			}
		}
	}
}
