package initialize

import (
	"context"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// InitTenantRedis 初始化租户相关的Redis功能
func InitTenantRedis() {
	// 检查Redis配置是否存在
	if global.GVA_CONFIG.Redis.Addr == "" {
		global.GVA_LOG.Info("Redis配置为空，跳过分布式租户功能初始化")
		return
	}

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:         global.GVA_CONFIG.Redis.Addr,
		Password:     global.GVA_CONFIG.Redis.Password,
		DB:           global.GVA_CONFIG.Redis.DB,
		PoolSize:     10,
		MinIdleConns: 5,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	})

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		global.GVA_LOG.Error("Redis连接失败，分布式租户功能将不可用", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Redis连接成功，初始化分布式租户功能")

	// 初始化分布式租户上下文
	global.InitDistributedTenantContext(rdb)

	// 初始化分布式租户缓存
	global.InitDistributedTenantCache(rdb)

	global.GVA_LOG.Info("分布式租户功能初始化完成")
}

// GetTenantRedisClient 获取租户专用的Redis客户端（如果需要独立的Redis实例）
func GetTenantRedisClient() *redis.Client {
	// 可以配置独立的Redis实例用于租户功能
	return redis.NewClient(&redis.Options{
		Addr:         global.GVA_CONFIG.Redis.Addr,
		Password:     global.GVA_CONFIG.Redis.Password,
		DB:           global.GVA_CONFIG.Redis.DB + 1, // 使用不同的DB
		PoolSize:     10,
		MinIdleConns: 5,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	})
}

// CleanupTenantRedis 清理租户相关的Redis数据（用于测试或维护）
func CleanupTenantRedis() error {
	if !global.IsDistributedMode() {
		return nil
	}

	ctx := context.Background()

	// 清理所有租户会话
	err := global.CleanupExpiredSessions(ctx)
	if err != nil {
		global.GVA_LOG.Error("清理过期会话失败", zap.Error(err))
		return err
	}

	// 清理所有租户缓存
	err = global.ClearAllTenantCacheDistributed(ctx)
	if err != nil {
		global.GVA_LOG.Error("清理租户缓存失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("租户Redis数据清理完成")
	return nil
}

// GetTenantRedisStats 获取租户Redis统计信息
func GetTenantRedisStats() map[string]interface{} {
	stats := make(map[string]interface{})

	if !global.IsDistributedMode() {
		stats["distributed_mode"] = false
		return stats
	}

	ctx := context.Background()

	// 获取活跃会话数
	if activeSessions, err := global.GetActiveSessions(ctx); err == nil {
		stats["active_sessions"] = activeSessions
	}

	// 获取缓存统计
	cacheStats := global.GetDistributedCacheStats(ctx)
	for k, v := range cacheStats {
		stats[k] = v
	}

	stats["distributed_mode"] = true
	return stats
}