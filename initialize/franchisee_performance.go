package initialize

import (
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/finance"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/utils"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	"time"
)

type Performance interface {
	Run()
}

var PerformanceJob = new(performance)

type performance struct{}

func NewPerformance() Performance {
	return &performance{}
}
func (p *performance) Run() {
	// 2023-10-31 00:00:00
	targetTime := utils.YesterdayDate()
	begin, end := utils.GetMonthlyStartAndEndDateByTargetTime(targetTime)
	if end.After(targetTime) {
		end = targetTime
	}
	for !begin.After(end) {
		p.FranchiseePerformance(begin)
		begin = begin.AddDate(0, 0, 1)
	}
}

func (p *performance) FranchiseePerformance(targetTime time.Time) {
	locker := utils.NewDistributedLockEngine("job:franchisee-performance" + targetTime.Format("2006-01-02"))
	locker.Timeout(1800)
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()

	allFranchiseeIDs, _, err := p.FranchiseePerformanceIsFinish(targetTime)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee performance is finish", zap.Error(err))
		return
	}

	for _, franchiseeID := range allFranchiseeIDs {
		if err := p.FranchiseePerformanceByFranchiseeID(franchiseeID, targetTime); err != nil {
			global.GVA_LOG.Error("failed to get franchisee performance by franchisee id", zap.Error(err))
			continue
		}
	}
}

// FranchiseePerformanceIsFinish 统计是否完成
func (p *performance) FranchiseePerformanceIsFinish(targetTime time.Time) ([]int, bool, error) {
	start, end := utils.GetStartAndEndByTargetTime(targetTime)
	var targetDayTotalFranchiseeIDs []int
	// 1. 查订单昨天全部的加盟商个数
	err := global.GVA_DB.Model(&orders.Order{}).
		Where("created_at between ? and  ?", start, end).
		Where("franchisee_id > 0 and order_sub_type in(0,1)").
		Pluck("distinct franchisee_id", &targetDayTotalFranchiseeIDs).Error
	if err != nil {
		return nil, false, err
	}
	// 2. 查统计表全天全部的加盟商个数
	var targetDayTotalFranchiseePerformance int64
	targetDay := targetTime.Format("2006-01-02")
	err = global.GVA_DB.Model(&finance.FranchiseePerformance{}).
		Where("date = ?", targetDay).
		Select("distinct franchisee_id").
		Count(&targetDayTotalFranchiseePerformance).Error
	if err != nil {
		return nil, false, err
	}
	return targetDayTotalFranchiseeIDs, targetDayTotalFranchiseePerformance >= int64(len(targetDayTotalFranchiseeIDs)), nil
}

func (p *performance) FranchiseePerformanceByFranchiseeID(franchiseeID int, targetTime time.Time) error {
	// 1.启用业绩统计的专区
	specialMallIDs, err := dao.SpecialMall.GetCaclPerformanceSpecialMallIDs()
	if err != nil {
		return err
	}
	generalMallID, err := p.GetgeneralMallID(specialMallIDs)
	if err != nil {
		return err
	}
	// 2.查询加盟商指定天的业绩
	ps, err := dao.Order.GetSpecialMallPerformanceBySpecialMallIDsAndFranchiseeID(specialMallIDs, franchiseeID, generalMallID, targetTime)
	if err != nil {
		return err
	}
	date := time.Date(targetTime.Year(), targetTime.Month(), targetTime.Day(), 0, 0, 0, 0, targetTime.Location())
	var rows []finance.FranchiseePerformance
	for _, p := range ps {
		rows = append(rows, finance.FranchiseePerformance{
			Date:          &date,
			FranchiseeId:  &franchiseeID,
			SpecialMallId: &p.SpecialMallID,
			Performance:   &p.Performance,
		})
	}
	if len(rows) == 0 {
		return nil
	}
	return global.GVA_DB.Model(&finance.FranchiseePerformance{}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "date"}, {Name: "franchisee_id"}, {Name: "special_mall_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"performance"}),
		}).
		Create(&rows).Error
}

// GetgeneralMallID 查询通用专区ID
func (p *performance) GetgeneralMallID(specialMallIDs []int) (int, error) {
	if len(specialMallIDs) == 0 {
		return 0, nil
	}
	var generalMallID int
	err := global.GVA_DB.Model(&products.SpecialMall{}).
		Where("id in (?)", specialMallIDs).
		Where("name like '%通用专区%'").
		Select("id").
		Scan(&generalMallID).Error

	if err != nil {
		return 0, err
	}
	return generalMallID, nil
}
