package tenant

import (
	"reflect"
	"strings"

	"github.com/OSQianXing/guanpu-server/global"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TenantPlugin GORM租户插件
type TenantPlugin struct{}

// Name 插件名称
func (tp *TenantPlugin) Name() string {
	return "tenant"
}

// Initialize 初始化插件
func (tp *TenantPlugin) Initialize(db *gorm.DB) error {
	// 注册回调函数
	err := db.Callback().Query().Before("gorm:query").Register("tenant:query", tp.beforeQuery)
	if err != nil {
		return err
	}

	err = db.Callback().Create().Before("gorm:create").Register("tenant:create", tp.beforeCreate)
	if err != nil {
		return err
	}

	err = db.Callback().Update().Before("gorm:update").Register("tenant:update", tp.beforeUpdate)
	if err != nil {
		return err
	}

	err = db.Callback().Delete().Before("gorm:delete").Register("tenant:delete", tp.beforeDelete)
	if err != nil {
		return err
	}

	return nil
}

// beforeQuery 查询前回调
func (tp *TenantPlugin) beforeQuery(db *gorm.DB) {
	// 检查是否跳过租户过滤
	if tp.shouldSkipTenant(db) {
		global.GVA_LOG.Debug("GORM租户插件: 跳过租户过滤",
			zap.String("table", tp.getTableName(db)),
			zap.String("operation", "query"))
		return
	}

	tableName := tp.getTableName(db)
	if !global.IsTenantTable(tableName) {
		global.GVA_LOG.Debug("GORM租户插件: 非租户表，跳过处理",
			zap.String("table", tableName),
			zap.String("operation", "query"))
		return
	}

	var tenantID uint

	// 优先从Context获取租户ID
	if ctx := db.Statement.Context; ctx != nil {
		if id, ok := global.GetTenantFromContext(ctx); ok && id > 0 {
			tenantID = id
		}
	}

	// 降级到线程安全的全局变量
	if tenantID == 0 {
		tenantID = global.GetCurrentTenantIDSafe()
	}

	if tenantID == 0 {
		global.GVA_LOG.Warn("GORM租户插件: 未找到租户ID，跳过租户过滤",
			zap.String("table", tableName),
			zap.String("operation", "query"))

		// 记录安全事件：缺少租户ID的查询尝试
		if ctx := db.Statement.Context; ctx != nil {
			if userID, ok := global.GetUserFromContext(ctx); ok {
				global.RecordTenantIsolationBypass(ctx, userID, 0, "", "", "query",
					"查询租户表时缺少租户ID", map[string]interface{}{
						"table":     tableName,
						"operation": "query",
					})
			}
		}
		return
	}

	// 添加租户过滤条件
	db.Where("tenant_id = ?", tenantID)
	global.GVA_LOG.Debug("GORM租户插件: 已添加租户过滤条件",
		zap.String("table", tableName),
		zap.Uint("tenantID", tenantID),
		zap.String("operation", "query"))

	// 检查是否处于降级模式
	if !global.IsTenantSystemHealthy() {
		global.GVA_LOG.Warn("GORM租户插件: 系统处于降级模式，租户隔离可能不完整",
			zap.String("table", tableName),
			zap.Uint("tenantID", tenantID))
	}
}

// beforeCreate 创建前回调
func (tp *TenantPlugin) beforeCreate(db *gorm.DB) {
	if tp.shouldSkipTenant(db) {
		global.GVA_LOG.Debug("GORM租户插件: 跳过租户处理",
			zap.String("table", tp.getTableName(db)),
			zap.String("operation", "create"))
		return
	}

	tableName := tp.getTableName(db)
	if !global.IsTenantTable(tableName) {
		global.GVA_LOG.Debug("GORM租户插件: 非租户表，跳过处理",
			zap.String("table", tableName),
			zap.String("operation", "create"))
		return
	}

	var tenantID uint

	if ctx := db.Statement.Context; ctx != nil {
		if id, ok := global.GetTenantFromContext(ctx); ok && id > 0 {
			tenantID = id
		}
	}

	if tenantID == 0 {
		tenantID = global.GetCurrentTenantIDSafe()
	}

	if tenantID == 0 {
		global.GVA_LOG.Warn("GORM租户插件: 未找到租户ID，跳过租户ID设置",
			zap.String("table", tableName),
			zap.String("operation", "create"))
		return
	}

	tp.setTenantID(db, tenantID)
	global.GVA_LOG.Debug("GORM租户插件: 已设置租户ID",
		zap.String("table", tableName),
		zap.Uint("tenantID", tenantID),
		zap.String("operation", "create"))
}

// beforeUpdate 更新前回调
func (tp *TenantPlugin) beforeUpdate(db *gorm.DB) {
	// 检查是否跳过租户过滤
	if tp.shouldSkipTenant(db) {
		return
	}

	tableName := tp.getTableName(db)
	if !global.IsTenantTable(tableName) {
		return
	}

	var tenantID uint

	// 优先从Context获取租户ID
	if ctx := db.Statement.Context; ctx != nil {
		if id, ok := global.GetTenantFromContext(ctx); ok && id > 0 {
			tenantID = id
		}
	}

	// 降级到线程安全的全局变量
	if tenantID == 0 {
		tenantID = global.GetCurrentTenantIDSafe()
	}

	if tenantID == 0 {
		return
	}

	// 添加租户过滤条件
	db.Where("tenant_id = ?", tenantID)
}

// beforeDelete 删除前回调
func (tp *TenantPlugin) beforeDelete(db *gorm.DB) {
	// 检查是否跳过租户过滤
	if tp.shouldSkipTenant(db) {
		return
	}

	tableName := tp.getTableName(db)
	if !global.IsTenantTable(tableName) {
		return
	}

	var tenantID uint

	// 优先从Context获取租户ID
	if ctx := db.Statement.Context; ctx != nil {
		if id, ok := global.GetTenantFromContext(ctx); ok && id > 0 {
			tenantID = id
		}
	}

	// 降级到线程安全的全局变量
	if tenantID == 0 {
		tenantID = global.GetCurrentTenantIDSafe()
	}

	if tenantID == 0 {
		return
	}

	// 添加租户过滤条件
	db.Where("tenant_id = ?", tenantID)
}

// shouldSkipTenant 检查是否应该跳过租户处理
func (tp *TenantPlugin) shouldSkipTenant(db *gorm.DB) bool {
	// 检查是否设置了跳过租户的标志
	if skip, ok := db.Get("skip_tenant"); ok && skip.(bool) {
		return true
	}

	// 检查是否是超级管理员操作
	if isSuper, ok := db.Get("super_admin"); ok && isSuper.(bool) {
		return true
	}

	return false
}

// getTableName 获取表名
func (tp *TenantPlugin) getTableName(db *gorm.DB) string {
	if db.Statement.Table != "" {
		return db.Statement.Table
	}

	if db.Statement.Model != nil {
		if tabler, ok := db.Statement.Model.(interface{ TableName() string }); ok {
			return tabler.TableName()
		}

		modelType := reflect.TypeOf(db.Statement.Model)
		if modelType.Kind() == reflect.Ptr {
			modelType = modelType.Elem()
		}

		// 转换为下划线命名
		return tp.toSnakeCase(modelType.Name())
	}

	return ""
}

// setTenantID 为模型设置租户ID
func (tp *TenantPlugin) setTenantID(db *gorm.DB, tenantID uint) {
	if db.Statement.Dest == nil {
		return
	}

	destValue := reflect.ValueOf(db.Statement.Dest)
	if destValue.Kind() == reflect.Ptr {
		destValue = destValue.Elem()
	}

	switch destValue.Kind() {
	case reflect.Slice:
		// 处理批量插入
		for i := 0; i < destValue.Len(); i++ {
			tp.setTenantIDForStruct(destValue.Index(i), tenantID)
		}
	case reflect.Struct:
		// 处理单个插入
		tp.setTenantIDForStruct(destValue, tenantID)
	}
}

// setTenantIDForStruct 为结构体设置租户ID
func (tp *TenantPlugin) setTenantIDForStruct(structValue reflect.Value, tenantID uint) {
	if structValue.Kind() == reflect.Ptr {
		structValue = structValue.Elem()
	}

	if structValue.Kind() != reflect.Struct {
		return
	}

	// 查找TenantID字段
	tenantField := structValue.FieldByName("TenantID")
	if !tenantField.IsValid() || !tenantField.CanSet() {
		return
	}

	// 只有当TenantID为0时才设置
	if tenantField.Uint() == 0 {
		tenantField.SetUint(uint64(tenantID))
	}
}

// toSnakeCase 转换为下划线命名
func (tp *TenantPlugin) toSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// SkipTenant 跳过租户过滤的作用域
func SkipTenant(db *gorm.DB) *gorm.DB {
	return db.Set("skip_tenant", true)
}

// SuperAdmin 超级管理员作用域
func SuperAdmin(db *gorm.DB) *gorm.DB {
	return db.Set("super_admin", true)
}

// WithTenant 指定租户的作用域
func WithTenant(tenantID uint) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 使用Context传递租户ID，避免全局状态污染
		ctx := global.WithTenantContext(db.Statement.Context, tenantID)
		return db.WithContext(ctx)
	}
}
