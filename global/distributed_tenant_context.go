package global

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// DistributedTenantContext 分布式租户上下文管理
type DistributedTenantContext struct {
	redis  *redis.Client
	prefix string
	ttl    time.Duration
}

// TenantSession 租户会话信息
type TenantSession struct {
	TenantID   uint      `json:"tenant_id"`
	UserID     uint      `json:"user_id"`
	SessionID  string    `json:"session_id"`
	CreatedAt  time.Time `json:"created_at"`
	LastAccess time.Time `json:"last_access"`
}

var distributedTenantCtx *DistributedTenantContext

// 新增上下文键
const (
	SessionContextKey = "session_id"
	UserContextKey    = "user_id"
)

// InitDistributedTenantContext 初始化分布式租户上下文
func InitDistributedTenantContext(redisClient *redis.Client) {
	distributedTenantCtx = &DistributedTenantContext{
		redis:  redisClient,
		prefix: "tenant:session:",
		ttl:    30 * time.Minute, // 30分钟会话超时
	}
}

// SetTenantSession 设置租户会话
func SetTenantSession(ctx context.Context, sessionID string, tenantID, userID uint) error {
	if distributedTenantCtx == nil {
		// 如果Redis未初始化，降级到本地存储
		return nil
	}

	session := TenantSession{
		TenantID:   tenantID,
		UserID:     userID,
		SessionID:  sessionID,
		CreatedAt:  time.Now(),
		LastAccess: time.Now(),
	}

	data, err := json.Marshal(session)
	if err != nil {
		return err
	}

	key := distributedTenantCtx.prefix + sessionID
	return distributedTenantCtx.redis.Set(ctx, key, data, distributedTenantCtx.ttl).Err()
}

// GetTenantSession 获取租户会话
func GetTenantSession(ctx context.Context, sessionID string) (*TenantSession, error) {
	if distributedTenantCtx == nil {
		return nil, fmt.Errorf("distributed tenant context not initialized")
	}

	key := distributedTenantCtx.prefix + sessionID
	data, err := distributedTenantCtx.redis.Get(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var session TenantSession
	err = json.Unmarshal([]byte(data), &session)
	if err != nil {
		return nil, err
	}

	// 更新最后访问时间
	session.LastAccess = time.Now()
	updatedData, _ := json.Marshal(session)
	distributedTenantCtx.redis.Set(ctx, key, updatedData, distributedTenantCtx.ttl)

	return &session, nil
}

// ClearTenantSession 清理租户会话
func ClearTenantSession(ctx context.Context, sessionID string) error {
	if distributedTenantCtx == nil {
		return nil
	}

	key := distributedTenantCtx.prefix + sessionID
	return distributedTenantCtx.redis.Del(ctx, key).Err()
}

// WithSessionContext 创建带会话信息的上下文
func WithSessionContext(ctx context.Context, sessionID string) context.Context {
	return context.WithValue(ctx, SessionContextKey, sessionID)
}

// GetSessionFromContext 从上下文获取会话ID
func GetSessionFromContext(ctx context.Context) (string, bool) {
	sessionID, ok := ctx.Value(SessionContextKey).(string)
	return sessionID, ok
}

// WithUserContext 创建带用户信息的上下文
func WithUserContext(ctx context.Context, userID uint) context.Context {
	return context.WithValue(ctx, UserContextKey, userID)
}

// GetUserFromContext 从上下文获取用户ID
func GetUserFromContext(ctx context.Context) (uint, bool) {
	userID, ok := ctx.Value(UserContextKey).(uint)
	return userID, ok
}

// GetTenantFromSession 从会话获取租户信息
func GetTenantFromSession(ctx context.Context, sessionID string) (uint, error) {
	session, err := GetTenantSession(ctx, sessionID)
	if err != nil {
		return 0, err
	}
	return session.TenantID, nil
}

// IsDistributedMode 检查是否启用分布式模式
func IsDistributedMode() bool {
	return distributedTenantCtx != nil
}

// GetActiveSessions 获取活跃会话统计
func GetActiveSessions(ctx context.Context) (int64, error) {
	if distributedTenantCtx == nil {
		return 0, fmt.Errorf("distributed tenant context not initialized")
	}

	pattern := distributedTenantCtx.prefix + "*"
	keys, err := distributedTenantCtx.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return 0, err
	}

	return int64(len(keys)), nil
}

// CleanupExpiredSessions 清理过期会话（可以定期调用）
func CleanupExpiredSessions(ctx context.Context) error {
	if distributedTenantCtx == nil {
		return nil
	}

	pattern := distributedTenantCtx.prefix + "*"
	keys, err := distributedTenantCtx.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	expiredKeys := make([]string, 0)
	for _, key := range keys {
		ttl, err := distributedTenantCtx.redis.TTL(ctx, key).Result()
		if err != nil || ttl <= 0 {
			expiredKeys = append(expiredKeys, key)
		}
	}

	if len(expiredKeys) > 0 {
		return distributedTenantCtx.redis.Del(ctx, expiredKeys...).Err()
	}

	return nil
}