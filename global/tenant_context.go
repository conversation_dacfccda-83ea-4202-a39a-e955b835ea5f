package global

import (
	"context"
	"runtime"
	"strconv"
	"strings"
	"sync"
)

// TenantContext 租户上下文管理
type TenantContext struct {
	tenantID uint
	mu       sync.RWMutex
}

// 线程本地存储，解决并发安全问题
type threadLocalTenant struct {
	tenants map[int64]uint
	mutex   sync.RWMutex
}

var (
	// 全局租户上下文（保留向后兼容）
	globalTenantContext = &TenantContext{}

	// 线程本地租户存储
	tlsTenant = &threadLocalTenant{
		tenants: make(map[int64]uint),
	}

	// 租户上下文键
	TenantContextKey = "tenant_id"
)

// 获取当前goroutine ID
func getGoroutineID() int64 {
	buf := make([]byte, 64)
	buf = buf[:runtime.Stack(buf, false)]
	buf = buf[10:] // 跳过 "goroutine "
	buf = buf[:strings.IndexByte(string(buf), ' ')]
	id, _ := strconv.ParseInt(string(buf), 10, 64)
	return id
}

// SetTenantID 设置当前租户ID
func (tc *TenantContext) SetTenantID(tenantID uint) {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	tc.tenantID = tenantID
}

// GetTenantID 获取当前租户ID
func (tc *TenantContext) GetTenantID() uint {
	tc.mu.RLock()
	defer tc.mu.RUnlock()
	return tc.tenantID
}

// SetCurrentTenantID 设置当前请求的租户ID（保留向后兼容）
func SetCurrentTenantID(tenantID uint) {
	globalTenantContext.SetTenantID(tenantID)
}

// GetCurrentTenantID 获取当前请求的租户ID（保留向后兼容）
func GetCurrentTenantID() uint {
	return globalTenantContext.GetTenantID()
}

// SetCurrentTenantIDSafe 线程安全的设置租户ID
func SetCurrentTenantIDSafe(tenantID uint) {
	gid := getGoroutineID()
	tlsTenant.mutex.Lock()
	tlsTenant.tenants[gid] = tenantID
	tlsTenant.mutex.Unlock()

	// 同时设置全局变量（向后兼容）
	SetCurrentTenantID(tenantID)
}

// GetCurrentTenantIDSafe 线程安全的获取租户ID
func GetCurrentTenantIDSafe() uint {
	gid := getGoroutineID()
	tlsTenant.mutex.RLock()
	tenantID := tlsTenant.tenants[gid]
	tlsTenant.mutex.RUnlock()

	if tenantID == 0 {
		// 降级到全局变量
		return GetCurrentTenantID()
	}
	return tenantID
}

// ClearCurrentTenantID 清理当前租户ID
func ClearCurrentTenantID() {
	gid := getGoroutineID()
	tlsTenant.mutex.Lock()
	delete(tlsTenant.tenants, gid)
	tlsTenant.mutex.Unlock()

	// 同时清理全局变量
	SetCurrentTenantID(0)
}

// WithTenantContext 创建带租户信息的上下文
func WithTenantContext(ctx context.Context, tenantID uint) context.Context {
	return context.WithValue(ctx, TenantContextKey, tenantID)
}

// GetTenantFromContext 从上下文获取租户ID
func GetTenantFromContext(ctx context.Context) (uint, bool) {
	tenantID, ok := ctx.Value(TenantContextKey).(uint)
	return tenantID, ok
}

// TenantScope GORM作用域 (DEPRECATED: GORM tenant plugin handles this automatically)
// func TenantScope(tenantID uint) func(db *gorm.DB) *gorm.DB {
// 	return func(db *gorm.DB) *gorm.DB {
// 		if tenantID > 0 {
// 			return db.Where("tenant_id = ?", tenantID)
// 		}
// 		return db
// 	}
// }

// AutoTenantScope 自动租户作用域 (DEPRECATED: GORM tenant plugin handles this automatically)
// func AutoTenantScope() func(db *gorm.DB) *gorm.DB {
// 	return func(db *gorm.DB) *gorm.DB {
// 		tenantID := GetCurrentTenantID() // Or GetCurrentTenantIDSafe()
// 		if tenantID > 0 {
// 			return db.Where("tenant_id = ?", tenantID)
// 		}
// 		return db
// 	}
// }

// IsTenantTable 检查表是否需要租户隔离
func IsTenantTable(tableName string) bool {
	// 定义需要租户隔离的表
	tenantTables := map[string]bool{
		"order":                  true,
		"order_goods":            true,
		"order_delivery":         true,
		"order_return":           true,
		"order_remark":           true,
		"products":               true,
		"product_brand":          true,
		"product_category":       true,
		"franchisees":            true,
		"franchisee_address":     true,
		"franchisee_category":    true,
		"recharge_record":        true,
		"cart":                   true,
		"cloud_warehouse":        true,
		"cloud_warehouse_record": true,
		"big_warehouse_order":    true,
		"big_warehouse_stock":    true,
		"big_warehouse_record":   true,
		"order_online_pay":       true,
		"franchisee_performance": true,
		// 添加其他需要租户隔离的表
	}

	return tenantTables[tableName]
}
