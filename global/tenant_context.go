package global

import (
	"context"
	"reflect"
	"runtime"
	"strconv"
	"strings"
	"sync"

	"go.uber.org/zap"
)

// TenantContext 租户上下文管理
type TenantContext struct {
	tenantID uint
	mu       sync.RWMutex
}

// 线程本地存储，解决并发安全问题
type threadLocalTenant struct {
	tenants map[int64]uint
	mutex   sync.RWMutex
}

var (
	// 全局租户上下文（保留向后兼容）
	globalTenantContext = &TenantContext{}

	// 线程本地租户存储
	tlsTenant = &threadLocalTenant{
		tenants: make(map[int64]uint),
	}

	// 租户上下文键
	TenantContextKey = "tenant_id"
)

// 获取当前goroutine ID
func getGoroutineID() int64 {
	buf := make([]byte, 64)
	buf = buf[:runtime.Stack(buf, false)]
	buf = buf[10:] // 跳过 "goroutine "
	buf = buf[:strings.IndexByte(string(buf), ' ')]
	id, _ := strconv.ParseInt(string(buf), 10, 64)
	return id
}

// SetTenantID 设置当前租户ID
func (tc *TenantContext) SetTenantID(tenantID uint) {
	tc.mu.Lock()
	defer tc.mu.Unlock()
	tc.tenantID = tenantID
}

// GetTenantID 获取当前租户ID
func (tc *TenantContext) GetTenantID() uint {
	tc.mu.RLock()
	defer tc.mu.RUnlock()
	return tc.tenantID
}

// SetCurrentTenantID 设置当前请求的租户ID（保留向后兼容）
func SetCurrentTenantID(tenantID uint) {
	globalTenantContext.SetTenantID(tenantID)
}

// GetCurrentTenantID 获取当前请求的租户ID（保留向后兼容）
func GetCurrentTenantID() uint {
	return globalTenantContext.GetTenantID()
}

// SetCurrentTenantIDSafe 线程安全的设置租户ID
func SetCurrentTenantIDSafe(tenantID uint) {
	gid := getGoroutineID()
	tlsTenant.mutex.Lock()
	tlsTenant.tenants[gid] = tenantID
	tlsTenant.mutex.Unlock()

	// 同时设置全局变量（向后兼容）
	SetCurrentTenantID(tenantID)
}

// GetCurrentTenantIDSafe 线程安全的获取租户ID
func GetCurrentTenantIDSafe() uint {
	gid := getGoroutineID()
	tlsTenant.mutex.RLock()
	tenantID := tlsTenant.tenants[gid]
	tlsTenant.mutex.RUnlock()

	if tenantID == 0 {
		// 降级到全局变量
		return GetCurrentTenantID()
	}
	return tenantID
}

// ClearCurrentTenantID 清理当前租户ID
func ClearCurrentTenantID() {
	gid := getGoroutineID()
	tlsTenant.mutex.Lock()
	delete(tlsTenant.tenants, gid)
	tlsTenant.mutex.Unlock()

	// 同时清理全局变量
	SetCurrentTenantID(0)
}

// WithTenantContext 创建带租户信息的上下文
func WithTenantContext(ctx context.Context, tenantID uint) context.Context {
	return context.WithValue(ctx, TenantContextKey, tenantID)
}

// GetTenantFromContext 从上下文获取租户ID
func GetTenantFromContext(ctx context.Context) (uint, bool) {
	tenantID, ok := ctx.Value(TenantContextKey).(uint)
	return tenantID, ok
}

// TenantScope GORM作用域 (DEPRECATED: GORM tenant plugin handles this automatically)
// func TenantScope(tenantID uint) func(db *gorm.DB) *gorm.DB {
// 	return func(db *gorm.DB) *gorm.DB {
// 		if tenantID > 0 {
// 			return db.Where("tenant_id = ?", tenantID)
// 		}
// 		return db
// 	}
// }

// AutoTenantScope 自动租户作用域 (DEPRECATED: GORM tenant plugin handles this automatically)
// func AutoTenantScope() func(db *gorm.DB) *gorm.DB {
// 	return func(db *gorm.DB) *gorm.DB {
// 		tenantID := GetCurrentTenantID() // Or GetCurrentTenantIDSafe()
// 		if tenantID > 0 {
// 			return db.Where("tenant_id = ?", tenantID)
// 		}
// 		return db
// 	}
// }

// 自动检测的租户表缓存
var (
	autoDetectedTenantTables map[string]bool
	tenantTablesMutex        sync.RWMutex
	tenantTablesInitialized  bool
)

// IsTenantTable 检查表是否需要租户隔离
func IsTenantTable(tableName string) bool {
	// 首先检查自动检测的表
	if isAutoDetectedTenantTable(tableName) {
		return true
	}

	// 回退到手动配置的表（向后兼容）
	manualTenantTables := map[string]bool{
		"order":                  true,
		"order_goods":            true,
		"order_delivery":         true,
		"order_return":           true,
		"order_remark":           true,
		"products":               true,
		"product_brand":          true,
		"product_category":       true,
		"franchisees":            true,
		"franchisee_address":     true,
		"franchisee_category":    true,
		"recharge_record":        true,
		"cart":                   true,
		"cloud_warehouse":        true,
		"cloud_warehouse_record": true,
		"big_warehouse_order":    true,
		"big_warehouse_stock":    true,
		"big_warehouse_record":   true,
		"order_online_pay":       true,
		"franchisee_performance": true,
		// 添加其他需要租户隔离的表
	}

	return manualTenantTables[tableName]
}

// isAutoDetectedTenantTable 检查是否为自动检测的租户表
func isAutoDetectedTenantTable(tableName string) bool {
	tenantTablesMutex.RLock()
	defer tenantTablesMutex.RUnlock()

	if autoDetectedTenantTables == nil {
		return false
	}

	return autoDetectedTenantTables[tableName]
}

// RegisterTenantModel 注册包含TenantID字段的模型
func RegisterTenantModel(model interface{}) {
	tenantTablesMutex.Lock()
	defer tenantTablesMutex.Unlock()

	if autoDetectedTenantTables == nil {
		autoDetectedTenantTables = make(map[string]bool)
	}

	tableName := getTableNameFromModel(model)
	if tableName != "" && hasTenantIDField(model) {
		autoDetectedTenantTables[tableName] = true
		if GVA_LOG != nil {
			GVA_LOG.Info("自动注册租户表",
				zap.String("tableName", tableName),
				zap.String("modelType", reflect.TypeOf(model).String()))
		}
	}
}

// getTableNameFromModel 从模型获取表名
func getTableNameFromModel(model interface{}) string {
	// 尝试调用TableName方法
	if tabler, ok := model.(interface{ TableName() string }); ok {
		return tabler.TableName()
	}

	// 如果没有TableName方法，使用反射获取类型名并转换为下划线命名
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	return toSnakeCase(modelType.Name())
}

// hasTenantIDField 检查模型是否包含TenantID字段
func hasTenantIDField(model interface{}) bool {
	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	if modelType.Kind() != reflect.Struct {
		return false
	}

	// 检查是否有TenantID字段
	for i := 0; i < modelType.NumField(); i++ {
		field := modelType.Field(i)
		if field.Name == "TenantID" || field.Tag.Get("gorm") == "column:tenant_id" {
			return true
		}
	}

	return false
}

// toSnakeCase 将驼峰命名转换为下划线命名
func toSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(r)
	}
	return strings.ToLower(result.String())
}

// InitAutoTenantDetection 初始化自动租户检测
// 注册所有包含TenantID字段的模型
func InitAutoTenantDetection() {
	// 注册已知的租户模型
	// 这些模型将被自动检测并注册

	// 注意：这里需要导入相关的模型包
	// 为了避免循环导入，我们提供一个注册接口
	// 让各个模块自己注册自己的租户模型

	if GVA_LOG != nil {
		GVA_LOG.Info("自动租户检测初始化完成")
	}
}

// GetAutoDetectedTenantTables 获取自动检测到的租户表列表（用于调试）
func GetAutoDetectedTenantTables() map[string]bool {
	tenantTablesMutex.RLock()
	defer tenantTablesMutex.RUnlock()

	result := make(map[string]bool)
	for k, v := range autoDetectedTenantTables {
		result[k] = v
	}
	return result
}
