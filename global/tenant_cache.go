package global

import (
	"sync"
	"time"

	"go.uber.org/zap"
)

// TenantCache 租户缓存管理器
type TenantCache struct {
	cache         map[uint]*CachedTenant
	mutex         sync.RWMutex
	ttl           time.Duration
	maxSize       int
	hitCount      int64
	missCount     int64
	refreshTicker *time.Ticker
	stopChan      chan struct{}
}

// CachedTenant 缓存的租户信息
type CachedTenant struct {
	IsValid     bool
	CachedAt    time.Time
	LastAccess  time.Time
	AccessCount int64
}

var tenantCache = &TenantCache{
	cache:    make(map[uint]*CachedTenant),
	ttl:      5 * time.Minute, // 5分钟缓存
	maxSize:  10000,           // 最大缓存10000个租户
	stopChan: make(chan struct{}),
}

// 初始化缓存清理定时器
func init() {
	tenantCache.startCleanupTimer()
}

// ValidateTenantCached 验证租户有效性（使用缓存）
func ValidateTenantCached(tenantID uint) bool {
	if tenantID == 0 {
		return false
	}

	// 检查缓存
	tenantCache.mutex.RLock()
	cached, exists := tenantCache.cache[tenantID]
	tenantCache.mutex.RUnlock()

	if exists && time.Since(cached.CachedAt) < tenantCache.ttl {
		// 缓存命中，更新访问统计
		tenantCache.mutex.Lock()
		cached.LastAccess = time.Now()
		cached.AccessCount++
		tenantCache.hitCount++
		tenantCache.mutex.Unlock()
		return cached.IsValid
	}

	// 缓存未命中
	tenantCache.mutex.Lock()
	tenantCache.missCount++
	tenantCache.mutex.Unlock()

	// 从数据库验证 - 使用原始SQL避免循环导入
	var count int64
	var status *bool
	var expireDate *time.Time

	err := GVA_DB.Table("tenants").
		Select("COUNT(*) as count, status, expire_date").
		Where("id = ?", tenantID).
		Row().Scan(&count, &status, &expireDate)

	isValid := false
	if err == nil && count > 0 {
		isValid = (status == nil || *status) &&
			(expireDate == nil || expireDate.After(time.Now()))
	}

	// 更新缓存
	tenantCache.mutex.Lock()

	// 检查缓存大小限制
	if len(tenantCache.cache) >= tenantCache.maxSize {
		tenantCache.evictLRU()
	}

	now := time.Now()
	tenantCache.cache[tenantID] = &CachedTenant{
		IsValid:     isValid,
		CachedAt:    now,
		LastAccess:  now,
		AccessCount: 1,
	}
	tenantCache.mutex.Unlock()

	return isValid
}

// InvalidateTenantCache 使租户缓存失效
func InvalidateTenantCache(tenantID uint) {
	tenantCache.mutex.Lock()
	delete(tenantCache.cache, tenantID)
	tenantCache.mutex.Unlock()
}

// ClearTenantCache 清空所有租户缓存
func ClearTenantCache() {
	tenantCache.mutex.Lock()
	tenantCache.cache = make(map[uint]*CachedTenant)
	tenantCache.mutex.Unlock()
}

// GetTenantCacheStats 获取缓存统计信息
func GetTenantCacheStats() map[string]interface{} {
	tenantCache.mutex.RLock()
	defer tenantCache.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_cached": len(tenantCache.cache),
		"ttl_seconds":  int(tenantCache.ttl.Seconds()),
	}

	validCount := 0
	expiredCount := 0

	for _, cached := range tenantCache.cache {
		if time.Since(cached.CachedAt) > tenantCache.ttl {
			expiredCount++
		} else if cached.IsValid {
			validCount++
		}
	}

	stats["valid_cached"] = validCount
	stats["expired_cached"] = expiredCount
	stats["hit_count"] = tenantCache.hitCount
	stats["miss_count"] = tenantCache.missCount

	// 计算命中率
	totalRequests := tenantCache.hitCount + tenantCache.missCount
	if totalRequests > 0 {
		stats["hit_rate"] = float64(tenantCache.hitCount) / float64(totalRequests)
	} else {
		stats["hit_rate"] = 0.0
	}

	return stats
}

// startCleanupTimer 启动缓存清理定时器
func (tc *TenantCache) startCleanupTimer() {
	tc.refreshTicker = time.NewTicker(tc.ttl / 2) // 每2.5分钟清理一次
	go func() {
		for {
			select {
			case <-tc.refreshTicker.C:
				tc.cleanupExpiredEntries()
			case <-tc.stopChan:
				tc.refreshTicker.Stop()
				return
			}
		}
	}()
}

// cleanupExpiredEntries 清理过期的缓存条目
func (tc *TenantCache) cleanupExpiredEntries() {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()

	now := time.Now()
	expiredKeys := make([]uint, 0)

	for key, cached := range tc.cache {
		if now.Sub(cached.CachedAt) > tc.ttl {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(tc.cache, key)
	}

	if len(expiredKeys) > 0 && GVA_LOG != nil {
		GVA_LOG.Debug("清理过期租户缓存",
			zap.Int("expiredCount", len(expiredKeys)),
			zap.Int("remainingCount", len(tc.cache)))
	}
}

// evictLRU 淘汰最少使用的缓存条目
func (tc *TenantCache) evictLRU() {
	if len(tc.cache) <= tc.maxSize {
		return
	}

	var lruKey uint
	var lruTime time.Time = time.Now()

	for key, cached := range tc.cache {
		if cached.LastAccess.Before(lruTime) {
			lruTime = cached.LastAccess
			lruKey = key
		}
	}

	if lruKey > 0 {
		delete(tc.cache, lruKey)
		if GVA_LOG != nil {
			GVA_LOG.Debug("淘汰LRU租户缓存",
				zap.Uint("tenantID", lruKey),
				zap.Time("lastAccess", lruTime))
		}
	}
}

// StopCleanupTimer 停止缓存清理定时器
func (tc *TenantCache) StopCleanupTimer() {
	close(tc.stopChan)
}

// PrewarmCache 预热缓存
func PrewarmCache(tenantIDs []uint) {
	if len(tenantIDs) == 0 {
		return
	}

	for _, tenantID := range tenantIDs {
		// 触发缓存加载
		ValidateTenantCached(tenantID)
	}

	if GVA_LOG != nil {
		GVA_LOG.Info("租户缓存预热完成",
			zap.Int("prewarmedCount", len(tenantIDs)))
	}
}
