package global

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// TenantMonitorMetrics 租户监控指标
type TenantMonitorMetrics struct {
	// 基础指标
	TotalRequests       int64 `json:"total_requests"`
	TenantRequests      int64 `json:"tenant_requests"`
	CrossTenantAttempts int64 `json:"cross_tenant_attempts"`
	FailedValidations   int64 `json:"failed_validations"`
	
	// 性能指标
	AvgResponseTime     float64 `json:"avg_response_time_ms"`
	CacheHitRate        float64 `json:"cache_hit_rate"`
	DatabaseQueryCount  int64   `json:"database_query_count"`
	
	// 错误指标
	ErrorCount          int64 `json:"error_count"`
	FallbackModeCount   int64 `json:"fallback_mode_count"`
	SecurityEventCount  int64 `json:"security_event_count"`
	
	// 时间戳
	LastUpdated         time.Time `json:"last_updated"`
}

// TenantMonitor 租户监控器
type TenantMonitor struct {
	metrics           TenantMonitorMetrics
	mutex             sync.RWMutex
	alertThresholds   AlertThresholds
	monitoringEnabled bool
	ticker            *time.Ticker
	stopChan          chan struct{}
}

// AlertThresholds 告警阈值配置
type AlertThresholds struct {
	CrossTenantAttemptRate  float64 `json:"cross_tenant_attempt_rate"`  // 跨租户访问尝试率阈值
	FailedValidationRate    float64 `json:"failed_validation_rate"`     // 验证失败率阈值
	AvgResponseTimeMs       float64 `json:"avg_response_time_ms"`        // 平均响应时间阈值
	CacheHitRateMin         float64 `json:"cache_hit_rate_min"`          // 缓存命中率最低阈值
	ErrorRateMax            float64 `json:"error_rate_max"`              // 错误率最大阈值
	SecurityEventRateMax    float64 `json:"security_event_rate_max"`     // 安全事件率最大阈值
}

var (
	tenantMonitor *TenantMonitor
	monitorOnce   sync.Once
)

// GetTenantMonitor 获取租户监控器实例（单例）
func GetTenantMonitor() *TenantMonitor {
	monitorOnce.Do(func() {
		tenantMonitor = &TenantMonitor{
			metrics: TenantMonitorMetrics{
				LastUpdated: time.Now(),
			},
			alertThresholds: AlertThresholds{
				CrossTenantAttemptRate:  0.01, // 1%
				FailedValidationRate:    0.05, // 5%
				AvgResponseTimeMs:       100,  // 100ms
				CacheHitRateMin:         0.8,  // 80%
				ErrorRateMax:            0.02, // 2%
				SecurityEventRateMax:    0.001, // 0.1%
			},
			monitoringEnabled: true,
			stopChan:          make(chan struct{}),
		}
		tenantMonitor.startMonitoring()
	})
	return tenantMonitor
}

// startMonitoring 启动监控
func (tm *TenantMonitor) startMonitoring() {
	tm.ticker = time.NewTicker(1 * time.Minute) // 每分钟检查一次
	go func() {
		for {
			select {
			case <-tm.ticker.C:
				tm.collectMetrics()
				tm.checkAlerts()
			case <-tm.stopChan:
				tm.ticker.Stop()
				return
			}
		}
	}()
}

// StopMonitoring 停止监控
func (tm *TenantMonitor) StopMonitoring() {
	close(tm.stopChan)
}

// RecordRequest 记录请求
func (tm *TenantMonitor) RecordRequest(isTenantRequest bool, responseTime time.Duration) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.TotalRequests++
	if isTenantRequest {
		tm.metrics.TenantRequests++
	}
	
	// 更新平均响应时间（简单移动平均）
	if tm.metrics.TotalRequests == 1 {
		tm.metrics.AvgResponseTime = float64(responseTime.Nanoseconds()) / 1e6
	} else {
		tm.metrics.AvgResponseTime = (tm.metrics.AvgResponseTime*0.9) + (float64(responseTime.Nanoseconds())/1e6*0.1)
	}
}

// RecordCrossTenantAttempt 记录跨租户访问尝试
func (tm *TenantMonitor) RecordCrossTenantAttempt() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.CrossTenantAttempts++
}

// RecordFailedValidation 记录验证失败
func (tm *TenantMonitor) RecordFailedValidation() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.FailedValidations++
}

// RecordError 记录错误
func (tm *TenantMonitor) RecordError() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.ErrorCount++
}

// RecordFallbackMode 记录降级模式
func (tm *TenantMonitor) RecordFallbackMode() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.FallbackModeCount++
}

// RecordSecurityEvent 记录安全事件
func (tm *TenantMonitor) RecordSecurityEvent() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.SecurityEventCount++
}

// RecordDatabaseQuery 记录数据库查询
func (tm *TenantMonitor) RecordDatabaseQuery() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics.DatabaseQueryCount++
}

// collectMetrics 收集指标
func (tm *TenantMonitor) collectMetrics() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	// 更新缓存命中率
	if cacheStats := GetTenantCacheStats(); cacheStats != nil {
		if hitRate, ok := cacheStats["hit_rate"].(float64); ok {
			tm.metrics.CacheHitRate = hitRate
		}
	}
	
	tm.metrics.LastUpdated = time.Now()
}

// checkAlerts 检查告警
func (tm *TenantMonitor) checkAlerts() {
	tm.mutex.RLock()
	metrics := tm.metrics
	thresholds := tm.alertThresholds
	tm.mutex.RUnlock()
	
	if metrics.TotalRequests == 0 {
		return // 没有请求数据，跳过检查
	}
	
	// 检查跨租户访问尝试率
	crossTenantRate := float64(metrics.CrossTenantAttempts) / float64(metrics.TotalRequests)
	if crossTenantRate > thresholds.CrossTenantAttemptRate {
		tm.triggerAlert("cross_tenant_attempt_rate", crossTenantRate, thresholds.CrossTenantAttemptRate)
	}
	
	// 检查验证失败率
	failedValidationRate := float64(metrics.FailedValidations) / float64(metrics.TotalRequests)
	if failedValidationRate > thresholds.FailedValidationRate {
		tm.triggerAlert("failed_validation_rate", failedValidationRate, thresholds.FailedValidationRate)
	}
	
	// 检查平均响应时间
	if metrics.AvgResponseTime > thresholds.AvgResponseTimeMs {
		tm.triggerAlert("avg_response_time", metrics.AvgResponseTime, thresholds.AvgResponseTimeMs)
	}
	
	// 检查缓存命中率
	if metrics.CacheHitRate < thresholds.CacheHitRateMin {
		tm.triggerAlert("cache_hit_rate", metrics.CacheHitRate, thresholds.CacheHitRateMin)
	}
	
	// 检查错误率
	errorRate := float64(metrics.ErrorCount) / float64(metrics.TotalRequests)
	if errorRate > thresholds.ErrorRateMax {
		tm.triggerAlert("error_rate", errorRate, thresholds.ErrorRateMax)
	}
	
	// 检查安全事件率
	securityEventRate := float64(metrics.SecurityEventCount) / float64(metrics.TotalRequests)
	if securityEventRate > thresholds.SecurityEventRateMax {
		tm.triggerAlert("security_event_rate", securityEventRate, thresholds.SecurityEventRateMax)
	}
}

// triggerAlert 触发告警
func (tm *TenantMonitor) triggerAlert(alertType string, currentValue, threshold float64) {
	if GVA_LOG == nil {
		return
	}
	
	GVA_LOG.Error("🚨 租户监控告警",
		zap.String("alert_type", alertType),
		zap.Float64("current_value", currentValue),
		zap.Float64("threshold", threshold),
		zap.Time("timestamp", time.Now()))
	
	// 这里可以集成其他告警渠道，如邮件、短信、钉钉等
	tm.sendAlert(alertType, currentValue, threshold)
}

// sendAlert 发送告警（可扩展）
func (tm *TenantMonitor) sendAlert(alertType string, currentValue, threshold float64) {
	// TODO: 集成告警渠道
	// 1. 邮件告警
	// 2. 短信告警
	// 3. 钉钉/企业微信告警
	// 4. Prometheus/Grafana告警
	
	if GVA_LOG != nil {
		GVA_LOG.Info("📧 告警已发送",
			zap.String("alert_type", alertType),
			zap.Float64("current_value", currentValue),
			zap.Float64("threshold", threshold))
	}
}

// GetMetrics 获取监控指标
func (tm *TenantMonitor) GetMetrics() TenantMonitorMetrics {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()
	
	return tm.metrics
}

// GetHealthStatus 获取健康状态
func (tm *TenantMonitor) GetHealthStatus() map[string]interface{} {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()
	
	status := map[string]interface{}{
		"healthy": true,
		"issues":  []string{},
	}
	
	issues := []string{}
	
	if tm.metrics.TotalRequests > 0 {
		// 检查各项指标
		crossTenantRate := float64(tm.metrics.CrossTenantAttempts) / float64(tm.metrics.TotalRequests)
		if crossTenantRate > tm.alertThresholds.CrossTenantAttemptRate {
			issues = append(issues, "跨租户访问尝试率过高")
		}
		
		failedValidationRate := float64(tm.metrics.FailedValidations) / float64(tm.metrics.TotalRequests)
		if failedValidationRate > tm.alertThresholds.FailedValidationRate {
			issues = append(issues, "租户验证失败率过高")
		}
		
		if tm.metrics.AvgResponseTime > tm.alertThresholds.AvgResponseTimeMs {
			issues = append(issues, "平均响应时间过长")
		}
		
		if tm.metrics.CacheHitRate < tm.alertThresholds.CacheHitRateMin {
			issues = append(issues, "缓存命中率过低")
		}
	}
	
	// 检查系统状态
	if !IsTenantSystemHealthy() {
		issues = append(issues, "租户系统处于降级模式")
	}
	
	if len(issues) > 0 {
		status["healthy"] = false
		status["issues"] = issues
	}
	
	return status
}

// ResetMetrics 重置指标
func (tm *TenantMonitor) ResetMetrics() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.metrics = TenantMonitorMetrics{
		LastUpdated: time.Now(),
	}
}

// UpdateThresholds 更新告警阈值
func (tm *TenantMonitor) UpdateThresholds(thresholds AlertThresholds) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	
	tm.alertThresholds = thresholds
	
	if GVA_LOG != nil {
		GVA_LOG.Info("告警阈值已更新", zap.Any("thresholds", thresholds))
	}
}

// 便捷函数

// RecordTenantRequest 记录租户请求
func RecordTenantRequest(responseTime time.Duration) {
	monitor := GetTenantMonitor()
	monitor.RecordRequest(true, responseTime)
}

// RecordNonTenantRequest 记录非租户请求
func RecordNonTenantRequest(responseTime time.Duration) {
	monitor := GetTenantMonitor()
	monitor.RecordRequest(false, responseTime)
}

// RecordTenantCrossTenantAttempt 记录跨租户访问尝试
func RecordTenantCrossTenantAttempt() {
	monitor := GetTenantMonitor()
	monitor.RecordCrossTenantAttempt()
}

// RecordTenantValidationFailure 记录租户验证失败
func RecordTenantValidationFailure() {
	monitor := GetTenantMonitor()
	monitor.RecordFailedValidation()
}

// GetTenantSystemHealth 获取租户系统健康状态
func GetTenantSystemHealth() map[string]interface{} {
	monitor := GetTenantMonitor()
	return monitor.GetHealthStatus()
}
