package global

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// DistributedTenantCache 分布式租户缓存
type DistributedTenantCache struct {
	redis      *redis.Client
	prefix     string
	ttl        time.Duration
	localCache *LocalTenantCache // L1缓存
}

// LocalTenantCache 本地租户缓存
type LocalTenantCache struct {
	cache map[uint]*CachedTenant
	mutex sync.RWMutex
	ttl   time.Duration
}

// CachedTenantInfo Redis中的租户缓存信息
type CachedTenantInfo struct {
	TenantID   uint       `json:"tenant_id"`
	IsValid    bool       `json:"is_valid"`
	Status     *bool      `json:"status"`
	ExpireDate *time.Time `json:"expire_date"`
	CachedAt   time.Time  `json:"cached_at"`
}

var distributedCache *DistributedTenantCache

// InitDistributedTenantCache 初始化分布式租户缓存
func InitDistributedTenantCache(redisClient *redis.Client) {
	distributedCache = &DistributedTenantCache{
		redis:  redisClient,
		prefix: "tenant:cache:",
		ttl:    10 * time.Minute, // Redis缓存10分钟
		localCache: &LocalTenantCache{
			cache: make(map[uint]*CachedTenant),
			ttl:   2 * time.Minute, // 本地缓存2分钟
		},
	}
}

// ValidateTenantDistributed 分布式租户验证
func ValidateTenantDistributed(ctx context.Context, tenantID uint) bool {
	if tenantID == 0 {
		return false
	}

	// 如果分布式缓存未初始化，降级到原有方法
	if distributedCache == nil {
		return ValidateTenantCached(tenantID)
	}

	// L1缓存：本地内存缓存（最快）
	if cached := distributedCache.getFromLocalCache(tenantID); cached != nil {
		return cached.IsValid
	}

	// L2缓存：Redis分布式缓存
	if cached := distributedCache.getFromRedisCache(ctx, tenantID); cached != nil {
		// 更新本地缓存
		distributedCache.setToLocalCache(tenantID, cached.IsValid)
		return cached.IsValid
	}

	// L3：数据库查询
	isValid := distributedCache.validateFromDatabase(ctx, tenantID)

	// 更新所有缓存层
	distributedCache.setToRedisCache(ctx, tenantID, isValid)
	distributedCache.setToLocalCache(tenantID, isValid)

	return isValid
}

// getFromLocalCache 从本地缓存获取
func (dc *DistributedTenantCache) getFromLocalCache(tenantID uint) *CachedTenant {
	dc.localCache.mutex.RLock()
	defer dc.localCache.mutex.RUnlock()

	cached, exists := dc.localCache.cache[tenantID]
	if !exists || time.Since(cached.CachedAt) > dc.localCache.ttl {
		return nil
	}

	return cached
}

// getFromRedisCache 从Redis缓存获取
func (dc *DistributedTenantCache) getFromRedisCache(ctx context.Context, tenantID uint) *CachedTenantInfo {
	key := fmt.Sprintf("%s%d", dc.prefix, tenantID)
	data, err := dc.redis.Get(ctx, key).Result()
	if err != nil {
		return nil
	}

	var cached CachedTenantInfo
	err = json.Unmarshal([]byte(data), &cached)
	if err != nil {
		return nil
	}

	return &cached
}

// setToRedisCache 设置Redis缓存
func (dc *DistributedTenantCache) setToRedisCache(ctx context.Context, tenantID uint, isValid bool) {
	cached := CachedTenantInfo{
		TenantID: tenantID,
		IsValid:  isValid,
		CachedAt: time.Now(),
	}

	data, err := json.Marshal(cached)
	if err != nil {
		return
	}

	key := fmt.Sprintf("%s%d", dc.prefix, tenantID)
	dc.redis.Set(ctx, key, data, dc.ttl)
}

// setToLocalCache 设置本地缓存
func (dc *DistributedTenantCache) setToLocalCache(tenantID uint, isValid bool) {
	dc.localCache.mutex.Lock()
	defer dc.localCache.mutex.Unlock()

	dc.localCache.cache[tenantID] = &CachedTenant{
		IsValid:  isValid,
		CachedAt: time.Now(),
	}
}

// validateFromDatabase 从数据库验证
func (dc *DistributedTenantCache) validateFromDatabase(ctx context.Context, tenantID uint) bool {
	var count int64
	var status *bool
	var expireDate *time.Time

	err := GVA_DB.Table("tenants").
		Select("COUNT(*) as count, status, expire_date").
		Where("id = ?", tenantID).
		Row().Scan(&count, &status, &expireDate)

	if err != nil || count == 0 {
		return false
	}

	return (status == nil || *status) &&
		(expireDate == nil || expireDate.After(time.Now()))
}

// InvalidateTenantCacheDistributed 使分布式缓存失效
func InvalidateTenantCacheDistributed(ctx context.Context, tenantID uint) error {
	if distributedCache == nil {
		// 降级到本地缓存失效
		InvalidateTenantCache(tenantID)
		return nil
	}

	// 清理Redis缓存
	key := fmt.Sprintf("%s%d", distributedCache.prefix, tenantID)
	err := distributedCache.redis.Del(ctx, key).Err()

	// 清理本地缓存
	distributedCache.localCache.mutex.Lock()
	delete(distributedCache.localCache.cache, tenantID)
	distributedCache.localCache.mutex.Unlock()

	return err
}

// ClearAllTenantCacheDistributed 清空所有分布式缓存
func ClearAllTenantCacheDistributed(ctx context.Context) error {
	if distributedCache == nil {
		ClearTenantCache()
		return nil
	}

	// 清理Redis缓存
	pattern := distributedCache.prefix + "*"
	keys, err := distributedCache.redis.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		err = distributedCache.redis.Del(ctx, keys...).Err()
		if err != nil {
			return err
		}
	}

	// 清理本地缓存
	distributedCache.localCache.mutex.Lock()
	distributedCache.localCache.cache = make(map[uint]*CachedTenant)
	distributedCache.localCache.mutex.Unlock()

	return nil
}

// GetDistributedCacheStats 获取分布式缓存统计信息
func GetDistributedCacheStats(ctx context.Context) map[string]interface{} {
	stats := map[string]interface{}{
		"distributed_mode": distributedCache != nil,
	}

	if distributedCache == nil {
		// 降级到本地缓存统计
		localStats := GetTenantCacheStats()
		for k, v := range localStats {
			stats[k] = v
		}
		return stats
	}

	// 本地缓存统计
	distributedCache.localCache.mutex.RLock()
	localCacheSize := len(distributedCache.localCache.cache)
	distributedCache.localCache.mutex.RUnlock()

	stats["local_cache_size"] = localCacheSize
	stats["local_cache_ttl"] = int(distributedCache.localCache.ttl.Seconds())

	// Redis缓存统计
	pattern := distributedCache.prefix + "*"
	keys, err := distributedCache.redis.Keys(ctx, pattern).Result()
	if err == nil {
		stats["redis_cache_size"] = len(keys)
	}
	stats["redis_cache_ttl"] = int(distributedCache.ttl.Seconds())

	return stats
}

// WarmupTenantCache 预热租户缓存
func WarmupTenantCache(ctx context.Context, tenantIDs []uint) error {
	if distributedCache == nil {
		return fmt.Errorf("distributed cache not initialized")
	}

	for _, tenantID := range tenantIDs {
		// 预热缓存
		ValidateTenantDistributed(ctx, tenantID)
	}

	return nil
}

// IsDistributedCacheEnabled 检查分布式缓存是否启用
func IsDistributedCacheEnabled() bool {
	return distributedCache != nil
}