package global

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// SecurityEventType 安全事件类型
type SecurityEventType string

const (
	// 租户相关安全事件
	EventCrossTenantAccess     SecurityEventType = "cross_tenant_access"     // 跨租户访问尝试
	EventTenantIDMismatch      SecurityEventType = "tenant_id_mismatch"      // 租户ID不匹配
	EventInvalidTenantID       SecurityEventType = "invalid_tenant_id"       // 无效租户ID
	EventTenantIsolationBypass SecurityEventType = "tenant_isolation_bypass" // 租户隔离绕过尝试
	
	// 权限相关安全事件
	EventUnauthorizedAccess    SecurityEventType = "unauthorized_access"     // 未授权访问
	EventPrivilegeEscalation   SecurityEventType = "privilege_escalation"    // 权限提升尝试
	EventSuperAdminAccess      SecurityEventType = "super_admin_access"      // 超级管理员访问
	
	// 数据相关安全事件
	EventSensitiveDataAccess   SecurityEventType = "sensitive_data_access"   // 敏感数据访问
	EventDataExportAttempt     SecurityEventType = "data_export_attempt"     // 数据导出尝试
	EventBulkDataAccess        SecurityEventType = "bulk_data_access"        // 批量数据访问
)

// SecurityEvent 安全事件
type SecurityEvent struct {
	ID          string            `json:"id"`
	Type        SecurityEventType `json:"type"`
	Timestamp   time.Time         `json:"timestamp"`
	UserID      uint              `json:"user_id"`
	TenantID    uint              `json:"tenant_id"`
	TargetTenantID uint           `json:"target_tenant_id,omitempty"`
	IP          string            `json:"ip"`
	UserAgent   string            `json:"user_agent"`
	Path        string            `json:"path"`
	Method      string            `json:"method"`
	Message     string            `json:"message"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Severity    SecuritySeverity  `json:"severity"`
}

// SecuritySeverity 安全事件严重程度
type SecuritySeverity string

const (
	SeverityLow      SecuritySeverity = "low"      // 低风险
	SeverityMedium   SecuritySeverity = "medium"   // 中风险
	SeverityHigh     SecuritySeverity = "high"     // 高风险
	SeverityCritical SecuritySeverity = "critical" // 严重风险
)

// SecurityAuditor 安全审计器
type SecurityAuditor struct {
	events     []SecurityEvent
	mutex      sync.RWMutex
	maxEvents  int
	alertRules []AlertRule
}

// AlertRule 告警规则
type AlertRule struct {
	EventType     SecurityEventType
	Threshold     int           // 阈值
	TimeWindow    time.Duration // 时间窗口
	Severity      SecuritySeverity
	Action        AlertAction
}

// AlertAction 告警动作
type AlertAction string

const (
	ActionLog   AlertAction = "log"   // 记录日志
	ActionEmail AlertAction = "email" // 发送邮件
	ActionBlock AlertAction = "block" // 阻止访问
)

var (
	securityAuditor *SecurityAuditor
	auditorOnce     sync.Once
)

// GetSecurityAuditor 获取安全审计器实例（单例）
func GetSecurityAuditor() *SecurityAuditor {
	auditorOnce.Do(func() {
		securityAuditor = &SecurityAuditor{
			events:    make([]SecurityEvent, 0),
			maxEvents: 10000, // 最多保存10000个事件
			alertRules: []AlertRule{
				{
					EventType:  EventCrossTenantAccess,
					Threshold:  5,
					TimeWindow: 5 * time.Minute,
					Severity:   SeverityHigh,
					Action:     ActionLog,
				},
				{
					EventType:  EventTenantIsolationBypass,
					Threshold:  1,
					TimeWindow: 1 * time.Minute,
					Severity:   SeverityCritical,
					Action:     ActionBlock,
				},
				{
					EventType:  EventUnauthorizedAccess,
					Threshold:  10,
					TimeWindow: 10 * time.Minute,
					Severity:   SeverityMedium,
					Action:     ActionLog,
				},
			},
		}
	})
	return securityAuditor
}

// RecordSecurityEvent 记录安全事件
func (sa *SecurityAuditor) RecordSecurityEvent(event SecurityEvent) {
	sa.mutex.Lock()
	defer sa.mutex.Unlock()

	// 设置事件ID和时间戳
	if event.ID == "" {
		event.ID = generateEventID()
	}
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}

	// 添加事件到列表
	sa.events = append(sa.events, event)

	// 限制事件数量
	if len(sa.events) > sa.maxEvents {
		sa.events = sa.events[len(sa.events)-sa.maxEvents:]
	}

	// 记录日志
	sa.logSecurityEvent(event)

	// 检查告警规则
	sa.checkAlertRules(event)
}

// logSecurityEvent 记录安全事件日志
func (sa *SecurityAuditor) logSecurityEvent(event SecurityEvent) {
	if GVA_LOG == nil {
		return
	}

	fields := []zap.Field{
		zap.String("event_id", event.ID),
		zap.String("event_type", string(event.Type)),
		zap.Time("timestamp", event.Timestamp),
		zap.Uint("user_id", event.UserID),
		zap.Uint("tenant_id", event.TenantID),
		zap.String("ip", event.IP),
		zap.String("path", event.Path),
		zap.String("method", event.Method),
		zap.String("message", event.Message),
		zap.String("severity", string(event.Severity)),
	}

	if event.TargetTenantID > 0 {
		fields = append(fields, zap.Uint("target_tenant_id", event.TargetTenantID))
	}

	if event.Details != nil {
		fields = append(fields, zap.Any("details", event.Details))
	}

	switch event.Severity {
	case SeverityCritical:
		GVA_LOG.Error("🚨 严重安全事件", fields...)
	case SeverityHigh:
		GVA_LOG.Warn("⚠️ 高风险安全事件", fields...)
	case SeverityMedium:
		GVA_LOG.Warn("⚠️ 中风险安全事件", fields...)
	default:
		GVA_LOG.Info("ℹ️ 安全事件", fields...)
	}
}

// checkAlertRules 检查告警规则
func (sa *SecurityAuditor) checkAlertRules(event SecurityEvent) {
	for _, rule := range sa.alertRules {
		if rule.EventType == event.Type {
			// 统计时间窗口内的同类事件数量
			count := sa.countEventsInTimeWindow(event.Type, rule.TimeWindow)
			if count >= rule.Threshold {
				sa.triggerAlert(rule, event, count)
			}
		}
	}
}

// countEventsInTimeWindow 统计时间窗口内的事件数量
func (sa *SecurityAuditor) countEventsInTimeWindow(eventType SecurityEventType, timeWindow time.Duration) int {
	now := time.Now()
	cutoff := now.Add(-timeWindow)
	count := 0

	for _, event := range sa.events {
		if event.Type == eventType && event.Timestamp.After(cutoff) {
			count++
		}
	}

	return count
}

// triggerAlert 触发告警
func (sa *SecurityAuditor) triggerAlert(rule AlertRule, event SecurityEvent, count int) {
	if GVA_LOG == nil {
		return
	}

	GVA_LOG.Error("🚨 安全告警触发",
		zap.String("rule_event_type", string(rule.EventType)),
		zap.Int("threshold", rule.Threshold),
		zap.Int("actual_count", count),
		zap.Duration("time_window", rule.TimeWindow),
		zap.String("action", string(rule.Action)),
		zap.String("latest_event_id", event.ID))

	// 根据告警动作执行相应操作
	switch rule.Action {
	case ActionBlock:
		// 这里可以实现阻止访问的逻辑
		GVA_LOG.Error("🚫 触发阻止访问动作", zap.String("event_type", string(rule.EventType)))
	case ActionEmail:
		// 这里可以实现发送邮件的逻辑
		GVA_LOG.Info("📧 触发邮件告警动作", zap.String("event_type", string(rule.EventType)))
	}
}

// GetRecentEvents 获取最近的安全事件
func (sa *SecurityAuditor) GetRecentEvents(limit int) []SecurityEvent {
	sa.mutex.RLock()
	defer sa.mutex.RUnlock()

	if limit <= 0 || limit > len(sa.events) {
		limit = len(sa.events)
	}

	// 返回最近的事件（倒序）
	result := make([]SecurityEvent, limit)
	for i := 0; i < limit; i++ {
		result[i] = sa.events[len(sa.events)-1-i]
	}

	return result
}

// generateEventID 生成事件ID
func generateEventID() string {
	return time.Now().Format("20060102150405") + "-" + generateRandomString(8)
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(result)
}

// RecordCrossTenantAccess 记录跨租户访问尝试
func RecordCrossTenantAccess(ctx context.Context, userID, userTenantID, targetTenantID uint, ip, path, method, message string) {
	auditor := GetSecurityAuditor()
	auditor.RecordSecurityEvent(SecurityEvent{
		Type:           EventCrossTenantAccess,
		UserID:         userID,
		TenantID:       userTenantID,
		TargetTenantID: targetTenantID,
		IP:             ip,
		Path:           path,
		Method:         method,
		Message:        message,
		Severity:       SeverityHigh,
	})
}

// RecordTenantIsolationBypass 记录租户隔离绕过尝试
func RecordTenantIsolationBypass(ctx context.Context, userID, tenantID uint, ip, path, method, message string, details map[string]interface{}) {
	auditor := GetSecurityAuditor()
	auditor.RecordSecurityEvent(SecurityEvent{
		Type:     EventTenantIsolationBypass,
		UserID:   userID,
		TenantID: tenantID,
		IP:       ip,
		Path:     path,
		Method:   method,
		Message:  message,
		Details:  details,
		Severity: SeverityCritical,
	})
}
