package global

import (
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// TenantErrorType 租户错误类型
type TenantErrorType string

const (
	ErrorDatabaseConnection TenantErrorType = "database_connection"
	ErrorCacheFailure       TenantErrorType = "cache_failure"
	ErrorValidationFailure  TenantErrorType = "validation_failure"
	ErrorContextMissing     TenantErrorType = "context_missing"
	ErrorPluginFailure      TenantErrorType = "plugin_failure"
	ErrorDistributedFailure TenantErrorType = "distributed_failure"
)

// TenantError 租户相关错误
type TenantError struct {
	Type      TenantErrorType        `json:"type"`
	Message   string                 `json:"message"`
	TenantID  uint                   `json:"tenant_id,omitempty"`
	UserID    uint                   `json:"user_id,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Context   map[string]interface{} `json:"context,omitempty"`
	Cause     error                  `json:"cause,omitempty"`
}

// Error 实现error接口
func (te *TenantError) Error() string {
	return fmt.Sprintf("[%s] %s (TenantID: %d, UserID: %d)", te.Type, te.Message, te.TenantID, te.UserID)
}

// TenantErrorHandler 租户错误处理器
type TenantErrorHandler struct {
	errors           []TenantError
	mutex            sync.RWMutex
	maxErrors        int
	fallbackMode     bool
	fallbackDuration time.Duration
	lastFallback     time.Time
	errorCounts      map[TenantErrorType]int
}

var (
	tenantErrorHandler *TenantErrorHandler
	errorHandlerOnce   sync.Once
)

// GetTenantErrorHandler 获取租户错误处理器实例（单例）
func GetTenantErrorHandler() *TenantErrorHandler {
	errorHandlerOnce.Do(func() {
		tenantErrorHandler = &TenantErrorHandler{
			errors:           make([]TenantError, 0),
			maxErrors:        1000,
			fallbackDuration: 5 * time.Minute,
			errorCounts:      make(map[TenantErrorType]int),
		}
	})
	return tenantErrorHandler
}

// HandleTenantError 处理租户错误
func (teh *TenantErrorHandler) HandleTenantError(err TenantError) {
	teh.mutex.Lock()
	defer teh.mutex.Unlock()

	// 设置时间戳
	if err.Timestamp.IsZero() {
		err.Timestamp = time.Now()
	}

	// 添加到错误列表
	teh.errors = append(teh.errors, err)

	// 限制错误数量
	if len(teh.errors) > teh.maxErrors {
		teh.errors = teh.errors[len(teh.errors)-teh.maxErrors:]
	}

	// 更新错误计数
	teh.errorCounts[err.Type]++

	// 记录日志
	teh.logError(err)

	// 检查是否需要进入降级模式
	teh.checkFallbackMode(err)
}

// logError 记录错误日志
func (teh *TenantErrorHandler) logError(err TenantError) {
	if GVA_LOG == nil {
		return
	}

	fields := []zap.Field{
		zap.String("error_type", string(err.Type)),
		zap.String("message", err.Message),
		zap.Time("timestamp", err.Timestamp),
	}

	if err.TenantID > 0 {
		fields = append(fields, zap.Uint("tenant_id", err.TenantID))
	}

	if err.UserID > 0 {
		fields = append(fields, zap.Uint("user_id", err.UserID))
	}

	if err.Context != nil {
		fields = append(fields, zap.Any("context", err.Context))
	}

	if err.Cause != nil {
		fields = append(fields, zap.Error(err.Cause))
	}

	switch err.Type {
	case ErrorDatabaseConnection, ErrorDistributedFailure:
		GVA_LOG.Error("🚨 租户系统严重错误", fields...)
	case ErrorCacheFailure, ErrorPluginFailure:
		GVA_LOG.Warn("⚠️ 租户系统警告", fields...)
	default:
		GVA_LOG.Info("ℹ️ 租户系统信息", fields...)
	}
}

// checkFallbackMode 检查是否需要进入降级模式
func (teh *TenantErrorHandler) checkFallbackMode(err TenantError) {
	// 严重错误类型触发降级模式
	criticalErrors := []TenantErrorType{
		ErrorDatabaseConnection,
		ErrorDistributedFailure,
	}

	for _, criticalType := range criticalErrors {
		if err.Type == criticalType {
			teh.enterFallbackMode()
			break
		}
	}

	// 错误频率过高也触发降级模式
	if teh.errorCounts[err.Type] >= 10 {
		// 在5分钟内同类错误超过10次
		recentErrors := teh.getRecentErrors(5*time.Minute, err.Type)
		if len(recentErrors) >= 10 {
			teh.enterFallbackMode()
		}
	}
}

// enterFallbackMode 进入降级模式
func (teh *TenantErrorHandler) enterFallbackMode() {
	if teh.fallbackMode {
		return // 已经在降级模式
	}

	teh.fallbackMode = true
	teh.lastFallback = time.Now()

	if GVA_LOG != nil {
		GVA_LOG.Error("🚨 租户系统进入降级模式",
			zap.Duration("duration", teh.fallbackDuration),
			zap.Time("until", teh.lastFallback.Add(teh.fallbackDuration)))
	}

	// 启动恢复定时器
	go func() {
		time.Sleep(teh.fallbackDuration)
		teh.exitFallbackMode()
	}()
}

// exitFallbackMode 退出降级模式
func (teh *TenantErrorHandler) exitFallbackMode() {
	teh.mutex.Lock()
	defer teh.mutex.Unlock()

	if !teh.fallbackMode {
		return
	}

	teh.fallbackMode = false

	if GVA_LOG != nil {
		GVA_LOG.Info("✅ 租户系统退出降级模式")
	}
}

// IsFallbackMode 检查是否处于降级模式
func (teh *TenantErrorHandler) IsFallbackMode() bool {
	teh.mutex.RLock()
	defer teh.mutex.RUnlock()
	return teh.fallbackMode
}

// getRecentErrors 获取最近的错误
func (teh *TenantErrorHandler) getRecentErrors(duration time.Duration, errorType TenantErrorType) []TenantError {
	cutoff := time.Now().Add(-duration)
	var recentErrors []TenantError

	for _, err := range teh.errors {
		if err.Timestamp.After(cutoff) && err.Type == errorType {
			recentErrors = append(recentErrors, err)
		}
	}

	return recentErrors
}

// GetErrorStats 获取错误统计
func (teh *TenantErrorHandler) GetErrorStats() map[string]interface{} {
	teh.mutex.RLock()
	defer teh.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_errors":      len(teh.errors),
		"fallback_mode":     teh.fallbackMode,
		"error_counts":      teh.errorCounts,
		"max_errors":        teh.maxErrors,
		"fallback_duration": teh.fallbackDuration.String(),
	}

	if teh.fallbackMode {
		stats["fallback_since"] = teh.lastFallback
		stats["fallback_until"] = teh.lastFallback.Add(teh.fallbackDuration)
	}

	return stats
}

// ClearErrors 清空错误记录
func (teh *TenantErrorHandler) ClearErrors() {
	teh.mutex.Lock()
	defer teh.mutex.Unlock()

	teh.errors = make([]TenantError, 0)
	teh.errorCounts = make(map[TenantErrorType]int)
}

// 便捷函数

// HandleDatabaseError 处理数据库错误
func HandleDatabaseError(tenantID, userID uint, err error, context map[string]interface{}) {
	handler := GetTenantErrorHandler()
	handler.HandleTenantError(TenantError{
		Type:     ErrorDatabaseConnection,
		Message:  "数据库连接或查询失败",
		TenantID: tenantID,
		UserID:   userID,
		Context:  context,
		Cause:    err,
	})
}

// HandleCacheError 处理缓存错误
func HandleCacheError(tenantID, userID uint, err error, context map[string]interface{}) {
	handler := GetTenantErrorHandler()
	handler.HandleTenantError(TenantError{
		Type:     ErrorCacheFailure,
		Message:  "缓存操作失败",
		TenantID: tenantID,
		UserID:   userID,
		Context:  context,
		Cause:    err,
	})
}

// HandleValidationError 处理验证错误
func HandleValidationError(tenantID, userID uint, message string, context map[string]interface{}) {
	handler := GetTenantErrorHandler()
	handler.HandleTenantError(TenantError{
		Type:     ErrorValidationFailure,
		Message:  message,
		TenantID: tenantID,
		UserID:   userID,
		Context:  context,
	})
}

// HandlePluginError 处理插件错误
func HandlePluginError(tenantID, userID uint, err error, context map[string]interface{}) {
	handler := GetTenantErrorHandler()
	handler.HandleTenantError(TenantError{
		Type:     ErrorPluginFailure,
		Message:  "GORM租户插件执行失败",
		TenantID: tenantID,
		UserID:   userID,
		Context:  context,
		Cause:    err,
	})
}

// IsTenantSystemHealthy 检查租户系统是否健康
func IsTenantSystemHealthy() bool {
	handler := GetTenantErrorHandler()
	return !handler.IsFallbackMode()
}

// ValidateTenantWithFallback 带降级的租户验证
func ValidateTenantWithFallback(tenantID uint) bool {
	handler := GetTenantErrorHandler()

	// 如果处于降级模式，使用简化验证
	if handler.IsFallbackMode() {
		// 降级模式：只检查租户ID是否大于0
		return tenantID > 0
	}

	// 正常模式：使用完整验证
	return ValidateTenantCached(tenantID)
}
