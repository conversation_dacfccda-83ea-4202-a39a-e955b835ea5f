package global

const (
	PlatformTenantID              uint   = 0    // 特殊租户ID，代表平台
	PlatformSuperAdminAuthorityID uint   = 1    // 平台超级管理员的角色ID
	TenantAdminAuthorityID        uint   = 100  // 租户管理员的角色ID
	DefaultTenantActualID         uint   = 10001 // 默认租户的固定ID
	DefaultTenantCode             string = "default_tenant" // 默认租户的编码
	TenantAdminRole               string = "TENANT_ADMIN"   // 默认租户中管理员的角色标识
	AdminUserName                 string = "admin"          // 超级管理员用户名
)