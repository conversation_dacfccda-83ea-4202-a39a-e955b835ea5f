package system

import (
	"context"
	"errors"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/apprelease"
	"github.com/OSQianXing/guanpu-server/model/system"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type tenantInit struct{}

// NewTenantInit 创建一个新的 Tenant 初始化器
func NewTenantInit() *tenantInit {
	return &tenantInit{}
}

func (ti *tenantInit) InitializerName() string {
	return "Tenant Initializer"
}

// MigrateTable 不再执行自动迁移
func (ti *tenantInit) MigrateTable(ctx context.Context) (context.Context, error) {
	global.GVA_LOG.Info("租户表迁移跳过，由外部迁移工具处理")
	return ctx, nil
}

// TableCreated 检查租户相关表是否存在
func (ti *tenantInit) TableCreated(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}
	return db.Migrator().HasTable(&system.Tenant{}) &&
		db.Migrator().HasTable(&system.TenantAppConfig{}) &&
		db.Migrator().HasTable(&system.UserTenantRelation{}) &&
		db.Migrator().HasTable(&system.SysUserAuthority{}) // 确保sys_user_authority表存在
}

// InitializeData 初始化租户数据
func (ti *tenantInit) InitializeData(ctx context.Context) (next context.Context, err error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, errors.New("missing db context")
	}
	next = ctx

	// 1. 确保超级管理员用户存在并具有平台超管权限
	var adminUser system.SysUser
	if err := db.Where("username = ?", global.AdminUserName).First(&adminUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return next, errors.New("超级管理员 'admin' 用户不存在")
		}
		return next, err
	}

	// 验证admin用户是否具有平台超级管理员权限
	var platformAdminAuth system.SysUserAuthority
	err = db.Where("sys_user_id = ? AND sys_authority_authority_id = ? AND tenant_id = ?",
		adminUser.ID,
		global.PlatformSuperAdminAuthorityID,
		global.PlatformTenantID,
	).First(&platformAdminAuth).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return next, errors.New("admin用户缺少平台超级管理员权限")
		}
		return next, err
	}

	// 2. 创建默认租户（使用固定ID）
	var tenant system.Tenant
	tenantStatus := true
	err = db.Where("code = ?", global.DefaultTenantCode).First(&tenant).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("默认租户不存在，开始创建...")
		tenant = system.Tenant{
			GVA_MODEL:    global.GVA_MODEL{ID: global.DefaultTenantActualID}, // 使用固定ID
			Name:         "默认租户",
			Code:         global.DefaultTenantCode,
			Status:       &tenantStatus,
			PrimaryColor: "#1890ff",
		}

		// 创建关联的 TenantAppConfig
		appConfig := system.TenantAppConfig{
			AppName: "默认应用",
		}
		tenant.AppConfig = &appConfig

		if err = db.Create(&tenant).Error; err != nil {
			global.GVA_LOG.Error("创建默认租户失败", zap.Error(err))
			return next, err
		}
		global.GVA_LOG.Info("默认租户创建成功", zap.Uint("ID", tenant.ID))
	} else if err != nil {
		return next, err
	} else {
		global.GVA_LOG.Info("默认租户已存在", zap.Uint("ID", tenant.ID))
	}

	// 创建默认的应用发布记录
	now := time.Now()
	release := apprelease.AppRelease{
		ReleaseDate: &now,
		OsType:      "android",
		VersionVal:  "1.0.0",
		UpdateUrl:   "https://example.com/update",
		Desc:        "默认初始版本",
		MustUpdate:  "0",
		Status:      "online",
	}
	if err = db.Create(&release).Error; err != nil {
		global.GVA_LOG.Error("创建默认应用发布记录失败", zap.Error(err))
		return next, err
	}
	global.GVA_LOG.Info("创建默认应用发布记录成功")

	// 3. 创建用户与默认租户的关联
	var relation system.UserTenantRelation
	err = db.Where("user_id = ? AND tenant_id = ?", adminUser.ID, tenant.ID).First(&relation).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("创建 admin 用户与默认租户的关联...")
		joinTime := time.Now()
		relationStatus := true
		relation = system.UserTenantRelation{
			UserID:    adminUser.ID,
			TenantID:  tenant.ID,
			IsDefault: true,
			Role:      global.TenantAdminRole, // 使用常量角色
			Status:    &relationStatus,
			JoinTime:  &joinTime,
		}
		if err = db.Create(&relation).Error; err != nil {
			global.GVA_LOG.Error("创建用户租户关联失败", zap.Error(err))
			return next, err
		}
	} else if err != nil {
		return next, err
	} else {
		// 更新关联信息确保为默认
		if !relation.IsDefault {
			relation.IsDefault = true
			if err = db.Save(&relation).Error; err != nil {
				global.GVA_LOG.Error("更新用户租户关联失败", zap.Error(err))
				return next, err
			}
		}
	}

	// 4. 为admin用户在默认租户下添加租户管理员权限
	var tenantAdminAuth system.SysUserAuthority
	err = db.Where("sys_user_id = ? AND sys_authority_authority_id = ? AND tenant_id = ?",
		adminUser.ID,
		global.TenantAdminAuthorityID,
		tenant.ID,
	).First(&tenantAdminAuth).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("添加租户管理员权限...")
		tenantAdminAuth = system.SysUserAuthority{
			SysUserId:               adminUser.ID,
			SysAuthorityAuthorityId: global.TenantAdminAuthorityID,
		}
		if err = db.Create(&tenantAdminAuth).Error; err != nil {
			global.GVA_LOG.Error("添加租户管理员权限失败", zap.Error(err))
			return next, err
		}
	} else if err != nil {
		return next, err
	}

	return context.WithValue(next, ti.InitializerName(), tenant), nil
}

// DataInserted 检查租户数据是否已初始化
func (ti *tenantInit) DataInserted(ctx context.Context) bool {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return false
	}

	// 1. 检查默认租户是否存在
	var tenant system.Tenant
	if err := db.Where("code = ?", global.DefaultTenantCode).First(&tenant).Error; err != nil {
		return false
	}

	// 2. 检查admin用户是否存在
	var adminUser system.SysUser
	if err := db.Where("username = ?", global.AdminUserName).First(&adminUser).Error; err != nil {
		return false
	}

	// 3. 检查用户租户关联是否存在
	var relation system.UserTenantRelation
	if err := db.Where("user_id = ? AND tenant_id = ?", adminUser.ID, tenant.ID).First(&relation).Error; err != nil {
		return false
	}

	// 4. 检查租户管理员权限是否存在
	var tenantAdminAuth system.SysUserAuthority
	if err := db.Where("sys_user_id = ? AND sys_authority_authority_id = ? AND tenant_id = ?",
		adminUser.ID,
		global.TenantAdminAuthorityID,
		tenant.ID,
	).First(&tenantAdminAuth).Error; err != nil {
		return false
	}

	return relation.IsDefault
}