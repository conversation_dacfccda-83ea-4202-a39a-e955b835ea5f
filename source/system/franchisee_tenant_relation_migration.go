package system

import (
	"context"
	"errors"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	sysModel "github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/service/system"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const initOrderFranchiseeTenantRelation = system.InitOrderExternal + 2

type initFranchiseeTenantRelation struct{}

// auto run
func init() {
	system.RegisterInit(initOrderFranchiseeTenantRelation, &initFranchiseeTenantRelation{})
}

func (i *initFranchiseeTenantRelation) MigrateTable(ctx context.Context) (context.Context, error) {
	return ctx, nil // 不需要创建表，只是数据迁移
}

func (i *initFranchiseeTenantRelation) TableCreated(ctx context.Context) bool {
	return true // 总是返回true，因为我们只做数据迁移
}

func (i *initFranchiseeTenantRelation) InitializerName() string {
	return "franchisee_tenant_relation_migration"
}

func (i *initFranchiseeTenantRelation) InitializeData(ctx context.Context) (next context.Context, err error) {
	db := global.GVA_DB
	global.GVA_LOG.Info("开始加盟商租户关联数据迁移...")

	// 检查是否已经执行过迁移（通过检查是否有加盟商租户关联数据）
	var count int64
	err = db.Model(&franchisees.FranchiseeTenantRelation{}).Count(&count).Error
	if err == nil && count > 0 {
		global.GVA_LOG.Info("加盟商租户关联数据迁移已执行过，跳过")
		return ctx, nil
	}

	// 1. 确保默认租户存在
	var defaultTenant sysModel.Tenant
	err = db.Where("code = ?", "default").First(&defaultTenant).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("创建默认租户...")
		status := true
		defaultTenant = sysModel.Tenant{
			Name:           "默认租户",
			Code:           "default",
			Logo:           "",
			PrimaryColor:   "#409EFF",
			SecondaryColor: "#67C23A",
			Status:         &status,
			ContactName:    "系统管理员",
			ContactPhone:   "",
		}
		if err = db.Create(&defaultTenant).Error; err != nil {
			global.GVA_LOG.Error("创建默认租户失败", zap.Error(err))
			return ctx, err
		}
	} else if err != nil {
		global.GVA_LOG.Error("查询默认租户失败", zap.Error(err))
		return ctx, err
	}

	// 2. 为所有现有加盟商创建租户关联
	err = i.createFranchiseeTenantRelations(db, defaultTenant.ID)
	if err != nil {
		global.GVA_LOG.Error("创建加盟商租户关联失败", zap.Error(err))
		return ctx, err
	}

	global.GVA_LOG.Info("加盟商租户关联数据迁移完成")
	return ctx, nil
}

// createFranchiseeTenantRelations 为所有现有加盟商创建租户关联
func (i *initFranchiseeTenantRelation) createFranchiseeTenantRelations(db *gorm.DB, tenantID uint) error {
	global.GVA_LOG.Info("开始创建加盟商租户关联...")

	// 获取所有加盟商
	var franchiseeList []franchisees.Franchisee
	err := db.Find(&franchiseeList).Error
	if err != nil {
		return err
	}

	if len(franchiseeList) == 0 {
		global.GVA_LOG.Info("没有找到加盟商数据，跳过关联创建")
		return nil
	}

	// 为每个加盟商创建租户关联
	var relations []franchisees.FranchiseeTenantRelation
	for i, franchisee := range franchiseeList {
		relation := franchisees.FranchiseeTenantRelation{
			FranchiseeID: franchisee.ID,
			TenantID:     tenantID,
			Status:       franchisees.StatusActive,
			Role:         franchisees.RoleFranchisee,
			IsDefault:    true, // 第一个租户设为默认
			Remark:       "系统迁移时自动创建",
		}
		relations = append(relations, relation)

		// 批量插入，每100条提交一次
		if (i+1)%100 == 0 || i == len(franchiseeList)-1 {
			if err = db.Create(&relations).Error; err != nil {
				global.GVA_LOG.Error("批量创建加盟商租户关联失败", 
					zap.Error(err), 
					zap.Int("batch_size", len(relations)))
				return err
			}
			global.GVA_LOG.Info("批量创建加盟商租户关联成功", 
				zap.Int("batch_size", len(relations)),
				zap.Int("total_processed", i+1))
			relations = relations[:0] // 清空切片
		}
	}

	global.GVA_LOG.Info("加盟商租户关联创建完成", zap.Int("total_franchisees", len(franchiseeList)))
	return nil
}

func (i *initFranchiseeTenantRelation) DataInserted(ctx context.Context) bool {
	db := global.GVA_DB
	var count int64
	err := db.Model(&franchisees.FranchiseeTenantRelation{}).Count(&count).Error
	return err == nil && count > 0
}
