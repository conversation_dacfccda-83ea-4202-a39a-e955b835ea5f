package system

import (
	"context"
	"errors"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const initOrderTenantDataMigration = "tenant_data_migration"

type initTenantDataMigration struct{}

// auto run
func init() {
	system.RegisterInit(initOrderTenantDataMigration, &initTenantDataMigration{})
}

func (i *initTenantDataMigration) MigrateTable(ctx context.Context) (context.Context, error) {
	return ctx, nil // 不需要创建表，只是数据迁移
}

func (i *initTenantDataMigration) TableCreated(ctx context.Context) bool {
	return true // 总是返回true，因为我们只做数据迁移
}

func (i *initTenantDataMigration) InitializerName() string {
	return initOrderTenantDataMigration
}

func (i *initTenantDataMigration) InitializeData(ctx context.Context) (next context.Context, err error) {
	db := global.GVA_DB
	global.GVA_LOG.Info("开始租户数据迁移...")

	// 检查是否已经执行过迁移
	var migrationRecord system.SysInitDB
	err = db.Where("init_db_name = ?", initOrderTenantDataMigration).First(&migrationRecord).Error
	if err == nil {
		global.GVA_LOG.Info("租户数据迁移已执行过，跳过")
		return ctx, nil
	}

	// 1. 确保默认租户存在
	var defaultTenant system.Tenant
	err = db.Where("code = ?", "default").First(&defaultTenant).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("创建默认租户...")
		status := true
		defaultTenant = system.Tenant{
			Name:           "默认租户",
			Code:           "default",
			Logo:           "",
			PrimaryColor:   "#409EFF",
			SecondaryColor: "#67C23A",
			Status:         &status,
			ContactName:    "系统管理员",
			ContactPhone:   "",
			ContactEmail:   "",
		}
		if err = db.Create(&defaultTenant).Error; err != nil {
			global.GVA_LOG.Error("创建默认租户失败", zap.Error(err))
			return ctx, err
		}
	} else if err != nil {
		global.GVA_LOG.Error("查询默认租户失败", zap.Error(err))
		return ctx, err
	}

	// 2. 迁移加盟商数据
	err = i.migrateFranchiseeData(db, defaultTenant.ID)
	if err != nil {
		global.GVA_LOG.Error("迁移加盟商数据失败", zap.Error(err))
		return ctx, err
	}

	// 3. 迁移加盟商团队数据
	err = i.migrateFranchiseeTeamData(db, defaultTenant.ID)
	if err != nil {
		global.GVA_LOG.Error("迁移加盟商团队数据失败", zap.Error(err))
		return ctx, err
	}

	// 4. 迁移加盟商链接数据
	err = i.migrateFranchiseeLinkData(db, defaultTenant.ID)
	if err != nil {
		global.GVA_LOG.Error("迁移加盟商链接数据失败", zap.Error(err))
		return ctx, err
	}

	// 5. 迁移加盟商成员链接数据
	err = i.migrateFranchiseeMemberLinkData(db, defaultTenant.ID)
	if err != nil {
		global.GVA_LOG.Error("迁移加盟商成员链接数据失败", zap.Error(err))
		return ctx, err
	}

	// 6. 为加盟商用户创建租户关联
	err = i.createFranchiseeUserTenantRelations(db, defaultTenant.ID)
	if err != nil {
		global.GVA_LOG.Error("创建加盟商用户租户关联失败", zap.Error(err))
		return ctx, err
	}

	global.GVA_LOG.Info("租户数据迁移完成")
	return ctx, nil
}

// migrateFranchiseeData 迁移加盟商数据
func (i *initTenantDataMigration) migrateFranchiseeData(db *gorm.DB, tenantID uint) error {
	global.GVA_LOG.Info("开始迁移加盟商数据...")
	
	// 更新所有TenantID为0或NULL的加盟商记录
	result := db.Model(&franchisees.Franchisee{}).
		Where("tenant_id = 0 OR tenant_id IS NULL").
		Update("tenant_id", tenantID)
	
	if result.Error != nil {
		return result.Error
	}
	
	global.GVA_LOG.Info("加盟商数据迁移完成", zap.Int64("affected_rows", result.RowsAffected))
	return nil
}

// migrateFranchiseeTeamData 迁移加盟商团队数据
func (i *initTenantDataMigration) migrateFranchiseeTeamData(db *gorm.DB, tenantID uint) error {
	global.GVA_LOG.Info("开始迁移加盟商团队数据...")
	
	result := db.Model(&franchisees.FranchiseeTeam{}).
		Where("tenant_id = 0 OR tenant_id IS NULL").
		Update("tenant_id", tenantID)
	
	if result.Error != nil {
		return result.Error
	}
	
	global.GVA_LOG.Info("加盟商团队数据迁移完成", zap.Int64("affected_rows", result.RowsAffected))
	return nil
}

// migrateFranchiseeLinkData 迁移加盟商链接数据
func (i *initTenantDataMigration) migrateFranchiseeLinkData(db *gorm.DB, tenantID uint) error {
	global.GVA_LOG.Info("开始迁移加盟商链接数据...")
	
	result := db.Model(&franchisees.FranchiseeLink{}).
		Where("tenant_id = 0 OR tenant_id IS NULL").
		Update("tenant_id", tenantID)
	
	if result.Error != nil {
		return result.Error
	}
	
	global.GVA_LOG.Info("加盟商链接数据迁移完成", zap.Int64("affected_rows", result.RowsAffected))
	return nil
}

// migrateFranchiseeMemberLinkData 迁移加盟商成员链接数据
func (i *initTenantDataMigration) migrateFranchiseeMemberLinkData(db *gorm.DB, tenantID uint) error {
	global.GVA_LOG.Info("开始迁移加盟商成员链接数据...")
	
	result := db.Model(&franchisees.FranchiseeMemberLink{}).
		Where("tenant_id = 0 OR tenant_id IS NULL").
		Update("tenant_id", tenantID)
	
	if result.Error != nil {
		return result.Error
	}
	
	global.GVA_LOG.Info("加盟商成员链接数据迁移完成", zap.Int64("affected_rows", result.RowsAffected))
	return nil
}

// createFranchiseeUserTenantRelations 为加盟商用户创建租户关联
func (i *initTenantDataMigration) createFranchiseeUserTenantRelations(db *gorm.DB, tenantID uint) error {
	global.GVA_LOG.Info("开始创建加盟商用户租户关联...")
	
	// 获取所有加盟商的用户ID
	var franchiseeUsers []struct {
		UserID uint `gorm:"column:user_id"`
	}
	
	err := db.Table("franchisee").
		Select("DISTINCT user_id").
		Where("user_id > 0 AND tenant_id = ?", tenantID).
		Find(&franchiseeUsers).Error
	
	if err != nil {
		return err
	}
	
	tenantService := service.ServiceGroupApp.SystemServiceGroup.TenantService
	
	for _, fu := range franchiseeUsers {
		// 检查是否已存在租户关联
		var existingRelation system.UserTenantRelation
		err = db.Where("user_id = ? AND tenant_id = ?", fu.UserID, tenantID).
			First(&existingRelation).Error
		
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建用户租户关联
			err = tenantService.AddUserToTenant(fu.UserID, tenantID, "franchisee")
			if err != nil {
				global.GVA_LOG.Warn("创建用户租户关联失败", 
					zap.Uint("userID", fu.UserID), 
					zap.Uint("tenantID", tenantID), 
					zap.Error(err))
				continue
			}
			global.GVA_LOG.Debug("创建用户租户关联成功", 
				zap.Uint("userID", fu.UserID), 
				zap.Uint("tenantID", tenantID))
		} else if err != nil {
			global.GVA_LOG.Warn("检查用户租户关联失败", 
				zap.Uint("userID", fu.UserID), 
				zap.Error(err))
		}
	}
	
	global.GVA_LOG.Info("加盟商用户租户关联创建完成", zap.Int("processed_users", len(franchiseeUsers)))
	return nil
}

func (i *initTenantDataMigration) DataInserted(ctx context.Context) bool {
	db := global.GVA_DB
	var migrationRecord system.SysInitDB
	err := db.Where("init_db_name = ?", initOrderTenantDataMigration).First(&migrationRecord).Error
	return err == nil
}
