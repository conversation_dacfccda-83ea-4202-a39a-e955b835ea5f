package test

import (
	"testing"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/stretchr/testify/assert"
)

func TestTenantService(t *testing.T) {
	// 初始化测试环境
	// 这里假设已经初始化了数据库连接
	
	tenantService := service.ServiceGroupApp.SystemServiceGroup.TenantService
	
	t.Run("创建租户", func(t *testing.T) {
		status := true
		tenant := system.Tenant{
			Name:           "测试租户",
			Code:           "test_tenant_001",
			Logo:           "https://example.com/logo.png",
			PrimaryColor:   "#007bff",
			SecondaryColor: "#6c757d",
			Status:         &status,
			ContactName:    "张三",
			ContactPhone:   "13800138000",
		}
		
		err := tenantService.CreateTenant(tenant)
		assert.NoError(t, err, "创建租户应该成功")
	})
	
	t.Run("获取租户列表", func(t *testing.T) {
		pageInfo := request.PageInfo{
			Page:     1,
			PageSize: 10,
		}
		
		list, total, err := tenantService.GetTenantList(pageInfo)
		assert.NoError(t, err, "获取租户列表应该成功")
		assert.Greater(t, total, int64(0), "应该有租户数据")
		assert.NotEmpty(t, list, "租户列表不应该为空")
	})
	
	t.Run("添加用户到租户", func(t *testing.T) {
		// 假设用户ID为1，租户ID为1
		err := tenantService.AddUserToTenant(1, 1, "admin")
		assert.NoError(t, err, "添加用户到租户应该成功")
	})
	
	t.Run("获取用户租户", func(t *testing.T) {
		tenants, err := tenantService.GetUserTenants(1)
		assert.NoError(t, err, "获取用户租户应该成功")
		assert.NotEmpty(t, tenants, "用户应该有关联的租户")
	})
	
	t.Run("生成租户令牌", func(t *testing.T) {
		token, err := tenantService.GenerateTenantToken(1, 1)
		assert.NoError(t, err, "生成租户令牌应该成功")
		assert.NotEmpty(t, token, "令牌不应该为空")
	})
}

func TestTenantValidation(t *testing.T) {
	t.Run("验证有效租户", func(t *testing.T) {
		// 创建一个有效的租户
		status := true
		expireDate := time.Now().Add(30 * 24 * time.Hour) // 30天后过期
		tenant := system.Tenant{
			Name:         "有效租户",
			Code:         "valid_tenant",
			Status:       &status,
			ExpireDate:   &expireDate,
		}
		
		err := global.GVA_DB.Create(&tenant).Error
		assert.NoError(t, err, "创建租户应该成功")
		
		isValid := system.ValidateTenant(tenant.ID)
		assert.True(t, isValid, "租户应该是有效的")
	})
	
	t.Run("验证无效租户", func(t *testing.T) {
		// 测试不存在的租户
		isValid := system.ValidateTenant(99999)
		assert.False(t, isValid, "不存在的租户应该是无效的")
		
		// 测试禁用的租户
		status := false
		tenant := system.Tenant{
			Name:   "禁用租户",
			Code:   "disabled_tenant",
			Status: &status,
		}
		
		err := global.GVA_DB.Create(&tenant).Error
		assert.NoError(t, err, "创建租户应该成功")
		
		isValid = system.ValidateTenant(tenant.ID)
		assert.False(t, isValid, "禁用的租户应该是无效的")
		
		// 测试过期的租户
		status = true
		expireDate := time.Now().Add(-24 * time.Hour) // 昨天过期
		expiredTenant := system.Tenant{
			Name:       "过期租户",
			Code:       "expired_tenant",
			Status:     &status,
			ExpireDate: &expireDate,
		}
		
		err = global.GVA_DB.Create(&expiredTenant).Error
		assert.NoError(t, err, "创建租户应该成功")
		
		isValid = system.ValidateTenant(expiredTenant.ID)
		assert.False(t, isValid, "过期的租户应该是无效的")
	})
}

func TestTenantMiddleware(t *testing.T) {
	// 这里可以添加中间件的测试
	// 测试租户验证中间件是否正确工作
	t.Run("租户中间件验证", func(t *testing.T) {
		// 模拟HTTP请求测试中间件
		// 这需要设置gin的测试环境
		t.Skip("需要设置完整的HTTP测试环境")
	})
}