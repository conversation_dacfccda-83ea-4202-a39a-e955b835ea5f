package test

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/stretchr/testify/assert"
)

// TestTenantConcurrentSafety 测试租户上下文的并发安全性
func TestTenantConcurrentSafety(t *testing.T) {
	var wg sync.WaitGroup
	results := make(map[int][]uint)
	mutex := sync.Mutex{}

	// 并发设置不同租户ID
	for i := 1; i <= 10; i++ {
		wg.Add(1)
		go func(tenantID int) {
			defer wg.Done()

			// 使用线程安全的方法设置租户ID
			global.SetCurrentTenantIDSafe(uint(tenantID))
			time.Sleep(10 * time.Millisecond) // 模拟处理时间

			// 验证获取的租户ID是否正确
			gotID := global.GetCurrentTenantIDSafe()

			mutex.Lock()
			results[tenantID] = append(results[tenantID], gotID)
			mutex.Unlock()
		}(i)
	}

	wg.Wait()

	// 验证每个goroutine都获取到了正确的租户ID
	for expectedID, gotIDs := range results {
		for _, gotID := range gotIDs {
			assert.Equal(t, uint(expectedID), gotID,
				"期望租户ID %d，实际获取 %d", expectedID, gotID)
		}
	}
}

// TestTenantContextIsolation 测试租户上下文隔离
func TestTenantContextIsolation(t *testing.T) {
	// 模拟不同租户的并发请求
	var wg sync.WaitGroup
	results := make(map[uint][]string)
	mutex := sync.Mutex{}

	tenantIDs := []uint{1, 2, 3, 4, 5}

	for _, tenantID := range tenantIDs {
		wg.Add(1)
		go func(tid uint) {
			defer wg.Done()

			// 创建带租户信息的上下文
			ctx := global.WithTenantContext(context.Background(), tid)

			// 模拟数据库操作
			time.Sleep(5 * time.Millisecond)

			// 从上下文获取租户ID
			gotTenantID, ok := global.GetTenantFromContext(ctx)
			assert.True(t, ok, "应该能从上下文获取租户ID")

			mutex.Lock()
			results[tid] = append(results[tid], string(rune(gotTenantID)))
			mutex.Unlock()
		}(tenantID)
	}

	wg.Wait()

	// 验证每个租户只能访问自己的数据
	for expectedTenantID := range results {
		assert.Contains(t, results, expectedTenantID,
			"结果中应该包含租户 %d", expectedTenantID)
	}
}

// TestTenantCachePerformance 测试租户缓存性能
func TestTenantCachePerformance(t *testing.T) {
	tenantID := uint(1)

	// 测试缓存性能
	start := time.Now()
	for i := 0; i < 1000; i++ {
		global.ValidateTenantCached(tenantID)
	}
	duration := time.Since(start)

	// 验证性能提升（应该很快完成）
	assert.Less(t, duration, 100*time.Millisecond,
		"1000次缓存验证应该在100ms内完成")

	// 获取缓存统计
	stats := global.GetTenantCacheStats()
	assert.Greater(t, stats["total_cached"], 0,
		"缓存中应该有数据")
}

// TestTenantValidationWithCache 测试带缓存的租户验证
func TestTenantValidationWithCache(t *testing.T) {
	// 测试有效租户
	validTenantID := uint(1)
	isValid := global.ValidateTenantCached(validTenantID)
	// 注意：这里的结果取决于数据库中是否有对应的租户数据
	// 在实际测试中，应该先创建测试数据
	t.Logf("租户 %d 验证结果: %v", validTenantID, isValid)

	// 测试无效租户
	invalidTenantID := uint(99999)
	isValid = global.ValidateTenantCached(invalidTenantID)
	assert.False(t, isValid, "不存在的租户应该验证失败")

	// 测试零值租户
	zeroTenantID := uint(0)
	isValid = global.ValidateTenantCached(zeroTenantID)
	assert.False(t, isValid, "零值租户应该验证失败")
}

// BenchmarkTenantValidation 基准测试租户验证性能
func BenchmarkTenantValidation(b *testing.B) {
	tenantID := uint(1)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		global.ValidateTenantCached(tenantID)
	}
}

// BenchmarkConcurrentTenantAccess 基准测试并发租户访问
func BenchmarkConcurrentTenantAccess(b *testing.B) {
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			tenantID := uint((b.N % 10) + 1) // 模拟1-10的租户ID
			ctx := global.WithTenantContext(context.Background(), tenantID)

			// 模拟从上下文获取租户ID的操作
			_, _ = global.GetTenantFromContext(ctx)
		}
	})
}

// TestTenantMiddlewareCleanup 测试中间件清理逻辑
func TestTenantMiddlewareCleanup(t *testing.T) {
	// 设置租户ID
	tenantID := uint(123)
	global.SetCurrentTenantIDSafe(tenantID)

	// 验证设置成功
	gotID := global.GetCurrentTenantIDSafe()
	assert.Equal(t, tenantID, gotID, "租户ID应该设置成功")

	// 清理租户ID
	global.ClearCurrentTenantID()

	// 验证清理成功
	gotID = global.GetCurrentTenantIDSafe()
	assert.Equal(t, uint(0), gotID, "租户ID应该被清理")
}

// TestTenantTableNameConsistency 测试表名一致性
func TestTenantTableNameConsistency(t *testing.T) {
	// 测试Order模型的表名
	order := orders.Order{}
	tableName := order.TableName()
	assert.Equal(t, "orders", tableName, "Order模型应该返回正确的表名")

	// 测试租户表识别
	isTenantTable := global.IsTenantTable(tableName)
	assert.True(t, isTenantTable, "orders表应该被识别为租户表")
}