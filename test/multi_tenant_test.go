package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/system"
	franchiseesService "github.com/OSQianXing/guanpu-server/service/franchisees"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// TestMultiTenantLogin 测试多租户登录功能
func TestMultiTenantLogin(t *testing.T) {
	// 初始化测试环境
	setupTestEnvironment()

	// 创建测试数据
	user, tenant1, tenant2, franchisee1, franchisee2 := createTestData(t)

	service := franchiseesService.FranchiseeMultiTenantService{}

	t.Run("测试用户认证", func(t *testing.T) {
		// 测试正确的用户名密码
		authenticatedUser, err := service.AuthenticateUser(user.Username, "password123")
		assert.NoError(t, err)
		assert.Equal(t, user.ID, authenticatedUser.ID)

		// 测试错误的密码
		_, err = service.AuthenticateUser(user.Username, "wrongpassword")
		assert.Error(t, err)

		// 测试不存在的用户
		_, err = service.AuthenticateUser("nonexistent", "password123")
		assert.Error(t, err)
	})

	t.Run("测试获取用户租户列表", func(t *testing.T) {
		tenants, err := service.GetUserFranchisees(user.ID)
		assert.NoError(t, err)
		assert.Len(t, tenants, 2)

		// 验证租户信息
		tenantIDs := make(map[uint]bool)
		for _, tenant := range tenants {
			tenantIDs[tenant.TenantID] = true
		}
		assert.True(t, tenantIDs[tenant1.ID])
		assert.True(t, tenantIDs[tenant2.ID])
	})

	t.Run("测试单租户登录", func(t *testing.T) {
		// 创建只有一个租户的用户
		singleTenantUser := createSingleTenantUser(t, tenant1)
		
		tenants, err := service.GetUserFranchisees(singleTenantUser.ID)
		assert.NoError(t, err)
		assert.Len(t, tenants, 1)

		// 测试单租户登录
		var franchisee franchisees.Franchisee
		err = global.GVA_DB.Where("user_id = ? AND tenant_id = ?", singleTenantUser.ID, tenant1.ID).First(&franchisee).Error
		assert.NoError(t, err)

		token, err := service.HandleSingleTenantLogin(singleTenantUser, &franchisee)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
	})

	t.Run("测试多租户登录", func(t *testing.T) {
		response, err := service.HandleMultiTenantLogin(user)
		assert.NoError(t, err)
		assert.True(t, response.NeedTenantSelection)
		assert.Len(t, response.AvailableTenants, 2)
		assert.NotEmpty(t, response.TempToken)
	})

	t.Run("测试确认租户登录", func(t *testing.T) {
		// 先生成临时令牌
		tempToken, err := service.GenerateTempToken(user)
		assert.NoError(t, err)

		// 确认租户登录
		token, err := service.ConfirmTenantLogin(tempToken, tenant1.ID)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// 测试无效的租户ID
		_, err = service.ConfirmTenantLogin(tempToken, 999)
		assert.Error(t, err)
	})

	t.Run("测试切换租户", func(t *testing.T) {
		// 切换到租户1
		token, err := service.SwitchTenant(user.ID, tenant1.ID)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// 切换到租户2
		token, err = service.SwitchTenant(user.ID, tenant2.ID)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// 测试切换到无权限的租户
		_, err = service.SwitchTenant(user.ID, 999)
		assert.Error(t, err)
	})

	// 清理测试数据
	cleanupTestData(user, tenant1, tenant2, franchisee1, franchisee2)
}

// setupTestEnvironment 设置测试环境
func setupTestEnvironment() {
	// 初始化配置
	initialize.Viper()
	initialize.Logger()
	initialize.DB()
	initialize.Redis()
}

// createTestData 创建测试数据
func createTestData(t *testing.T) (*system.SysUser, *system.Tenant, *system.Tenant, *franchisees.Franchisee, *franchisees.Franchisee) {
	// 创建测试用户
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user := &system.SysUser{
		Username: "testuser",
		Password: string(hashedPassword),
		NickName: "Test User",
	}
	err := global.GVA_DB.Create(user).Error
	assert.NoError(t, err)

	// 创建测试租户1
	tenant1 := &system.Tenant{
		Name: "Test Tenant 1",
		Code: "tenant1",
	}
	err = global.GVA_DB.Create(tenant1).Error
	assert.NoError(t, err)

	// 创建测试租户2
	tenant2 := &system.Tenant{
		Name: "Test Tenant 2",
		Code: "tenant2",
	}
	err = global.GVA_DB.Create(tenant2).Error
	assert.NoError(t, err)

	// 创建加盟商1（租户1）
	franchisee1 := &franchisees.Franchisee{
		TenantID: tenant1.ID,
		UserID:   user.ID,
		Name:     "Franchisee 1",
		Code:     "F001",
	}
	err = global.GVA_DB.Create(franchisee1).Error
	assert.NoError(t, err)

	// 创建加盟商2（租户2）
	franchisee2 := &franchisees.Franchisee{
		TenantID: tenant2.ID,
		UserID:   user.ID,
		Name:     "Franchisee 2",
		Code:     "F002",
	}
	err = global.GVA_DB.Create(franchisee2).Error
	assert.NoError(t, err)

	return user, tenant1, tenant2, franchisee1, franchisee2
}

// createSingleTenantUser 创建只有一个租户的用户
func createSingleTenantUser(t *testing.T, tenant *system.Tenant) *system.SysUser {
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user := &system.SysUser{
		Username: "singletenant",
		Password: string(hashedPassword),
		NickName: "Single Tenant User",
	}
	err := global.GVA_DB.Create(user).Error
	assert.NoError(t, err)

	// 创建加盟商
	franchisee := &franchisees.Franchisee{
		TenantID: tenant.ID,
		UserID:   user.ID,
		Name:     "Single Franchisee",
		Code:     "SF001",
	}
	err = global.GVA_DB.Create(franchisee).Error
	assert.NoError(t, err)

	return user
}

// cleanupTestData 清理测试数据
func cleanupTestData(user *system.SysUser, tenant1, tenant2 *system.Tenant, franchisee1, franchisee2 *franchisees.Franchisee) {
	global.GVA_DB.Unscoped().Delete(franchisee1)
	global.GVA_DB.Unscoped().Delete(franchisee2)
	global.GVA_DB.Unscoped().Delete(tenant1)
	global.GVA_DB.Unscoped().Delete(tenant2)
	global.GVA_DB.Unscoped().Delete(user)
}

// TestMultiTenantAPI 测试多租户API接口
func TestMultiTenantAPI(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	router := gin.New()
	
	// 这里需要设置实际的路由
	// router.POST("/app/auth/login", authApi.FranchiseeLogin)
	// router.POST("/app/auth/confirm-tenant", authApi.ConfirmTenantLogin)
	// router.POST("/app/auth/switch-tenant", authApi.SwitchTenant)
	// router.GET("/app/auth/my-tenants", authApi.GetMyTenants)

	t.Run("测试登录API", func(t *testing.T) {
		loginData := map[string]interface{}{
			"username":  "testuser",
			"password":  "password123",
			"captcha":   "1234",
			"captchaId": "test-captcha-id",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest("POST", "/app/auth/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// 这里需要根据实际的API响应进行断言
		// assert.Equal(t, http.StatusOK, w.Code)
	})
}

// BenchmarkMultiTenantLogin 性能测试
func BenchmarkMultiTenantLogin(b *testing.B) {
	setupTestEnvironment()
	service := franchiseesService.FranchiseeMultiTenantService{}

	// 创建测试用户
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("password123"), bcrypt.DefaultCost)
	user := &system.SysUser{
		Username: "benchuser",
		Password: string(hashedPassword),
		NickName: "Bench User",
	}
	global.GVA_DB.Create(user)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.AuthenticateUser(user.Username, "password123")
		if err != nil {
			b.Fatal(err)
		}
	}

	// 清理
	global.GVA_DB.Unscoped().Delete(user)
}
