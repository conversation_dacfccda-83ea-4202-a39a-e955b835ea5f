package test

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/stretchr/testify/assert"
)

// TestTenantPerformance 测试多租户系统性能
func TestTenantPerformance(t *testing.T) {
	if global.GVA_DB == nil {
		t.Skip("跳过需要数据库连接的性能测试")
		return
	}

	t.Run("租户验证性能测试", testTenantValidationPerformance)
	t.Run("租户查询性能测试", testTenantQueryPerformance)
	t.Run("并发租户操作测试", testConcurrentTenantOperations)
	t.Run("缓存性能测试", testCachePerformance)
}

// testTenantValidationPerformance 测试租户验证性能
func testTenantValidationPerformance(t *testing.T) {
	tenantID := uint(1)
	iterations := 1000

	// 预热缓存
	global.ValidateTenantCached(tenantID)

	// 测试缓存命中性能
	start := time.Now()
	for i := 0; i < iterations; i++ {
		global.ValidateTenantCached(tenantID)
	}
	duration := time.Since(start)

	avgTime := duration / time.Duration(iterations)
	t.Logf("租户验证平均耗时: %v", avgTime)

	// 性能要求：平均每次验证应该在1ms以内
	assert.Less(t, avgTime, 1*time.Millisecond, "租户验证性能不达标")

	// 检查缓存命中率
	stats := global.GetTenantCacheStats()
	if hitRate, ok := stats["hit_rate"].(float64); ok {
		t.Logf("缓存命中率: %.2f%%", hitRate*100)
		assert.Greater(t, hitRate, 0.9, "缓存命中率应该大于90%")
	}
}

// testTenantQueryPerformance 测试租户查询性能
func testTenantQueryPerformance(t *testing.T) {
	tenantID := uint(1)
	ctx := global.WithTenantContext(context.Background(), tenantID)

	// 创建测试数据
	testOrders := make([]orders.Order, 100)
	for i := 0; i < 100; i++ {
		testOrders[i] = orders.Order{
			TenantID: tenantID,
			OrderNo:  fmt.Sprintf("PERF-TEST-%d", i),
			Status:   1, // 使用数字状态
		}
	}

	// 批量插入测试数据
	err := global.GVA_DB.WithContext(ctx).CreateInBatches(testOrders, 50).Error
	if err != nil {
		t.Logf("创建测试数据失败: %v", err)
		return
	}

	// 清理测试数据
	defer func() {
		global.GVA_DB.Where("order_no LIKE ?", "PERF-TEST-%").Delete(&orders.Order{})
	}()

	// 测试查询性能
	iterations := 100
	start := time.Now()

	for i := 0; i < iterations; i++ {
		var queryOrders []orders.Order
		global.GVA_DB.WithContext(ctx).
			Where("status = ?", 1).
			Limit(10).
			Find(&queryOrders)
	}

	duration := time.Since(start)
	avgTime := duration / time.Duration(iterations)
	t.Logf("租户查询平均耗时: %v", avgTime)

	// 性能要求：平均每次查询应该在10ms以内
	assert.Less(t, avgTime, 10*time.Millisecond, "租户查询性能不达标")
}

// testConcurrentTenantOperations 测试并发租户操作
func testConcurrentTenantOperations(t *testing.T) {
	numGoroutines := 50
	operationsPerGoroutine := 20
	tenantIDs := []uint{1, 2, 3, 4, 5}

	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make(map[uint]int)

	start := time.Now()

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			tenantID := tenantIDs[goroutineID%len(tenantIDs)]
			ctx := global.WithTenantContext(context.Background(), tenantID)

			for j := 0; j < operationsPerGoroutine; j++ {
				// 模拟租户操作：验证 + 查询
				if global.ValidateTenantCached(tenantID) {
					var count int64
					global.GVA_DB.WithContext(ctx).
						Model(&orders.Order{}).
						Where("status = ?", 1).
						Count(&count)

					mu.Lock()
					results[tenantID]++
					mu.Unlock()
				}
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	totalOperations := numGoroutines * operationsPerGoroutine
	avgTime := duration / time.Duration(totalOperations)
	t.Logf("并发操作总耗时: %v", duration)
	t.Logf("平均每次操作耗时: %v", avgTime)
	t.Logf("操作结果统计: %+v", results)

	// 性能要求：平均每次操作应该在50ms以内
	assert.Less(t, avgTime, 50*time.Millisecond, "并发操作性能不达标")

	// 验证所有租户都有操作记录
	for _, tenantID := range tenantIDs {
		assert.Greater(t, results[tenantID], 0, fmt.Sprintf("租户%d没有成功操作", tenantID))
	}
}

// testCachePerformance 测试缓存性能
func testCachePerformance(t *testing.T) {
	// 清空缓存
	global.ClearTenantCache()

	tenantIDs := []uint{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	iterations := 100

	// 测试缓存未命中性能
	start := time.Now()
	for i := 0; i < iterations; i++ {
		tenantID := tenantIDs[i%len(tenantIDs)]
		global.ValidateTenantCached(tenantID)
	}
	missTime := time.Since(start)

	// 测试缓存命中性能
	start = time.Now()
	for i := 0; i < iterations; i++ {
		tenantID := tenantIDs[i%len(tenantIDs)]
		global.ValidateTenantCached(tenantID)
	}
	hitTime := time.Since(start)

	t.Logf("缓存未命中总耗时: %v", missTime)
	t.Logf("缓存命中总耗时: %v", hitTime)

	// 缓存命中应该比未命中快至少5倍
	speedup := float64(missTime) / float64(hitTime)
	t.Logf("缓存加速比: %.2fx", speedup)
	assert.Greater(t, speedup, 5.0, "缓存加速效果不明显")

	// 检查缓存统计
	stats := global.GetTenantCacheStats()
	t.Logf("缓存统计: %+v", stats)
}

// BenchmarkTenantValidation 基准测试：租户验证
func BenchmarkTenantValidation(b *testing.B) {
	if global.GVA_DB == nil {
		b.Skip("跳过需要数据库连接的基准测试")
		return
	}

	tenantID := uint(1)
	// 预热缓存
	global.ValidateTenantCached(tenantID)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			global.ValidateTenantCached(tenantID)
		}
	})
}

// BenchmarkTenantQuery 基准测试：租户查询
func BenchmarkTenantQuery(b *testing.B) {
	if global.GVA_DB == nil {
		b.Skip("跳过需要数据库连接的基准测试")
		return
	}

	tenantID := uint(1)
	ctx := global.WithTenantContext(context.Background(), tenantID)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			var orders []orders.Order
			global.GVA_DB.WithContext(ctx).
				Where("status = ?", 1).
				Limit(10).
				Find(&orders)
		}
	})
}

// BenchmarkTenantContextCreation 基准测试：租户上下文创建
func BenchmarkTenantContextCreation(b *testing.B) {
	tenantID := uint(1)
	baseCtx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ctx := global.WithTenantContext(baseCtx, tenantID)
		_ = ctx
	}
}

// TestMemoryUsage 测试内存使用情况
func TestMemoryUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过内存使用测试")
	}

	// 获取初始内存统计
	var m1 runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m1)

	// 执行大量租户操作
	for i := 0; i < 10000; i++ {
		tenantID := uint(i%100 + 1)
		global.ValidateTenantCached(tenantID)

		ctx := global.WithTenantContext(context.Background(), tenantID)
		_ = ctx
	}

	// 获取最终内存统计
	var m2 runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&m2)

	memoryIncrease := m2.Alloc - m1.Alloc
	t.Logf("内存增长: %d bytes", memoryIncrease)

	// 内存增长应该控制在合理范围内（比如10MB）
	assert.Less(t, memoryIncrease, uint64(10*1024*1024), "内存使用增长过多")
}

// TestSystemResourceUsage 测试系统资源使用情况
func TestSystemResourceUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过系统资源测试")
	}

	// 监控系统资源使用
	monitor := global.GetTenantMonitor()
	initialMetrics := monitor.GetMetrics()

	// 执行一系列操作
	for i := 0; i < 1000; i++ {
		tenantID := uint(i%10 + 1)

		// 模拟请求处理
		start := time.Now()
		global.ValidateTenantCached(tenantID)
		responseTime := time.Since(start)

		// 记录监控指标
		global.RecordTenantRequest(responseTime)
	}

	// 检查最终指标
	finalMetrics := monitor.GetMetrics()

	t.Logf("初始指标: %+v", initialMetrics)
	t.Logf("最终指标: %+v", finalMetrics)

	// 验证指标合理性
	assert.Greater(t, finalMetrics.TotalRequests, initialMetrics.TotalRequests)
	assert.Less(t, finalMetrics.AvgResponseTime, 10.0) // 平均响应时间应该小于10ms
}
