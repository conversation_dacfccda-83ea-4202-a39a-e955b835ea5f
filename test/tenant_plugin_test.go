package test

import (
	"context"
	"testing"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"
	"github.com/stretchr/testify/assert"
)

// TestTenantPluginWorkingStatus 测试GORM租户插件工作状态
func TestTenantPluginWorkingStatus(t *testing.T) {
	// 初始化数据库连接
	setupTestDB(t)
	defer teardownTestDB(t)

	t.Run("测试租户插件自动注入租户ID", func(t *testing.T) {
		testTenantAutoInjection(t)
	})

	t.Run("测试租户插件自动过滤查询", func(t *testing.T) {
		testTenantAutoFiltering(t)
	})

	t.Run("测试跳过租户隔离功能", func(t *testing.T) {
		testSkipTenantIsolation(t)
	})

	t.Run("测试超级管理员权限", func(t *testing.T) {
		testSuperAdminAccess(t)
	})
}

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) {
	// 初始化配置和数据库
	global.GVA_DB = initialize.Gorm()
	if global.GVA_DB == nil {
		t.Fatal("Failed to initialize database")
	}

	// 确保租户插件已注册
	err := global.GVA_DB.Use(&tenant.TenantPlugin{})
	if err != nil {
		t.Fatalf("Failed to register tenant plugin: %v", err)
	}

	// 创建测试表
	err = global.GVA_DB.AutoMigrate(&orders.Order{}, &products.Product{})
	if err != nil {
		t.Fatalf("Failed to migrate test tables: %v", err)
	}
}

// teardownTestDB 清理测试数据库
func teardownTestDB(t *testing.T) {
	if global.GVA_DB != nil {
		// 清理测试数据
		global.GVA_DB.Exec("DELETE FROM orders WHERE tenant_id IN (999, 998)")
		global.GVA_DB.Exec("DELETE FROM products WHERE tenant_id IN (999, 998)")
	}
}

// testTenantAutoInjection 测试租户ID自动注入
func testTenantAutoInjection(t *testing.T) {
	// 设置租户上下文
	tenantID := uint(999)
	ctx := global.WithTenantContext(context.Background(), tenantID)

	// 创建订单（应该自动注入租户ID）
	order := orders.Order{
		OrderNo:      "TEST-ORDER-001",
		FranchiseeId: 1,
		Amount:       100,
	}

	err := global.GVA_DB.WithContext(ctx).Create(&order).Error
	assert.NoError(t, err, "创建订单应该成功")

	// 验证租户ID是否被自动注入
	var savedOrder orders.Order
	err = global.GVA_DB.Scopes(tenant.SkipTenant).Where("order_no = ?", "TEST-ORDER-001").First(&savedOrder).Error
	assert.NoError(t, err, "查询订单应该成功")
	assert.Equal(t, tenantID, savedOrder.TenantID, "租户ID应该被自动注入")

	t.Logf("✅ 租户ID自动注入测试通过: 订单 %s 的租户ID为 %d", savedOrder.OrderNo, savedOrder.TenantID)
}

// testTenantAutoFiltering 测试租户自动过滤
func testTenantAutoFiltering(t *testing.T) {
	// 创建两个不同租户的订单
	tenant1ID := uint(999)
	tenant2ID := uint(998)

	// 租户1的订单
	order1 := orders.Order{
		OrderNo:      "TEST-ORDER-TENANT1",
		FranchiseeId: 1,
		Amount:       100,
		TenantID:     tenant1ID,
	}
	err := global.GVA_DB.Scopes(tenant.SkipTenant).Create(&order1).Error
	assert.NoError(t, err, "创建租户1订单应该成功")

	// 租户2的订单
	order2 := orders.Order{
		OrderNo:      "TEST-ORDER-TENANT2",
		FranchiseeId: 2,
		Amount:       200,
		TenantID:     tenant2ID,
	}
	err = global.GVA_DB.Scopes(tenant.SkipTenant).Create(&order2).Error
	assert.NoError(t, err, "创建租户2订单应该成功")

	// 测试租户1只能看到自己的订单
	ctx1 := global.WithTenantContext(context.Background(), tenant1ID)
	var tenant1Orders []orders.Order
	err = global.GVA_DB.WithContext(ctx1).Find(&tenant1Orders).Error
	assert.NoError(t, err, "查询租户1订单应该成功")

	// 验证只返回租户1的订单
	foundTenant1Order := false
	foundTenant2Order := false
	for _, order := range tenant1Orders {
		if order.OrderNo == "TEST-ORDER-TENANT1" {
			foundTenant1Order = true
		}
		if order.OrderNo == "TEST-ORDER-TENANT2" {
			foundTenant2Order = true
		}
	}

	assert.True(t, foundTenant1Order, "应该能找到租户1的订单")
	assert.False(t, foundTenant2Order, "不应该能找到租户2的订单")

	t.Logf("✅ 租户自动过滤测试通过: 租户1查询到 %d 个订单，未查询到租户2的订单", len(tenant1Orders))
}

// testSkipTenantIsolation 测试跳过租户隔离
func testSkipTenantIsolation(t *testing.T) {
	// 使用跳过租户隔离的作用域查询所有订单
	var allOrders []orders.Order
	err := global.GVA_DB.Scopes(tenant.SkipTenant).Find(&allOrders).Error
	assert.NoError(t, err, "跳过租户隔离查询应该成功")

	// 验证能查询到所有租户的订单
	foundTenant1Order := false
	foundTenant2Order := false
	for _, order := range allOrders {
		if order.OrderNo == "TEST-ORDER-TENANT1" {
			foundTenant1Order = true
		}
		if order.OrderNo == "TEST-ORDER-TENANT2" {
			foundTenant2Order = true
		}
	}

	assert.True(t, foundTenant1Order, "跳过租户隔离应该能找到租户1的订单")
	assert.True(t, foundTenant2Order, "跳过租户隔离应该能找到租户2的订单")

	t.Logf("✅ 跳过租户隔离测试通过: 查询到 %d 个订单，包含所有租户的数据", len(allOrders))
}

// testSuperAdminAccess 测试超级管理员权限
func testSuperAdminAccess(t *testing.T) {
	// 使用超级管理员作用域查询所有订单
	var allOrders []orders.Order
	err := global.GVA_DB.Scopes(tenant.SuperAdmin).Find(&allOrders).Error
	assert.NoError(t, err, "超级管理员查询应该成功")

	// 验证能查询到所有租户的订单
	foundTenant1Order := false
	foundTenant2Order := false
	for _, order := range allOrders {
		if order.OrderNo == "TEST-ORDER-TENANT1" {
			foundTenant1Order = true
		}
		if order.OrderNo == "TEST-ORDER-TENANT2" {
			foundTenant2Order = true
		}
	}

	assert.True(t, foundTenant1Order, "超级管理员应该能找到租户1的订单")
	assert.True(t, foundTenant2Order, "超级管理员应该能找到租户2的订单")

	t.Logf("✅ 超级管理员权限测试通过: 查询到 %d 个订单，包含所有租户的数据", len(allOrders))
}

// TestTenantTableDetection 测试租户表检测功能
func TestTenantTableDetection(t *testing.T) {
	testCases := []struct {
		tableName string
		expected  bool
	}{
		{"order", true}, // 修改为正确的表名
		{"products", true},
		{"order_goods", true},
		{"franchisees", true},
		{"sys_users", false},
		{"sys_apis", false},
		{"unknown_table", false},
	}

	for _, tc := range testCases {
		result := global.IsTenantTable(tc.tableName)
		assert.Equal(t, tc.expected, result, "表 %s 的租户检测结果应该为 %v", tc.tableName, tc.expected)
	}

	t.Log("✅ 租户表检测功能测试通过")
}

// TestOrderTableNameConsistency 测试订单表名一致性
func TestOrderTableNameConsistency(t *testing.T) {
	// 测试Order模型的TableName方法
	orderTableName := orders.Order{}.TableName()
	assert.Equal(t, "order", orderTableName, "Order模型的TableName应该返回'order'")

	// 测试租户表配置中是否包含正确的表名
	isOrderTenantTable := global.IsTenantTable(orderTableName)
	assert.True(t, isOrderTenantTable, "订单表应该被配置为租户表")

	t.Logf("✅ 订单表名一致性测试通过: TableName()='%s', IsTenantTable=%v", orderTableName, isOrderTenantTable)
}
