package test

import (
	"context"
	"testing"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"
	"github.com/stretchr/testify/assert"
)

// TestTenantPluginWorkingStatus 测试GORM租户插件工作状态
func TestTenantPluginWorkingStatus(t *testing.T) {
	// 初始化数据库连接
	setupTestDB(t)
	defer teardownTestDB(t)

	t.Run("测试租户插件自动注入租户ID", func(t *testing.T) {
		testTenantAutoInjection(t)
	})

	t.Run("测试租户插件自动过滤查询", func(t *testing.T) {
		testTenantAutoFiltering(t)
	})

	t.Run("测试跳过租户隔离功能", func(t *testing.T) {
		testSkipTenantIsolation(t)
	})

	t.Run("测试超级管理员权限", func(t *testing.T) {
		testSuperAdminAccess(t)
	})
}

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) {
	// 初始化配置和数据库
	global.GVA_DB = initialize.Gorm()
	if global.GVA_DB == nil {
		t.Fatal("Failed to initialize database")
	}

	// 确保租户插件已注册
	err := global.GVA_DB.Use(&tenant.TenantPlugin{})
	if err != nil {
		t.Fatalf("Failed to register tenant plugin: %v", err)
	}

	// 创建测试表
	err = global.GVA_DB.AutoMigrate(&orders.Order{}, &products.Product{})
	if err != nil {
		t.Fatalf("Failed to migrate test tables: %v", err)
	}
}

// teardownTestDB 清理测试数据库
func teardownTestDB(t *testing.T) {
	if global.GVA_DB != nil {
		// 清理测试数据
		global.GVA_DB.Exec("DELETE FROM orders WHERE tenant_id IN (999, 998)")
		global.GVA_DB.Exec("DELETE FROM products WHERE tenant_id IN (999, 998)")
	}
}

// testTenantAutoInjection 测试租户ID自动注入
func testTenantAutoInjection(t *testing.T) {
	// 设置租户上下文
	tenantID := uint(999)
	ctx := global.WithTenantContext(context.Background(), tenantID)

	// 创建订单（应该自动注入租户ID）
	order := orders.Order{
		OrderNo:      "TEST-ORDER-001",
		FranchiseeId: 1,
		Amount:       100,
	}

	err := global.GVA_DB.WithContext(ctx).Create(&order).Error
	assert.NoError(t, err, "创建订单应该成功")

	// 验证租户ID是否被自动注入
	var savedOrder orders.Order
	err = global.GVA_DB.Scopes(tenant.SkipTenant).Where("order_no = ?", "TEST-ORDER-001").First(&savedOrder).Error
	assert.NoError(t, err, "查询订单应该成功")
	assert.Equal(t, tenantID, savedOrder.TenantID, "租户ID应该被自动注入")

	t.Logf("✅ 租户ID自动注入测试通过: 订单 %s 的租户ID为 %d", savedOrder.OrderNo, savedOrder.TenantID)
}

// testTenantAutoFiltering 测试租户自动过滤
func testTenantAutoFiltering(t *testing.T) {
	// 创建两个不同租户的订单
	tenant1ID := uint(999)
	tenant2ID := uint(998)

	// 租户1的订单
	order1 := orders.Order{
		OrderNo:      "TEST-ORDER-TENANT1",
		FranchiseeId: 1,
		Amount:       100,
		TenantID:     tenant1ID,
	}
	err := global.GVA_DB.Scopes(tenant.SkipTenant).Create(&order1).Error
	assert.NoError(t, err, "创建租户1订单应该成功")

	// 租户2的订单
	order2 := orders.Order{
		OrderNo:      "TEST-ORDER-TENANT2",
		FranchiseeId: 2,
		Amount:       200,
		TenantID:     tenant2ID,
	}
	err = global.GVA_DB.Scopes(tenant.SkipTenant).Create(&order2).Error
	assert.NoError(t, err, "创建租户2订单应该成功")

	// 测试租户1只能看到自己的订单
	ctx1 := global.WithTenantContext(context.Background(), tenant1ID)
	var tenant1Orders []orders.Order
	err = global.GVA_DB.WithContext(ctx1).Find(&tenant1Orders).Error
	assert.NoError(t, err, "查询租户1订单应该成功")

	// 验证只返回租户1的订单
	foundTenant1Order := false
	foundTenant2Order := false
	for _, order := range tenant1Orders {
		if order.OrderNo == "TEST-ORDER-TENANT1" {
			foundTenant1Order = true
		}
		if order.OrderNo == "TEST-ORDER-TENANT2" {
			foundTenant2Order = true
		}
	}

	assert.True(t, foundTenant1Order, "应该能找到租户1的订单")
	assert.False(t, foundTenant2Order, "不应该能找到租户2的订单")

	t.Logf("✅ 租户自动过滤测试通过: 租户1查询到 %d 个订单，未查询到租户2的订单", len(tenant1Orders))
}

// testSkipTenantIsolation 测试跳过租户隔离
func testSkipTenantIsolation(t *testing.T) {
	// 使用跳过租户隔离的作用域查询所有订单
	var allOrders []orders.Order
	err := global.GVA_DB.Scopes(tenant.SkipTenant).Find(&allOrders).Error
	assert.NoError(t, err, "跳过租户隔离查询应该成功")

	// 验证能查询到所有租户的订单
	foundTenant1Order := false
	foundTenant2Order := false
	for _, order := range allOrders {
		if order.OrderNo == "TEST-ORDER-TENANT1" {
			foundTenant1Order = true
		}
		if order.OrderNo == "TEST-ORDER-TENANT2" {
			foundTenant2Order = true
		}
	}

	assert.True(t, foundTenant1Order, "跳过租户隔离应该能找到租户1的订单")
	assert.True(t, foundTenant2Order, "跳过租户隔离应该能找到租户2的订单")

	t.Logf("✅ 跳过租户隔离测试通过: 查询到 %d 个订单，包含所有租户的数据", len(allOrders))
}

// testSuperAdminAccess 测试超级管理员权限
func testSuperAdminAccess(t *testing.T) {
	// 使用超级管理员作用域查询所有订单
	var allOrders []orders.Order
	err := global.GVA_DB.Scopes(tenant.SuperAdmin).Find(&allOrders).Error
	assert.NoError(t, err, "超级管理员查询应该成功")

	// 验证能查询到所有租户的订单
	foundTenant1Order := false
	foundTenant2Order := false
	for _, order := range allOrders {
		if order.OrderNo == "TEST-ORDER-TENANT1" {
			foundTenant1Order = true
		}
		if order.OrderNo == "TEST-ORDER-TENANT2" {
			foundTenant2Order = true
		}
	}

	assert.True(t, foundTenant1Order, "超级管理员应该能找到租户1的订单")
	assert.True(t, foundTenant2Order, "超级管理员应该能找到租户2的订单")

	t.Logf("✅ 超级管理员权限测试通过: 查询到 %d 个订单，包含所有租户的数据", len(allOrders))
}

// TestTenantTableDetection 测试租户表检测功能
func TestTenantTableDetection(t *testing.T) {
	testCases := []struct {
		tableName string
		expected  bool
	}{
		{"order", true}, // 修改为正确的表名
		{"products", true},
		{"order_goods", true},
		{"franchisees", true},
		{"sys_users", false},
		{"sys_apis", false},
		{"unknown_table", false},
	}

	for _, tc := range testCases {
		result := global.IsTenantTable(tc.tableName)
		assert.Equal(t, tc.expected, result, "表 %s 的租户检测结果应该为 %v", tc.tableName, tc.expected)
	}

	t.Log("✅ 租户表检测功能测试通过")
}

// TestOrderTableNameConsistency 测试订单表名一致性
func TestOrderTableNameConsistency(t *testing.T) {
	// 测试Order模型的TableName方法
	orderTableName := orders.Order{}.TableName()
	assert.Equal(t, "order", orderTableName, "Order模型的TableName应该返回'order'")

	// 测试租户表配置中是否包含正确的表名
	isOrderTenantTable := global.IsTenantTable(orderTableName)
	assert.True(t, isOrderTenantTable, "订单表应该被配置为租户表")

	t.Logf("✅ 订单表名一致性测试通过: TableName()='%s', IsTenantTable=%v", orderTableName, isOrderTenantTable)
}

// TestAutoTenantDetection 测试自动租户检测功能
func TestAutoTenantDetection(t *testing.T) {
	// 测试RegisterTenantModel函数
	global.RegisterTenantModel(orders.Order{})
	global.RegisterTenantModel(products.Product{})

	// 测试自动检测的表
	orderTableName := orders.Order{}.TableName()
	productTableName := (&products.Product{}).TableName()

	// 验证自动检测是否工作
	assert.True(t, global.IsTenantTable(orderTableName), "订单表应该被自动检测为租户表")
	assert.True(t, global.IsTenantTable(productTableName), "产品表应该被自动检测为租户表")

	// 测试非租户表
	assert.False(t, global.IsTenantTable("sys_users"), "系统用户表不应该是租户表")
	assert.False(t, global.IsTenantTable("unknown_table"), "未知表不应该是租户表")

	// 获取自动检测到的表列表
	autoDetectedTables := global.GetAutoDetectedTenantTables()
	t.Logf("✅ 自动检测到的租户表: %v", autoDetectedTables)

	// 验证表列表包含我们注册的表
	found := false
	for tableName := range autoDetectedTables {
		if tableName == orderTableName {
			found = true
			break
		}
	}
	assert.True(t, found, "自动检测的表列表应该包含订单表")

	t.Log("✅ 自动租户检测功能测试通过")
}

// TestSecurityAudit 测试安全审计功能
func TestSecurityAudit(t *testing.T) {
	// 获取安全审计器
	auditor := global.GetSecurityAuditor()
	assert.NotNil(t, auditor, "安全审计器不应该为nil")

	// 记录一个跨租户访问事件
	global.RecordCrossTenantAccess(context.Background(), 123, 1, 2, "192.168.1.100", "/api/orders", "GET", "尝试访问其他租户的订单")

	// 记录一个租户隔离绕过事件
	global.RecordTenantIsolationBypass(context.Background(), 123, 1, "192.168.1.100", "/api/orders", "GET", "尝试绕过租户隔离", map[string]interface{}{
		"table":     "orders",
		"operation": "query",
	})

	// 获取最近的事件
	recentEvents := auditor.GetRecentEvents(10)
	assert.GreaterOrEqual(t, len(recentEvents), 2, "应该至少有2个安全事件")

	// 验证事件类型
	foundCrossTenantEvent := false
	foundBypassEvent := false
	for _, event := range recentEvents {
		if event.Type == global.EventCrossTenantAccess {
			foundCrossTenantEvent = true
			assert.Equal(t, uint(123), event.UserID, "用户ID应该匹配")
			assert.Equal(t, uint(1), event.TenantID, "租户ID应该匹配")
			assert.Equal(t, uint(2), event.TargetTenantID, "目标租户ID应该匹配")
		}
		if event.Type == global.EventTenantIsolationBypass {
			foundBypassEvent = true
			assert.Equal(t, global.SeverityCritical, event.Severity, "租户隔离绕过事件应该是严重级别")
		}
	}

	assert.True(t, foundCrossTenantEvent, "应该找到跨租户访问事件")
	assert.True(t, foundBypassEvent, "应该找到租户隔离绕过事件")

	t.Log("✅ 安全审计功能测试通过")
}

// TestTenantCacheOptimization 测试租户缓存性能优化
func TestTenantCacheOptimization(t *testing.T) {
	// 初始化数据库连接（如果需要）
	if global.GVA_DB == nil {
		// 跳过需要数据库的测试
		t.Skip("跳过需要数据库连接的测试")
		return
	}

	// 清空缓存统计
	global.ClearTenantCache()

	// 测试缓存命中和未命中
	tenantID := uint(999)

	// 第一次访问（缓存未命中）
	result1 := global.ValidateTenantCached(tenantID)

	// 第二次访问（缓存命中）
	result2 := global.ValidateTenantCached(tenantID)

	// 结果应该一致
	assert.Equal(t, result1, result2, "缓存命中和未命中的结果应该一致")

	// 获取缓存统计
	stats := global.GetTenantCacheStats()

	// 验证统计信息
	assert.GreaterOrEqual(t, stats["total_cached"].(int), 1, "应该至少有1个缓存条目")
	assert.GreaterOrEqual(t, stats["hit_count"].(int64), int64(1), "应该至少有1次缓存命中")
	assert.GreaterOrEqual(t, stats["miss_count"].(int64), int64(1), "应该至少有1次缓存未命中")

	hitRate := stats["hit_rate"].(float64)
	assert.GreaterOrEqual(t, hitRate, 0.0, "命中率应该大于等于0")
	assert.LessOrEqual(t, hitRate, 1.0, "命中率应该小于等于1")

	t.Logf("✅ 租户缓存性能测试通过: 命中率=%.2f, 总缓存=%d", hitRate, stats["total_cached"])

	// 测试缓存预热
	preloadTenantIDs := []uint{1001, 1002, 1003}
	global.PrewarmCache(preloadTenantIDs)

	// 验证预热后的缓存
	statsAfterPrewarm := global.GetTenantCacheStats()
	assert.GreaterOrEqual(t, statsAfterPrewarm["total_cached"].(int), len(preloadTenantIDs),
		"预热后缓存数量应该增加")

	t.Log("✅ 租户缓存预热测试通过")
}
