package test

import (
	"context"
	"testing"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
)

// setupTestRedis 初始化测试Redis连接
func setupTestRedis(t *testing.T) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       15, // 测试专用DB
	})

	_, err := rdb.Ping(context.Background()).Result()
	if err != nil {
		t.Skip("Redis连接失败，跳过测试")
		return nil
	}

	// 清理测试数据
	rdb.FlushDB(context.Background())
	return rdb
}

// TestDistributedTenantSession 测试分布式租户会话管理
func TestDistributedTenantSession(t *testing.T) {
	rdb := setupTestRedis(t)
	if rdb == nil {
		return
	}
	defer rdb.Close()

	// 保存原始Redis连接并恢复
	originalRedis := global.GVA_REDIS
	defer func() { global.GVA_REDIS = originalRedis }()
	global.GVA_REDIS = rdb

	ctx := context.Background()
	testCases := []struct {
		name      string
		sessionID string
		tenantID  uint
		userID    uint
	}{
		{"正常会话", "session-normal-1", 1, 1001},
		{"特殊字符会话", "session-special-@#$%", 2, 1002},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试会话生命周期
			err := global.SetTenantSession(ctx, tc.sessionID, tc.tenantID, tc.userID)
			assert.NoError(t, err)

			session, err := global.GetTenantSession(ctx, tc.sessionID)
			assert.NoError(t, err)
			assert.Equal(t, tc.tenantID, session.TenantID)
			assert.Equal(t, tc.userID, session.UserID)

			// 测试会话清理
			err = global.ClearTenantSession(ctx, tc.sessionID)
			assert.NoError(t, err)
			_, err = global.GetTenantSession(ctx, tc.sessionID)
			assert.Error(t, err)
		})
	}
}

// TestDistributedTenantCache 测试分布式租户缓存功能
func TestDistributedTenantCache(t *testing.T) {
	rdb := setupTestRedis(t)
	if rdb == nil {
		return
	}
	defer rdb.Close()

	// 保存原始Redis连接并恢复
	originalRedis := global.GVA_REDIS
	defer func() { global.GVA_REDIS = originalRedis }()
	global.GVA_REDIS = rdb

	ctx := context.Background()
	testTenants := []uint{1, 2, 3}

	t.Run("缓存验证", func(t *testing.T) {
		for _, tenantID := range testTenants {
			valid := global.ValidateTenantDistributed(ctx, tenantID)
			assert.True(t, valid, "租户验证应该成功")
		}
	})

	t.Run("缓存失效", func(t *testing.T) {
		for _, tenantID := range testTenants {
			err := global.InvalidateTenantCacheDistributed(ctx, tenantID)
			assert.NoError(t, err)
		}
	})
}