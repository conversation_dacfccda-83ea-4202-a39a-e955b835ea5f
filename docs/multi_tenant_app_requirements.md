# 多租户统一订货app开发需求评估报告

## 一、项目背景分析

通过对现有代码库的分析，当前系统是一个基于Go语言开发的后端服务，使用Gin框架构建API接口，采用GORM作为ORM框架进行数据库操作。系统主要功能包括商品管理、订单管理、加盟商管理、支付管理等模块，目前主要服务于林酒道馆app。

现有系统的主要特点：
1. 采用MVC架构模式，代码组织清晰
2. 已实现完整的商品管理、订单管理、用户管理等核心功能
3. 数据库采用MySQL，并使用Redis进行缓存
4. 已有完整的JWT认证机制和权限管理
5. 系统配置灵活，支持多环境部署

**重要概念澄清**：
当前系统中的"品牌"概念仅指商品品牌（`product_brand`），与本次需求中的"多品牌"概念不同。本次需求中的"多品牌"实际上是指**多租户**系统，即在同一套系统架构下支持多个独立的业务主体（租户），每个租户拥有自己的数据和配置，但共享底层基础设施和核心功能。

## 二、需求分析与技术挑战

### 2.1 核心需求

1. **架构设计方案**：将现有林酒道馆app和一款好酒app以小程序形式保持不变，开发多租户统一订货app

2. **功能需求**：
   - 商品管理：包括商品列表、商品详情、商品分类、商品属性、区域定价、商品规则等
   - 商城管理：包括下载地址/二维码、注册页面、登录页面、商城分类导航、商品查看页等
   - 加盟管理：目前支持多商品组合及订购数量阈值，需要开发其他场景的方案
   - 云仓库：需要支持多租户配置
   - 财务管理：需要支持多租户配置

3. **集成需求**：
   - 一款好酒app需定制化开发
   - 后台app功能基本与林酒道馆app一致
   - 用户系统不变
   - 业务统计数据格式保持不变
   - 购买规则包括会员积分计算和赠品政策

4. **UI/UX需求**：
   - 需明确一款好酒app的配色、图片等信息流设计

5. **加盟商多租户关联需求**：
   - 同一个用户账号可以在多个租户中拥有加盟商身份
   - 用户登录后可以选择要登录的租户身份进行操作
   - 系统需要为每个用户设置一个默认租户，作为登录后的初始选择
   - 每个租户中的加盟商信息（如编码、余额、积分等）完全独立

### 2.2 技术挑战

通过代码分析，以下技术挑战需要解决：

1. **多租户支持**：
   - 当前系统缺乏租户模型和租户隔离机制
   - 需要设计并实现完整的多租户架构

2. **数据隔离与共享**：
   - 需要设计合理的数据隔离机制，确保不同租户的数据安全
   - 同时需要设计共享数据的机制，如用户系统

3. **UI定制化**：
   - 需要为不同租户提供不同的UI配置
   - 当前系统缺乏UI配置相关的模型和服务

4. **业务规则扩展**：
   - 现有的商品规则模型需要扩展，以支持租户特定的业务规则
   - 当前的`product_gift_rule`模型需要进行重构

5. **API接口适配**：
   - 需要调整现有API接口，以支持多租户参数

6. **加盟商多租户关联**：
   - 需要重构加盟商数据模型，实现与租户的1:1关系
   - 需要实现多租户登录选择和租户切换机制
   - 需要调整认证流程，支持租户上下文和加盟商身份验证

## 三、技术方案建议

### 3.1 系统架构设计

建议采用以下架构设计方案：

1. **多租户架构**：
   - 在数据库层面实现多租户隔离，为相关表添加`tenant_id`字段
   - 在服务层实现租户数据的过滤和隔离

2. **微服务拆分**：
   - 考虑将系统拆分为核心服务和租户特定服务
   - 核心服务包括用户管理、订单处理等通用功能
   - 租户特定服务包括UI配置、租户特定业务规则等

3. **API网关**：
   - 实现API网关，根据租户标识路由请求到对应的服务
   - 在网关层实现认证和基本的请求验证

4. **配置中心**：
   - 实现集中式配置管理，支持不同租户的配置隔离
   - 支持配置热更新，无需重启服务

5. **加盟商租户关联机制**：
   - 重构加盟商模型，直接关联租户ID（1:1关系）
   - 设计多租户登录选择和租户切换接口
   - 在认证流程中加入租户上下文和加盟商身份处理

### 3.2 数据库设计调整

需要对现有数据库模型进行以下调整：

1. **租户模型设计**：
```go
type Tenant struct {
    global.GVA_MODEL
    Name          string `json:"name" gorm:"column:name;comment:租户名称;size:56;"`
    Code          string `json:"code" gorm:"column:code;comment:租户编码;size:56;unique;"`
    Logo          string `json:"logo" gorm:"column:logo;comment:租户logo;size:255;"`
    PrimaryColor  string `json:"primaryColor" gorm:"column:primary_color;comment:主色调;size:20;"`
    SecondaryColor string `json:"secondaryColor" gorm:"column:secondary_color;comment:次色调;size:20;"`
    AppConfig     *TenantAppConfig `json:"appConfig" gorm:"foreignKey:TenantID;references:ID;"`
    Status        *bool  `json:"status" gorm:"column:status;comment:状态(启用/禁用);"`
    ContactName   string `json:"contactName" gorm:"column:contact_name;comment:联系人;size:50;"`
    ContactPhone  string `json:"contactPhone" gorm:"column:contact_phone;comment:联系电话;size:20;"`
    ExpireDate    *time.Time `json:"expireDate" gorm:"column:expire_date;comment:租约到期日期;"`
}

type TenantAppConfig struct {
    global.GVA_MODEL
    TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;"`
    AppName       string `json:"appName" gorm:"column:app_name;comment:应用名称;size:100;"`
    AppLogo       string `json:"appLogo" gorm:"column:app_logo;comment:应用logo;size:255;"`
    LoginBgImage  string `json:"loginBgImage" gorm:"column:login_bg_image;comment:登录背景图;size:255;"`
    HomePageConfig string `json:"homePageConfig" gorm:"column:home_page_config;comment:首页配置;type:json;"`
    // 其他UI配置字段
}
```

2. **用户租户关联模型**：
```go
type UserTenantRelation struct {
    global.GVA_MODEL
    UserID       uint   `json:"userId" gorm:"column:user_id;comment:用户ID;index:idx_user_tenant,unique;"`
    TenantID     uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;index:idx_user_tenant,unique;"`
    IsDefault    bool   `json:"isDefault" gorm:"column:is_default;comment:是否默认租户;"`
    Role         string `json:"role" gorm:"column:role;comment:用户在该租户中的角色;size:50;"`
    Status       *bool  `json:"status" gorm:"column:status;comment:状态(启用/禁用);"`
    JoinTime     *time.Time `json:"joinTime" gorm:"column:join_time;comment:加入时间;"`
}
```

3. **现有模型扩展**：
   - 在`Product`、`Order`、`Franchisee`等核心模型中添加`TenantID`字段
   - 调整相关的查询逻辑，加入租户过滤

4. **权限模型调整**：
   - 扩展现有权限模型，支持租户级别的权限控制
   - 实现跨租户管理员角色

### 3.3 服务层调整

需要对现有服务层进行以下调整：

1. **服务接口改造**：
   - 在服务接口中添加租户参数
   - 实现租户数据隔离的中间件

2. **业务逻辑扩展**：
   - 重构商品规则相关逻辑，支持租户特定的业务规则
   - 实现租户特定的业务处理流程

3. **缓存策略调整**：
   - 调整缓存key的生成策略，加入租户标识
   - 优化缓存更新机制，避免跨租户缓存污染

4. **用户租户关联服务**：
```go
// UserTenantService 用户租户关联服务
type UserTenantService struct {}

// GetUserTenants 获取用户关联的所有租户
func (s *UserTenantService) GetUserTenants(userID uint) ([]response.UserTenantInfo, error) {
    // 实现逻辑
}

// SwitchTenant 切换用户当前租户
func (s *UserTenantService) SwitchTenant(userID, tenantID uint) error {
    // 实现逻辑
}

// SetDefaultTenant 设置用户默认租户
func (s *UserTenantService) SetDefaultTenant(userID, tenantID uint) error {
    // 实现逻辑
}

// GetDefaultTenant 获取用户默认租户
func (s *UserTenantService) GetDefaultTenant(userID uint) (*response.TenantInfo, error) {
    // 实现逻辑
}
```

### 3.4 API接口调整

需要对现有API接口进行以下调整：

1. **接口参数扩展**：
   - 在请求参数中添加租户标识
   - 在响应中包含租户相关信息

2. **接口权限控制**：
   - 实现基于租户的接口访问控制
   - 支持跨租户接口调用的认证机制

3. **API文档更新**：
   - 更新Swagger文档，反映多租户支持
   - 为不同租户提供定制化的API文档

4. **加盟商多租户登录接口**：
```go
// FranchiseeAuthApi 加盟商认证API
type FranchiseeAuthApi struct {}

// Login 加盟商登录
// @Tags FranchiseeAuth
// @Summary 加盟商登录
// @accept application/json
// @Produce application/json
// @Param data body request.LoginRequest true "登录信息"
// @Success 200 {object} response.LoginResponse "成功"
// @Router /app/auth/login [post]
func (api *FranchiseeAuthApi) Login(c *gin.Context) {
    // 实现逻辑：返回租户选择或直接登录
}

// ConfirmTenant 确认租户登录
// @Tags FranchiseeAuth
// @Summary 确认租户登录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ConfirmTenantRequest true "租户ID"
// @Success 200 {object} response.LoginResponse "成功"
// @Router /app/auth/confirm-tenant [post]
func (api *FranchiseeAuthApi) ConfirmTenant(c *gin.Context) {
    // 实现逻辑
}

// SwitchTenant 切换租户
// @Tags FranchiseeAuth
// @Summary 切换租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SwitchTenantRequest true "租户ID"
// @Success 200 {object} response.LoginResponse "成功"
// @Router /app/auth/switch-tenant [post]
func (api *FranchiseeAuthApi) SwitchTenant(c *gin.Context) {
    // 实现逻辑
}

// GetMyTenants 获取我的租户列表
// @Tags FranchiseeAuth
// @Summary 获取我的租户列表
// @Security ApiKeyAuth
// @Produce application/json
// @Success 200 {object} response.Response{data=[]response.FranchiseeWithTenant} "成功"
// @Router /app/auth/my-tenants [get]
func (api *FranchiseeAuthApi) GetMyTenants(c *gin.Context) {
    // 实现逻辑
}
```

### 3.5 认证流程调整

需要对现有认证流程进行以下调整：

1. **登录流程改造**：
   - 用户登录成功后，查询用户的加盟商身份
   - 单租户直接登录，多租户显示选择界面
   - 生成包含租户信息和加盟商信息的JWT令牌

2. **JWT令牌扩展**：
```go
// JWT声明结构
type CustomClaims struct {
    BaseClaims
    BufferTime   int64
    jwt.StandardClaims
    UserID       uint   `json:"userId"`       // 用户ID
    TenantID     uint   `json:"tenantId"`     // 当前租户ID
    FranchiseeID uint   `json:"franchiseeId"` // 当前加盟商ID
    TenantCode   string `json:"tenantCode"`   // 租户编码
}
```

3. **租户切换机制**：
   - 实现租户切换接口，生成新的JWT令牌
   - 在前端实现租户切换UI
   - 切换租户后刷新页面数据

4. **中间件改造**：
```go
// TenantMiddleware 租户中间件
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims, exists := c.Get("claims")
        if !exists {
            response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
            c.Abort()
            return
        }
        
        customClaims, ok := claims.(*CustomClaims)
        if !ok {
            response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
            c.Abort()
            return
        }
        
        // 将租户ID添加到上下文
        c.Set("tenantId", customClaims.TenantID)
        c.Next()
    }
}
```

## 四、开发周期与成本估算

### 4.1 开发阶段划分

1. **需求分析与设计阶段（3周）**
   - 详细需求收集与分析
   - 系统架构设计
   - 数据库模型设计
   - API接口设计
   - UI/UX设计规划

2. **核心功能开发阶段（9周）**
   - 多租户架构实现（2周）
   - 租户管理模块开发（1周）
   - 用户租户关联模块开发（1周）
   - 商品管理模块改造（2周）
   - 订单管理模块改造（2周）
   - 用户系统集成（1周）

3. **租户特定功能开发阶段（6周）**
   - UI配置系统开发（2周）
   - 商品规则扩展开发（2周）
   - 租户特定业务逻辑实现（2周）

4. **测试与优化阶段（4周）**
   - 单元测试与集成测试（2周）
   - 性能测试与优化（1周）
   - 安全测试与修复（1周）

5. **部署与上线阶段（2周）**
   - 环境配置与部署（1周）
   - 数据迁移与验证（0.5周）
   - 上线支持与监控（0.5周）

**总计开发周期：24周（约6个月）**

### 4.2 资源需求

1. **开发团队配置**：
   - 项目经理：1名
   - 后端开发工程师：3名
   - 前端/小程序开发工程师：2名
   - UI/UX设计师：1名
   - 测试工程师：2名
   - DevOps工程师：1名

2. **基础设施需求**：
   - 开发环境服务器：2台
   - 测试环境服务器：2台
   - 生产环境服务器：根据负载需求确定（建议至少3台）
   - 数据库服务器：主从架构（至少2台）
   - 缓存服务器：Redis集群（至少3台）

### 4.3 成本估算

1. **人力成本**：
   - 开发团队（10人）：约¥105万-¥125万（基于市场平均薪资，6个月）

2. **基础设施成本**：
   - 服务器租赁费用：约¥5万-¥8万（开发和测试期间）
   - 云服务费用：约¥3万-¥5万（包括对象存储、CDN等）
   - 第三方服务费用：约¥2万-¥3万（短信、支付接口等）

3. **软件许可成本**：
   - 开发工具和软件许可：约¥1万-¥2万

4. **其他成本**：
   - 培训费用：约¥1万-¥2万
   - 运维支持：约¥2万-¥3万

**总计成本估算：约¥119万-¥148万**

## 五、风险评估与应对策略

### 5.1 主要风险

1. **技术风险**：
   - 多租户架构设计不当导致数据隔离问题
   - 性能瓶颈，特别是在多租户并发访问时
   - 现有代码重构可能引入新的bug
   - 用户多租户关联机制设计不当导致认证问题

2. **业务风险**：
   - 租户特定需求理解不充分
   - 用户体验在不同租户间不一致
   - 业务规则复杂度超出预期
   - 用户在多租户间切换的体验不佳

3. **项目管理风险**：
   - 需求变更频繁
   - 开发周期延长
   - 资源分配不合理

### 5.2 应对策略

1. **技术风险应对**：
   - 采用成熟的多租户设计模式
   - 进行全面的性能测试和负载测试
   - 建立完善的自动化测试体系
   - 采用灰度发布策略，降低风险
   - 设计合理的用户租户关联模型和认证机制

2. **业务风险应对**：
   - 与各租户方进行充分沟通
   - 建立UI/UX设计规范
   - 实现灵活的业务规则配置系统
   - 优化用户租户切换体验

3. **项目管理风险应对**：
   - 采用敏捷开发方法，快速响应变更
   - 建立明确的里程碑和交付计划
   - 合理分配资源，保持适当冗余

## 六、实施建议

### 6.1 分阶段实施策略

建议采用以下分阶段实施策略：

1. **第一阶段：基础架构改造**
   - 实现多租户架构
   - 改造核心数据模型
   - 实现基本的租户管理功能
   - 实现用户多租户关联机制

2. **第二阶段：林酒道馆app迁移**
   - 将现有林酒道馆app迁移到新架构
   - 验证核心功能正常运行
   - 收集用户反馈并优化

3. **第三阶段：一款好酒app开发**
   - 基于新架构开发一款好酒app
   - 实现租户特定的UI和业务规则
   - 进行全面测试与优化

4. **第四阶段：平台能力扩展**
   - 完善多租户管理平台
   - 优化运营和数据分析功能
   - 提升系统整体性能和可扩展性

### 6.2 关键成功因素

1. **明确的需求定义**：
   - 详细记录各租户的特定需求
   - 明确功能优先级和交付标准

2. **合理的架构设计**：
   - 确保架构设计支持未来扩展
   - 平衡灵活性和复杂性

3. **有效的沟通机制**：
   - 建立与各租户方的定期沟通机制
   - 及时解决需求变更和技术问题

4. **完善的测试策略**：
   - 建立自动化测试体系
   - 进行全面的兼容性和性能测试

5. **持续的优化改进**：
   - 收集用户反馈并持续优化
   - 定期进行代码审查和重构

## 七、总结与建议

基于对现有代码的分析和需求的理解，多租户统一订货app的开发是可行的，但需要进行较大规模的架构调整和代码重构。主要挑战在于多租户架构的设计和实现，用户多租户关联机制的设计，以及租户特定功能的灵活配置。

建议采取以下关键措施：

1. **深入需求分析**：与各租户方进行深入沟通，明确具体需求和优先级。

2. **架构先行**：优先完成多租户架构设计和实现，为后续开发奠定基础。

3. **渐进式实施**：采用分阶段实施策略，降低风险，快速获取反馈。

4. **重视测试**：建立完善的测试体系，确保系统稳定性和性能。

5. **灵活配置**：实现灵活的配置系统，支持租户特定的业务规则和UI。

6. **用户体验优先**：优化用户多租户切换体验，确保操作简单直观。

通过合理的规划和实施，相信可以在预期的时间和成本范围内完成多租户统一订货app的开发，为企业提供强大的多租户管理平台。
