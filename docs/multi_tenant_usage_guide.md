# 多租户系统使用指南

## 概述

本系统实现了完整的多租户架构，支持数据隔离、租户管理、用户租户关联等功能。

## 核心功能

### 1. 租户管理

#### 创建租户
```bash
POST /api/tenant/createTenant
Content-Type: application/json

{
  "name": "测试公司",
  "code": "test_company",
  "logo": "https://example.com/logo.png",
  "primaryColor": "#007bff",
  "secondaryColor": "#6c757d",
  "contactName": "张三",
  "contactPhone": "13800138000"
}
```

#### 获取租户列表
```bash
GET /api/tenant/getTenantList?page=1&pageSize=10
```

#### 更新租户信息
```bash
PUT /api/tenant/updateTenant
Content-Type: application/json

{
  "id": 1,
  "name": "更新后的公司名",
  "primaryColor": "#28a745"
}
```

### 2. 用户租户关联

#### 添加用户到租户
```bash
POST /api/tenant/addUserToTenant
Content-Type: application/json

{
  "userId": 1,
  "tenantId": 2,
  "role": "admin"
}
```

#### 获取用户关联的租户
```bash
GET /api/tenant/getUserTenants
```

#### 切换租户
```bash
POST /api/tenant/switchTenant/2
```

返回新的JWT令牌，包含租户信息。

### 3. 数据隔离

系统通过以下方式实现数据隔离：

1. **JWT令牌包含租户ID**：用户登录后，JWT令牌中包含当前租户ID
2. **中间件验证**：所有API请求都会通过租户中间件验证
3. **数据库查询过滤**：所有数据库操作都会自动添加租户ID过滤条件

#### 订单数据隔离示例

```go
// 创建订单时自动添加租户ID
func CreateOrder(orderReq *ordersReq.CreateOrderRequest, tenantID uint) {
    orderReq.TenantID = tenantID
    // ... 其他逻辑
}

// 查询订单时自动过滤租户
func GetOrderList(tenantID uint) {
    db.Where("tenant_id = ?", tenantID).Find(&orders)
}
```

## 使用流程

### 1. 系统初始化

系统启动时会自动创建默认租户：
- 租户名称：默认租户
- 租户编码：default
- 状态：启用

### 2. 创建新租户

管理员可以通过API创建新租户，每个租户都有独立的：
- 基本信息（名称、编码、联系方式）
- 应用配置（应用名称、Logo、主题色彩）
- 用户关联关系

### 3. 用户租户关联

- 用户可以属于多个租户
- 每个用户在每个租户中可以有不同的角色
- 用户可以设置默认租户
- 支持租户间切换

### 4. 数据访问

- 所有API请求都需要包含有效的JWT令牌
- 令牌中包含当前租户ID
- 系统自动根据租户ID过滤数据

## 中间件配置

### 租户验证中间件

```go
// 在需要租户隔离的路由组中使用
PrivateGroup.Use(middleware.TenantMiddleware())
```

中间件会：
1. 从JWT令牌中提取租户ID
2. 验证租户是否有效（未禁用、未过期）
3. 将租户ID设置到上下文中

### JWT令牌结构

```go
type CustomClaims struct {
    BaseClaims
    BufferTime int64
    TenantID   uint   `json:"tenantId"`  // 租户ID
    jwt.RegisteredClaims
}
```

## 数据库设计

### 租户表 (tenant)
- id: 租户ID
- name: 租户名称
- code: 租户编码（唯一）
- status: 状态（启用/禁用）
- expire_date: 到期时间

### 用户租户关联表 (user_tenant_relation)
- user_id: 用户ID
- tenant_id: 租户ID
- is_default: 是否默认租户
- role: 用户在该租户中的角色

### 业务表改造
所有需要租户隔离的业务表都添加了 `tenant_id` 字段：
- orders: 订单表
- products: 商品表
- franchisees: 加盟商表
- 等等...

## 最佳实践

### 1. 服务层设计

为需要租户隔离的服务创建带租户过滤的方法：

```go
// 原方法
func (s *OrderService) GetOrderList(info request.PageInfo) ([]Order, int64, error)

// 租户版本
func (s *OrderService) GetOrderListWithTenant(info request.PageInfo, tenantID uint) ([]Order, int64, error)
```

### 2. API设计

在控制器中获取租户ID：

```go
func (api *OrderApi) GetOrderList(c *gin.Context) {
    tenantID := utils.GetTenantID(c)
    if tenantID == 0 {
        response.FailWithMessage("租户信息无效", c)
        return
    }
    
    // 使用租户ID进行业务处理
    list, total, err := orderService.GetOrderListWithTenant(pageInfo, tenantID)
}
```

### 3. 错误处理

- 租户不存在：返回 "租户无效" 错误
- 租户已禁用：返回 "租户已禁用" 错误
- 租户已过期：返回 "租户已过期" 错误
- 用户无权限访问租户：返回 "无权限访问" 错误

## 测试

系统提供了完整的测试用例：

```bash
# 运行租户相关测试
go test ./test/tenant_test.go -v
```

测试覆盖：
- 租户CRUD操作
- 用户租户关联
- 租户验证逻辑
- 数据隔离验证

## 注意事项

1. **数据迁移**：现有数据需要分配到默认租户
2. **性能考虑**：所有查询都会增加租户ID条件，需要确保相关索引
3. **备份策略**：可以按租户进行数据备份
4. **监控告警**：建议对租户级别的资源使用进行监控

## 扩展功能

### 1. 租户配额管理
- 用户数量限制
- 存储空间限制
- API调用频率限制

### 2. 租户计费
- 按用户数计费
- 按存储空间计费
- 按功能模块计费

### 3. 租户自定义
- 自定义主题
- 自定义Logo
- 自定义功能模块