# 多租户系统使用指南

> **⚠️ 重要更新说明**：本指南已基于新的加盟商1:1关系设计进行重构。详细设计请参考 [`franchisee_multi_tenant_login_design.md`](./franchisee_multi_tenant_login_design.md)

## 概述

本系统实现了基于加盟商1:1关系的多租户架构，支持：
- ✅ **数据隔离**：完全透明的租户数据隔离
- ✅ **租户管理**：租户的创建、配置和管理
- ✅ **加盟商多租户登录**：一个用户可以在多个租户中拥有不同的加盟商身份
- ✅ **权限管理**：基于租户和加盟商的权限控制

## 🚀 快速启动

### **前提条件**
- MySQL 5.7+ 数据库
- Go 1.19+ 环境
- Redis（可选）

### **5分钟快速部署**

#### **第一步：数据库迁移**
```bash
# 1. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行迁移
mysql -u username -p database_name < sql/add_tenant_id_to_franchisee.sql

# 3. 验证迁移
mysql -u username -p database_name -e "DESCRIBE franchisee;" | grep tenant_id
```

#### **第二步：启动应用**
```bash
# 1. 编译应用
go build -o guanpu-server main.go

# 2. 启动服务
./guanpu-server

# 3. 验证启动
curl http://localhost:8888/health
```

#### **第三步：测试功能**
```bash
# 测试统一登录API
curl -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password",
    "captcha": "1234",
    "captchaId": "test"
  }'
```

## 核心功能

### 1. 租户管理

#### 创建租户
```bash
POST /api/tenant/createTenant
Content-Type: application/json
Authorization: Bearer <ADMIN_TOKEN>

{
  "name": "测试公司",
  "code": "test_company",
  "logo": "https://example.com/logo.png",
  "primaryColor": "#007bff",
  "secondaryColor": "#6c757d",
  "contactName": "张三",
  "contactPhone": "13800138000"
}
```

#### 获取租户列表
```bash
GET /api/tenant/getTenantList?page=1&pageSize=10
Authorization: Bearer <ADMIN_TOKEN>
```

#### 更新租户信息
```bash
PUT /api/tenant/updateTenant
Content-Type: application/json
Authorization: Bearer <ADMIN_TOKEN>

{
  "id": 1,
  "name": "更新后的公司名",
  "primaryColor": "#28a745"
}
```

### 2. 加盟商多租户登录

#### 加盟商登录（可能需要选择租户）
```bash
POST /app/auth/login
Content-Type: application/json

{
  "username": "user123",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "captcha-uuid"
}
```

**响应**：如果用户在多个租户中有加盟商身份，需要选择租户。

#### 确认租户登录
```bash
POST /app/auth/confirm-tenant
Content-Type: application/json
Authorization: Bearer <TEMP_TOKEN>

{
  "tenantId": 1
}
```

#### 获取我的租户列表
```bash
GET /app/auth/my-tenants
Authorization: Bearer <TOKEN>
```

#### 切换租户
```bash
POST /app/auth/switch-tenant
Content-Type: application/json
Authorization: Bearer <TOKEN>

{
  "tenantId": 2
}
```

返回新的JWT令牌，包含新租户和加盟商信息。

### 3. 数据隔离机制

系统通过以下方式实现完全透明的数据隔离：

1. **JWT令牌包含完整信息**：包含用户ID、租户ID、加盟商ID
2. **中间件验证**：验证用户在租户中的加盟商身份
3. **GORM插件自动过滤**：所有数据库操作自动添加租户ID过滤条件
4. **加盟商身份验证**：确保用户只能访问其加盟商身份对应的数据

#### 数据隔离示例

```go
// 创建订单时自动设置租户ID和加盟商ID
func CreateOrder(orderReq *ordersReq.CreateOrderRequest, claims *CustomClaims) {
    // GORM插件会自动设置 TenantID
    orderReq.FranchiseeID = claims.FranchiseeID
    // ... 其他逻辑
}

// 查询订单时自动过滤租户（GORM插件自动处理）
func GetOrderList() {
    // 无需手动添加租户过滤，GORM插件自动处理
    db.Find(&orders) // 自动添加 WHERE tenant_id = ?
}
```

## 使用流程

### 1. 系统初始化

系统启动时会自动：
- 创建默认租户（租户名称：默认租户，编码：default）
- 注册GORM租户插件
- 初始化租户相关表结构

### 2. 创建新租户

管理员可以通过API创建新租户，每个租户都有独立的：
- 基本信息（名称、编码、联系方式）
- 应用配置（应用名称、Logo、主题色彩）
- 加盟商数据隔离

### 3. 加盟商注册和关联

- 用户可以在多个租户中注册为加盟商
- 每个用户在每个租户中只能有一个加盟商身份（1:1关系）
- 加盟商信息包含租户特定的业务数据
- 支持租户间切换（切换加盟商身份）

### 4. 数据访问流程

1. **登录**：用户登录后获取可用的租户列表
2. **选择租户**：如果有多个租户，用户选择要登录的租户
3. **获取令牌**：系统生成包含租户和加盟商信息的JWT令牌
4. **数据访问**：所有API请求自动过滤到当前租户的数据

## 中间件配置

### 租户访问中间件

```go
// 加盟商应用路由（需要验证加盟商身份）
AppGroup.Use(middleware.TenantAccessMiddleware())

// 管理端路由（只需要租户隔离）
AdminGroup.Use(middleware.TenantIsolationMiddleware())
```

**TenantAccessMiddleware** 会：
1. 从JWT令牌中提取用户ID、租户ID、加盟商ID
2. 验证用户在当前租户中确实有加盟商身份
3. 验证租户是否有效（未禁用、未过期）
4. 设置租户上下文

### 新的JWT令牌结构

```go
type CustomClaims struct {
    BaseClaims
    BufferTime   int64
    UserID       uint   `json:"userId"`       // 用户ID
    TenantID     uint   `json:"tenantId"`     // 租户ID
    FranchiseeID uint   `json:"franchiseeId"` // 加盟商ID
    TenantCode   string `json:"tenantCode"`   // 租户编码
    jwt.RegisteredClaims
}
```

## 数据库设计

### 租户表 (tenant)
- id: 租户ID
- name: 租户名称
- code: 租户编码（唯一）
- status: 状态（启用/禁用）
- expire_date: 到期时间

### ❌ 已废弃：用户租户关联表 (user_tenant_relation)
> **废弃原因**：新设计中用户通过加盟商身份直接关联租户，无需中间关联表。

### 加盟商表 (franchisees) - 核心关联表
- id: 加盟商ID
- tenant_id: 租户ID（直接关联）
- user_id: 用户ID
- code: 加盟商编码（租户内唯一）
- name: 加盟商名称
- 复合唯一索引：(tenant_id, user_id)、(tenant_id, code)

### 业务表改造
所有需要租户隔离的业务表都添加了 `tenant_id` 字段和索引：
- orders: 订单表
- products: 商品表
- order_goods: 订单商品表
- 等等...

## 最佳实践

### 1. 服务层设计（GORM插件自动处理）

**新设计**：无需手动处理租户过滤，GORM插件自动处理

```go
// 推荐：直接使用原方法，GORM插件自动添加租户过滤
func (s *OrderService) GetOrderList(info request.PageInfo) ([]Order, int64, error) {
    // GORM插件会自动添加 WHERE tenant_id = ? 条件
    return s.db.Find(&orders).Error
}

// 不推荐：手动添加租户过滤（除非有特殊需求）
func (s *OrderService) GetOrderListWithTenant(info request.PageInfo, tenantID uint) ([]Order, int64, error)
```

### 2. API设计

在控制器中获取租户和加盟商信息：

```go
func (api *OrderApi) GetOrderList(c *gin.Context) {
    // 中间件已经验证了租户和加盟商身份
    tenantID := utils.GetTenantID(c)
    franchiseeID := utils.GetFranchiseeID(c)

    // 直接调用服务方法，GORM插件自动处理租户过滤
    list, total, err := orderService.GetOrderList(pageInfo)

    // 如果需要加盟商级别的过滤
    if needFranchiseeFilter {
        list, total, err = orderService.GetOrderListByFranchisee(pageInfo, franchiseeID)
    }
}
```

### 3. 错误处理

- 租户不存在：返回 "租户无效" 错误
- 租户已禁用：返回 "租户已禁用" 错误
- 租户已过期：返回 "租户已过期" 错误
- 用户无加盟商身份：返回 "无权访问该租户数据" 错误
- 加盟商身份验证失败：返回 "加盟商身份无效" 错误

## 测试

系统提供了完整的测试用例：

```bash
# 运行多租户相关测试
go test ./test/tenant_test.go -v
go test ./test/franchisee_test.go -v
```

测试覆盖：
- 租户CRUD操作
- 加盟商多租户登录流程
- 租户切换功能
- 数据隔离验证
- GORM插件功能测试

## 注意事项

1. **数据迁移**：现有数据需要分配到默认租户，现有加盟商需要关联到对应租户
2. **性能考虑**：GORM插件自动添加租户过滤，确保 `tenant_id` 字段有索引
3. **备份策略**：可以按租户进行数据备份和恢复
4. **监控告警**：建议对租户级别的资源使用进行监控
5. **加盟商身份**：确保用户在租户中的加盟商身份数据完整性

## 扩展功能

### 1. 租户配额管理
- 加盟商数量限制
- 存储空间限制
- API调用频率限制

### 2. 租户计费
- 按加盟商数量计费
- 按数据使用量计费
- 按功能模块计费

---

> **📚 相关文档**：
> - 详细技术设计：[`multi_tenant_detailed_design.md`](./multi_tenant_detailed_design.md)
> - 登录设计方案：[`franchisee_multi_tenant_login_design.md`](./franchisee_multi_tenant_login_design.md)
> - API接口文档：[`multi_tenant_api_documentation.md`](./multi_tenant_api_documentation.md)
- 按用户数计费
- 按存储空间计费
- 按功能模块计费

### 3. 租户自定义
- 自定义主题
- 自定义Logo
- 自定义功能模块