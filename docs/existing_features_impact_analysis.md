# 现有功能多租户改造影响分析

## 📋 **文档概述**

本文档详细分析多租户功能对现有商城系统各个功能模块的具体影响，包括代码变更、数据库变更、API变更等技术层面的详细分析。

## 🔍 **现有功能模块详细影响分析**

### **1. 用户认证模块**

#### **影响的文件和功能**

| 文件路径 | 原有功能 | 需要变更的内容 | 变更复杂度 |
|---------|---------|---------------|-----------|
| `api/v1/system/sys_user.go` | 单一登录逻辑 | 增加租户识别和选择逻辑 | 🔴 高 |
| `service/system/sys_user.go` | 用户验证服务 | 增加租户关联验证 | 🟡 中 |
| `model/system/sys_user.go` | 用户数据模型 | 保持不变（通过关联表管理） | 🟢 低 |
| `middleware/jwt.go` | JWT中间件 | 增加租户信息验证 | 🟡 中 |

#### **具体变更内容**

**登录API变更**：
```go
// 原有登录逻辑
func Login(username, password string) (*User, error) {
    // 简单的用户名密码验证
}

// 多租户登录逻辑
func Login(username, password, tenantCode string) (*LoginResponse, error) {
    // 1. 用户名密码验证
    // 2. 检查是否为加盟商
    // 3. 获取租户关联
    // 4. 处理租户选择逻辑
    // 5. 生成包含租户信息的JWT
}
```

**JWT结构变更**：
```go
// 原有JWT Claims
type Claims struct {
    UserID      uint   `json:"userId"`
    Username    string `json:"username"`
    AuthorityId uint   `json:"authorityId"`
}

// 多租户JWT Claims
type Claims struct {
    UserID      uint   `json:"userId"`
    Username    string `json:"username"`
    AuthorityId uint   `json:"authorityId"`
    TenantID    uint   `json:"tenantId"`    // 新增
    UserType    int    `json:"userType"`    // 新增：区分用户类型
}
```

### **2. 加盟商管理模块**

#### **影响的文件和功能**

| 文件路径 | 原有功能 | 需要变更的内容 | 变更复杂度 |
|---------|---------|---------------|-----------|
| `api/v1/franchisees/franchisee.go` | 全局加盟商管理 | 增加租户过滤逻辑 | 🔴 高 |
| `service/franchisees/franchisee.go` | 加盟商业务逻辑 | 所有查询增加租户条件 | 🔴 高 |
| `model/franchisees/franchisee.go` | 加盟商数据模型 | 移除直接的TenantID字段 | 🟡 中 |

#### **具体变更内容**

**API接口变更**：
```go
// 原有加盟商列表API
func GetFranchiseeList(c *gin.Context) {
    // 返回所有加盟商
    franchisees := franchiseeService.GetAllFranchisees()
}

// 多租户加盟商列表API
func GetFranchiseeList(c *gin.Context) {
    // 从JWT获取租户ID
    tenantID := getTenantIDFromJWT(c)
    // 返回租户内的加盟商
    franchisees := franchiseeService.GetFranchiseesByTenant(tenantID)
}
```

**服务层变更**：
```go
// 原有查询逻辑
func GetAllFranchisees() []Franchisee {
    var franchisees []Franchisee
    db.Find(&franchisees)
    return franchisees
}

// 多租户查询逻辑
func GetFranchiseesByTenant(tenantID uint) []Franchisee {
    var franchisees []Franchisee
    db.Table("franchisee").
        Joins("INNER JOIN franchisee_tenant_relation ON franchisee.id = franchisee_tenant_relation.franchisee_id").
        Where("franchisee_tenant_relation.tenant_id = ? AND franchisee_tenant_relation.status = ?", 
              tenantID, "active").
        Find(&franchisees)
    return franchisees
}
```

### **3. 订单管理模块**

#### **影响的文件和功能**

| 文件路径 | 原有功能 | 需要变更的内容 | 变更复杂度 |
|---------|---------|---------------|-----------|
| `api/v1/orders/order.go` | 全局订单管理 | 增加租户过滤和验证 | 🔴 高 |
| `service/orders/order.go` | 订单业务逻辑 | 所有操作增加租户验证 | 🔴 高 |
| `model/orders/order.go` | 订单数据模型 | 需要添加TenantID字段 | 🔴 高 |

#### **具体变更内容**

**数据模型变更**：
```go
// 原有订单模型
type Order struct {
    global.GVA_MODEL
    OrderNo      string  `json:"orderNo"`
    FranchiseeID uint    `json:"franchiseeId"`
    Amount       float64 `json:"amount"`
    Status       string  `json:"status"`
}

// 多租户订单模型
type Order struct {
    global.GVA_MODEL
    TenantID     uint    `json:"tenantId" gorm:"column:tenant_id;not null;index"` // 新增
    OrderNo      string  `json:"orderNo"`
    FranchiseeID uint    `json:"franchiseeId"`
    Amount       float64 `json:"amount"`
    Status       string  `json:"status"`
}
```

**查询逻辑变更**：
```go
// 原有订单查询
func GetOrderList(pageInfo request.PageInfo) ([]Order, int64, error) {
    var orders []Order
    var total int64
    db.Model(&Order{}).Count(&total)
    db.Limit(pageInfo.PageSize).Offset(offset).Find(&orders)
    return orders, total, nil
}

// 多租户订单查询
func GetOrderList(tenantID uint, pageInfo request.PageInfo) ([]Order, int64, error) {
    var orders []Order
    var total int64
    db.Model(&Order{}).Where("tenant_id = ?", tenantID).Count(&total)
    db.Where("tenant_id = ?", tenantID).Limit(pageInfo.PageSize).Offset(offset).Find(&orders)
    return orders, total, nil
}
```

### **4. 商品管理模块**

#### **影响的文件和功能**

| 文件路径 | 原有功能 | 需要变更的内容 | 变更复杂度 |
|---------|---------|---------------|-----------|
| `api/v1/products/product.go` | 全局商品管理 | 增加租户隔离 | 🔴 高 |
| `service/products/product.go` | 商品业务逻辑 | 租户内商品管理 | 🔴 高 |
| `model/products/product.go` | 商品数据模型 | 添加TenantID字段 | 🔴 高 |
| `model/products/category.go` | 商品分类模型 | 添加TenantID字段 | 🔴 高 |

#### **具体变更内容**

**商品分类变更**：
```go
// 原有分类模型
type Category struct {
    global.GVA_MODEL
    Name     string `json:"name"`
    ParentID uint   `json:"parentId"`
    Sort     int    `json:"sort"`
}

// 多租户分类模型
type Category struct {
    global.GVA_MODEL
    TenantID uint   `json:"tenantId" gorm:"column:tenant_id;not null;index"` // 新增
    Name     string `json:"name"`
    ParentID uint   `json:"parentId"`
    Sort     int    `json:"sort"`
}
```

### **5. 财务管理模块**

#### **影响的文件和功能**

| 文件路径 | 原有功能 | 需要变更的内容 | 变更复杂度 |
|---------|---------|---------------|-----------|
| `api/v1/finance/finance.go` | 全局财务统计 | 租户维度财务统计 | 🔴 高 |
| `service/finance/finance.go` | 财务计算逻辑 | 租户内财务计算 | 🔴 高 |
| `model/finance/account.go` | 账户模型 | 添加TenantID字段 | 🔴 高 |

#### **具体变更内容**

**财务统计变更**：
```go
// 原有收入统计
func GetTotalRevenue() float64 {
    var total float64
    db.Model(&Order{}).Where("status = ?", "completed").Sum("amount", &total)
    return total
}

// 多租户收入统计
func GetTenantRevenue(tenantID uint) float64 {
    var total float64
    db.Model(&Order{}).Where("tenant_id = ? AND status = ?", tenantID, "completed").Sum("amount", &total)
    return total
}
```

## 📊 **数据库变更汇总**

### **需要添加TenantID字段的表**

| 表名 | 字段名 | 字段类型 | 索引 | 备注 |
|-----|-------|---------|------|------|
| `order` | `tenant_id` | `bigint unsigned` | 是 | 订单租户归属 |
| `product` | `tenant_id` | `bigint unsigned` | 是 | 商品租户归属 |
| `category` | `tenant_id` | `bigint unsigned` | 是 | 分类租户归属 |
| `coupon` | `tenant_id` | `bigint unsigned` | 是 | 优惠券租户归属 |
| `promotion` | `tenant_id` | `bigint unsigned` | 是 | 促销活动租户归属 |
| `customer_service` | `tenant_id` | `bigint unsigned` | 是 | 客服工单租户归属 |

### **新增的关联表**

| 表名 | 主要字段 | 用途 |
|-----|---------|------|
| `franchisee_tenant_relation` | `franchisee_id`, `tenant_id`, `role`, `status` | 管理加盟商租户关系 |
| `tenant_config` | `tenant_id`, `config_key`, `config_value` | 租户配置信息 |

### **数据迁移SQL示例**

```sql
-- 为现有表添加tenant_id字段
ALTER TABLE `order` ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 AFTER `id`;
ALTER TABLE `product` ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 AFTER `id`;
ALTER TABLE `category` ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 AFTER `id`;

-- 添加索引
CREATE INDEX `idx_order_tenant_id` ON `order` (`tenant_id`);
CREATE INDEX `idx_product_tenant_id` ON `product` (`tenant_id`);
CREATE INDEX `idx_category_tenant_id` ON `category` (`tenant_id`);

-- 为现有数据设置默认租户
UPDATE `order` SET `tenant_id` = 1 WHERE `tenant_id` = 0;
UPDATE `product` SET `tenant_id` = 1 WHERE `tenant_id` = 0;
UPDATE `category` SET `tenant_id` = 1 WHERE `tenant_id` = 0;
```

## 🔧 **API接口变更汇总**

### **需要修改的API接口**

| 接口路径 | 原有功能 | 变更内容 | 兼容性 |
|---------|---------|---------|-------|
| `POST /base/login` | 简单登录 | 增加租户选择逻辑 | 向后兼容 |
| `GET /franchisee/list` | 全局列表 | 租户过滤列表 | 需要适配 |
| `GET /order/list` | 全局订单 | 租户订单 | 需要适配 |
| `GET /product/list` | 全局商品 | 租户商品 | 需要适配 |
| `GET /finance/statistics` | 全局统计 | 租户统计 | 需要适配 |

### **新增的API接口**

| 接口路径 | 功能描述 | 用户角色 |
|---------|---------|---------|
| `GET /tenant/list` | 获取租户列表 | 超级管理员 |
| `POST /tenant/create` | 创建租户 | 超级管理员 |
| `PUT /tenant/update` | 更新租户信息 | 租户管理员 |
| `POST /franchisee/addToTenant` | 添加加盟商到租户 | 租户管理员 |
| `DELETE /franchisee/removeFromTenant` | 从租户移除加盟商 | 租户管理员 |
| `PUT /franchisee/setDefaultTenant` | 设置默认租户 | 租户管理员 |

## 🎯 **关键技术挑战**

### **1. 数据一致性保证**

**挑战**：确保多租户数据迁移过程中的数据一致性
**解决方案**：
- 分步骤迁移，每步都有验证
- 使用事务确保原子性
- 提供回滚机制

### **2. 性能优化**

**挑战**：多租户查询可能影响系统性能
**解决方案**：
- 为tenant_id字段添加索引
- 优化查询语句，避免全表扫描
- 使用缓存减少数据库查询

### **3. 权限控制复杂性**

**挑战**：多层级权限控制的复杂性
**解决方案**：
- 设计清晰的权限模型
- 使用中间件统一权限验证
- 提供权限测试工具

### **4. 数据隔离安全性**

**挑战**：确保租户间数据完全隔离
**解决方案**：
- 在ORM层面强制租户过滤
- 定期进行安全审计
- 实施数据访问日志

## 📋 **开发检查清单**

### **代码变更检查**

- [ ] 所有查询都增加了租户过滤条件
- [ ] JWT中间件正确验证租户信息
- [ ] API接口都有租户权限验证
- [ ] 错误处理包含租户相关错误
- [ ] 日志记录包含租户信息

### **数据库变更检查**

- [ ] 所有需要的表都添加了tenant_id字段
- [ ] 相关索引都已创建
- [ ] 外键约束正确设置
- [ ] 数据迁移脚本经过测试
- [ ] 回滚脚本准备就绪

### **测试覆盖检查**

- [ ] 单元测试覆盖租户隔离逻辑
- [ ] 集成测试验证跨租户访问被阻止
- [ ] 性能测试确保多租户不影响性能
- [ ] 安全测试验证数据隔离
- [ ] 用户体验测试确保流程顺畅

---

**文档版本**: v1.0  
**创建日期**: 2024-01-01  
**维护团队**: 技术团队  
**审核状态**: 待审核
