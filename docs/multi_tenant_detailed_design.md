# 多租户统一订货App详细设计文档

## 一、架构设计
### 1.1 租户模型设计
```go
type Tenant struct {
	global.GVA_MODEL
	Name          string `json:"name" gorm:"column:name;comment:租户名称;size:56;"` // 租户名称
	Code          string `json:"code" gorm:"column:code;comment:租户编码;size:56;unique;"` // 租户编码
	Logo          string `json:"logo" gorm:"column:logo;comment:租户logo;size:255;"` // 租户logo
	PrimaryColor  string `json:"primaryColor" gorm:"column:primary_color;comment:主色调;size:20;"` // 主色调
	SecondaryColor string `json:"secondaryColor" gorm:"column:secondary_color;comment:次色调;size:20;"` // 次色调
	AppConfig     *TenantAppConfig `json:"appConfig" gorm:"foreignKey:TenantID;references:ID;"` // 应用配置
	Status        *bool  `json:"status" gorm:"column:status;comment:状态(启用/禁用);"` // 状态
	ContactName   string `json:"contactName" gorm:"column:contact_name;comment:联系人;size:50;"` // 联系人
	ContactPhone  string `json:"contactPhone" gorm:"column:contact_phone;comment:联系电话;size:20;"` // 联系电话
	ExpireDate    *time.Time `json:"expireDate" gorm:"column:expire_date;comment:租约到期日期;"` // 租约到期
}

type TenantAppConfig struct {
	global.GVA_MODEL
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;"` // 租户ID
	AppName       string `json:"appName" gorm:"column:app_name;comment:应用名称;size:100;"` // 应用名称
	AppLogo       string `json:"appLogo" gorm:"column:app_logo;comment:应用logo;size:255;"` // 应用logo
	LoginBgImage  string `json:"loginBgImage" gorm:"column:login_bg_image;comment:登录背景图;size:255;"` // 登录背景
	HomePageConfig string `json:"homePageConfig" gorm:"column:home_page_config;comment:首页配置;type:json;"` // 首页配置
}
```

### 1.2 加盟商租户关联模型
```go
// 加盟商模型直接关联租户（1:1关系）
type Franchisee struct {
	global.GVA_MODEL
	TenantID    uint   `json:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID"`
	UserID      uint   `json:"userId" gorm:"column:user_id;not null;index;comment:用户ID"`
	Code        string `json:"code" gorm:"column:code;not null;comment:加盟商编码"`
	Name        string `json:"name" gorm:"column:name;not null;comment:加盟商名称"`
	// 所有业务字段都按租户隔离
	Balance     int    `json:"balance" gorm:"column:balance;comment:余额"`
	Points      int    `json:"points" gorm:"column:points;comment:积分"`
	// ... 其他字段

	// 复合唯一索引
	// UNIQUE KEY `uk_tenant_user` (`tenant_id`, `user_id`)
	// UNIQUE KEY `uk_tenant_code` (`tenant_id`, `code`)
}

// 用户租户偏好表（可选）
type UserTenantPreference struct {
	global.GVA_MODEL
	UserID            uint      `json:"userId" gorm:"column:user_id;not null;unique;comment:用户ID"`
	DefaultTenantID   *uint     `json:"defaultTenantId" gorm:"column:default_tenant_id;comment:默认租户ID"`
	LastLoginTenantID *uint     `json:"lastLoginTenantId" gorm:"column:last_login_tenant_id;comment:最后登录租户ID"`
	LastLoginTime     *time.Time `json:"lastLoginTime" gorm:"column:last_login_time;comment:最后登录时间"`
}
```

## 二、数据库改造
### 2.1 核心模型扩展
```go
// Product模型改造
type Product struct {
	global.GVA_MODEL
	Name          string `json:"name" gorm:"column:name;comment:商品名称;size:255;"` // 商品名称
	Code          string `json:"code" gorm:"column:code;comment:商品编码;size:50;"` // 商品编码
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;size:10;"` // 租户ID
	SpecialMallID int   `json:"specialMallId" gorm:"column:special_mall_id;comment:专区ID;size:10;"` // 专区ID
	// ...其他字段保持不变
}

// Order模型改造
type Order struct {
	global.GVA_MODEL
	OrderNo       string `json:"orderNo" gorm:"column:order_no;comment:订单号;size:36;"` // 订单号
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;size:10;"` // 租户ID
	FranchiseeId int   `json:"franchiseeId" gorm:"column:franchisee_id;comment:加盟商ID;size:10;"` // 加盟商ID
	// ...其他字段保持不变
}
```

### 2.2 数据隔离策略
| 模块 | 隔离方式 | 共享方式 |
|------|----------|----------|
| 商品管理 | 按TenantID隔离 | 通用商品分类 |
| 订单管理 | 按TenantID隔离 | 跨租户报表 |
| 加盟商管理 | 按TenantID隔离 | 跨租户审批 |
| 财务管理 | 按TenantID隔离 | 跨租户结算 |

## 三、服务层改造
### 3.1 租户中间件
```go
func TenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get("claims")
		if !exists {
			response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
			c.Abort()
			return
		}
		
		customClaims, ok := claims.(*CustomClaims)
		if !ok {
			response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
			c.Abort()
			return
		}
		
		// 验证租户有效性
		if !Tenant.ValidateTenant(customClaims.TenantID) {
			response.FailWithMessage("租户无效", c)
			c.Abort()
			return
		}
		
		// 设置租户上下文
		c.Set("tenantId", customClaims.TenantID)
		c.Next()
	}
}
```

### 3.2 缓存策略
```go
// 改造前
func GetProductCache(key string) *Product {
	return cache.Get(key)
}

// 改造后
func GetProductCache(tenantID uint, productID uint) *Product {
	return cache.Get(fmt.Sprintf("tenant:%d:product:%d", tenantID, productID))
}
```

## 四、API接口调整
### 4.1 认证流程改造
```go
// 登录接口响应结构
type LoginResponse struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
	TenantID     uint   `json:"tenantId"` // 当前租户ID
	DefaultTenant uint  `json:"defaultTenant"` // 默认租户
	AvailableTenants []TenantInfo `json:"availableTenants"` // 可用租户列表
}

// 租户切换接口
func SwitchTenant(c *gin.Context) {
	var req SwitchTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	
	// 生成包含新租户信息的JWT
	newToken, err := utils.GenerateTenantToken(req.UserID, req.TenantID)
	if err != nil {
		response.FailWithMessage("租户切换失败", c)
		return
	}
	
	response.SuccessWithToken(newToken, c)
}
```

## 五、关键改造点
### 5.1 数据模型改造
1. 在所有核心模型中添加TenantID字段
2. 建立Tenant与Franchisee的关联关系
3. 改造现有查询逻辑，添加租户过滤

### 5.2 业务逻辑改造
1. 重构商品规则引擎，支持租户特定规则
2. 改造支付流程，支持租户级支付配置
3. 实现租户级数据统计与报表

## 六、风险与应对
### 6.1 数据迁移风险
- **应对方案**：分阶段迁移，先迁移基础数据，再迁移业务数据

### 6.2 性能风险
- **应对方案**：建立租户级索引，优化查询逻辑

### 6.3 安全风险
- **应对方案**：强化租户隔离，实施严格的权限控制

## 七、开发计划
| 阶段 | 时间 | 交付物 |
|------|------|--------|
| 架构设计 | 2周 | 系统架构文档 |
| 核心改造 | 4周 | 租户模型、数据隔离 |
| 业务迁移 | 6周 | 多租户商品/订单系统 |
| 测试优化 | 3周 | 自动化测试套件 |
| 上线部署 | 1周 | 生产环境配置 |

## 八、成本估算
- **人力成本**：¥85万-¥100万
- **基础设施**：¥8万-¥12万
- **其他成本**：¥3万-¥5万