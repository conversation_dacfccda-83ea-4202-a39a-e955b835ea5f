# 多租户统一订货App详细设计文档

## 一、架构设计
### 1.1 租户模型设计
```go
type Tenant struct {
	global.GVA_MODEL
	Name          string `json:"name" gorm:"column:name;comment:租户名称;size:56;"` // 租户名称
	Code          string `json:"code" gorm:"column:code;comment:租户编码;size:56;unique;"` // 租户编码
	Logo          string `json:"logo" gorm:"column:logo;comment:租户logo;size:255;"` // 租户logo
	PrimaryColor  string `json:"primaryColor" gorm:"column:primary_color;comment:主色调;size:20;"` // 主色调
	SecondaryColor string `json:"secondaryColor" gorm:"column:secondary_color;comment:次色调;size:20;"` // 次色调
	AppConfig     *TenantAppConfig `json:"appConfig" gorm:"foreignKey:TenantID;references:ID;"` // 应用配置
	Status        *bool  `json:"status" gorm:"column:status;comment:状态(启用/禁用);"` // 状态
	ContactName   string `json:"contactName" gorm:"column:contact_name;comment:联系人;size:50;"` // 联系人
	ContactPhone  string `json:"contactPhone" gorm:"column:contact_phone;comment:联系电话;size:20;"` // 联系电话
	ExpireDate    *time.Time `json:"expireDate" gorm:"column:expire_date;comment:租约到期日期;"` // 租约到期
}

type TenantAppConfig struct {
	global.GVA_MODEL
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;"` // 租户ID
	AppName       string `json:"appName" gorm:"column:app_name;comment:应用名称;size:100;"` // 应用名称
	AppLogo       string `json:"appLogo" gorm:"column:app_logo;comment:应用logo;size:255;"` // 应用logo
	LoginBgImage  string `json:"loginBgImage" gorm:"column:login_bg_image;comment:登录背景图;size:255;"` // 登录背景
	HomePageConfig string `json:"homePageConfig" gorm:"column:home_page_config;comment:首页配置;type:json;"` // 首页配置
}
```

### 1.2 租户核心模型
```go
// 租户基本信息模型
type Tenant struct {
    global.GVA_MODEL
    Name          string `json:"name" gorm:"column:name;comment:租户名称;size:56;"`
    Code          string `json:"code" gorm:"column:code;comment:租户编码;size:56;unique;"`
    Logo          string `json:"logo" gorm:"column:logo;comment:租户logo;size:255;"`
    PrimaryColor  string `json:"primaryColor" gorm:"column:primary_color;comment:主色调;size:20;"`
    SecondaryColor string `json:"secondaryColor" gorm:"column:secondary_color;comment:次色调;size:20;"`
    AppConfig     *TenantAppConfig `json:"appConfig" gorm:"foreignKey:TenantID;references:ID;"`
    Status        *bool  `json:"status" gorm:"column:status;comment:状态(启用/禁用);"`
    ContactName   string `json:"contactName" gorm:"column:contact_name;comment:联系人;size:50;"`
    ContactPhone  string `json:"contactPhone" gorm:"column:contact_phone;comment:联系电话;size:20;"`
    ExpireDate    *time.Time `json:"expireDate" gorm:"column:expire_date;comment:租约到期日期;"`
}

// 租户应用配置模型
type TenantAppConfig struct {
    global.GVA_MODEL
    TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;"`
    AppName       string `json:"appName" gorm:"column:app_name;comment:应用名称;size:100;"`
    AppLogo       string `json:"appLogo" gorm:"column:app_logo;comment:应用logo;size:255;"`
    LoginBgImage  string `json:"loginBgImage" gorm:"column:login_bg_image;comment:登录背景图;size:255;"`
    HomePageConfig string `json:"homePageConfig" gorm:"column:home_page_config;comment:首页配置;type:json;"`
}
```

### 1.3 加盟商租户关联模型
```go
// 加盟商模型直接关联租户（1:1关系）
type Franchisee struct {
	global.GVA_MODEL
	TenantID    uint   `json:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID"`
	UserID      uint   `json:"userId" gorm:"column:user_id;not null;index;comment:用户ID"`
	Code        string `json:"code" gorm:"column:code;not null;comment:加盟商编码"`
	Name        string `json:"name" gorm:"column:name;not null;comment:加盟商名称"`
	// 所有业务字段都按租户隔离
	FCategoryId *int   `json:"fCategoryId" gorm:"column:f_category_id;comment:加盟商分类ID"`
	Linkman     string `json:"linkman" gorm:"column:linkman;comment:联系人"`
	Balance     int    `json:"balance" gorm:"column:balance;comment:余额"`
	Points      int    `json:"points" gorm:"column:points;comment:积分"`
	InviterID   int    `json:"inviterId" gorm:"column:inviter_id;comment:邀请人ID"`
	MarketLeadID int   `json:"marketLeadId" gorm:"column:market_lead_id;comment:市场负责人ID"`
	SupervisionLeadID int `json:"supervisionLeadId" gorm:"column:supervision_lead_id;comment:监管负责人ID"`
	// ... 其他字段

	// 复合唯一索引
	// UNIQUE KEY `uk_tenant_user` (`tenant_id`, `user_id`)
	// UNIQUE KEY `uk_tenant_code` (`tenant_id`, `code`)
}

// 用户租户偏好表（可选）
type UserTenantPreference struct {
	global.GVA_MODEL
	UserID            uint      `json:"userId" gorm:"column:user_id;not null;unique;comment:用户ID"`
	DefaultTenantID   *uint     `json:"defaultTenantId" gorm:"column:default_tenant_id;comment:默认租户ID"`
	LastLoginTenantID *uint     `json:"lastLoginTenantId" gorm:"column:last_login_tenant_id;comment:最后登录租户ID"`
	LastLoginTime     *time.Time `json:"lastLoginTime" gorm:"column:last_login_time;comment:最后登录时间"`
}
```

### 1.4 JWT声明扩展
```go
// 扩展的JWT声明结构（适配新的加盟商模型）
type CustomClaims struct {
    BaseClaims
    BufferTime   int64
    jwt.RegisteredClaims
    UserID       uint   `json:"userId"`       // 用户ID
    TenantID     uint   `json:"tenantId"`     // 当前租户ID
    FranchiseeID uint   `json:"franchiseeId"` // 当前加盟商ID
    TenantCode   string `json:"tenantCode"`   // 租户编码
}

// 扩展的基础声明
type BaseClaims struct {
    UUID           uuid.UUID
    ID             uint
    Username       string
    NickName       string
    AuthorityId    uint
    UserType       UserType `json:"userType"`       // 用户类型
    IsSuperAdmin   bool     `json:"isSuperAdmin"`   // 是否为超级管理员
    ManagedTenants []uint   `json:"managedTenants,omitempty"` // 管理的租户列表
}

// 用户类型定义
type UserType int
const (
    UserTypeNormal      UserType = 0 // 普通用户（加盟商）
    UserTypeTenantAdmin UserType = 1 // 租户管理员
    UserTypeSystemAdmin UserType = 2 // 系统管理员
    UserTypeSuperAdmin  UserType = 3 // 超级管理员
)
```

## 二、数据库改造
### 2.1 核心模型扩展
```go
// Product模型改造
type Product struct {
	global.GVA_MODEL
	Name          string `json:"name" gorm:"column:name;comment:商品名称;size:255;"` // 商品名称
	Code          string `json:"code" gorm:"column:code;comment:商品编码;size:50;"` // 商品编码
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;size:10;"` // 租户ID
	SpecialMallID int   `json:"specialMallId" gorm:"column:special_mall_id;comment:专区ID;size:10;"` // 专区ID
	// ...其他字段保持不变
}

// Order模型改造
type Order struct {
	global.GVA_MODEL
	OrderNo       string `json:"orderNo" gorm:"column:order_no;comment:订单号;size:36;"` // 订单号
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;size:10;"` // 租户ID
	FranchiseeId int   `json:"franchiseeId" gorm:"column:franchisee_id;comment:加盟商ID;size:10;"` // 加盟商ID
	// ...其他字段保持不变
}
```

### 2.2 数据隔离策略
| 模块 | 隔离方式 | 共享方式 |
|------|----------|----------|
| 商品管理 | 按TenantID隔离 | 通用商品分类 |
| 订单管理 | 按TenantID隔离 | 跨租户报表 |
| 加盟商管理 | 按TenantID隔离 | 跨租户审批 |
| 财务管理 | 按TenantID隔离 | 跨租户结算 |

## 三、服务层改造
### 3.1 租户中间件
```go
func TenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get("claims")
		if !exists {
			response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
			c.Abort()
			return
		}
		
		customClaims, ok := claims.(*CustomClaims)
		if !ok {
			response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
			c.Abort()
			return
		}
		
		// 验证租户有效性
		if !Tenant.ValidateTenant(customClaims.TenantID) {
			response.FailWithMessage("租户无效", c)
			c.Abort()
			return
		}
		
		// 设置租户上下文
		c.Set("tenantId", customClaims.TenantID)
		c.Next()
	}
}
```

### 3.2 缓存策略
```go
// 改造前
func GetProductCache(key string) *Product {
	return cache.Get(key)
}

// 改造后
func GetProductCache(tenantID uint, productID uint) *Product {
	return cache.Get(fmt.Sprintf("tenant:%d:product:%d", tenantID, productID))
}
```

## 四、API接口调整
### 4.1 认证流程改造
```go
// 登录接口响应结构
type LoginResponse struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
	TenantID     uint   `json:"tenantId"` // 当前租户ID
	DefaultTenant uint  `json:"defaultTenant"` // 默认租户
	AvailableTenants []TenantInfo `json:"availableTenants"` // 可用租户列表
}

// 租户切换接口
func SwitchTenant(c *gin.Context) {
	var req SwitchTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	
	// 生成包含新租户信息的JWT
	newToken, err := utils.GenerateTenantToken(req.UserID, req.TenantID)
	if err != nil {
		response.FailWithMessage("租户切换失败", c)
		return
	}
	
	response.SuccessWithToken(newToken, c)
}
```

## 四、核心技术组件设计

### 4.1 全局租户上下文管理
**文件位置**：`global/tenant_context.go`

```go
// 租户上下文管理器
type TenantContext struct {
    tenantID uint
    mu       sync.RWMutex
}

// 核心功能函数
func SetCurrentTenantID(tenantID uint)     // 设置当前租户ID
func GetCurrentTenantID() uint             // 获取当前租户ID
func WithTenantContext(ctx context.Context, tenantID uint) context.Context // 创建租户上下文
func GetTenantFromContext(ctx context.Context) (uint, bool) // 从上下文获取租户ID

// GORM作用域
func TenantScope(tenantID uint) func(db *gorm.DB) *gorm.DB // 租户作用域
func AutoTenantScope() func(db *gorm.DB) *gorm.DB          // 自动租户作用域

// 表检查
func IsTenantTable(tableName string) bool // 检查表是否需要租户隔离
```

**关键特性**：
- 线程安全的租户上下文管理
- 支持GORM作用域自动应用
- 智能识别需要租户隔离的表
- 支持上下文传递

### 4.2 GORM租户插件
**文件位置**：`plugin/tenant/tenant_plugin.go`

```go
// 租户插件结构
type TenantPlugin struct{}

// 核心回调函数
func (tp *TenantPlugin) beforeQuery(db *gorm.DB)  // 查询前添加租户过滤
func (tp *TenantPlugin) beforeCreate(db *gorm.DB) // 创建前设置租户ID
func (tp *TenantPlugin) beforeUpdate(db *gorm.DB) // 更新前添加租户过滤
func (tp *TenantPlugin) beforeDelete(db *gorm.DB) // 删除前添加租户过滤

// 辅助函数
func (tp *TenantPlugin) shouldSkipTenant(db *gorm.DB) bool // 检查是否跳过租户处理
func (tp *TenantPlugin) getTableName(db *gorm.DB) string   // 获取表名
func (tp *TenantPlugin) setTenantID(db *gorm.DB, tenantID uint) // 设置租户ID

// 作用域函数
func SkipTenant(db *gorm.DB) *gorm.DB           // 跳过租户过滤
func SuperAdmin(db *gorm.DB) *gorm.DB           // 超级管理员作用域
func WithTenant(tenantID uint) func(db *gorm.DB) *gorm.DB // 指定租户作用域
```

**工作原理**：
1. **查询操作**：自动添加 `WHERE tenant_id = ?` 条件
2. **创建操作**：自动设置记录的 `tenant_id` 字段
3. **更新操作**：自动添加租户过滤条件
4. **删除操作**：自动添加租户过滤条件

**智能识别**：
- 根据表名判断是否需要租户隔离
- 支持超级管理员权限绕过
- 支持临时跳过租户过滤

### 4.3 超级管理员体系设计

#### 4.3.1 超级管理员操作日志
**文件位置**：`model/system/super_admin_log.go`

```go
// 超级管理员操作日志模型
type SuperAdminOperationLog struct {
    global.GVA_MODEL
    UserID        uint            `json:"userId" gorm:"column:user_id;comment:操作用户ID;not null;index"`
    OperationType string          `json:"operationType" gorm:"column:operation_type;comment:操作类型;size:50;not null;index"`
    TargetType    string          `json:"targetType" gorm:"column:target_type;comment:目标类型;size:50;not null"`
    TargetID      *uint           `json:"targetId" gorm:"column:target_id;comment:目标ID"`
    TenantID      *uint           `json:"tenantId" gorm:"column:tenant_id;comment:涉及的租户ID;index"`
    OperationDesc string          `json:"operationDesc" gorm:"column:operation_desc;comment:操作描述;type:text"`
    RequestData   json.RawMessage `json:"requestData" gorm:"column:request_data;comment:请求数据;type:json"`
    ResponseData  json.RawMessage `json:"responseData" gorm:"column:response_data;comment:响应数据;type:json"`
    IPAddress     string          `json:"ipAddress" gorm:"column:ip_address;comment:IP地址;size:45"`
    UserAgent     string          `json:"userAgent" gorm:"column:user_agent;comment:用户代理;type:text"`
    Status        string          `json:"status" gorm:"column:status;comment:操作状态;size:20;default:success"`
    ErrorMessage  string          `json:"errorMessage" gorm:"column:error_message;comment:错误信息;type:text"`
    Duration      int64           `json:"duration" gorm:"column:duration;comment:操作耗时(毫秒)"`
    User          SysUser         `json:"user" gorm:"foreignKey:UserID;references:ID"`
}

// 操作类型常量
const (
    OpTypeTenantCreate    = "tenant_create"
    OpTypeTenantUpdate    = "tenant_update"
    OpTypeTenantDelete    = "tenant_delete"
    OpTypeUserCreate      = "user_create"
    OpTypeUserUpdate      = "user_update"
    OpTypeDataQuery       = "data_query"
    OpTypeFranchiseeQuery = "franchisee_query"
    // ... 更多操作类型
)
```

#### 4.3.2 超级管理员中间件
**文件位置**：`middleware/super_admin.go`

```go
// 超级管理员权限验证中间件
func SuperAdminMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims := utils.GetUserInfo(c)
        if claims == nil {
            response.FailWithMessage("未授权访问", c)
            c.Abort()
            return
        }

        // 检查是否为超级管理员
        if !claims.IsSuperAdmin && claims.UserType != systemReq.UserTypeSuperAdmin {
            response.FailWithMessage("需要超级管理员权限", c)
            c.Abort()
            return
        }

        // 设置超级管理员标志，跳过租户隔离
        c.Set("is_super_admin", true)
        c.Set("skip_tenant", true)
        global.SetCurrentTenantID(0)

        c.Next()
    }
}

// 超级管理员操作日志中间件
func SuperAdminLogMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        startTime := time.Now()

        // 记录请求数据
        var requestData json.RawMessage
        if c.Request.Method != "GET" {
            if body, err := c.GetRawData(); err == nil {
                requestData = body
                c.Request.Body = utils.NewReadCloser(body)
            }
        }

        // 创建响应写入器来捕获响应
        writer := &responseWriter{
            ResponseWriter: c.Writer,
            body:          make([]byte, 0),
        }
        c.Writer = writer

        c.Next()

        // 异步记录操作日志
        go func() {
            log := &system.SuperAdminOperationLog{
                UserID:        claims.BaseClaims.ID,
                OperationType: getOperationType(c.Request.Method, c.FullPath()),
                TargetType:    getTargetType(c.FullPath()),
                OperationDesc: getOperationDesc(c.Request.Method, c.FullPath()),
                RequestData:   requestData,
                ResponseData:  writer.body,
                IPAddress:     c.ClientIP(),
                UserAgent:     c.Request.UserAgent(),
                Status:        getStatus(c.Writer.Status()),
                Duration:      time.Since(startTime).Milliseconds(),
            }
            system.CreateSuperAdminLog(log)
        }()
    }
}
```

**关键特性**：
- 严格的权限验证
- 自动跳过租户隔离
- 详细的操作日志记录
- 异步日志记录，不影响性能

### 4.4 租户中间件设计
**文件位置**：`middleware/tenant.go`

```go
// 租户验证中间件（适配新的加盟商模型）
func TenantAccessMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims, exists := c.Get("claims")
        if !exists {
            response.FailWithMessage("未授权", c)
            c.Abort()
            return
        }

        customClaims, ok := claims.(*systemReq.CustomClaims)
        if !ok {
            response.FailWithMessage("令牌格式错误", c)
            c.Abort()
            return
        }

        // 验证用户在当前租户中确实有加盟商身份
        var count int64
        global.GVA_DB.Model(&franchisees.Franchisee{}).
            Where("user_id = ? AND tenant_id = ?", customClaims.UserID, customClaims.TenantID).
            Count(&count)

        if count == 0 {
            response.FailWithMessage("无权访问该租户数据", c)
            c.Abort()
            return
        }

        // 验证租户有效性
        if !system.ValidateTenant(customClaims.TenantID) {
            global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", customClaims.TenantID))
            response.FailWithMessage("租户无效", c)
            c.Abort()
            return
        }

        // 设置租户上下文
        c.Set("tenant_id", customClaims.TenantID)
        c.Set("franchisee_id", customClaims.FranchiseeID)
        c.Set("user_id", customClaims.UserID)

        // 设置全局租户上下文
        global.SetCurrentTenantID(customClaims.TenantID)

        c.Next()

        // 请求结束后清理租户上下文
        defer func() {
            global.SetCurrentTenantID(0)
        }()
    }
}

// 租户隔离中间件（管理端使用）
func TenantIsolationMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims, exists := c.Get("claims")
        if !exists {
            response.FailWithMessage("未授权", c)
            c.Abort()
            return
        }

        customClaims, ok := claims.(*systemReq.CustomClaims)
        if !ok {
            response.FailWithMessage("令牌格式错误", c)
            c.Abort()
            return
        }

        // 验证租户有效性
        if !system.ValidateTenant(customClaims.TenantID) {
            response.FailWithMessage("租户无效", c)
            c.Abort()
            return
        }

        // 设置租户上下文
        c.Set("tenant_id", customClaims.TenantID)
        global.SetCurrentTenantID(customClaims.TenantID)

        c.Next()

        // 清理上下文
        defer func() {
            global.SetCurrentTenantID(0)
        }()
    }
}
```

**关键特性**：
- 自动验证租户有效性
- 验证用户在租户中的加盟商身份
- 设置全局租户上下文
- 请求结束后自动清理

### 4.5 工具函数设计
**文件位置**：`utils/claims.go`

```go
// 获取租户ID
func GetTenantID(c *gin.Context) uint {
    if claims, exists := c.Get("claims"); !exists {
        if cl, err := GetClaims(c); err != nil {
            return 0
        } else {
            return cl.TenantID
        }
    } else {
        waitUse := claims.(*systemReq.CustomClaims)
        return waitUse.TenantID
    }
}

// 获取加盟商ID
func GetFranchiseeID(c *gin.Context) uint {
    if claims, exists := c.Get("claims"); !exists {
        if cl, err := GetClaims(c); err != nil {
            return 0
        } else {
            return cl.FranchiseeID
        }
    } else {
        waitUse := claims.(*systemReq.CustomClaims)
        return waitUse.FranchiseeID
    }
}

// 生成包含租户和加盟商信息的JWT
func GenerateFranchiseeToken(user *system.SysUser, franchisee *franchisees.Franchisee) (string, error) {
    claims := systemReq.CustomClaims{
        BaseClaims: systemReq.BaseClaims{
            UUID:        user.UUID,
            ID:          user.ID,
            Username:    user.Username,
            NickName:    user.NickName,
            AuthorityId: user.AuthorityId,
            UserType:    systemReq.UserTypeNormal,
            IsSuperAdmin: false,
        },
        UserID:       user.ID,
        TenantID:     franchisee.TenantID,
        FranchiseeID: franchisee.ID,
        TenantCode:   franchisee.TenantCode, // 需要关联查询获取
        BufferTime:   global.GVA_CONFIG.JWT.BufferTime,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(global.GVA_CONFIG.JWT.ExpiresTime) * time.Second)),
            Issuer:    global.GVA_CONFIG.JWT.Issuer,
        },
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(global.GVA_CONFIG.JWT.SigningKey))
}
```

## 五、关键改造点
### 5.1 数据模型改造
1. 在所有核心模型中添加TenantID字段
2. 建立Tenant与Franchisee的关联关系
3. 改造现有查询逻辑，添加租户过滤

### 5.2 业务逻辑改造
1. 重构商品规则引擎，支持租户特定规则
2. 改造支付流程，支持租户级支付配置
3. 实现租户级数据统计与报表

## 六、风险与应对
### 6.1 数据迁移风险
- **应对方案**：分阶段迁移，先迁移基础数据，再迁移业务数据

### 6.2 性能风险
- **应对方案**：建立租户级索引，优化查询逻辑

### 6.3 安全风险
- **应对方案**：强化租户隔离，实施严格的权限控制

## 七、开发计划
| 阶段 | 时间 | 交付物 |
|------|------|--------|
| 架构设计 | 2周 | 系统架构文档 |
| 核心改造 | 4周 | 租户模型、数据隔离 |
| 业务迁移 | 6周 | 多租户商品/订单系统 |
| 测试优化 | 3周 | 自动化测试套件 |
| 上线部署 | 1周 | 生产环境配置 |

## 八、成本估算
- **人力成本**：¥85万-¥100万
- **基础设施**：¥8万-¥12万
- **其他成本**：¥3万-¥5万