# 多租户系统分布式部署分析与优化方案

## 🔍 分布式环境下的问题分析

### 当前实现的局限性

#### 1. Goroutine ID方案的问题
```go
// 当前实现 - 仅适用于单机环境
func getGoroutineID() int64 {
    buf := make([]byte, 64)
    buf = buf[:runtime.Stack(buf, false)]
    // ... 解析goroutine ID
}
```

**问题**：
- ❌ 只能在单个进程内工作
- ❌ 无法跨服务实例共享租户上下文
- ❌ 负载均衡后请求可能路由到不同实例
- ❌ 微服务调用链中租户信息丢失

#### 2. 内存缓存的局限性
```go
// 当前实现 - 仅本地缓存
var tenantCache = &TenantCache{
    cache: make(map[uint]*CachedTenant),
    ttl:   5 * time.Minute,
}
```

**问题**：
- ❌ 每个实例都有独立缓存
- ❌ 缓存不一致问题
- ❌ 内存浪费
- ❌ 缓存更新延迟

## 🏗️ 分布式优化方案

### 方案1：基于Redis的分布式租户上下文

#### 1.1 Redis租户上下文管理
```go
// global/distributed_tenant_context.go
package global

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "github.com/go-redis/redis/v8"
)

type DistributedTenantContext struct {
    redis  *redis.Client
    prefix string
    ttl    time.Duration
}

type TenantSession struct {
    TenantID    uint      `json:"tenant_id"`
    UserID      uint      `json:"user_id"`
    SessionID   string    `json:"session_id"`
    CreatedAt   time.Time `json:"created_at"`
    LastAccess  time.Time `json:"last_access"`
}

var distributedTenantCtx *DistributedTenantContext

// InitDistributedTenantContext 初始化分布式租户上下文
func InitDistributedTenantContext(redisClient *redis.Client) {
    distributedTenantCtx = &DistributedTenantContext{
        redis:  redisClient,
        prefix: "tenant:session:",
        ttl:    30 * time.Minute, // 30分钟会话超时
    }
}

// SetTenantSession 设置租户会话
func SetTenantSession(ctx context.Context, sessionID string, tenantID, userID uint) error {
    session := TenantSession{
        TenantID:   tenantID,
        UserID:     userID,
        SessionID:  sessionID,
        CreatedAt:  time.Now(),
        LastAccess: time.Now(),
    }
    
    data, err := json.Marshal(session)
    if err != nil {
        return err
    }
    
    key := distributedTenantCtx.prefix + sessionID
    return distributedTenantCtx.redis.Set(ctx, key, data, distributedTenantCtx.ttl).Err()
}

// GetTenantSession 获取租户会话
func GetTenantSession(ctx context.Context, sessionID string) (*TenantSession, error) {
    key := distributedTenantCtx.prefix + sessionID
    data, err := distributedTenantCtx.redis.Get(ctx, key).Result()
    if err != nil {
        return nil, err
    }
    
    var session TenantSession
    err = json.Unmarshal([]byte(data), &session)
    if err != nil {
        return nil, err
    }
    
    // 更新最后访问时间
    session.LastAccess = time.Now()
    updatedData, _ := json.Marshal(session)
    distributedTenantCtx.redis.Set(ctx, key, updatedData, distributedTenantCtx.ttl)
    
    return &session, nil
}

// ClearTenantSession 清理租户会话
func ClearTenantSession(ctx context.Context, sessionID string) error {
    key := distributedTenantCtx.prefix + sessionID
    return distributedTenantCtx.redis.Del(ctx, key).Err()
}
```

#### 1.2 分布式租户中间件
```go
// middleware/distributed_tenant.go
package middleware

import (
    "github.com/OSQianXing/guanpu-server/global"
    "github.com/OSQianXing/guanpu-server/model/common/response"
    systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
    "github.com/gin-gonic/gin"
    "github.com/google/uuid"
    "go.uber.org/zap"
)

// DistributedTenantMiddleware 分布式租户中间件
func DistributedTenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 生成或获取会话ID
        sessionID := c.GetHeader("X-Session-ID")
        if sessionID == "" {
            sessionID = uuid.New().String()
            c.Header("X-Session-ID", sessionID)
        }
        
        claims, exists := c.Get("claims")
        if !exists {
            response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
            c.Abort()
            return
        }
        
        customClaims, ok := claims.(*systemReq.CustomClaims)
        if !ok {
            response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
            c.Abort()
            return
        }
        
        // 验证租户有效性
        if !global.ValidateTenantDistributed(c.Request.Context(), customClaims.TenantID) {
            global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", customClaims.TenantID))
            response.FailWithMessage("租户无效", c)
            c.Abort()
            return
        }
        
        // 设置分布式租户会话
        err := global.SetTenantSession(c.Request.Context(), sessionID, customClaims.TenantID, customClaims.ID)
        if err != nil {
            global.GVA_LOG.Error("设置租户会话失败", zap.Error(err))
            response.FailWithMessage("系统错误", c)
            c.Abort()
            return
        }
        
        // 设置上下文
        c.Set("sessionId", sessionID)
        c.Set("tenantId", customClaims.TenantID)
        ctx := global.WithTenantContext(c.Request.Context(), customClaims.TenantID)
        ctx = global.WithSessionContext(ctx, sessionID)
        c.Request = c.Request.WithContext(ctx)
        
        // 请求结束后清理（可选，Redis会自动过期）
        defer func() {
            // 可以选择立即清理或让Redis自动过期
            // global.ClearTenantSession(c.Request.Context(), sessionID)
        }()
        
        c.Next()
    }
}
```

### 方案2：基于Redis的分布式缓存

#### 2.1 分布式租户缓存
```go
// global/distributed_tenant_cache.go
package global

import (
    "context"
    "encoding/json"
    "fmt"
    "time"
    
    "github.com/go-redis/redis/v8"
)

type DistributedTenantCache struct {
    redis      *redis.Client
    prefix     string
    ttl        time.Duration
    localCache *TenantCache // L1缓存
}

type CachedTenantInfo struct {
    TenantID    uint       `json:"tenant_id"`
    IsValid     bool       `json:"is_valid"`
    Status      *bool      `json:"status"`
    ExpireDate  *time.Time `json:"expire_date"`
    CachedAt    time.Time  `json:"cached_at"`
}

var distributedCache *DistributedTenantCache

// InitDistributedTenantCache 初始化分布式租户缓存
func InitDistributedTenantCache(redisClient *redis.Client) {
    distributedCache = &DistributedTenantCache{
        redis:      redisClient,
        prefix:     "tenant:cache:",
        ttl:        10 * time.Minute, // Redis缓存10分钟
        localCache: &TenantCache{     // 本地缓存2分钟
            cache: make(map[uint]*CachedTenant),
            ttl:   2 * time.Minute,
        },
    }
}

// ValidateTenantDistributed 分布式租户验证
func ValidateTenantDistributed(ctx context.Context, tenantID uint) bool {
    if tenantID == 0 {
        return false
    }
    
    // L1缓存：本地内存缓存（最快）
    if cached := distributedCache.getFromLocalCache(tenantID); cached != nil {
        return cached.IsValid
    }
    
    // L2缓存：Redis分布式缓存
    if cached := distributedCache.getFromRedisCache(ctx, tenantID); cached != nil {
        // 更新本地缓存
        distributedCache.setToLocalCache(tenantID, cached.IsValid)
        return cached.IsValid
    }
    
    // L3：数据库查询
    isValid := distributedCache.validateFromDatabase(ctx, tenantID)
    
    // 更新所有缓存层
    distributedCache.setToRedisCache(ctx, tenantID, isValid)
    distributedCache.setToLocalCache(tenantID, isValid)
    
    return isValid
}

func (dc *DistributedTenantCache) getFromLocalCache(tenantID uint) *CachedTenant {
    dc.localCache.mutex.RLock()
    defer dc.localCache.mutex.RUnlock()
    
    cached, exists := dc.localCache.cache[tenantID]
    if !exists || time.Since(cached.CachedAt) > dc.localCache.ttl {
        return nil
    }
    
    return cached
}

func (dc *DistributedTenantCache) getFromRedisCache(ctx context.Context, tenantID uint) *CachedTenantInfo {
    key := fmt.Sprintf("%s%d", dc.prefix, tenantID)
    data, err := dc.redis.Get(ctx, key).Result()
    if err != nil {
        return nil
    }
    
    var cached CachedTenantInfo
    err = json.Unmarshal([]byte(data), &cached)
    if err != nil {
        return nil
    }
    
    return &cached
}

func (dc *DistributedTenantCache) setToRedisCache(ctx context.Context, tenantID uint, isValid bool) {
    cached := CachedTenantInfo{
        TenantID: tenantID,
        IsValid:  isValid,
        CachedAt: time.Now(),
    }
    
    data, err := json.Marshal(cached)
    if err != nil {
        return
    }
    
    key := fmt.Sprintf("%s%d", dc.prefix, tenantID)
    dc.redis.Set(ctx, key, data, dc.ttl)
}

func (dc *DistributedTenantCache) setToLocalCache(tenantID uint, isValid bool) {
    dc.localCache.mutex.Lock()
    defer dc.localCache.mutex.Unlock()
    
    dc.localCache.cache[tenantID] = &CachedTenant{
        IsValid:  isValid,
        CachedAt: time.Now(),
    }
}

func (dc *DistributedTenantCache) validateFromDatabase(ctx context.Context, tenantID uint) bool {
    var count int64
    var status *bool
    var expireDate *time.Time
    
    err := GVA_DB.Table("tenants").
        Select("COUNT(*) as count, status, expire_date").
        Where("id = ?", tenantID).
        Row().Scan(&count, &status, &expireDate)
    
    if err != nil || count == 0 {
        return false
    }
    
    return (status == nil || *status) &&
           (expireDate == nil || expireDate.After(time.Now()))
}

// InvalidateTenantCacheDistributed 使分布式缓存失效
func InvalidateTenantCacheDistributed(ctx context.Context, tenantID uint) error {
    // 清理Redis缓存
    key := fmt.Sprintf("%s%d", distributedCache.prefix, tenantID)
    err := distributedCache.redis.Del(ctx, key).Err()
    
    // 清理本地缓存
    distributedCache.localCache.mutex.Lock()
    delete(distributedCache.localCache.cache, tenantID)
    distributedCache.localCache.mutex.Unlock()
    
    return err
}
```

### 方案3：微服务间租户传递

#### 3.1 HTTP头传递
```go
// utils/tenant_propagation.go
package utils

import (
    "context"
    "net/http"
    "strconv"
    
    "github.com/OSQianXing/guanpu-server/global"
)

const (
    TenantIDHeader  = "X-Tenant-ID"
    SessionIDHeader = "X-Session-ID"
    UserIDHeader    = "X-User-ID"
)

// InjectTenantHeaders 注入租户相关头信息
func InjectTenantHeaders(req *http.Request, ctx context.Context) {
    if tenantID, ok := global.GetTenantFromContext(ctx); ok {
        req.Header.Set(TenantIDHeader, strconv.FormatUint(uint64(tenantID), 10))
    }
    
    if sessionID, ok := global.GetSessionFromContext(ctx); ok {
        req.Header.Set(SessionIDHeader, sessionID)
    }
    
    if userID, ok := global.GetUserFromContext(ctx); ok {
        req.Header.Set(UserIDHeader, strconv.FormatUint(uint64(userID), 10))
    }
}

// ExtractTenantFromHeaders 从HTTP头提取租户信息
func ExtractTenantFromHeaders(req *http.Request) (tenantID uint, sessionID string, userID uint) {
    if tid := req.Header.Get(TenantIDHeader); tid != "" {
        if id, err := strconv.ParseUint(tid, 10, 32); err == nil {
            tenantID = uint(id)
        }
    }
    
    sessionID = req.Header.Get(SessionIDHeader)
    
    if uid := req.Header.Get(UserIDHeader); uid != "" {
        if id, err := strconv.ParseUint(uid, 10, 32); err == nil {
            userID = uint(id)
        }
    }
    
    return
}
```

#### 3.2 gRPC拦截器
```go
// interceptor/tenant_interceptor.go
package interceptor

import (
    "context"
    "strconv"
    
    "github.com/OSQianXing/guanpu-server/global"
    "google.golang.org/grpc"
    "google.golang.org/grpc/metadata"
)

// TenantUnaryInterceptor gRPC租户拦截器
func TenantUnaryInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
    // 从metadata提取租户信息
    if md, ok := metadata.FromIncomingContext(ctx); ok {
        if tenantIDs := md.Get("tenant-id"); len(tenantIDs) > 0 {
            if tenantID, err := strconv.ParseUint(tenantIDs[0], 10, 32); err == nil {
                ctx = global.WithTenantContext(ctx, uint(tenantID))
            }
        }
        
        if sessionIDs := md.Get("session-id"); len(sessionIDs) > 0 {
            ctx = global.WithSessionContext(ctx, sessionIDs[0])
        }
    }
    
    return handler(ctx, req)
}

// TenantStreamInterceptor gRPC流租户拦截器
func TenantStreamInterceptor(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
    ctx := ss.Context()
    
    // 从metadata提取租户信息
    if md, ok := metadata.FromIncomingContext(ctx); ok {
        if tenantIDs := md.Get("tenant-id"); len(tenantIDs) > 0 {
            if tenantID, err := strconv.ParseUint(tenantIDs[0], 10, 32); err == nil {
                ctx = global.WithTenantContext(ctx, uint(tenantID))
            }
        }
    }
    
    return handler(srv, &wrappedStream{ServerStream: ss, ctx: ctx})
}

type wrappedStream struct {
    grpc.ServerStream
    ctx context.Context
}

func (w *wrappedStream) Context() context.Context {
    return w.ctx
}
```

## 🔧 初始化配置

### Redis配置
```go
// initialize/redis_tenant.go
package initialize

import (
    "github.com/OSQianXing/guanpu-server/global"
    "github.com/go-redis/redis/v8"
)

func InitTenantRedis() {
    // 使用现有的Redis连接或创建新的
    rdb := redis.NewClient(&redis.Options{
        Addr:     global.GVA_CONFIG.Redis.Addr,
        Password: global.GVA_CONFIG.Redis.Password,
        DB:       global.GVA_CONFIG.Redis.DB,
    })
    
    // 初始化分布式租户上下文
    global.InitDistributedTenantContext(rdb)
    
    // 初始化分布式租户缓存
    global.InitDistributedTenantCache(rdb)
}
```

## 📊 性能对比

### 缓存层级性能
```
L1 本地缓存：    <0.1ms
L2 Redis缓存：   1-2ms
L3 数据库查询：  10-50ms
```

### 分布式vs单机
```
单机方案：
- 并发能力：1000用户/实例
- 缓存一致性：❌ 无法保证
- 扩展性：❌ 受限于单机

分布式方案：
- 并发能力：10000+用户/集群
- 缓存一致性：✅ Redis保证
- 扩展性：✅ 水平扩展
```

## 🎯 推荐方案

### 生产环境推荐
1. **使用Redis分布式会话管理**
2. **实施多层缓存策略**
3. **HTTP头传递租户信息**
4. **gRPC拦截器支持**

### 部署建议
1. **Redis集群** - 高可用性
2. **监控告警** - 缓存命中率、响应时间
3. **渐进式迁移** - 从单机到分布式
4. **性能测试** - 验证分布式性能

通过这些优化，多租户系统将能够在分布式环境中稳定、高效地运行。