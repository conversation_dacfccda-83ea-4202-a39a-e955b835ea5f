# API文档更新 - 统一登录接口

## 📋 **更新概述**

本文档描述了统一登录接口的更新内容，包括新增的多租户支持功能。

## 🔄 **API变更说明**

### **接口统一化**

- **之前**: 分离的登录接口
  - 普通用户: `POST /base/login`
  - 加盟商: `POST /base/franchiseeLogin` (已废弃)
- **现在**: 统一登录接口
  - 所有用户: `POST /base/login`

## 📝 **API详细文档**

### **POST /base/login - 统一登录接口**

#### **接口描述**

统一的用户登录接口，支持普通用户和加盟商登录，自动识别用户类型并处理多租户逻辑。

#### **请求参数**

| 参数名        | 类型     | 必填  | 说明                 |
| ---------- | ------ | --- | ------------------ |
| username   | string | 是   | 用户名（普通用户）或手机号（加盟商） |
| password   | string | 是   | 密码                 |
| tenantCode | string | 否   | 租户代码（仅加盟商多租户场景使用）  |
| captcha    | string | 否   | 验证码                |
| captchaId  | string | 否   | 验证码ID              |

#### **请求示例**

```json
// 普通用户登录
{
  "username": "admin",
  "password": "admin123",
  "captcha": "1234",
  "captchaId": "uuid-1234"
}

// 加盟商登录（单租户）
{
  "username": "13800138001",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "uuid-1234"
}

// 加盟商登录（指定租户）
{
  "username": "13800138002",
  "password": "password123",
  "tenantCode": "tenant_a",
  "captcha": "1234",
  "captchaId": "uuid-1234"
}
```

#### **响应参数**

| 参数名                 | 类型      | 说明                |
| ------------------- | ------- | ----------------- |
| user                | object  | 用户信息              |
| token               | string  | JWT访问令牌           |
| expiresAt           | number  | 令牌过期时间（毫秒时间戳）     |
| needTenantSelection | boolean | 是否需要租户选择（仅多租户加盟商） |
| availableTenants    | array   | 可用租户列表（仅多租户场景）    |
| tenantId            | number  | 当前租户ID（仅加盟商）      |

#### **响应示例**

**场景1: 普通用户登录成功**

```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "nickName": "管理员",
      "headerImg": "",
      "authorityId": 888
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000
  },
  "msg": "登录成功"
}
```

**场景2: 单租户加盟商登录成功**

```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 2,
      "username": "13800138001",
      "nickName": "张三",
      "headerImg": "",
      "authorityId": 999
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000,
    "tenantId": 1
  },
  "msg": "登录成功"
}
```

**场景3: 多租户加盟商需要选择租户**

```json
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [
      {
        "id": 1,
        "franchiseeId": 2,
        "tenantId": 1,
        "status": "active",
        "role": "franchisee",
        "isDefault": true,
        "tenant": {
          "id": 1,
          "name": "默认租户",
          "code": "default"
        }
      },
      {
        "id": 2,
        "franchiseeId": 2,
        "tenantId": 2,
        "status": "active",
        "role": "manager",
        "isDefault": false,
        "tenant": {
          "id": 2,
          "name": "租户A",
          "code": "tenant_a"
        }
      }
    ]
  },
  "msg": "请选择要登录的租户"
}
```

**场景4: 指定租户登录成功**

```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 2,
      "username": "13800138002",
      "nickName": "李四",
      "headerImg": "",
      "authorityId": 999
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000,
    "tenantId": 2
  },
  "msg": "登录成功"
}
```

#### **错误响应**

| 错误码  | 错误信息         | 说明       |
| ---- | ------------ | -------- |
| 1001 | 用户名不存在或者密码错误 | 认证失败     |
| 1002 | 用户被禁止登录      | 用户被停用    |
| 1003 | 验证码错误        | 验证码验证失败  |
| 1004 | 加盟商没有关联任何租户  | 加盟商无租户权限 |
| 1005 | 指定的租户不存在     | 租户代码无效   |
| 1006 | 加盟商不属于指定租户   | 租户权限不足   |

#### **错误响应示例**

```json
{
  "code": 1001,
  "data": null,
  "msg": "用户名不存在或者密码错误"
}
```

## 🔐 **JWT令牌说明**

### **令牌结构**

JWT令牌包含以下信息：

- 用户基本信息（ID、用户名、昵称等）
- 权限信息（角色ID、权限列表等）
- 租户信息（仅加盟商，包含租户ID）
- 过期时间等标准字段

### **租户信息**

对于加盟商用户，JWT中包含额外的租户信息：

```json
{
  "userId": 2,
  "username": "13800138002",
  "tenantId": 1,
  "userType": 3,
  "exp": 1640995200
}
```

## 🔄 **客户端集成指南**

### **登录流程**

```javascript
// 1. 发送登录请求
const loginResponse = await fetch('/base/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'user_input',
    password: 'password_input',
    tenantCode: 'optional_tenant_code'
  })
});

const result = await loginResponse.json();

// 2. 处理响应
if (result.code === 0) {
  if (result.data.needTenantSelection) {
    // 显示租户选择界面
    showTenantSelection(result.data.availableTenants);
  } else {
    // 登录成功，保存token
    localStorage.setItem('token', result.data.token);
    localStorage.setItem('tenantId', result.data.tenantId);
    // 跳转到主页面
    redirectToHome();
  }
} else {
  // 显示错误信息
  showError(result.msg);
}
```

### **租户选择处理**

```javascript
// 用户选择租户后重新登录
async function loginWithTenant(username, password, tenantCode) {
  const response = await fetch('/base/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username: username,
      password: password,
      tenantCode: tenantCode
    })
  });

  const result = await response.json();
  if (result.code === 0) {
    localStorage.setItem('token', result.data.token);
    localStorage.setItem('tenantId', result.data.tenantId);
    redirectToHome();
  }
}
```

### **API请求认证**

```javascript
// 在所有API请求中携带token
const apiRequest = async (url, options = {}) => {
  const token = localStorage.getItem('token');

  return fetch(url, {
    ...options,
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      ...options.headers
    }
  });
};
```

## 📱 **移动端适配**

### **Android示例**

```java
// 登录请求
public class LoginRequest {
    private String username;
    private String password;
    private String tenantCode;
    private String captcha;
    private String captchaId;

    // getters and setters
}

// 登录响应处理
public void handleLoginResponse(LoginResponse response) {
    if (response.getCode() == 0) {
        LoginData data = response.getData();
        if (data.isNeedTenantSelection()) {
            // 显示租户选择对话框
            showTenantSelectionDialog(data.getAvailableTenants());
        } else {
            // 保存token和租户信息
            saveToken(data.getToken());
            saveTenantId(data.getTenantId());
            navigateToHome();
        }
    } else {
        showError(response.getMsg());
    }
}
```

### **iOS示例**

```swift
// 登录请求结构
struct LoginRequest: Codable {
    let username: String
    let password: String
    let tenantCode: String?
    let captcha: String?
    let captchaId: String?
}

// 登录响应处理
func handleLoginResponse(_ response: LoginResponse) {
    if response.code == 0 {
        let data = response.data
        if data.needTenantSelection == true {
            // 显示租户选择界面
            showTenantSelection(data.availableTenants)
        } else {
            // 保存认证信息
            TokenManager.shared.saveToken(data.token)
            TenantManager.shared.saveTenantId(data.tenantId)
            navigateToHome()
        }
    } else {
        showError(response.msg)
    }
}
```

## 🔧 **开发注意事项**

### **向后兼容性**

- 现有的普通用户登录完全不受影响
- 客户端可以逐步迁移到新的响应格式
- 旧的API调用方式仍然有效

### **错误处理**

- 客户端应该处理所有可能的错误场景
- 特别注意多租户相关的错误情况
- 提供友好的用户提示信息

### **安全考虑**

- JWT令牌包含敏感的租户信息，需要安全存储
- 不同租户的令牌不能混用
- 定期刷新令牌以保证安全性

### **性能优化**

- 租户信息查询已优化，响应时间在可接受范围内
- 建议客户端缓存租户列表以减少重复请求
- 使用适当的超时设置避免长时间等待

---

**文档版本**: v2.0  
**更新日期**: 2024-01-01  
**维护人员**: 开发团队
