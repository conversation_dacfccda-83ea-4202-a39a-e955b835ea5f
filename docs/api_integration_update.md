# API集成更新说明

## 📋 **更新概述**

根据您的建议，我们已经将加盟商登录功能集成到现有的 `/base/login` API中，而不是创建新的 `/base/franchiseeLogin` API。这是更好的设计选择。

## 🔄 **主要变更**

### **1. 统一登录API**

#### **之前的设计**：
- 普通用户登录：`POST /base/login`
- 加盟商登录：`POST /base/franchiseeLogin` ❌

#### **现在的设计**：
- 统一登录：`POST /base/login` ✅
- 自动识别用户类型（普通用户 vs 加盟商）
- 智能处理多租户逻辑

### **2. 请求结构扩展**

```go
// 扩展后的登录请求
type Login struct {
    Username   string `json:"username"`   // 用户名（可以是手机号）
    Password   string `json:"password"`   // 密码
    TenantCode string `json:"tenantCode"` // 可选：租户代码（用于加盟商多租户登录）
    Captcha    string `json:"captcha"`    // 验证码
    CaptchaId  string `json:"captchaId"`  // 验证码ID
}
```

### **3. 响应结构扩展**

```go
// 扩展后的登录响应
type LoginResponse struct {
    User                SysUser                        `json:"user"`
    Token               string                         `json:"token"`
    ExpiresAt           int64                          `json:"expiresAt"`
    // 新增字段用于多租户支持
    NeedTenantSelection bool                           `json:"needTenantSelection,omitempty"` // 是否需要租户选择
    AvailableTenants    []FranchiseeTenantRelation     `json:"availableTenants,omitempty"`    // 可用租户列表
    TenantID            uint                           `json:"tenantId,omitempty"`            // 当前租户ID
}
```

## 🎯 **登录流程**

### **智能用户类型识别**

```mermaid
graph TD
    A[POST /base/login] --> B[验证用户名密码]
    B --> C{用户是否为加盟商?}
    C -->|否| D[普通用户登录流程]
    C -->|是| E[加盟商多租户登录流程]
    D --> F[生成普通JWT]
    E --> G{租户数量?}
    G -->|1个| H[直接登录该租户]
    G -->|多个| I{指定租户代码?}
    I -->|是| J[验证并登录指定租户]
    I -->|否| K{有默认租户?}
    K -->|是| L[登录默认租户]
    K -->|否| M[返回租户选择列表]
    H --> N[生成租户JWT]
    J --> N
    L --> N
    F --> O[返回登录成功]
    N --> O
    M --> P[返回需要选择租户]
```

## 📝 **API使用示例**

### **场景1：普通用户登录**
```json
// 请求
{
  "username": "admin",
  "password": "admin123",
  "captcha": "1234",
  "captchaId": "uuid"
}

// 响应
{
  "code": 0,
  "data": {
    "user": {...},
    "token": "jwt_token",
    "expiresAt": 1640995200000
  },
  "msg": "登录成功"
}
```

### **场景2：单租户加盟商登录**
```json
// 请求
{
  "username": "13800138001",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "uuid"
}

// 响应
{
  "code": 0,
  "data": {
    "user": {...},
    "token": "jwt_token",
    "expiresAt": 1640995200000,
    "tenantId": 1
  },
  "msg": "登录成功"
}
```

### **场景3：多租户加盟商登录（需要选择）**
```json
// 请求
{
  "username": "13800138002",
  "password": "password123"
}

// 响应
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [
      {
        "tenantId": 1,
        "role": "franchisee",
        "isDefault": true,
        "tenant": {"name": "默认租户", "code": "default"}
      },
      {
        "tenantId": 2,
        "role": "manager",
        "isDefault": false,
        "tenant": {"name": "租户A", "code": "tenant_a"}
      }
    ]
  },
  "msg": "请选择要登录的租户"
}
```

### **场景4：指定租户登录**
```json
// 请求
{
  "username": "13800138002",
  "password": "password123",
  "tenantCode": "tenant_a"
}

// 响应
{
  "code": 0,
  "data": {
    "user": {...},
    "token": "jwt_token",
    "expiresAt": 1640995200000,
    "tenantId": 2
  },
  "msg": "登录成功"
}
```

## 🔧 **实现细节**

### **核心逻辑**

1. **用户类型自动识别**：
   - 通过 `UserID` 查询是否存在对应的加盟商记录
   - 如果存在，走加盟商多租户流程
   - 如果不存在，走普通用户流程

2. **租户选择逻辑**：
   - 单租户：直接登录
   - 多租户 + 指定租户：验证权限后登录
   - 多租户 + 有默认租户：使用默认租户登录
   - 多租户 + 无默认租户：返回选择列表

3. **JWT生成**：
   - 普通用户：标准JWT
   - 加盟商：包含租户信息的JWT

## ✅ **优势**

1. **API统一性**：只有一个登录接口，简化客户端调用
2. **向后兼容**：现有的普通用户登录不受影响
3. **智能识别**：自动识别用户类型，无需客户端判断
4. **灵活性**：支持多种租户选择场景
5. **可扩展性**：易于添加新的用户类型和登录逻辑

## 📋 **测试更新**

### **需要更新的测试用例**

1. **API端点**：所有测试用例中的 `/base/franchiseeLogin` 改为 `/base/login`
2. **请求结构**：`systemReq.FranchiseeLogin` 改为 `systemReq.Login`
3. **响应结构**：`systemRes.FranchiseeLoginResponse` 改为 `systemRes.LoginResponse`

### **新增测试场景**

1. **混合登录测试**：在同一个测试中验证普通用户和加盟商登录
2. **API兼容性测试**：确保现有普通用户登录不受影响
3. **用户类型识别测试**：验证系统正确识别用户类型

## 🚀 **部署说明**

1. **无需路由变更**：复用现有的 `/base/login` 路由
2. **数据库兼容**：新的关联表不影响现有数据
3. **客户端更新**：
   - 加盟商登录改用 `/base/login`
   - 添加 `tenantCode` 字段支持
   - 处理 `needTenantSelection` 响应

## 📊 **影响评估**

### **正面影响**
- ✅ API设计更加统一和简洁
- ✅ 减少客户端复杂度
- ✅ 提高代码复用性
- ✅ 更好的用户体验

### **需要注意**
- 🔍 确保现有普通用户登录功能不受影响
- 🔍 客户端需要更新处理多租户响应
- 🔍 测试用例需要相应更新

**这个统一的API设计更加优雅和实用！** 🎯
