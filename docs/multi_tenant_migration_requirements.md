# 多租户功能改造需求文档

## 📋 概述

本文档基于当前系统的实际情况，详细说明从单租户到多租户系统改造的完整功能需求和实现方案。系统已完成大部分多租户基础架构，本文档重点梳理剩余待完成的功能模块。

## 🎯 背景与目标

### 背景
- 原系统设计为单商户使用
- 现需改造为支持多租户的加盟商管理SaaS平台
- 系统已实现核心多租户架构，需要完善剩余功能模块

### 目标
1. **完整数据隔离**：确保不同租户间数据完全隔离
2. **功能完整性**：所有业务模块支持多租户
3. **管理简化**：内部系统，管理功能相对简单但数据隔离要严格
4. **开发规范**：建立完整的功能修改列表作为开发依据

## 🏗️ 当前多租户实现状况

### ✅ 已完成的核心架构
1. **租户模型**：`Tenant`、`TenantAppConfig`、`UserTenantRelation`
2. **数据隔离机制**：GORM租户插件自动处理SQL过滤
3. **中间件体系**：`TenantMiddleware`、`TenantIsolationMiddleware`
4. **权限控制**：基于Casbin的RBAC + 租户隔离
5. **上下文管理**：全局租户上下文和线程安全机制

### ✅ 已完成租户隔离的业务模块
1. **加盟商管理**：`router/franchisees/*` - 已应用 `TenantIsolationMiddleware`
2. **商品管理**：`router/products/*` - 已应用 `TenantIsolationMiddleware`  
3. **订单管理**：`router/orders/*` - 已应用 `TenantIsolationMiddleware`
4. **APP端接口**：`AppGroup` - 已应用 `UnifiedTenantMiddleware`
5. **支付模块**：`AppPayGroup` - 已应用 `UnifiedTenantMiddleware`

### ✅ 已配置租户表
```go
// global/tenant_context.go 中已配置的表
"order", "order_goods", "order_delivery", "order_return", "order_remark"
"products", "product_brand", "product_category"  
"franchisees", "franchisee_address", "franchisee_category"
"recharge_record", "cart", "cloud_warehouse", "cloud_warehouse_record"
"big_warehouse_order", "big_warehouse_stock", "big_warehouse_record"
"order_online_pay", "franchisee_performance"
```

## 🔧 待完成的功能模块清单

### 0. 加盟商数据模型重构 🔴 **最高优先级**

#### 问题分析
- 当前使用 `FranchiseeTenantRelation` 表管理M:N关系，但几乎所有字段都需要租户隔离
- 需要重构为1:1关系，简化数据模型和业务逻辑

#### 重构要求
```go
// 1. 删除 FranchiseeTenantRelation 表
DROP TABLE franchisee_tenant_relation;

// 2. 修改 franchisee 表结构
ALTER TABLE franchisee ADD COLUMN tenant_id INT NOT NULL AFTER id;
ALTER TABLE franchisee ADD INDEX idx_tenant_id (tenant_id);
ALTER TABLE franchisee ADD UNIQUE KEY uk_tenant_user (tenant_id, user_id);
ALTER TABLE franchisee ADD UNIQUE KEY uk_tenant_code (tenant_id, code);

// 3. 数据迁移（从两套独立系统）
INSERT INTO franchisee (tenant_id, user_id, code, name, ...)
SELECT 1 as tenant_id, user_id, code, name, ... FROM system_a.franchisee;
INSERT INTO franchisee (tenant_id, user_id, code, name, ...)
SELECT 2 as tenant_id, user_id, code, name, ... FROM system_b.franchisee;
```

#### 影响范围
- **数据模型**：franchisee表结构变更
- **业务逻辑**：所有加盟商相关查询需要添加tenant_id条件
- **登录流程**：需要实现多租户登录选择逻辑
- **API接口**：加盟商相关接口需要重构

### 1. 财务管理模块 ⚠️

#### 问题分析
- `router/finance/franchisee_performance.go` **缺少租户隔离中间件**
- 财务数据涉及敏感信息，必须严格隔离

#### 修改要求
```go
// 需要在以下路由添加 TenantIsolationMiddleware
franchiseePerformanceRouter := Router.Group("franchiseePerformance").Use(middleware.OperationRecord()).Use(middleware.TenantIsolationMiddleware())
franchiseePerformanceRouterWithoutRecord := Router.Group("franchiseePerformance").Use(middleware.TenantIsolationMiddleware())
```

#### 涉及表格
- `franchisee_performance` ✅ 已配置
- `franchisee_performance_month` ❌ 需要添加到租户表配置
- `franchisee_history_performance` ❌ 需要添加到租户表配置

### 2. 大仓管理模块 ⚠️

#### 问题分析  
- `router/bigwarehouse/*` 所有路由 **缺少租户隔离中间件**
- 仓储数据按租户隔离，避免库存数据混乱

#### 修改要求
需要为以下路由文件添加 `TenantIsolationMiddleware`：
- `big_warehouse.go`
- `big_warehouse_allocation.go` 
- `big_warehouse_coverage.go`
- `big_warehouse_goods.go`
- `big_warehouse_manager.go`
- `big_warehouse_order.go` ✅ 已配置表
- `big_warehouse_record.go` ✅ 已配置表  
- `big_warehouse_stock.go` ✅ 已配置表

#### 涉及表格
```go
// 需要添加到 global/tenant_context.go
"big_warehouse": true,
"big_warehouse_allocation": true,
"big_warehouse_coverage": true, 
"big_warehouse_goods": true,
"big_warehouse_manager": true,
```

### 3. 通用数据模块 ⚠️

#### 问题分析
- `router/common/*` 路由缺少租户隔离
- 物流模板、城市数据等可能需要按租户配置

#### 修改要求
需要评估以下模块是否需要租户隔离：
- `city.go` - 城市数据（可能全局共享）
- `logistics_template.go` - 物流模板（需要租户隔离）
- `product_gift.go` - 商品赠品规则（需要租户隔离）

#### 涉及表格
```go
// 需要添加到租户表配置（待确认）
"logistics_template": true,
"logistics_template_area": true,
"product_gift_rule": true,
```

### 4. 仓储管理模块 ⚠️

#### 问题分析
- `router/warehouse/*` 路由缺少租户隔离中间件
- 云仓数据已配置表但路由未隔离

#### 修改要求
```go
// warehouse/cloud_warehouse.go 需要添加中间件
cloudWarehouseRouter := Router.Group("cloudWarehouse").Use(middleware.OperationRecord()).Use(middleware.TenantIsolationMiddleware())
```

### 5. 自动任务模块 ⚠️

#### 问题分析
- `router/autoJob/*` 定时任务可能需要按租户执行
- 商品任务、业绩计算等需要租户隔离

#### 修改要求
需要评估定时任务是否需要租户上下文：
- `product_job.go` - 商品相关任务

### 6. 分销管理模块 ⚠️

#### 问题分析
- `router/distribution/*` 分销功能缺少租户隔离
- 加盟商审核、备用加盟商等需要按租户管理

#### 修改要求
```go
// 需要为分销相关路由添加租户隔离
distributionRouter := Router.Group("distribution").Use(middleware.TenantIsolationMiddleware())
```

#### 涉及表格
```go
// 需要添加到租户表配置
"franchisee_approve": true,
"franchisee_standby": true,
"distribution_config": true,
```

## 📝 完整功能修改清单

### 优先级1：核心业务模块（必须完成）

#### 1.1 财务管理模块
- [ ] `router/finance/franchisee_performance.go` 添加 `TenantIsolationMiddleware`
- [ ] 租户表配置添加：`franchisee_performance_month`、`franchisee_history_performance`

#### 1.2 大仓管理模块  
- [ ] `router/bigwarehouse/*.go` 所有路由添加 `TenantIsolationMiddleware`
- [ ] 租户表配置添加：`big_warehouse`、`big_warehouse_allocation`、`big_warehouse_coverage`、`big_warehouse_goods`、`big_warehouse_manager`

#### 1.3 仓储管理模块
- [ ] `router/warehouse/cloud_warehouse.go` 添加 `TenantIsolationMiddleware`
- [ ] `router/warehouse/cloud_warehouse_record.go` 添加 `TenantIsolationMiddleware`

### 优先级2：扩展功能模块（建议完成）

#### 2.1 分销管理模块
- [ ] `router/distribution/*.go` 添加 `TenantIsolationMiddleware`
- [ ] 租户表配置添加：`franchisee_approve`、`franchisee_standby`、`distribution_config`

#### 2.2 通用数据模块
- [ ] 评估 `router/common/logistics_template.go` 是否需要租户隔离
- [ ] 评估 `router/common/product_gift.go` 是否需要租户隔离
- [ ] 租户表配置添加：`logistics_template`、`logistics_template_area`、`product_gift_rule`

### 优先级3：系统功能模块（可选完成）

#### 3.1 自动任务模块
- [ ] 评估 `router/autoJob/product_job.go` 是否需要租户上下文
- [ ] 定时任务执行时的租户上下文设置

#### 3.2 应用发布模块
- [ ] 评估 `router/apprelease/*.go` 是否需要租户隔离
- [ ] APP版本管理是否按租户区分

## 🔍 验证和测试要求

### 数据隔离验证
1. **跨租户访问测试**：确保用户无法访问其他租户数据
2. **SQL注入测试**：验证租户过滤无法被绕过
3. **API权限测试**：确保所有API都有正确的租户验证

### 功能完整性验证
1. **业务流程测试**：各业务模块在多租户环境下正常运行
2. **数据一致性测试**：租户数据创建、更新、删除的一致性
3. **性能测试**：多租户环境下的系统性能表现

### 安全性验证
1. **权限边界测试**：超级管理员权限的正确使用
2. **审计日志测试**：敏感操作的完整记录
3. **异常处理测试**：租户验证失败时的正确处理

## 📊 实施建议

### 开发顺序
1. **第零阶段**：加盟商数据模型重构（必须最先完成）
2. **第一阶段**：完成优先级1的核心业务模块
3. **第二阶段**：完成优先级2的扩展功能模块
4. **第三阶段**：评估并完成优先级3的系统功能模块

### 第零阶段详细计划
1. **数据库结构调整**：
   - 删除 `franchisee_tenant_relation` 表
   - 修改 `franchisee` 表添加 `tenant_id` 字段
   - 创建复合唯一索引

2. **数据迁移**：
   - 从两套独立系统导入数据
   - 处理用户账号合并问题
   - 验证数据完整性

3. **代码重构**：
   - 更新所有加盟商相关的查询逻辑
   - 实现多租户登录流程
   - 修改API接口返回结构

4. **测试验证**：
   - 单租户登录测试
   - 多租户登录测试
   - 数据隔离验证测试

### 代码规范
1. **统一中间件使用**：所有业务路由必须使用 `TenantIsolationMiddleware`
2. **租户表配置**：新增表及时添加到 `global/tenant_context.go`
3. **测试覆盖**：每个修改的模块都要有对应的测试用例

### 质量保证
1. **代码审查**：所有租户相关代码必须经过审查
2. **安全测试**：重点测试数据隔离和权限控制
3. **文档更新**：及时更新相关技术文档

## 🎯 验收标准

### 功能完整性
- [ ] 所有业务模块支持多租户
- [ ] 数据完全隔离，无跨租户访问
- [ ] 权限控制正确有效

### 技术质量
- [ ] 代码规范统一
- [ ] 测试覆盖率达标
- [ ] 性能指标合格

### 安全性
- [ ] 通过安全审计
- [ ] 无数据泄露风险
- [ ] 审计日志完整

## 📋 详细实施指南

### 1. 路由中间件添加标准模板

#### 标准模板
```go
// 示例：router/finance/franchisee_performance.go
func (s *FranchiseePerformanceRouter) InitFranchiseePerformanceRouter(Router *gin.RouterGroup) {
    // 添加租户隔离中间件
    franchiseePerformanceRouter := Router.Group("franchiseePerformance").Use(middleware.OperationRecord()).Use(middleware.TenantIsolationMiddleware())
    franchiseePerformanceRouterWithoutRecord := Router.Group("franchiseePerformance").Use(middleware.TenantIsolationMiddleware())

    var franchiseePerformanceApi = v1.ApiGroupApp.FinanceApiGroup.FranchiseePerformanceApi
    {
        // 需要操作记录的路由
        franchiseePerformanceRouter.POST("createFranchiseePerformance", franchiseePerformanceApi.CreateFranchiseePerformance)
        franchiseePerformanceRouter.PUT("updateFranchiseePerformance", franchiseePerformanceApi.UpdateFranchiseePerformance)
        franchiseePerformanceRouter.DELETE("deleteFranchiseePerformance", franchiseePerformanceApi.DeleteFranchiseePerformance)
    }
    {
        // 不需要操作记录的路由
        franchiseePerformanceRouterWithoutRecord.GET("findFranchiseePerformance", franchiseePerformanceApi.FindFranchiseePerformance)
        franchiseePerformanceRouterWithoutRecord.GET("getFranchiseePerformanceList", franchiseePerformanceApi.GetFranchiseePerformanceList)
    }
}
```

### 2. 租户表配置添加标准

#### 配置位置
文件：`global/tenant_context.go`

#### 添加方法
```go
// 在 IsTenantTable 函数的 manualTenantTables 中添加
manualTenantTables := map[string]bool{
    // 现有配置...

    // 新增财务相关表
    "franchisee_performance_month":    true,
    "franchisee_history_performance":  true,

    // 新增大仓相关表
    "big_warehouse":                   true,
    "big_warehouse_allocation":        true,
    "big_warehouse_coverage":          true,
    "big_warehouse_goods":             true,
    "big_warehouse_manager":           true,

    // 新增分销相关表
    "franchisee_approve":              true,
    "franchisee_standby":              true,
    "distribution_config":             true,

    // 新增通用数据表（根据业务需求确定）
    "logistics_template":              true,
    "logistics_template_area":         true,
    "product_gift_rule":               true,
}
```

### 3. 数据模型检查清单

#### 必须包含的字段
每个需要租户隔离的数据模型必须包含：
```go
type ExampleModel struct {
    global.GVA_MODEL
    TenantID uint   `json:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID"`
    // 其他业务字段...
}
```

#### 检查方法
1. 确认模型结构包含 `TenantID` 字段
2. 确认数据库表已添加 `tenant_id` 列
3. 确认表名已添加到租户表配置中

### 4. API测试验证清单

#### 数据隔离测试
```bash
# 1. 创建测试数据（租户1）
curl -X POST /api/v1/franchiseePerformance/createFranchiseePerformance \
  -H "Authorization: Bearer <tenant1_token>" \
  -d '{"name": "test_tenant1"}'

# 2. 尝试用租户2的token访问租户1的数据
curl -X GET /api/v1/franchiseePerformance/getFranchiseePerformanceList \
  -H "Authorization: Bearer <tenant2_token>"

# 3. 验证返回结果不包含租户1的数据
```

#### 权限验证测试
```bash
# 1. 测试超级管理员权限
curl -X GET /api/v1/franchiseePerformance/getFranchiseePerformanceList \
  -H "Authorization: Bearer <super_admin_token>"

# 2. 验证可以看到所有租户数据（如果有相应权限）
```

### 5. 常见问题和解决方案

#### 问题1：中间件不生效
**症状**：添加了中间件但数据仍然没有隔离
**解决方案**：
1. 检查路由组是否正确应用中间件
2. 检查中间件的执行顺序
3. 检查JWT token中是否包含正确的租户ID

#### 问题2：表配置不生效
**症状**：GORM插件没有自动过滤数据
**解决方案**：
1. 检查表名是否正确（注意单复数）
2. 检查 `IsTenantTable` 函数返回值
3. 重启应用确保配置生效

#### 问题3：性能问题
**症状**：添加租户过滤后查询变慢
**解决方案**：
1. 为 `tenant_id` 字段添加数据库索引
2. 创建复合索引：`(tenant_id, other_frequently_queried_fields)`
3. 优化查询语句

### 6. 代码审查检查点

#### 必检项目
- [ ] 所有业务路由都应用了 `TenantIsolationMiddleware`
- [ ] 所有业务表都添加到租户表配置中
- [ ] 数据模型包含正确的 `TenantID` 字段
- [ ] 数据库表包含 `tenant_id` 列和索引

#### 建议检查项目
- [ ] API文档更新了租户相关说明
- [ ] 单元测试覆盖了租户隔离场景
- [ ] 错误处理包含租户验证失败的情况
- [ ] 日志记录包含租户信息

## 🚀 快速开始指南

### 第一步：确认当前状态
```bash
# 1. 检查已完成的模块
grep -r "TenantIsolationMiddleware" router/

# 2. 检查租户表配置
cat global/tenant_context.go | grep -A 20 "manualTenantTables"
```

### 第二步：按优先级实施
1. **立即开始**：财务管理模块（涉及敏感数据）
2. **第二批次**：大仓管理模块（业务核心）
3. **第三批次**：其他扩展模块

### 第三步：验证测试
1. 部署到测试环境
2. 执行数据隔离测试
3. 执行性能测试
4. 执行安全测试

### 第四步：生产部署
1. 数据库迁移（添加缺失的 tenant_id 字段）
2. 应用部署
3. 监控和验证

## 📞 支持和联系

如果在实施过程中遇到问题，请：
1. 首先查阅本文档的常见问题部分
2. 检查相关的技术文档
3. 联系技术团队进行支持

---

**文档版本**：v1.0
**最后更新**：2024-12-25
**维护者**：技术团队
