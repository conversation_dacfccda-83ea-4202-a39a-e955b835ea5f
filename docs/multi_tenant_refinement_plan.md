# 多租户实现优化与修复计划

## 1. 背景与目标

本项目采用 GORM 租户插件 ([`plugin/tenant/tenant_plugin.go`](plugin/tenant/tenant_plugin.go)) 实现数据库层面的自动多租户数据隔离。该插件依赖于上游中间件正确设置租户上下文（`context.Context` 中的租户ID）或线程本地存储（TLS）中的租户ID。

经过分析，发现核心的租户设置中间件（`TenantMiddleware` 或 `DistributedTenantMiddleware`）并未在主要的私有路由组（如 `PrivateGroup`, `AppGroup`）上被显式应用，这可能导致 GORM 插件无法获取有效的租户ID，从而影响数据隔离的正确性。

**目标**：修复此疏漏，确保租户上下文被正确建立，GORM 插件能够可靠工作，并在此基础上优化和清理冗余的多租户实现代码。

## 2. 问题分析与发现

*   **GORM 租户插件**：在 [`plugin/tenant/tenant_plugin.go`](plugin/tenant/tenant_plugin.go) 中实现，通过 GORM 回调在 CRUD 操作前自动添加租户条件或设置租户ID。它依赖 [`global/tenant_context.go`](global/tenant_context.go) 中的 `IsTenantTable` 配置和从 `context.Context` 或 TLS 获取租户ID。
*   **租户上下文管理**：在 [`global/tenant_context.go`](global/tenant_context.go) 中实现，提供 `WithTenantContext` (注入到 `context.Context`) 和 `SetCurrentTenantIDSafe` (设置到TLS)。
*   **核心租户中间件**：
    *   [`middleware/tenant.go`](middleware/tenant.go): 基础租户中间件，从 `claims` 获取租户ID，调用 `system.ValidateTenant` 验证，然后设置租户上下文和TLS。
    *   [`middleware/distributed_tenant.go`](middleware/distributed_tenant.go): 分布式场景下的租户中间件，增加了分布式会话管理和基于配置的租户验证策略。
*   **JWT 认证中间件**：[`middleware/jwt.go`](middleware/jwt.go) 中的 `JWTAuth()` 负责解析JWT并将 `claims` 存入 `gin.Context` (键为 "claims")，但不直接设置租户上下文。
*   **Casbin 权限中间件**：[`middleware/casbin_rbac.go`](middleware/casbin_rbac.go) 中的 `CasbinHandler()` 从 `gin.Context` 获取 `claims` 进行权限检查，但不设置租户上下文。
*   **路由配置**：[`initialize/router.go`](initialize/router.go) 中，主要的私有路由组（`PrivateGroup`, `AppGroup`, `AppPayGroup`）应用了 `JWTAuth()` 和 `CasbinHandler()`，但**没有显式应用 `TenantMiddleware` 或 `DistributedTenantMiddleware`**。
*   **插件初始化**：[`initialize/plugin.go`](initialize/plugin.go) 中的 `InstallPlugin()` 主要负责注册插件路由，未发现全局应用租户中间件的逻辑。
*   **中间件选择逻辑**：通过分析 [`global/distributed_tenant_cache.go`](global/distributed_tenant_cache.go) 和 [`global/distributed_tenant_context.go`](global/distributed_tenant_context.go)，确定应使用 `global.IsDistributedMode()` 的返回值来决定应用 `TenantMiddleware` (false) 还是 `DistributedTenantMiddleware` (true)。

**核心问题**：由于租户中间件未在关键路由组上应用，GORM 插件可能无法通过 `context.Context` 获取租户ID，也可能无法从 TLS 获取正确的租户ID，导致数据隔离可能失效。

## 3. 修复与优化计划

### 3.1. 核心修复：正确应用租户中间件

**目标**：确保在需要租户隔离的私有路由组上，在JWT认证之后、其他业务中间件（如Casbin）之前，正确应用租户上下文设置中间件。

**步骤**：

1.  **确定应用的中间件**：
    *   在 [`initialize/router.go`](initialize/router.go) 中，或在应用中间件之前，通过 `global.IsDistributedMode()` 判断项目运行模式。
    *   如果 `global.IsDistributedMode() == true`，则选择 `middleware.DistributedTenantMiddleware()`。
    *   如果 `global.IsDistributedMode() == false`，则选择 `middleware.TenantMiddleware()`。

2.  **修改路由配置**：
    *   编辑 [`initialize/router.go`](initialize/router.go)。
    *   在 `PrivateGroup` 的定义处，修改中间件应用顺序：
        ```go
        // 示例:
        var selectedTenantMiddleware gin.HandlerFunc
        if global.IsDistributedMode() {
            selectedTenantMiddleware = middleware.DistributedTenantMiddleware()
        } else {
            selectedTenantMiddleware = middleware.TenantMiddleware()
        }
        PrivateGroup.Use(middleware.JWTAuth()).Use(selectedTenantMiddleware).Use(middleware.CasbinHandler())
        ```
    *   对 `AppGroup` ([`initialize/router.go:111`](initialize/router.go:111)) 和 `AppPayGroup` ([`initialize/router.go:177`](initialize/router.go:177)) 以及其他所有需要租户隔离的、应用了 `JWTAuth` 的私有路由组，进行类似的修改，确保它们也应用了 `selectedTenantMiddleware`。

### 3.2. 验证与测试（理论）

修改后，预期的请求处理流程（以 `PrivateGroup` 为例）：
```mermaid
graph TD
    A[HTTP Request] --> B(Gin Engine);
    B --> C{PrivateGroup};
    C -- Apply Middlewares --> E[middleware.JWTAuth];
    E -- Token Valid, 'claims' in gin.Context --> F[selectedTenantMiddleware];
    F -- Tenant Valid, TenantID in context.Context & TLS --> G[middleware.CasbinHandler];
    G -- Authorized --> H[Business Logic Handler];
    H -- GORM DB Call --> I[plugin.TenantPlugin];
    I -- Tenant ID from context.Context (preferred) or TLS --> J[Auto-filtered SQL Query];
    J --> K[Database];
    K --> J;
    J --> I;
    I --> H;
    H --> L[HTTP Response];
    L --> A;

    E -- Token Invalid --> L;
    F -- Tenant Invalid --> L;
    G -- Unauthorized --> L;

    style F fill:#ccf,stroke:#333,stroke-width:2px
    style I fill:#cfc,stroke:#333,stroke-width:2px
```
这将确保：
*   `system.ValidateTenant()` 被正确执行。
*   GORM 插件能从 `context.Context` 或 TLS 中可靠获取 `TenantID`。
*   多租户数据隔离机制按预期工作。

### 3.3. 后续重构与优化

在核心修复完成并验证后，进行以下工作：

1.  **移除冗余的订单租户代码**：
    *   按照 [`docs/order_tenant_removal_plan.md`](docs/order_tenant_removal_plan.md) 的计划，安全移除 [`service/orders/order_tenant.go`](service/orders/order_tenant.go)。
    *   理由：GORM 插件已提供数据层自动隔离，该文件中的封装是多余的。

2.  **审查并重构服务层代码**：
    *   检查 [`service/orders/order.go`](service/orders/order.go) 以及其他模块的服务层代码。
    *   移除那些为了手动处理租户隔离而添加的、现在已被GORM插件自动处理所覆盖的 `tenantID` 参数和相关过滤逻辑。
    *   目标是简化服务层代码，使其不感知底层的租户隔离细节（除非有特殊业务需求）。

3.  **检查并移除冗余的GORM Scopes**：
    *   [`global/tenant_context.go`](global/tenant_context.go) 中定义的 `TenantScope` 和 `AutoTenantScope` 可能是早期或辅助性的实现。
    *   在GORM插件稳定工作的前提下，评估这些手动Scope的必要性，如果不再需要，则考虑移除或标记为废弃，以避免混淆。

### 3.4. 文档更新

*   更新项目架构文档，准确描述多租户的实现机制，特别是中间件的应用顺序、租户上下文的建立和传播方式，以及GORM插件的角色。
*   修正 `docs/gorm_tenant_plugin_analysis.md` 中可能存在的与实际中间件应用不符的描述。

## 4. 风险评估

*   **核心修复的风险**：低。修改中间件顺序是标准操作，只要选择正确的租户中间件并正确应用，应能直接解决问题。需注意测试不同模式（单机/分布式）下的行为。
*   **后续重构的风险**：中。移除代码和修改服务层参数需要仔细的代码审查和充分的测试，以确保不引入新的bug或破坏现有功能。

## 5. 实施步骤概要

1.  **阶段1：核心修复**
    *   确定项目运行模式判断逻辑 (`global.IsDistributedMode()`)。
    *   在 [`initialize/router.go`](initialize/router.go) 中为所有相关的私有路由组应用正确的租户中间件。
    *   进行集成测试，确保登录、租户数据访问正常。
2.  **阶段2：冗余代码移除与服务层重构**
    *   执行 [`service/orders/order_tenant.go`](service/orders/order_tenant.go) 的移除。
    *   逐步审查其他服务模块，重构租户相关逻辑。
    *   单元测试和集成测试。
3.  **阶段3：文档完善**
    *   更新所有相关文档。

此计划旨在恢复并加强项目的多租户隔离能力，同时提升代码的简洁性和可维护性。