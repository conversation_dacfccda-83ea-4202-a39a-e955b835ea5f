# 多租户系统数据库索引优化方案

## 概述

为了提升多租户系统的查询性能，需要为租户相关字段添加合适的复合索引。本文档详细说明了索引优化的策略和具体实施方案。

## 索引优化原则

### 1. 租户隔离优先
- 所有租户表的查询都应该首先按 `tenant_id` 过滤
- `tenant_id` 应该是复合索引的第一个字段

### 2. 常用查询模式分析
- 按时间范围查询（created_at, updated_at）
- 按状态查询（status, state）
- 按关联ID查询（user_id, order_id, product_id等）

### 3. 索引设计策略
- 高选择性字段在前
- 范围查询字段在后
- 避免过多的复合索引

## 具体索引方案

### 订单表 (order)

#### 当前问题
- 缺少 `tenant_id` 相关的复合索引
- 按时间范围查询性能较差
- 按状态查询缺少优化

#### 推荐索引

```sql
-- 1. 租户+时间复合索引（最常用）
CREATE INDEX idx_order_tenant_created ON `order` (tenant_id, created_at DESC);

-- 2. 租户+状态+时间复合索引
CREATE INDEX idx_order_tenant_status_created ON `order` (tenant_id, status, created_at DESC);

-- 3. 租户+用户+时间复合索引
CREATE INDEX idx_order_tenant_user_created ON `order` (tenant_id, franchisee_id, created_at DESC);

-- 4. 租户+订单号索引（精确查询）
CREATE INDEX idx_order_tenant_no ON `order` (tenant_id, order_no);

-- 5. 租户+更新时间索引（同步场景）
CREATE INDEX idx_order_tenant_updated ON `order` (tenant_id, updated_at DESC);
```

### 订单商品表 (order_goods)

```sql
-- 1. 租户+订单ID复合索引
CREATE INDEX idx_order_goods_tenant_order ON order_goods (tenant_id, order_id);

-- 2. 租户+产品ID复合索引
CREATE INDEX idx_order_goods_tenant_product ON order_goods (tenant_id, product_id);

-- 3. 租户+时间复合索引
CREATE INDEX idx_order_goods_tenant_created ON order_goods (tenant_id, created_at DESC);
```

### 产品表 (products)

```sql
-- 1. 租户+分类+状态复合索引
CREATE INDEX idx_products_tenant_category_status ON products (tenant_id, category_id, status);

-- 2. 租户+品牌+状态复合索引
CREATE INDEX idx_products_tenant_brand_status ON products (tenant_id, brand_id, status);

-- 3. 租户+时间复合索引
CREATE INDEX idx_products_tenant_created ON products (tenant_id, created_at DESC);

-- 4. 租户+名称索引（搜索场景）
CREATE INDEX idx_products_tenant_name ON products (tenant_id, name);
```

### 加盟商表 (franchisees)

```sql
-- 1. 租户+状态+时间复合索引
CREATE INDEX idx_franchisees_tenant_status_created ON franchisees (tenant_id, status, created_at DESC);

-- 2. 租户+地区复合索引
CREATE INDEX idx_franchisees_tenant_region ON franchisees (tenant_id, province, city);

-- 3. 租户+分类复合索引
CREATE INDEX idx_franchisees_tenant_category ON franchisees (tenant_id, category_id);
```

### 订单配送表 (order_delivery)

```sql
-- 1. 租户+订单号复合索引
CREATE INDEX idx_order_delivery_tenant_order_no ON order_delivery (tenant_id, order_no);

-- 2. 租户+状态+时间复合索引
CREATE INDEX idx_order_delivery_tenant_status_created ON order_delivery (tenant_id, status, created_at DESC);

-- 3. 租户+配送员复合索引
CREATE INDEX idx_order_delivery_tenant_deliverer ON order_delivery (tenant_id, deliverer_id);
```

## 索引监控和维护

### 1. 索引使用情况监控

```sql
-- 查看索引使用统计
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    SEQ_IN_INDEX,
    COLUMN_NAME,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'your_database_name' 
    AND TABLE_NAME IN ('order', 'order_goods', 'products', 'franchisees')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
```

### 2. 慢查询分析

```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- 分析慢查询
SELECT 
    query_time,
    lock_time,
    rows_sent,
    rows_examined,
    sql_text
FROM mysql.slow_log 
WHERE sql_text LIKE '%tenant_id%'
ORDER BY query_time DESC
LIMIT 10;
```

### 3. 索引效果评估

```sql
-- 查看表的索引统计信息
SHOW INDEX FROM `order`;

-- 分析查询执行计划
EXPLAIN SELECT * FROM `order` 
WHERE tenant_id = 1 
    AND created_at >= '2024-01-01' 
    AND created_at < '2024-02-01'
ORDER BY created_at DESC;
```

## 实施计划

### 阶段一：核心表索引（立即执行）
1. 订单表 (order) - 最高优先级
2. 订单商品表 (order_goods)
3. 产品表 (products)

### 阶段二：扩展表索引（1周内）
1. 加盟商表 (franchisees)
2. 订单配送表 (order_delivery)
3. 其他业务表

### 阶段三：监控和优化（持续进行）
1. 部署索引使用监控
2. 分析慢查询日志
3. 根据实际使用情况调整索引

## 注意事项

### 1. 索引维护成本
- 每个索引都会增加写操作的开销
- 需要额外的存储空间
- 定期维护和重建索引

### 2. 索引选择性
- 确保 `tenant_id` 有足够的选择性
- 避免在低选择性字段上创建索引

### 3. 查询模式变化
- 定期审查查询模式
- 根据业务发展调整索引策略
- 删除不再使用的索引

## 性能预期

### 查询性能提升
- 租户隔离查询：提升 80-90%
- 时间范围查询：提升 70-80%
- 复合条件查询：提升 60-70%

### 存储开销
- 索引存储空间：增加 20-30%
- 写操作性能：下降 5-10%

## 监控指标

### 关键指标
1. 平均查询响应时间
2. 慢查询数量和比例
3. 索引命中率
4. 数据库CPU和IO使用率

### 告警阈值
- 平均查询时间 > 100ms
- 慢查询比例 > 5%
- 索引未命中的查询 > 1%

## 总结

通过实施这套索引优化方案，预期可以显著提升多租户系统的查询性能，特别是在租户隔离场景下的查询效率。同时需要建立完善的监控体系，确保索引策略能够随着业务发展持续优化。
