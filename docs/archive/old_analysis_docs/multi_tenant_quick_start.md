# 多租户系统快速启动指南

## 🚀 5分钟快速部署

### **前提条件**
- MySQL 5.7+ 数据库
- Go 1.19+ 环境
- Redis（可选）

### **第一步：数据库迁移**
```bash
# 1. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 执行迁移
mysql -u username -p database_name < sql/add_tenant_id_to_franchisee.sql

# 3. 验证迁移
mysql -u username -p database_name -e "DESCRIBE franchisee;" | grep tenant_id
```

### **第二步：启动应用**
```bash
# 1. 编译应用
go build -o guanpu-server main.go

# 2. 启动服务
./guanpu-server

# 3. 验证启动
curl http://localhost:8888/health
```

### **第三步：测试功能**
```bash
# 测试登录API
curl -X POST http://localhost:8888/app/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password", 
    "captcha": "1234",
    "captchaId": "test"
  }'
```

## 📋 核心API接口

### **1. 加盟商登录**
```http
POST /app/auth/login
Content-Type: application/json

{
  "username": "用户名",
  "password": "密码",
  "captcha": "验证码",
  "captchaId": "验证码ID"
}
```

**响应（单租户）：**
```json
{
  "code": 0,
  "data": {
    "token": "jwt_token_here",
    "user": {...},
    "tenant": {...},
    "franchisee": {...}
  },
  "msg": "登录成功"
}
```

**响应（多租户）：**
```json
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [...],
    "tempToken": "temp_jwt_token"
  },
  "msg": "请选择要登录的租户"
}
```

### **2. 确认租户登录**
```http
POST /app/auth/confirm-tenant
Content-Type: application/json

{
  "tempToken": "临时令牌",
  "tenantId": 租户ID
}
```

### **3. 切换租户**
```http
POST /app/auth/switch-tenant
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "tenantId": 新租户ID
}
```

### **4. 获取我的租户列表**
```http
GET /app/auth/my-tenants
Authorization: Bearer jwt_token
```

## 🔧 配置说明

### **JWT配置**
```yaml
jwt:
  signing-key: your_secret_key
  expires-time: 7d
  buffer-time: 1d
  issuer: guanpu-server
```

### **数据库配置**
```yaml
mysql:
  path: 127.0.0.1
  port: "3306"
  config: charset=utf8mb4&parseTime=True&loc=Local
  db-name: your_database
  username: your_username
  password: your_password
```

## 🛠️ 开发指南

### **获取当前租户信息**
```go
// 在中间件或API中获取租户信息
tenantID := utils.GetTenantID(c)
franchiseeID := utils.GetFranchiseeID(c)
tenantCode := utils.GetTenantCode(c)
```

### **使用租户访问中间件**
```go
// 在路由中使用TenantAccessMiddleware
router.Group("api").Use(middleware.TenantAccessMiddleware()).{
    // 这里的所有路由都会自动验证加盟商身份
    router.GET("data", handler.GetData)
}
```

### **跳过租户过滤**
```go
// 在需要跨租户查询时使用
var users []User
global.GVA_DB.Scopes(tenant.SkipTenant).Find(&users)
```

## 🔍 故障排除

### **常见问题**

#### **1. 登录失败：租户信息获取失败**
- 检查加盟商表是否有tenant_id字段
- 确认用户在对应租户中有加盟商身份

#### **2. 数据查询为空**
- 检查GORM租户插件是否正确注册
- 确认当前请求的租户上下文是否正确设置

#### **3. JWT令牌解析失败**
- 检查JWT配置是否正确
- 确认令牌格式是否包含新增的字段

### **调试命令**
```bash
# 检查数据库结构
mysql -u username -p -e "DESCRIBE franchisee;" database_name

# 检查租户数据
mysql -u username -p -e "SELECT tenant_id, COUNT(*) FROM franchisee GROUP BY tenant_id;" database_name

# 检查应用日志
tail -f logs/server.log | grep -E "(租户|tenant|franchisee)"
```

## 📊 监控指标

### **关键指标**
- 登录成功率：> 99%
- API响应时间：< 500ms
- 数据库查询时间：< 100ms
- 内存使用：稳定增长

### **监控命令**
```bash
# 检查应用状态
curl http://localhost:8888/health

# 检查数据库连接
mysql -u username -p -e "SHOW PROCESSLIST;" database_name

# 检查系统资源
top -p $(pgrep guanpu-server)
```

## 🔐 安全注意事项

### **生产环境配置**
1. **JWT密钥**：使用强随机密钥
2. **数据库权限**：使用最小权限原则
3. **HTTPS**：启用SSL/TLS加密
4. **日志安全**：避免记录敏感信息

### **安全检查清单**
- [ ] JWT密钥足够复杂
- [ ] 数据库连接加密
- [ ] API接口启用HTTPS
- [ ] 敏感数据不在日志中
- [ ] 跨租户访问被正确阻止

## 📚 相关文档

- [多租户详细技术设计](multi_tenant_detailed_design.md)
- [加盟商多租户登录设计](franchisee_multi_tenant_login_design.md)
- [多租户系统使用指南](multi_tenant_usage_guide.md)
- [多租户API文档](multi_tenant_api_documentation.md)
- [部署检查清单](multi_tenant_deployment_checklist.md)

## 🆘 获取帮助

### **技术支持**
- 查看详细文档：[文档中心](README.md)
- 检查实施进展：[架构Review报告](multi_tenant_architecture_review_report.md)
- 问题排查：[部署检查清单](multi_tenant_deployment_checklist.md)

### **常用资源**
- 数据库迁移脚本：`sql/add_tenant_id_to_franchisee.sql`
- 单元测试：`test/multi_tenant_test.go`
- 配置示例：`config.yaml`

---

**快速启动指南版本**: v1.0  
**最后更新**: 2024-12-25  
**适用系统**: 多租户系统 v2.0（基于加盟商1:1关系）

🎉 **恭喜！您已成功部署多租户系统！**
