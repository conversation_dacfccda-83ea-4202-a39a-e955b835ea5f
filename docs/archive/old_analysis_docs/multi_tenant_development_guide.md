# 多租户系统开发指南

## 概述

本指南详细介绍了如何在我们的系统中进行多租户开发，包括最佳实践、常见陷阱和解决方案。

## 目录

1. [架构概述](#架构概述)
2. [开发规范](#开发规范)
3. [数据模型设计](#数据模型设计)
4. [API开发](#api开发)
5. [测试指南](#测试指南)
6. [部署和运维](#部署和运维)
7. [故障排查](#故障排查)
8. [最佳实践](#最佳实践)

## 架构概述

### 多租户隔离策略

我们采用**行级租户隔离**策略，通过在每个租户表中添加 `tenant_id` 字段来实现数据隔离。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   租户A数据     │    │   租户B数据     │    │   租户C数据     │
│ tenant_id = 1   │    │ tenant_id = 2   │    │ tenant_id = 3   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────────┐
                    │      共享数据库         │
                    │   (单一数据库实例)      │
                    └─────────────────────────┘
```

### 核心组件

1. **GORM租户插件** (`plugin/tenant/tenant_plugin.go`)
   - 自动为查询添加租户过滤条件
   - 自动为新记录设置租户ID

2. **租户中间件** (`middleware/unified_tenant.go`)
   - 验证租户有效性
   - 设置租户上下文

3. **租户上下文管理** (`global/tenant_context.go`)
   - 管理当前请求的租户信息
   - 提供租户验证功能

4. **安全审计系统** (`global/security_audit.go`)
   - 记录跨租户访问尝试
   - 安全事件告警

5. **监控系统** (`global/tenant_monitor.go`)
   - 性能监控
   - 健康状态检查

## 开发规范

### 1. 数据模型规范

#### 租户表模型

所有需要租户隔离的模型必须包含 `TenantID` 字段：

```go
type Order struct {
    ID        uint      `gorm:"primarykey" json:"id"`
    TenantID  uint      `gorm:"column:tenant_id;not null;index" json:"tenant_id"`
    OrderNo   string    `gorm:"column:order_no;not null" json:"order_no"`
    Status    string    `gorm:"column:status;not null" json:"status"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}

func (Order) TableName() string {
    return "order"
}
```

#### 非租户表模型

系统配置、用户管理等全局数据不需要租户隔离：

```go
type SysUser struct {
    ID       uint   `gorm:"primarykey" json:"id"`
    Username string `gorm:"column:username;not null" json:"username"`
    // 注意：没有 TenantID 字段
}
```

### 2. 自动租户表注册

在 `initialize/tenant_models.go` 中注册所有租户模型：

```go
func registerOrderModels() {
    // 注册订单模型
    global.RegisterTenantModel(orders.Order{})
    global.RegisterTenantModel(orders.OrderGoods{})
    // ... 其他模型
}
```

### 3. 数据库操作规范

#### 正确的查询方式

```go
// ✅ 正确：使用租户上下文
func GetOrdersByStatus(ctx context.Context, status string) ([]Order, error) {
    var orders []Order
    err := global.GVA_DB.WithContext(ctx).
        Where("status = ?", status).
        Find(&orders).Error
    return orders, err
}
```

#### 错误的查询方式

```go
// ❌ 错误：没有使用租户上下文
func GetOrdersByStatus(status string) ([]Order, error) {
    var orders []Order
    err := global.GVA_DB.Where("status = ?", status).Find(&orders).Error
    return orders, err
}
```

#### 跳过租户隔离（谨慎使用）

```go
// 超级管理员操作，跳过租户隔离
func GetAllOrdersForAdmin() ([]Order, error) {
    var orders []Order
    err := global.GVA_DB.Scopes(tenant.SkipTenant).Find(&orders).Error
    return orders, err
}
```

## API开发

### 1. 控制器开发

```go
func (o *OrderApi) GetOrders(c *gin.Context) {
    // 从上下文获取租户ID（中间件已设置）
    tenantID, exists := global.GetTenantFromContext(c.Request.Context())
    if !exists {
        response.FailWithMessage("租户信息缺失", c)
        return
    }
    
    // 使用带租户上下文的查询
    orders, err := orderService.GetOrdersByTenant(c.Request.Context(), tenantID)
    if err != nil {
        response.FailWithMessage("查询失败", c)
        return
    }
    
    response.OkWithData(orders, c)
}
```

### 2. 服务层开发

```go
func (o *OrderService) GetOrdersByTenant(ctx context.Context, tenantID uint) ([]Order, error) {
    var orders []Order
    
    // 使用租户上下文，GORM插件会自动添加租户过滤
    err := global.GVA_DB.WithContext(ctx).
        Where("status = ?", "active").
        Find(&orders).Error
        
    return orders, err
}
```

### 3. 路由配置

确保需要租户隔离的路由使用租户中间件：

```go
func InitOrderRouter(Router *gin.RouterGroup) {
    orderRouter := Router.Group("order").Use(middleware.UnifiedTenantMiddleware())
    {
        orderRouter.GET("list", orderApi.GetOrders)
        orderRouter.POST("create", orderApi.CreateOrder)
        // ...
    }
}
```

## 测试指南

### 1. 单元测试

```go
func TestOrderService_GetOrdersByTenant(t *testing.T) {
    // 设置测试租户上下文
    ctx := global.WithTenantContext(context.Background(), 1)
    
    // 创建测试数据
    testOrder := Order{
        TenantID: 1,
        OrderNo:  "TEST001",
        Status:   "active",
    }
    
    // 执行测试
    orders, err := orderService.GetOrdersByTenant(ctx, 1)
    assert.NoError(t, err)
    assert.NotEmpty(t, orders)
}
```

### 2. 租户隔离测试

```go
func TestTenantIsolation(t *testing.T) {
    // 创建不同租户的数据
    tenant1Order := Order{TenantID: 1, OrderNo: "T1-001"}
    tenant2Order := Order{TenantID: 2, OrderNo: "T2-001"}
    
    // 保存数据
    global.GVA_DB.Create(&tenant1Order)
    global.GVA_DB.Create(&tenant2Order)
    
    // 测试租户1只能看到自己的数据
    ctx1 := global.WithTenantContext(context.Background(), 1)
    var orders1 []Order
    global.GVA_DB.WithContext(ctx1).Find(&orders1)
    
    assert.Len(t, orders1, 1)
    assert.Equal(t, uint(1), orders1[0].TenantID)
}
```

## 部署和运维

### 1. 数据库索引

确保为租户相关字段创建合适的索引：

```sql
-- 租户+时间复合索引
CREATE INDEX idx_order_tenant_created ON `order` (tenant_id, created_at DESC);

-- 租户+状态复合索引
CREATE INDEX idx_order_tenant_status ON `order` (tenant_id, status);
```

### 2. 监控配置

配置租户系统监控：

```yaml
# 告警阈值配置
tenant_monitoring:
  thresholds:
    cross_tenant_attempt_rate: 0.01  # 1%
    failed_validation_rate: 0.05     # 5%
    avg_response_time_ms: 100        # 100ms
    cache_hit_rate_min: 0.8          # 80%
```

### 3. 健康检查

定期检查租户系统健康状态：

```bash
# 健康检查API
curl http://localhost:8080/api/tenant/health

# 监控指标API
curl http://localhost:8080/api/tenant/metrics
```

## 故障排查

### 1. 常见问题

#### 问题：查询返回了其他租户的数据

**原因**：
- 没有使用租户上下文
- 使用了跳过租户隔离的作用域

**解决方案**：
```go
// 确保使用租户上下文
ctx := global.WithTenantContext(context.Background(), tenantID)
db.WithContext(ctx).Find(&records)
```

#### 问题：新创建的记录没有租户ID

**原因**：
- 模型没有注册到租户系统
- 没有使用租户上下文

**解决方案**：
```go
// 1. 注册模型
global.RegisterTenantModel(YourModel{})

// 2. 使用租户上下文创建
ctx := global.WithTenantContext(context.Background(), tenantID)
db.WithContext(ctx).Create(&record)
```

### 2. 调试工具

#### 启用GORM日志

```go
global.GVA_DB = global.GVA_DB.Debug() // 显示SQL查询
```

#### 检查租户插件状态

```go
// 查看自动检测的租户表
tables := global.GetAutoDetectedTenantTables()
fmt.Printf("租户表: %+v\n", tables)

// 检查表是否为租户表
isTenantTable := global.IsTenantTable("order")
fmt.Printf("order表是否为租户表: %v\n", isTenantTable)
```

## 最佳实践

### 1. 安全原则

- **默认隔离**：所有业务数据默认应该是租户隔离的
- **最小权限**：只在必要时使用跳过租户隔离的功能
- **审计日志**：记录所有跨租户访问尝试

### 2. 性能优化

- **索引优化**：为租户相关查询创建复合索引
- **缓存策略**：使用租户缓存减少数据库查询
- **批量操作**：避免在循环中进行数据库操作

### 3. 代码质量

- **一致性**：统一使用租户上下文
- **可测试性**：编写充分的租户隔离测试
- **文档化**：为新的租户表添加文档说明

### 4. 监控和告警

- **实时监控**：监控租户隔离的执行情况
- **异常告警**：设置跨租户访问告警
- **性能监控**：监控租户相关查询的性能

## 总结

多租户开发需要严格遵循规范，确保数据安全和系统稳定。通过使用我们提供的工具和遵循最佳实践，可以有效避免常见问题并提高开发效率。

如有疑问，请参考相关代码示例或联系架构团队。

## 附录：快速参考

### 常用代码片段

#### 1. 创建租户模型
```go
type YourModel struct {
    ID       uint `gorm:"primarykey"`
    TenantID uint `gorm:"column:tenant_id;not null;index"`
    // 其他字段...
}

func (YourModel) TableName() string {
    return "your_table"
}
```

#### 2. 注册租户模型
```go
// 在 initialize/tenant_models.go 中
global.RegisterTenantModel(YourModel{})
```

#### 3. 租户查询
```go
func GetData(ctx context.Context) ([]YourModel, error) {
    var data []YourModel
    err := global.GVA_DB.WithContext(ctx).Find(&data).Error
    return data, err
}
```

#### 4. API控制器
```go
func (api *YourApi) GetData(c *gin.Context) {
    data, err := service.GetData(c.Request.Context())
    if err != nil {
        response.FailWithMessage("查询失败", c)
        return
    }
    response.OkWithData(data, c)
}
```

### 检查清单

开发新功能时，请确保：

- [ ] 模型包含 `TenantID` 字段
- [ ] 模型已注册到租户系统
- [ ] API使用租户中间件
- [ ] 查询使用租户上下文
- [ ] 编写租户隔离测试
- [ ] 添加必要的数据库索引
- [ ] 更新API文档
