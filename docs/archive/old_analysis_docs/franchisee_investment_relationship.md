# 招商加盟商关系及业绩计算说明文档

## 一、基础概念

1. **顶级加盟商**：
   - 定义：`f_category_id = 43` 的加盟商
   - 示例：王志建（馆主）(ID: 65)

2. **加盟商关系**：
   - `franchisee_link.top_franchisee_id`：表示直接客户关系
   - `franchisee.inviter_id`：表示邀请关系

## 二、一代招商加盟商

### 定义
一代招商加盟商是指：被当前加盟商的直接客户邀请的顶级加盟商。

### 判定条件
1. 必须是顶级加盟商（`f_category_id = 43`）
2. 邀请人（`inviter_id`）必须是当前加盟商的直接客户
3. 不能是当前加盟商的直接客户

### 案例说明
以加盟商"王志建（馆主）"(ID: 65)为例：

1. 王志建的直接客户：
```sql
SELECT franchisee_id FROM franchisee_link WHERE top_franchisee_id = 65;
```
结果包括：
- 张献峰（ID: 3215）
- 师伟荣（ID: 3686）
等40个直接客户

2. 一代招商加盟商：
```sql
WITH my_customers AS (
    SELECT franchisee_id FROM franchisee_link 
    WHERE top_franchisee_id = 65
)
SELECT * FROM franchisee 
WHERE inviter_id IN (SELECT franchisee_id FROM my_customers)
AND f_category_id = 43 
AND id NOT IN (SELECT franchisee_id FROM my_customers);
```
结果：
- 杨余三（馆主）：被65直接邀请
- 王忠义（馆主）：被张献峰(3215)邀请
- 胡幸涛（馆主）：被师伟荣(3686)邀请

## 三、二代招商加盟商

### 定义
二代招商加盟商是指：被一代招商加盟商的直接客户邀请的顶级加盟商。

### 判定条件
1. 必须是顶级加盟商（`f_category_id = 43`）
2. 邀请人（`inviter_id`）必须是一代招商加盟商的直接客户

### 案例说明
继续以王志建（馆主）为例：

1. 一代招商加盟商的直接客户：
```sql
WITH first_gen AS (
    -- 获取一代招商加盟商列表
    SELECT * FROM franchisee 
    WHERE franchisee.inviter_id IN (
        SELECT franchisee_id FROM franchisee_link 
        WHERE franchisee_link.top_franchisee_id = 65
    ) 
    AND franchisee.id NOT IN (
        SELECT franchisee_id FROM franchisee_link 
        WHERE franchisee_link.top_franchisee_id = 65
    ) 
    AND franchisee.f_category_id = 43
),
first_gen_customers AS (
    -- 获取一代招商加盟商的客户
    SELECT franchisee_link.franchisee_id 
    FROM franchisee_link 
    WHERE franchisee_link.top_franchisee_id IN (
        SELECT id FROM first_gen
    )
) 
-- 获取二代招商加盟商
SELECT f.* from franchisee f 
WHERE f.inviter_id IN (
    SELECT franchisee_id FROM first_gen_customers
) 
AND f.f_category_id = 43;
```

2. 二代招商加盟商：
```sql
-- 被一代招商加盟商的客户邀请的顶级加盟商
SELECT * FROM franchisee 
WHERE inviter_id IN (
    SELECT franchisee_id FROM first_gen_customers
)
AND f_category_id = 43;
```
结果：
- 朱立松（馆主）：被ID 5162邀请
- 刘志民（馆主）：被胡幸涛邀请
- 崔营（馆主）：被ID 874邀请

## 四、业绩计算规则

### 4.1 业绩范围

1. **专区限制**：
   - 只计算 `special_mall.calc_performance = 1` 的专区业绩
   - 当前计算业绩的专区包括：
     * 团购商城（ID: 5）
     * 店商商城（ID: 6）
     * 分销商城（ID: 7）

2. **加盟商范围**：
   - 二代招商加盟商本人的业绩
   - 二代招商加盟商的所有客户（`franchisee_link.top_franchisee_id`）的业绩

### 4.2 计算示例

#### 案例一：王志建（馆主）的二代招商业绩

以加盟商"王志建（馆主）"(ID: 65)为例，2024年12月的二代招商业绩计算：

1. **朱立松（馆主）团队**：
   - 朱立松本人：313,200（分销商城）
   - 朱利阳：78,600（分销商城）
   - 赵杰：960,000（分销商城）
   - 团队总业绩：1,351,800

2. **刘志民（馆主）团队**：
   - 刘志民本人：540,000（团购商城）
   - 刘春荣：809,400（分销商城）
   - 刘忠常：649,000（分销商城）
   - 刘志启：794,600（分销商城）
   - 团队总业绩：2,793,000

总二代招商业绩：4,144,800

#### 案例二：唐山市两位加盟商的二代招商业绩对比

对比分析唐山市两位加盟商2024年11月的二代招商业绩：

1. **刘丽珠/菅志云（祥云商会秘书长）**
   - ID: 4626
   - 总业绩：908,000
   - 业绩构成（分销商城）：
     * 卢海臣（馆主）：252,000
     * 程冠雄：67,200
     * 刘焕文：588,800

2. **徐国刚（戎达商会秘书长）**
   - ID: 4706
   - 总业绩：2,004,000
   - 业绩构成（分销商城）：
     * 骆兴华：120,000
     * 韩福乐：920,000
     * 杜跃进：508,000
     * 孙大川（馆主）：336,000
     * 王雪琪（零售代理商）：120,000

3. **对比分析**：
   - 业绩总量：徐国刚（2,004,000）显著高于刘丽珠/菅志云（908,000）
   - 客户数量：徐国刚（5个）多于刘丽珠/菅志云（3个）
   - 业绩分布：
     * 徐国刚的业绩来源更分散，体现了更好的客户管理
     * 刘丽珠/菅志云的业绩主要集中在刘焕文（占比64.8%）
   - 单客户最高业绩：
     * 徐国刚团队：韩福乐（920,000）
     * 刘丽珠/菅志云团队：刘焕文（588,800）
   - 专区分布：两位加盟商的业绩均来自分销商城，未在其他专区产生业绩

### 4.3 SQL实现

```sql
WITH second_gen_and_customers AS (
    WITH second_gen AS (
        WITH first_gen AS (
            -- 获取一代招商加盟商
            SELECT * from franchisee 
            WHERE franchisee.inviter_id IN (
                SELECT franchisee_link.franchisee_id 
                FROM franchisee_link 
                WHERE franchisee_link.top_franchisee_id = 65
            ) 
            AND franchisee.id NOT IN (
                SELECT franchisee_link.franchisee_id 
                FROM franchisee_link 
                WHERE franchisee_link.top_franchisee_id = 65
            ) 
            AND franchisee.f_category_id = 43
        ),
        first_gen_customers AS (
            -- 获取一代招商加盟商的客户
            SELECT franchisee_link.franchisee_id 
            FROM franchisee_link 
            WHERE franchisee_link.top_franchisee_id IN (
                SELECT id FROM first_gen
            )
        ) 
        -- 获取二代招商加盟商
        SELECT f.* from franchisee f 
        WHERE f.inviter_id IN (
            SELECT franchisee_id FROM first_gen_customers
        ) 
        AND f.f_category_id = 43
    ) 
    -- 获取二代招商加盟商的所有客户
    SELECT DISTINCT fl.franchisee_id 
    FROM franchisee_link fl 
    WHERE fl.top_franchisee_id IN (
        SELECT id FROM second_gen
    )
) 
-- 计算业绩
SELECT SUM(fp.performance) as total_performance 
FROM franchisee_performance fp 
WHERE fp.franchisee_id IN (
    SELECT franchisee_id FROM second_gen_and_customers
) 
AND fp.special_mall_id IN (
    SELECT id FROM special_mall WHERE calc_performance = 1
) 
AND fp.date >= '2024-12-01' 
AND fp.date <= '2024-12-31';
```

## 五、业务代码实现

```go
func GetTopFranchiseesByGenerations(franchisee) (firstGenIDs, secondGenIDs []uint) {
    // 1. 获取一代招商加盟商
    firstGenIDs = GetMyTopFranchiseeIDs(franchisee.ID)
    
    // 2. 获取二代招商加盟商
    if len(firstGenIDs) > 0 {
        secondGenIDs = BatchGetMyTopFranchiseeIDs(firstGenIDs)
        // 过滤掉已经在一代中的加盟商
        secondGenIDs = filter(secondGenIDs, notIn(firstGenIDs))
    }
    return
}
```

## 六、注意事项

1. 招商关系判定以 `franchisee.inviter_id` 为准，不使用 `franchisee_link.inviter_id`
2. 直接客户关系以 `franchisee_link.top_franchisee_id` 为准
3. 只有顶级加盟商（`f_category_id = 43`）才会被计入招商业绩
4. 二代招商加盟商的计算需要先确定一代招商加盟商列表
