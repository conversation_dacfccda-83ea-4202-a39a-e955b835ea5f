# 加盟商租户关系系统部署检查清单

## 📋 **部署前准备**

### **1. 数据库备份**
- [ ] 备份生产数据库
- [ ] 验证备份文件完整性
- [ ] 确认回滚方案

### **2. 环境检查**
- [ ] 确认Go版本兼容性 (≥ 1.19)
- [ ] 确认MySQL版本 (≥ 8.0)
- [ ] 检查磁盘空间充足
- [ ] 验证网络连接正常

### **3. 代码审查**
- [ ] 代码已通过所有单元测试
- [ ] 代码已通过集成测试
- [ ] 代码已通过安全审查
- [ ] 所有TODO和FIXME已处理

## 🗃️ **数据库迁移**

### **1. 执行SQL脚本**
```bash
# 1. 连接数据库
mysql -u root -p guanpu_db

# 2. 执行创建脚本
source docs/sql/create_franchisee_tenant_relation.sql

# 3. 验证表结构
DESCRIBE franchisee_tenant_relation;

# 4. 检查数据完整性
SELECT COUNT(*) FROM franchisee_tenant_relation;
```

### **2. 数据迁移验证**
- [ ] `franchisee_tenant_relation` 表创建成功
- [ ] 默认租户创建成功
- [ ] 所有现有加盟商都有租户关联
- [ ] 唯一约束正常工作
- [ ] 索引创建成功

### **3. 数据一致性检查**
```sql
-- 检查孤立的加盟商（没有租户关联）
SELECT f.id, f.name 
FROM franchisee f 
LEFT JOIN franchisee_tenant_relation ftr ON f.id = ftr.franchisee_id 
WHERE ftr.franchisee_id IS NULL;

-- 检查重复的默认租户设置
SELECT franchisee_id, COUNT(*) as default_count
FROM franchisee_tenant_relation 
WHERE is_default = 1 
GROUP BY franchisee_id 
HAVING COUNT(*) > 1;

-- 检查数据分布
SELECT status, COUNT(*) FROM franchisee_tenant_relation GROUP BY status;
SELECT role, COUNT(*) FROM franchisee_tenant_relation GROUP BY role;
```

## 🚀 **应用部署**

### **1. 代码部署**
- [ ] 停止应用服务
- [ ] 备份当前版本
- [ ] 部署新版本代码
- [ ] 更新配置文件
- [ ] 启动应用服务

### **2. 自动迁移执行**
- [ ] 应用启动时自动迁移执行成功
- [ ] 检查应用日志无错误
- [ ] 验证所有服务正常启动

### **3. 服务健康检查**
```bash
# 检查应用状态
curl -f http://localhost:8888/health

# 检查数据库连接
curl -f http://localhost:8888/api/health/db

# 检查关键API
curl -f http://localhost:8888/base/login
```

## 🧪 **功能验证**

### **1. 普通用户登录测试**
```bash
# 测试管理员登录
curl -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```
- [ ] 管理员登录成功
- [ ] JWT生成正常
- [ ] 响应格式正确

### **2. 单租户加盟商登录测试**
```bash
# 测试单租户加盟商登录
curl -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "13800138001",
    "password": "password123"
  }'
```
- [ ] 加盟商登录成功
- [ ] 自动识别为加盟商
- [ ] 返回租户ID
- [ ] JWT包含租户信息

### **3. 多租户加盟商登录测试**
```bash
# 测试多租户加盟商登录（默认租户）
curl -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "13800138002",
    "password": "password123"
  }'

# 测试指定租户登录
curl -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "13800138002",
    "password": "password123",
    "tenantCode": "tenant_a"
  }'
```
- [ ] 默认租户登录成功
- [ ] 指定租户登录成功
- [ ] 租户权限验证正常
- [ ] JWT租户信息正确

### **4. 租户数据隔离测试**
```bash
# 获取不同租户的token并测试数据访问
TOKEN1=$(curl -s -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{"username":"13800138002","password":"password123","tenantCode":"default"}' \
  | jq -r '.data.token')

TOKEN2=$(curl -s -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{"username":"13800138002","password":"password123","tenantCode":"tenant_a"}' \
  | jq -r '.data.token')

# 测试数据隔离
curl -H "Authorization: Bearer $TOKEN1" http://localhost:8888/franchisee/getFranchiseeList
curl -H "Authorization: Bearer $TOKEN2" http://localhost:8888/franchisee/getFranchiseeList
```
- [ ] 不同租户返回不同数据
- [ ] 数据完全隔离
- [ ] 无跨租户数据泄露

## ❌ **错误场景验证**

### **1. 异常登录测试**
- [ ] 非加盟商用户登录正常（走普通用户流程）
- [ ] 无租户关联加盟商登录失败
- [ ] 指定不存在租户登录失败
- [ ] 指定无权限租户登录失败
- [ ] 停用用户登录失败

### **2. 边界条件测试**
- [ ] 空用户名/密码处理正确
- [ ] 特殊字符处理正确
- [ ] 超长输入处理正确
- [ ] 并发登录处理正确

## 📊 **性能验证**

### **1. 响应时间测试**
```bash
# 测试登录响应时间
time curl -X POST http://localhost:8888/base/login \
  -H "Content-Type: application/json" \
  -d '{"username":"13800138001","password":"password123"}'
```
- [ ] 登录响应时间 < 500ms
- [ ] 租户查询响应时间 < 200ms
- [ ] JWT生成时间 < 100ms

### **2. 并发测试**
```bash
# 使用ab工具进行并发测试
ab -n 1000 -c 10 -p login_data.json -T application/json http://localhost:8888/base/login
```
- [ ] 并发登录处理正常
- [ ] 无死锁或竞态条件
- [ ] 系统资源使用正常

## 🔒 **安全验证**

### **1. JWT安全性**
- [ ] JWT包含正确的租户信息
- [ ] JWT签名验证正常
- [ ] JWT过期时间设置正确
- [ ] 不同租户JWT不能互用

### **2. 权限验证**
- [ ] 租户权限验证正常
- [ ] 跨租户访问被阻止
- [ ] 角色权限正确应用
- [ ] 敏感信息不泄露

## 📝 **监控配置**

### **1. 日志监控**
- [ ] 登录成功/失败日志
- [ ] 租户切换日志
- [ ] 权限验证日志
- [ ] 错误异常日志

### **2. 指标监控**
- [ ] 登录成功率
- [ ] 登录响应时间
- [ ] 租户分布统计
- [ ] 错误率统计

### **3. 告警配置**
- [ ] 登录失败率过高告警
- [ ] 响应时间过长告警
- [ ] 数据库连接异常告警
- [ ] 系统资源异常告警

## 🔄 **回滚方案**

### **1. 应用回滚**
- [ ] 准备回滚脚本
- [ ] 验证回滚流程
- [ ] 确认回滚时间窗口
- [ ] 通知相关人员

### **2. 数据回滚**
- [ ] 准备数据回滚脚本
- [ ] 验证数据一致性
- [ ] 确认数据恢复方案
- [ ] 测试回滚流程

## ✅ **部署完成确认**

### **1. 功能确认**
- [ ] 所有核心功能正常
- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 安全验证通过

### **2. 文档更新**
- [ ] 更新API文档
- [ ] 更新部署文档
- [ ] 更新运维手册
- [ ] 更新用户手册

### **3. 团队通知**
- [ ] 通知开发团队
- [ ] 通知测试团队
- [ ] 通知运维团队
- [ ] 通知产品团队

## 📞 **应急联系**

### **关键人员**
- **技术负责人**: [姓名] - [电话] - [邮箱]
- **数据库管理员**: [姓名] - [电话] - [邮箱]
- **运维负责人**: [姓名] - [电话] - [邮箱]
- **产品负责人**: [姓名] - [电话] - [邮箱]

### **应急流程**
1. 发现问题立即评估影响范围
2. 如果影响用户使用，立即启动回滚
3. 通知相关人员并记录问题
4. 分析问题原因并制定修复方案
5. 验证修复方案并重新部署

---

**部署负责人**: _________________ **日期**: _________

**审核人**: _________________ **日期**: _________
