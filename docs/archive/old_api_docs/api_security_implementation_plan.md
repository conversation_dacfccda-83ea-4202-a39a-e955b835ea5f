# API安全和合并实施计划

## 🎯 目标概述

基于对现有API端点的分析，我们发现了以下关键问题：
1. **接口重复**：用户和租户管理存在多个功能相似的接口
2. **安全漏洞**：缺乏租户隔离，用户可传入任意租户ID
3. **权限混乱**：不同权限级别的接口缺乏统一设计

## 📊 当前架构问题分析

```mermaid
graph TD
    A[客户端请求] --> B{当前API设计}
    B --> C[/user/getUserList]
    B --> D[/user/getUserListForBusiness]
    B --> E[/super/users]
    B --> F[/tenant/getTenantList]
    B --> G[/super/tenants]
    
    C --> H[问题1: 功能重复]
    D --> H
    E --> H
    F --> I[问题2: 权限混乱]
    G --> I
    
    H --> J[维护困难]
    I --> K[安全风险]
    
    style H fill:#ffcccc
    style I fill:#ffcccc
    style J fill:#ff9999
    style K fill:#ff6666
```

## 🏗️ 目标架构设计

```mermaid
graph TD
    A[客户端请求] --> B[统一API网关]
    B --> C[租户隔离中间件]
    C --> D[权限验证中间件]
    D --> E[参数安全检查]
    E --> F{用户类型}
    
    F -->|超级管理员| G[跨租户操作]
    F -->|普通用户| H[租户内操作]
    
    G --> I[统一用户管理API]
    G --> J[统一租户管理API]
    H --> I
    
    I --> K[自动租户过滤]
    J --> K
    K --> L[数据库操作]
    
    style C fill:#ccffcc
    style D fill:#ccffcc
    style E fill:#ccffcc
    style K fill:#ccffcc
```

## 🔧 实施步骤

### 阶段一：安全基础设施（第1-2周）

#### 1.1 增强JWT Claims结构
```go
type CustomClaims struct {
    BaseClaims
    UserID       uint   `json:"userId"`
    TenantID     uint   `json:"tenantId"`
    IsSuperAdmin bool   `json:"isSuperAdmin"`
    Permissions  []string `json:"permissions"`
    jwt.RegisteredClaims
}
```

#### 1.2 创建租户隔离中间件
```mermaid
sequenceDiagram
    participant C as 客户端
    participant M as 租户隔离中间件
    participant A as API处理器
    participant D as 数据库
    
    C->>M: 发送请求
    M->>M: 验证JWT Token
    M->>M: 提取租户ID
    M->>M: 检查权限
    alt 超级管理员
        M->>A: 允许跨租户访问
    else 普通用户
        M->>M: 设置租户过滤
        M->>A: 限制租户内访问
    end
    A->>D: 执行数据库操作
    D->>A: 返回结果
    A->>C: 返回响应
```

### 阶段二：API接口合并（第3-4周）

#### 2.1 用户管理接口统一

**合并前：**
- `GET /user/getUserList`
- `GET /user/getUserListForBusiness` 
- `GET /super/users`

**合并后：**
- `POST /api/v1/users/list`

```mermaid
graph LR
    A[统一用户列表API] --> B{权限检查}
    B -->|超级管理员| C[查看所有租户用户]
    B -->|普通管理员| D[查看当前租户用户]
    B -->|普通用户| E[查看有限用户信息]
    
    C --> F[应用业务过滤]
    D --> F
    E --> F
    F --> G[返回结果]
```

#### 2.2 租户管理接口保持分离

**调整策略：**
- 保持超级管理员和普通租户管理的独立API路径
- 统一安全标准和参数验证机制

**普通租户管理：** `/api/v1/tenant/*`
- `GET /tenant/my-tenants` - 获取用户关联的租户列表
- `GET /tenant/current` - 获取当前租户信息
- `POST /tenant/switch/:id` - 切换租户

**超级管理员租户管理：** `/api/v1/super/*`
- `GET /super/tenants` - 获取所有租户列表（跨租户）
- `POST /super/tenants` - 创建租户
- `PUT /super/tenants/:id` - 更新任意租户
- `DELETE /super/tenants/:id` - 删除租户

```mermaid
graph TD
    A[租户管理请求] --> B{用户类型}
    B -->|普通用户| C[/api/v1/tenant/*]
    B -->|超级管理员| D[/api/v1/super/*]
    
    C --> E[租户内操作]
    D --> F[跨租户操作]
    
    E --> G[查看关联租户]
    E --> H[切换租户]
    
    F --> I[管理所有租户]
    F --> J[系统级操作]
    
    style C fill:#e1f5fe
    style D fill:#fff3e0
```

### 阶段三：参数安全加固（第5周）

#### 3.1 自动参数注入机制
```mermaid
flowchart TD
    A[接收请求参数] --> B{用户类型}
    B -->|超级管理员| C[保持原参数]
    B -->|普通用户| D[自动注入租户ID]
    
    D --> E[检查参数冲突]
    E -->|有冲突| F[返回权限错误]
    E -->|无冲突| G[继续处理]
    C --> G
    G --> H[执行业务逻辑]
```

#### 3.2 参数验证规则
```go
type SecurityRule struct {
    Field       string
    Required    bool
    AutoInject  bool
    ValidateFunc func(value interface{}, claims *CustomClaims) error
}

var UserListSecurityRules = []SecurityRule{
    {
        Field:      "TenantID",
        AutoInject: true,
        ValidateFunc: func(value interface{}, claims *CustomClaims) error {
            if !claims.IsSuperAdmin && value != nil {
                tenantID := value.(uint)
                if tenantID != claims.TenantID {
                    return errors.New("无权限访问其他租户数据")
                }
            }
            return nil
        },
    },
}
```

## 📋 详细实施清单

### 高优先级任务（立即开始）

- [ ] **创建租户隔离中间件**
  - [ ] 实现JWT验证增强
  - [ ] 添加租户ID自动提取
  - [ ] 实现权限检查逻辑
  
- [ ] **修复现有安全漏洞**
  - [ ] 审查所有接受租户ID参数的接口
  - [ ] 添加参数验证逻辑
  - [ ] 实现自动租户ID注入

- [ ] **统一用户管理接口**
  - [ ] 合并三个用户列表接口
  - [ ] 实现统一的权限控制
  - [ ] 添加业务类型过滤

### 中优先级任务（2-3周内完成）

- [ ] **优化租户管理接口**
  - [ ] 保持超级管理员和普通租户管理的独立路径
  - [ ] 统一两套接口的安全标准和参数验证
  - [ ] 增强超级管理员专用功能
  - [ ] 添加租户状态管理和监控

- [ ] **重构路由结构**
  - [ ] 设计新的路由层次结构
  - [ ] 实现向后兼容
  - [ ] 更新API文档

- [ ] **增强操作日志**
  - [ ] 记录所有敏感操作
  - [ ] 实现跨租户操作审计
  - [ ] 添加安全事件监控

### 低优先级任务（长期规划）

- [ ] **性能优化**
  - [ ] 实现查询缓存
  - [ ] 优化数据库索引
  - [ ] 添加分页优化

- [ ] **监控和告警**
  - [ ] 实现API调用监控
  - [ ] 添加异常访问告警
  - [ ] 创建安全仪表板

## 🧪 测试策略

### 安全测试用例

```mermaid
graph TD
    A[安全测试] --> B[权限测试]
    A --> C[参数注入测试]
    A --> D[跨租户访问测试]
    
    B --> B1[超级管理员权限]
    B --> B2[普通管理员权限]
    B --> B3[普通用户权限]
    
    C --> C1[自动租户ID注入]
    C --> C2[参数冲突检测]
    C --> C3[恶意参数过滤]
    
    D --> D1[租户隔离验证]
    D --> D2[数据泄露检测]
    D --> D3[权限提升检测]
```

### 测试场景示例

1. **租户隔离测试**
   - 用户A尝试访问租户B的数据
   - 验证是否被正确拒绝

2. **参数注入测试**
   - 普通用户传入其他租户ID
   - 验证是否自动替换为用户所属租户ID

3. **权限提升测试**
   - 普通用户尝试执行超级管理员操作
   - 验证是否被正确拒绝

## 📈 成功指标

### 安全指标
- [ ] 100% 的API接口通过租户隔离验证
- [ ] 0 个跨租户数据泄露事件
- [ ] 所有敏感操作都有审计日志

### 性能指标
- [ ] API响应时间不增加超过10%
- [ ] 数据库查询效率提升20%
- [ ] 内存使用量控制在合理范围

### 维护性指标
- [ ] 重复代码减少50%
- [ ] API文档完整性达到95%
- [ ] 代码测试覆盖率达到80%

## 🚀 部署计划

### 灰度发布策略

```mermaid
gantt
    title API安全改进部署时间线
    dateFormat  YYYY-MM-DD
    section 阶段一
    中间件开发    :a1, 2024-01-01, 7d
    安全测试      :a2, after a1, 3d
    灰度部署      :a3, after a2, 2d
    
    section 阶段二
    API合并开发   :b1, after a3, 10d
    集成测试      :b2, after b1, 5d
    生产部署      :b3, after b2, 2d
    
    section 阶段三
    参数加固      :c1, after b3, 7d
    全面测试      :c2, after c1, 5d
    最终部署      :c3, after c2, 2d
```

### 回滚计划
- 保留原有API接口作为备用
- 实现功能开关控制新旧接口
- 准备快速回滚脚本

## 🗺️ 最终路由结构设计

### 调整后的API路由架构

```
/api/v1/
├── users/                  # 统一用户管理（支持权限分级）
│   ├── POST /list          # 统一用户列表接口
│   ├── POST /create        # 创建用户
│   ├── PUT /:id           # 更新用户
│   └── DELETE /:id        # 删除用户
├── tenant/                 # 普通租户管理（租户内部操作）
│   ├── GET /current       # 获取当前租户信息
│   ├── GET /my-tenants    # 获取我的租户列表
│   ├── POST /switch/:id   # 切换租户
│   ├── PUT /update        # 更新当前租户信息
│   └── GET /users         # 获取当前租户用户列表
├── super/                  # 超级管理员专用（跨租户管理）
│   ├── GET /tenants       # 获取所有租户列表
│   ├── POST /tenants      # 创建租户
│   ├── PUT /tenants/:id   # 更新任意租户
│   ├── DELETE /tenants/:id # 删除租户
│   ├── GET /users         # 获取所有用户列表
│   ├── POST /users/promote # 提升用户权限
│   ├── POST /users/demote  # 降级用户权限
│   ├── GET /stats         # 系统统计
│   ├── GET /logs/operations # 操作日志
│   ├── GET /logs/stats    # 日志统计
│   └── GET /data/:type    # 跨租户数据查询
└── public/                 # 公开接口
    ├── POST /login        # 用户登录
    ├── POST /register     # 用户注册
    └── GET /health        # 健康检查
```

### 权限矩阵

| 接口路径 | 超级管理员 | 租户管理员 | 普通用户 | 说明 |
|---------|-----------|-----------|----------|------|
| `/api/v1/users/*` | ✅ 全部权限 | ✅ 租户内用户 | ❌ 无权限 | 统一用户管理 |
| `/api/v1/tenant/*` | ✅ 全部权限 | ✅ 当前租户 | ✅ 基础操作 | 租户内部管理 |
| `/api/v1/super/*` | ✅ 全部权限 | ❌ 无权限 | ❌ 无权限 | 超级管理员专用 |
| `/api/v1/public/*` | ✅ 全部权限 | ✅ 全部权限 | ✅ 全部权限 | 公开接口 |

### 安全策略总结

1. **路径分离原则**
   - 超级管理员功能独立路径 `/super/*`
   - 普通租户管理独立路径 `/tenant/*`
   - 用户管理统一路径 `/users/*` 但支持权限分级

2. **参数安全原则**
   - 非超级管理员自动注入租户ID
   - 跨租户参数自动验证和拒绝
   - 敏感操作强制权限检查

3. **审计日志原则**
   - 所有超级管理员操作记录日志
   - 跨租户数据访问记录日志
   - 权限变更操作记录日志

## � 联系和支持

如有任何问题或需要澄清，请联系：
- 架构团队：<EMAIL>
- 安全团队：<EMAIL>
- 开发团队：<EMAIL>