# API接口合并和多租户安全改进方案

## 1. 用户管理接口统一

### 当前状态
- `GET /user/getUserList` - 基础用户列表
- `GET /user/getUserListForBusiness` - 业务用户列表  
- `GET /super/users` - 超级管理员查看所有用户

### 合并方案
**统一接口：** `POST /api/v1/users/list`

```go
type UserListRequest struct {
    request.PageInfo
    // 业务类型过滤
    BusinessType *string `json:"businessType,omitempty"`
    // 角色过滤
    AuthorityId *uint `json:"authorityId,omitempty"`
    // 租户ID（仅超级管理员可指定，普通用户自动从JWT获取）
    TenantID *uint `json:"tenantId,omitempty"`
    // 搜索关键词
    Keyword *string `json:"keyword,omitempty"`
}

func (b *BaseApi) GetUserList(c *gin.Context) {
    var req UserListRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
    
    // 安全检查：从JWT获取用户信息
    claims := utils.GetUserInfo(c)
    if claims == nil {
        response.FailWithMessage("未授权访问", c)
        return
    }
    
    // 租户隔离：非超级管理员只能查看自己租户的用户
    if !claims.IsSuperAdmin {
        req.TenantID = &claims.TenantID
    }
    
    // 权限检查：普通用户不能指定其他租户
    if req.TenantID != nil && *req.TenantID != claims.TenantID && !claims.IsSuperAdmin {
        response.FailWithMessage("无权限访问其他租户数据", c)
        return
    }
    
    list, total, err := userService.GetUserList(req)
    if err != nil {
        response.FailWithMessage("获取失败", c)
        return
    }
    
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     req.Page,
        PageSize: req.PageSize,
    }, "获取成功", c)
}
```

## 2. 租户管理接口保持分离

### 当前状态分析
- `GET /tenant/getTenantList` - 普通租户管理（租户内部管理）
- `GET /super/tenants` - 超级管理员查看所有租户（跨租户管理）

### 调整方案
**保持独立路径，但统一安全标准：**

#### 2.1 普通租户管理接口 `/api/v1/tenant/*`
```go
// 租户内部管理 - 用户查看自己所属的租户信息
func (tenantApi *TenantApi) GetMyTenants(c *gin.Context) {
    claims := utils.GetUserInfo(c)
    if claims == nil {
        response.FailWithMessage("未授权访问", c)
        return
    }
    
    // 只能查看用户自己关联的租户
    tenants, err := tenantService.GetUserTenants(claims.UserID)
    if err != nil {
        response.FailWithMessage("获取失败", c)
        return
    }
    
    response.OkWithData(gin.H{"tenants": tenants}, c)
}
```

#### 2.2 超级管理员租户管理接口 `/api/v1/super/*`
```go
// 超级管理员专用 - 管理所有租户
func (superAdminApi *SuperAdminApi) GetAllTenants(c *gin.Context) {
    var req SuperAdminTenantListRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.FailWithMessage(err.Error(), c)
        return
    }
    
    claims := utils.GetUserInfo(c)
    if !claims.IsSuperAdmin {
        response.FailWithMessage("需要超级管理员权限", c)
        return
    }
    
    // 超级管理员可以查看所有租户，包括跨租户统计
    list, total, err := superAdminService.GetAllTenants(req)
    if err != nil {
        response.FailWithMessage("获取失败", c)
        return
    }
    
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     req.Page,
        PageSize: req.PageSize,
    }, "获取成功", c)
}
```

## 3. 多租户安全中间件增强

### 租户隔离中间件
```go
// TenantIsolationMiddleware 租户隔离中间件
func TenantIsolationMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 跳过超级管理员和公开接口
        if isSkipTenantCheck(c) {
            c.Next()
            return
        }
        
        claims := utils.GetUserInfo(c)
        if claims == nil {
            response.FailWithMessage("未授权访问", c)
            c.Abort()
            return
        }
        
        // 设置当前租户上下文
        global.SetCurrentTenantID(claims.TenantID)
        
        // 检查请求中的租户相关参数
        if err := validateTenantParams(c, claims.TenantID, claims.IsSuperAdmin); err != nil {
            response.FailWithMessage(err.Error(), c)
            c.Abort()
            return
        }
        
        c.Next()
    }
}

// validateTenantParams 验证请求参数中的租户ID
func validateTenantParams(c *gin.Context, userTenantID uint, isSuperAdmin bool) error {
    // 检查URL参数中的租户ID
    if tenantIDStr := c.Param("tenantId"); tenantIDStr != "" {
        tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
        if err != nil {
            return errors.New("租户ID格式错误")
        }
        
        if !isSuperAdmin && uint(tenantID) != userTenantID {
            return errors.New("无权限访问其他租户数据")
        }
    }
    
    // 检查查询参数中的租户ID
    if tenantIDStr := c.Query("tenantId"); tenantIDStr != "" {
        tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
        if err != nil {
            return errors.New("租户ID格式错误")
        }
        
        if !isSuperAdmin && uint(tenantID) != userTenantID {
            return errors.New("无权限访问其他租户数据")
        }
    }
    
    return nil
}
```

## 4. 请求参数安全处理

### 自动注入租户ID
```go
// AutoInjectTenantID 自动注入租户ID到请求参数
func AutoInjectTenantID(req interface{}, claims *systemReq.CustomClaims) error {
    if claims.IsSuperAdmin {
        return nil // 超级管理员跳过自动注入
    }
    
    v := reflect.ValueOf(req)
    if v.Kind() == reflect.Ptr {
        v = v.Elem()
    }
    
    // 查找TenantID字段并设置
    tenantIDField := v.FieldByName("TenantID")
    if tenantIDField.IsValid() && tenantIDField.CanSet() {
        if tenantIDField.Kind() == reflect.Ptr {
            tenantIDField.Set(reflect.ValueOf(&claims.TenantID))
        } else {
            tenantIDField.SetUint(uint64(claims.TenantID))
        }
    }
    
    return nil
}
```

## 5. 路由重构建议

### 统一路由结构
```
/api/v1/
├── users/
│   ├── POST /list          # 统一用户列表接口
│   ├── POST /create        # 创建用户
│   ├── PUT /:id           # 更新用户
│   └── DELETE /:id        # 删除用户
├── tenants/
│   ├── POST /list          # 统一租户列表接口（仅超级管理员）
│   ├── POST /create        # 创建租户（仅超级管理员）
│   ├── PUT /:id           # 更新租户
│   └── DELETE /:id        # 删除租户（仅超级管理员）
├── super/
│   ├── GET /stats         # 系统统计
│   ├── GET /logs          # 操作日志
│   └── POST /cross-tenant-data # 跨租户数据查询
└── tenant/
    ├── GET /current       # 获取当前租户信息
    ├── POST /switch/:id   # 切换租户
    └── GET /my-tenants    # 获取我的租户列表
```

## 6. 实施优先级

### 高优先级（立即实施）
1. 添加租户隔离中间件
2. 修复参数安全问题
3. 统一用户管理接口

### 中优先级（近期实施）
1. 统一租户管理接口
2. 重构路由结构
3. 添加操作日志

### 低优先级（长期规划）
1. API版本管理
2. 性能优化
3. 监控和告警

## 7. 安全检查清单

- [ ] 所有接口都通过JWT验证用户身份
- [ ] 非超级管理员无法访问其他租户数据
- [ ] 请求参数中的租户ID自动验证
- [ ] 敏感操作记录操作日志
- [ ] 跨租户操作需要特殊权限
- [ ] 数据库查询自动添加租户过滤条件