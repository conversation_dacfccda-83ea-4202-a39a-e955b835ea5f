# 多租户系统实施检查清单

## 📋 概述

本文档提供多租户系统改造的完整实施检查清单，确保所有关键步骤都得到正确执行。

## 🎯 第零阶段：加盟商数据模型重构

### ✅ 数据库结构调整

- [ ] **备份现有数据**
  - [ ] 备份 `franchisee` 表
  - [ ] 备份 `franchisee_tenant_relation` 表
  - [ ] 备份 `sys_user` 表

- [ ] **删除关联表**
  ```sql
  DROP TABLE franchisee_tenant_relation;
  ```

- [ ] **修改franchisee表结构**
  ```sql
  ALTER TABLE franchisee ADD COLUMN tenant_id INT NOT NULL AFTER id;
  ALTER TABLE franchisee ADD INDEX idx_tenant_id (tenant_id);
  ALTER TABLE franchisee ADD UNIQUE KEY uk_tenant_user (tenant_id, user_id);
  ALTER TABLE franchisee ADD UNIQUE KEY uk_tenant_code (tenant_id, code);
  ```

- [ ] **创建用户租户偏好表**
  ```sql
  CREATE TABLE user_tenant_preference (
      id INT PRIMARY KEY AUTO_INCREMENT,
      user_id INT NOT NULL,
      default_tenant_id INT,
      last_login_tenant_id INT,
      last_login_time TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY uk_user (user_id)
  );
  ```

### ✅ 数据迁移

- [ ] **从系统A导入数据**
  ```sql
  INSERT INTO franchisee (tenant_id, user_id, code, name, province, city, county, area, address, f_category_id, tel, linkman, remark, balance, points, inviter_id, is_old_franchisee)
  SELECT 1 as tenant_id, user_id, code, name, province, city, county, area, address, f_category_id, tel, linkman, remark, balance, points, inviter_id, is_old_franchisee
  FROM system_a.franchisee;
  ```

- [ ] **从系统B导入数据**
  ```sql
  INSERT INTO franchisee (tenant_id, user_id, code, name, province, city, county, area, address, f_category_id, tel, linkman, remark, balance, points, inviter_id, is_old_franchisee)
  SELECT 2 as tenant_id, user_id, code, name, province, city, county, area, address, f_category_id, tel, linkman, remark, balance, points, inviter_id, is_old_franchisee
  FROM system_b.franchisee;
  ```

- [ ] **处理用户账号合并**
  - [ ] 识别同一个人在两个系统中的不同user_id
  - [ ] 合并用户账号或保持独立
  - [ ] 更新franchisee表中的user_id引用

- [ ] **验证数据完整性**
  - [ ] 检查所有franchisee记录都有有效的tenant_id
  - [ ] 检查租户内code的唯一性
  - [ ] 检查用户在每个租户中最多只有一条记录

### ✅ 代码重构

- [ ] **更新数据模型**
  - [ ] 删除 `model/franchisees/franchisee_tenant_relation.go`
  - [ ] 更新 `model/franchisees/franchisee.go` 添加 `TenantID` 字段
  - [ ] 更新 `model/app/franchisee.go` 添加 `TenantID` 字段

- [ ] **更新查询逻辑**
  - [ ] 所有加盟商查询添加 `tenant_id` 条件
  - [ ] 更新 `service/franchisees/franchisee.go`
  - [ ] 更新 `service/app/franchisee.go`

- [ ] **实现登录逻辑**
  - [ ] 实现 `AuthenticateUser` 函数
  - [ ] 实现 `GetUserFranchisees` 函数
  - [ ] 实现 `HandleSingleTenantLogin` 函数
  - [ ] 实现 `HandleMultiTenantLogin` 函数
  - [ ] 实现 `ConfirmTenantLogin` 函数
  - [ ] 实现 `SwitchTenant` 函数

- [ ] **更新JWT结构**
  - [ ] 添加 `TenantID` 字段到JWT Claims
  - [ ] 添加 `FranchiseeID` 字段到JWT Claims
  - [ ] 更新JWT生成和验证逻辑

- [ ] **更新API接口**
  - [ ] 更新登录接口 `/app/auth/login`
  - [ ] 新增租户确认接口 `/app/auth/confirm-tenant`
  - [ ] 新增租户切换接口 `/app/auth/switch-tenant`
  - [ ] 新增我的租户接口 `/app/auth/my-tenants`

### ✅ 中间件更新

- [ ] **更新租户访问中间件**
  - [ ] 验证用户在当前租户中有加盟商身份
  - [ ] 设置租户上下文到gin.Context
  - [ ] 设置加盟商ID到gin.Context

- [ ] **应用中间件到APP路由**
  - [ ] `/app/` 路由组应用 `TenantAccessMiddleware`
  - [ ] 确保所有APP接口都有租户验证

## 🎯 第一阶段：核心业务模块

### ✅ 财务管理模块

- [ ] **添加租户隔离中间件**
  - [ ] `router/finance/franchisee_performance.go`

- [ ] **配置租户表**
  - [ ] 添加 `franchisee_performance_month` 到租户表配置
  - [ ] 添加 `franchisee_history_performance` 到租户表配置

### ✅ 大仓管理模块

- [ ] **添加租户隔离中间件**
  - [ ] `router/bigwarehouse/big_warehouse.go`
  - [ ] `router/bigwarehouse/big_warehouse_allocation.go`
  - [ ] `router/bigwarehouse/big_warehouse_coverage.go`
  - [ ] `router/bigwarehouse/big_warehouse_goods.go`
  - [ ] `router/bigwarehouse/big_warehouse_manager.go`

- [ ] **配置租户表**
  - [ ] 添加 `big_warehouse` 到租户表配置
  - [ ] 添加 `big_warehouse_allocation` 到租户表配置
  - [ ] 添加 `big_warehouse_coverage` 到租户表配置
  - [ ] 添加 `big_warehouse_goods` 到租户表配置
  - [ ] 添加 `big_warehouse_manager` 到租户表配置

### ✅ 仓储管理模块

- [ ] **添加租户隔离中间件**
  - [ ] `router/warehouse/cloud_warehouse.go`
  - [ ] `router/warehouse/cloud_warehouse_record.go`

## 🎯 第二阶段：扩展功能模块

### ✅ 分销管理模块

- [ ] **添加租户隔离中间件**
  - [ ] `router/distribution/*.go` 所有路由

- [ ] **配置租户表**
  - [ ] 添加 `franchisee_approve` 到租户表配置
  - [ ] 添加 `franchisee_standby` 到租户表配置
  - [ ] 添加 `distribution_config` 到租户表配置

### ✅ 通用数据模块

- [ ] **评估租户隔离需求**
  - [ ] `router/common/logistics_template.go`
  - [ ] `router/common/product_gift.go`

- [ ] **配置租户表（如需要）**
  - [ ] 添加 `logistics_template` 到租户表配置
  - [ ] 添加 `logistics_template_area` 到租户表配置
  - [ ] 添加 `product_gift_rule` 到租户表配置

## 🧪 测试验证

### ✅ 登录功能测试

- [ ] **单租户登录测试**
  - [ ] 用户只属于一个租户时直接登录
  - [ ] JWT包含正确的租户信息
  - [ ] 可以正常访问APP接口

- [ ] **多租户登录测试**
  - [ ] 用户属于多个租户时显示选择界面
  - [ ] 可以选择任意租户登录
  - [ ] 默认租户选择逻辑正确

- [ ] **租户切换测试**
  - [ ] 可以在运行时切换租户
  - [ ] 切换后JWT更新正确
  - [ ] 数据访问权限正确切换

### ✅ 数据隔离测试

- [ ] **加盟商数据隔离**
  - [ ] 租户A的用户无法看到租户B的加盟商
  - [ ] 加盟商列表按租户正确过滤
  - [ ] 加盟商详情查询有租户验证

- [ ] **订单数据隔离**
  - [ ] 订单列表按租户正确过滤
  - [ ] 订单商品关联验证租户权限
  - [ ] 发货单、退货单按租户隔离

- [ ] **财务数据隔离**
  - [ ] 业绩统计按租户隔离
  - [ ] 充值记录按租户隔离
  - [ ] 财务报表按租户生成

### ✅ 安全测试

- [ ] **跨租户访问测试**
  - [ ] 尝试访问其他租户的数据被拒绝
  - [ ] JWT伪造测试
  - [ ] SQL注入测试

- [ ] **权限边界测试**
  - [ ] 超级管理员权限测试
  - [ ] 租户管理员权限测试
  - [ ] 普通加盟商权限测试

## 📊 性能验证

### ✅ 查询性能测试

- [ ] **加盟商查询性能**
  - [ ] 大量数据下的查询响应时间
  - [ ] 索引使用情况检查
  - [ ] 慢查询日志分析

- [ ] **登录性能测试**
  - [ ] 多租户用户登录响应时间
  - [ ] 并发登录测试
  - [ ] 租户切换响应时间

## 🚀 上线准备

### ✅ 部署准备

- [ ] **数据库迁移脚本**
  - [ ] 准备完整的迁移SQL脚本
  - [ ] 准备回滚脚本
  - [ ] 在测试环境验证迁移脚本

- [ ] **配置更新**
  - [ ] 更新租户表配置
  - [ ] 更新JWT密钥配置
  - [ ] 更新中间件配置

- [ ] **监控告警**
  - [ ] 设置多租户相关监控指标
  - [ ] 设置数据隔离异常告警
  - [ ] 设置登录失败率告警

### ✅ 文档更新

- [ ] **技术文档**
  - [ ] 更新API文档
  - [ ] 更新数据库设计文档
  - [ ] 更新部署文档

- [ ] **用户文档**
  - [ ] 更新用户操作手册
  - [ ] 准备多租户功能说明
  - [ ] 准备常见问题解答

---

**文档版本**: v1.0  
**创建日期**: 2024-12-25  
**维护团队**: 技术团队  
**审核状态**: 实施检查清单
