# 多租户系统问题修复指导

## 修复优先级和实施计划

基于前面的漏洞分析，按照风险等级和影响范围，制定以下修复计划：

### P0 - 立即修复（严重安全漏洞）

#### 修复1：全局租户上下文并发安全问题

**问题描述**：当前使用全局变量存储租户ID，在并发环境下会导致数据混乱和泄露。

**修复策略**：采用渐进式改进，保持向后兼容性，同时引入基于Context的安全机制。

**修复步骤**：

1. **修改 `global/tenant_context.go`**
   - 保留现有全局变量接口（向后兼容）
   - 添加基于Context的新接口
   - 添加线程本地存储机制

2. **修改 `middleware/tenant.go`**
   - 修复defer执行时机问题
   - 同时设置Context和全局变量（过渡期）

3. **修改 `plugin/tenant/tenant_plugin.go`**
   - 优先从Context获取租户ID
   - 降级到全局变量（兼容性）
   - 修复WithTenant作用域实现

#### 修复2：订单表名不一致问题

**问题描述**：Order模型返回"order"，但租户表列表使用"orders"。

**修复策略**：统一表名，确保数据隔离正确生效。

**修复步骤**：

1. **检查数据库实际表名**
2. **统一模型定义和配置**
3. **验证租户隔离功能**

#### 修复3：JWT令牌字段缺失问题

**问题描述**：JWT令牌生成时缺少UserType、IsSuperAdmin等关键字段。

**修复策略**：完善JWT令牌生成逻辑，确保权限信息完整。

### P1 - 紧急修复（功能完整性）

#### 修复4：超级管理员中间件实现

**问题描述**：超级管理员中间件定义了但没有正确使用。

**修复策略**：实现完整的超级管理员权限验证和操作日志。

#### 修复5：租户验证性能优化

**问题描述**：每次验证都查询数据库，性能较差。

**修复策略**：添加内存缓存，提升验证性能。

## 具体修复代码

### 1. 修复全局租户上下文

```go
// global/tenant_context.go - 新增内容
package global

import (
    "context"
    "sync"
    "gorm.io/gorm"
)

// 线程本地存储，解决并发安全问题
type threadLocalTenant struct {
    tenants map[int64]uint
    mutex   sync.RWMutex
}

var tlsTenant = &threadLocalTenant{
    tenants: make(map[int64]uint),
}

// 获取当前goroutine ID（简化实现）
func getGoroutineID() int64 {
    // 实际实现中可以使用runtime获取goroutine ID
    // 这里使用简化版本
    return 1 // 临时实现
}

// SetCurrentTenantIDSafe 线程安全的设置租户ID
func SetCurrentTenantIDSafe(tenantID uint) {
    gid := getGoroutineID()
    tlsTenant.mutex.Lock()
    tlsTenant.tenants[gid] = tenantID
    tlsTenant.mutex.Unlock()
    
    // 同时设置全局变量（向后兼容）
    SetCurrentTenantID(tenantID)
}

// GetCurrentTenantIDSafe 线程安全的获取租户ID
func GetCurrentTenantIDSafe() uint {
    gid := getGoroutineID()
    tlsTenant.mutex.RLock()
    tenantID := tlsTenant.tenants[gid]
    tlsTenant.mutex.RUnlock()
    
    if tenantID == 0 {
        // 降级到全局变量
        return GetCurrentTenantID()
    }
    return tenantID
}

// ClearCurrentTenantID 清理当前租户ID
func ClearCurrentTenantID() {
    gid := getGoroutineID()
    tlsTenant.mutex.Lock()
    delete(tlsTenant.tenants, gid)
    tlsTenant.mutex.Unlock()
    
    // 同时清理全局变量
    SetCurrentTenantID(0)
}
```

### 2. 修复中间件defer问题

```go
// middleware/tenant.go - 修改内容
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 先设置defer，确保清理逻辑执行
        defer func() {
            global.ClearCurrentTenantID()
        }()
        
        claims, exists := c.Get("claims")
        if !exists {
            response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
            c.Abort()
            return
        }
        
        customClaims, ok := claims.(*systemReq.CustomClaims)
        if !ok {
            response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
            c.Abort()
            return
        }
        
        // 验证租户有效性
        if !system.ValidateTenant(customClaims.TenantID) {
            global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", customClaims.TenantID))
            response.FailWithMessage("租户无效", c)
            c.Abort()
            return
        }
        
        // 设置租户上下文（使用安全版本）
        c.Set("tenantId", customClaims.TenantID)
        ctx := global.WithTenantContext(c.Request.Context(), customClaims.TenantID)
        c.Request = c.Request.WithContext(ctx)
        
        // 设置线程安全的全局租户ID
        global.SetCurrentTenantIDSafe(customClaims.TenantID)
        
        c.Next()
    }
}
```

### 3. 修复GORM插件

```go
// plugin/tenant/tenant_plugin.go - 修改beforeQuery方法
func (tp *TenantPlugin) beforeQuery(db *gorm.DB) {
    // 检查是否跳过租户过滤
    if tp.shouldSkipTenant(db) {
        return
    }

    tableName := tp.getTableName(db)
    if !global.IsTenantTable(tableName) {
        return
    }

    var tenantID uint
    
    // 优先从Context获取租户ID
    if ctx := db.Statement.Context; ctx != nil {
        if id, ok := global.GetTenantFromContext(ctx); ok && id > 0 {
            tenantID = id
        }
    }
    
    // 降级到线程安全的全局变量
    if tenantID == 0 {
        tenantID = global.GetCurrentTenantIDSafe()
    }
    
    if tenantID == 0 {
        return
    }

    // 添加租户过滤条件
    db.Where("tenant_id = ?", tenantID)
}
```

### 4. 修复订单表名

```go
// model/orders/order.go - 修改TableName方法
func (Order) TableName() string {
    return "orders" // 统一使用复数形式
}
```

### 5. 修复JWT令牌生成

```go
// service/system/tenant.go - 修改GenerateTenantToken方法
func (tenantService *TenantService) GenerateTenantToken(userID, tenantID uint) (string, error) {
    var user system.SysUser
    err := global.GVA_DB.Where("id = ?", userID).First(&user).Error
    if err != nil {
        return "", err
    }
    
    // 检查用户是否属于该租户
    var relation system.UserTenantRelation
    err = global.GVA_DB.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&relation).Error
    if err != nil {
        return "", err
    }
    
    // 确定用户类型和权限
    userType := systemReq.UserTypeNormal
    isSuperAdmin := false
    var managedTenants []uint
    
    // 根据权限ID判断用户类型
    switch user.AuthorityId {
    case 888: // 超级管理员
        userType = systemReq.UserTypeSuperAdmin
        isSuperAdmin = true
    case 777: // 系统管理员
        userType = systemReq.UserTypeSystemAdmin
    default:
        if relation.Role == "admin" {
            userType = systemReq.UserTypeTenantAdmin
        }
    }
    
    // 获取管理的租户列表
    if userType >= systemReq.UserTypeTenantAdmin {
        var relations []system.UserTenantRelation
        err = global.GVA_DB.Where("user_id = ? AND role IN ?", userID, []string{"admin", "manager"}).Find(&relations).Error
        if err == nil {
            for _, rel := range relations {
                managedTenants = append(managedTenants, rel.TenantID)
            }
        }
    }
    
    j := utils.NewJWT()
    claims := systemReq.CustomClaims{
        BaseClaims: systemReq.BaseClaims{
            ID:             user.ID,
            UUID:           user.UUID,
            Username:       user.Username,
            NickName:       user.NickName,
            AuthorityId:    user.AuthorityId,
            UserType:       userType,
            IsSuperAdmin:   isSuperAdmin,
            ManagedTenants: managedTenants,
        },
        BufferTime: 60 * 60 * 24,
        TenantID:   tenantID,
    }
    
    token, err := j.CreateToken(claims)
    if err != nil {
        return "", err
    }
    
    return token, nil
}
```

### 6. 添加租户缓存

```go
// global/tenant_cache.go - 新文件
package global

import (
    "sync"
    "time"
    "github.com/OSQianXing/guanpu-server/model/system"
)

type TenantCache struct {
    cache map[uint]*CachedTenant
    mutex sync.RWMutex
    ttl   time.Duration
}

type CachedTenant struct {
    IsValid  bool
    CachedAt time.Time
}

var tenantCache = &TenantCache{
    cache: make(map[uint]*CachedTenant),
    ttl:   5 * time.Minute,
}

func ValidateTenantCached(tenantID uint) bool {
    if tenantID == 0 {
        return false
    }
    
    // 检查缓存
    tenantCache.mutex.RLock()
    cached, exists := tenantCache.cache[tenantID]
    tenantCache.mutex.RUnlock()
    
    if exists && time.Since(cached.CachedAt) < tenantCache.ttl {
        return cached.IsValid
    }
    
    // 从数据库验证
    var tenant system.Tenant
    err := GVA_DB.Where("id = ?", tenantID).First(&tenant).Error
    
    isValid := false
    if err == nil {
        isValid = (tenant.Status == nil || *tenant.Status) &&
                  (tenant.ExpireDate == nil || tenant.ExpireDate.After(time.Now()))
    }
    
    // 更新缓存
    tenantCache.mutex.Lock()
    tenantCache.cache[tenantID] = &CachedTenant{
        IsValid:  isValid,
        CachedAt: time.Now(),
    }
    tenantCache.mutex.Unlock()
    
    return isValid
}
```

## 测试验证

### 并发安全测试

```go
// test/tenant_concurrent_test.go
func TestTenantConcurrentSafety(t *testing.T) {
    var wg sync.WaitGroup
    results := make(map[int][]uint)
    mutex := sync.Mutex{}
    
    // 并发设置不同租户ID
    for i := 1; i <= 10; i++ {
        wg.Add(1)
        go func(tenantID int) {
            defer wg.Done()
            
            global.SetCurrentTenantIDSafe(uint(tenantID))
            time.Sleep(10 * time.Millisecond) // 模拟处理时间
            
            // 验证获取的租户ID是否正确
            gotID := global.GetCurrentTenantIDSafe()
            
            mutex.Lock()
            results[tenantID] = append(results[tenantID], gotID)
            mutex.Unlock()
        }(i)
    }
    
    wg.Wait()
    
    // 验证每个goroutine都获取到了正确的租户ID
    for expectedID, gotIDs := range results {
        for _, gotID := range gotIDs {
            assert.Equal(t, uint(expectedID), gotID, 
                "期望租户ID %d，实际获取 %d", expectedID, gotID)
        }
    }
}
```

## 部署注意事项

1. **渐进式部署**：先部署兼容版本，确保现有功能正常
2. **监控指标**：添加租户隔离相关的监控指标
3. **回滚准备**：准备快速回滚方案
4. **性能测试**：部署前进行充分的性能测试

## 风险控制

1. **向后兼容**：保留原有接口，避免破坏现有功能
2. **渐进改进**：分阶段实施，降低风险
3. **充分测试**：每个修复都要有对应的测试用例
4. **监控告警**：实时监控系统运行状态

通过以上修复方案，可以在保持系统稳定性的前提下，逐步解决多租户系统中的安全漏洞和功能问题。