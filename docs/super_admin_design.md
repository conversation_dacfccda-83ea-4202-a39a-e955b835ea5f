# 超级管理员设计文档

## 概述

超级管理员是多租户系统中的特殊角色，具有跨租户的管理权限，能够管理所有租户的数据和配置。本文档详细描述了超级管理员的设计思路、权限模型和实现方案。

## 设计原则

### 1. 权限分离
- **租户管理员**：只能管理自己租户内的数据
- **超级管理员**：可以管理所有租户的数据和系统配置
- **系统管理员**：介于两者之间，可以管理多个租户但不是所有

### 2. 安全性
- 超级管理员权限需要特殊标识
- 操作日志必须详细记录
- 敏感操作需要二次验证

### 3. 可审计性
- 所有超级管理员操作都要记录
- 支持操作回滚
- 提供详细的审计报告

## 权限模型

### 1. 用户类型定义

```go
type UserType int

const (
    UserTypeNormal     UserType = 0  // 普通用户
    UserTypeTenantAdmin UserType = 1  // 租户管理员
    UserTypeSystemAdmin UserType = 2  // 系统管理员
    UserTypeSuperAdmin  UserType = 3  // 超级管理员
)
```

### 2. 权限级别

#### 普通用户 (UserTypeNormal)
- 只能访问自己租户的数据
- 受租户数据隔离限制
- 无管理权限

#### 租户管理员 (UserTypeTenantAdmin)
- 可以管理自己租户的所有数据
- 可以管理租户内的用户
- 可以配置租户设置
- 受租户数据隔离限制

#### 系统管理员 (UserTypeSystemAdmin)
- 可以管理指定的多个租户
- 可以查看跨租户的统计数据
- 可以进行系统级配置
- 部分操作不受租户隔离限制

#### 超级管理员 (UserTypeSuperAdmin)
- 可以管理所有租户
- 可以创建、删除、修改租户
- 可以管理所有用户
- 可以进行系统级配置
- 所有操作都不受租户隔离限制

## 数据库设计

### 1. 用户表扩展

```sql
ALTER TABLE sys_user ADD COLUMN user_type TINYINT DEFAULT 0 COMMENT '用户类型：0-普通用户 1-租户管理员 2-系统管理员 3-超级管理员';
ALTER TABLE sys_user ADD COLUMN is_super_admin BOOLEAN DEFAULT FALSE COMMENT '是否为超级管理员';
ALTER TABLE sys_user ADD COLUMN managed_tenants JSON COMMENT '管理的租户列表（系统管理员使用）';
```

### 2. 超级管理员操作日志表

```sql
CREATE TABLE super_admin_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    target_type VARCHAR(50) NOT NULL COMMENT '目标类型：tenant/user/system',
    target_id BIGINT COMMENT '目标ID',
    tenant_id BIGINT COMMENT '涉及的租户ID',
    operation_desc TEXT COMMENT '操作描述',
    request_data JSON COMMENT '请求数据',
    response_data JSON COMMENT '响应数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_created_at (created_at)
) COMMENT '超级管理员操作日志表';
```

## 实现方案

### 1. JWT令牌扩展

```go
type CustomClaims struct {
    BaseClaims
    BufferTime   int64    `json:"bufferTime"`
    TenantID     uint     `json:"tenantId"`
    UserType     UserType `json:"userType"`
    IsSuperAdmin bool     `json:"isSuperAdmin"`
    ManagedTenants []uint `json:"managedTenants,omitempty"`
    jwt.RegisteredClaims
}
```

### 2. 中间件增强

#### 超级管理员中间件
```go
func SuperAdminMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims := utils.GetUserInfo(c)
        if claims == nil || !claims.IsSuperAdmin {
            response.FailWithMessage("需要超级管理员权限", c)
            c.Abort()
            return
        }
        
        // 设置超级管理员标志，跳过租户隔离
        c.Set("is_super_admin", true)
        c.Next()
    }
}
```

#### 租户隔离中间件增强
```go
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims := utils.GetUserInfo(c)
        if claims == nil {
            response.FailWithMessage("未授权", c)
            c.Abort()
            return
        }
        
        // 超级管理员跳过租户验证
        if claims.IsSuperAdmin {
            c.Set("skip_tenant", true)
            global.SetCurrentTenantID(0) // 超级管理员不设置租户ID
            c.Next()
            return
        }
        
        // 普通用户的租户验证逻辑...
    }
}
```

### 3. 数据访问控制

#### GORM插件增强
```go
func (tp *TenantPlugin) shouldSkipTenant(db *gorm.DB) bool {
    // 检查是否是超级管理员操作
    if isSuper, ok := db.Get("is_super_admin"); ok && isSuper.(bool) {
        return true
    }
    
    // 检查是否设置了跳过租户的标志
    if skip, ok := db.Get("skip_tenant"); ok && skip.(bool) {
        return true
    }
    
    return false
}
```

#### 服务层增强
```go
// 超级管理员专用的服务方法
func (s *OrderService) GetAllOrdersForSuperAdmin(pageInfo request.PageInfo) ([]Order, int64, error) {
    db := global.GVA_DB.Set("is_super_admin", true)
    // 查询所有租户的订单...
}

func (s *TenantService) GetAllTenantsForSuperAdmin() ([]Tenant, error) {
    db := global.GVA_DB.Set("is_super_admin", true)
    // 查询所有租户...
}
```

## API设计

### 1. 超级管理员专用接口

#### 租户管理
```
GET    /api/super/tenants              # 获取所有租户
POST   /api/super/tenants              # 创建租户
PUT    /api/super/tenants/:id          # 更新租户
DELETE /api/super/tenants/:id          # 删除租户
POST   /api/super/tenants/:id/disable  # 禁用租户
POST   /api/super/tenants/:id/enable   # 启用租户
```

#### 用户管理
```
GET    /api/super/users                # 获取所有用户
POST   /api/super/users                # 创建用户
PUT    /api/super/users/:id            # 更新用户
DELETE /api/super/users/:id            # 删除用户
POST   /api/super/users/:id/promote    # 提升为管理员
POST   /api/super/users/:id/demote     # 降级为普通用户
```

#### 数据管理
```
GET    /api/super/data/orders          # 查看所有订单
GET    /api/super/data/products        # 查看所有商品
GET    /api/super/data/franchisees     # 查看所有加盟商
```

#### 系统管理
```
GET    /api/super/system/stats         # 系统统计信息
GET    /api/super/system/logs          # 系统日志
POST   /api/super/system/backup        # 数据备份
POST   /api/super/system/restore       # 数据恢复
```

### 2. 操作日志接口
```
GET    /api/super/logs/operations      # 超级管理员操作日志
GET    /api/super/logs/audit           # 审计日志
POST   /api/super/logs/export          # 导出日志
```

## 安全措施

### 1. 身份验证
- 超级管理员账号需要特殊的创建流程
- 支持多因素认证（MFA）
- 定期强制修改密码

### 2. 权限控制
- 最小权限原则
- 操作权限细分
- 敏感操作需要二次确认

### 3. 审计监控
- 所有操作都要记录
- 异常操作实时告警
- 定期审计报告

### 4. 数据保护
- 敏感数据加密存储
- 操作日志不可篡改
- 支持数据备份和恢复

## 使用场景

### 1. 租户管理
- 创建新租户
- 配置租户参数
- 监控租户使用情况
- 处理租户问题

### 2. 用户管理
- 重置用户密码
- 管理用户权限
- 处理用户申诉
- 用户数据迁移

### 3. 系统维护
- 系统配置更新
- 数据库维护
- 性能监控
- 故障处理

### 4. 数据分析
- 跨租户数据统计
- 业务趋势分析
- 系统使用报告
- 异常数据检查

## 实施步骤

### 阶段1：基础架构
1. 扩展用户模型
2. 实现超级管理员中间件
3. 增强GORM插件
4. 创建操作日志系统

### 阶段2：API开发
1. 实现超级管理员API
2. 创建管理界面
3. 添加操作日志记录
4. 实现审计功能

### 阶段3：安全加固
1. 添加多因素认证
2. 实现操作审批流程
3. 加强监控告警
4. 完善备份恢复

### 阶段4：测试优化
1. 功能测试
2. 安全测试
3. 性能测试
4. 用户体验优化

## 注意事项

1. **权限边界**：明确超级管理员的权限边界，避免权限滥用
2. **操作审计**：所有超级管理员操作都要详细记录
3. **安全防护**：加强超级管理员账号的安全防护
4. **应急处理**：制定超级管理员账号的应急处理预案
5. **定期审查**：定期审查超级管理员的操作和权限

## 总结

超级管理员是多租户系统的重要组成部分，需要在功能强大和安全可控之间找到平衡。通过合理的设计和严格的实施，可以确保超级管理员既能有效管理系统，又不会对系统安全造成威胁。