# 多租户系统API文档

> **⚠️ 重要更新说明**：本文档已基于新的加盟商1:1关系设计进行重构。部分API已废弃，请参考最新的API设计。

## 概述

本文档描述了基于加盟商1:1关系的多租户系统API接口，包括：
- ✅ **租户管理API**：仍然有效
- ✅ **加盟商多租户登录API**：新设计的核心API
- ✅ **超级管理员API**：仍然有效
- ❌ **用户租户关联API**：已废弃

## 认证机制

### 新的JWT令牌结构

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "uuid": "user-uuid",
    "id": 1,
    "username": "admin",
    "nickName": "管理员",
    "authorityId": 1,
    "userType": 0,
    "isSuperAdmin": false,
    "userId": 1,
    "tenantId": 1,
    "franchiseeId": 123,
    "tenantCode": "tenant_001",
    "managedTenants": [1, 2],
    "exp": 1640995200,
    "iat": 1640908800,
    "nbf": 1640908800
  }
}
```

**新增字段说明**：
- `userId`: 用户ID
- `franchiseeId`: 当前加盟商ID
- `tenantCode`: 租户编码

### 请求头

所有需要认证的接口都需要在请求头中包含JWT令牌：

```
Authorization: Bearer <JWT_TOKEN>
```

## 1. 统一登录API

### 1.1 统一登录接口

**接口地址**: `POST /base/login`

**接口描述**: 统一的用户登录接口，支持普通用户和加盟商登录，自动识别用户类型并处理多租户逻辑

**接口统一化说明**:
- **之前**: 分离的登录接口（普通用户: `/base/login`，加盟商: `/base/franchiseeLogin` 已废弃）
- **现在**: 统一登录接口（所有用户: `/base/login`）

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名（普通用户）或手机号（加盟商） |
| password | string | 是 | 密码 |
| tenantCode | string | 否 | 租户代码（仅加盟商多租户场景使用） |
| captcha | string | 否 | 验证码 |
| captchaId | string | 否 | 验证码ID |

**请求示例**:

```json
// 普通用户登录
{
  "username": "admin",
  "password": "admin123",
  "captcha": "1234",
  "captchaId": "uuid-1234"
}

// 加盟商登录（单租户）
{
  "username": "13800138001",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "uuid-1234"
}

// 加盟商登录（指定租户）
{
  "username": "13800138002",
  "password": "password123",
  "tenantCode": "tenant_a",
  "captcha": "1234",
  "captchaId": "uuid-1234"
}
```

**响应参数**:

| 参数名 | 类型 | 说明 |
|--------|------|------|
| user | object | 用户信息 |
| token | string | JWT访问令牌 |
| expiresAt | number | 令牌过期时间（毫秒时间戳） |
| needTenantSelection | boolean | 是否需要租户选择（仅多租户加盟商） |
| availableTenants | array | 可用租户列表（仅多租户场景） |
| tempToken | string | 临时令牌（用于租户选择） |
| tenantId | number | 当前租户ID（仅加盟商） |
| franchiseeId | number | 当前加盟商ID（仅加盟商） |

**响应示例**:

**场景1: 普通用户登录成功**
```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "nickName": "管理员",
      "headerImg": "",
      "authorityId": 888
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000
  },
  "msg": "登录成功"
}
```

**场景2: 单租户加盟商登录成功**
```json
{
  "code": 0,
  "data": {
    "user": {
      "id": 2,
      "username": "13800138001",
      "nickName": "张三",
      "headerImg": "",
      "authorityId": 999
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000,
    "tenantId": 1,
    "franchiseeId": 101
  },
  "msg": "登录成功"
}
```

**场景3: 多租户加盟商需要选择租户**
```json
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [
      {
        "tenantId": 1,
        "tenantCode": "default",
        "tenantName": "默认租户",
        "franchiseeId": 101,
        "franchiseeName": "张三加盟店",
        "franchiseeCode": "F001",
        "isCurrent": false
      },
      {
        "tenantId": 2,
        "tenantCode": "tenant_a",
        "tenantName": "租户A",
        "franchiseeId": 102,
        "franchiseeName": "张三分店",
        "franchiseeCode": "F002",
        "isCurrent": false
      }
    ],
    "tempToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "msg": "请选择要登录的租户"
}
```

### 1.2 确认租户登录

**接口地址**: `POST /app/auth/confirm-tenant`

**接口描述**: 用户选择租户后确认登录

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tempToken | string | 是 | 临时令牌 |
| tenantId | number | 是 | 租户ID |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {"id": 2, "username": "user", "nickName": "用户"},
    "tenant": {"id": 1, "code": "tenant_a"},
    "franchisee": {"id": 101}
  },
  "msg": "登录成功"
}
```

### 1.3 切换租户

**接口地址**: `POST /app/auth/switch-tenant`

**接口描述**: 切换到指定租户

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenantId | number | 是 | 租户ID |

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tenant": {"id": 2, "code": "tenant_b"},
    "franchisee": {"id": 102}
  },
  "msg": "切换成功"
}
```

### 1.4 获取我的租户列表

**接口地址**: `GET /app/auth/my-tenants`

**接口描述**: 获取当前用户的所有租户列表

**响应示例**:
```json
{
  "code": 0,
  "data": [
    {
      "tenantId": 1,
      "tenantCode": "default",
      "tenantName": "默认租户",
      "franchiseeId": 101,
      "franchiseeName": "张三加盟店",
      "franchiseeCode": "F001",
      "isCurrent": true
    },
    {
      "tenantId": 2,
      "tenantCode": "tenant_a",
      "tenantName": "租户A",
      "franchiseeId": 102,
      "franchiseeName": "张三分店",
      "franchiseeCode": "F002",
      "isCurrent": false
    }
  ],
  "msg": "获取成功"
}
```

## 2. 租户管理API

### 1.1 创建租户

**接口地址**：`POST /api/tenant/createTenant`

**请求参数**：
```json
{
  "name": "测试公司",
  "code": "test_company",
  "logo": "https://example.com/logo.png",
  "primaryColor": "#007bff",
  "secondaryColor": "#6c757d",
  "contactName": "张三",
  "contactPhone": "13800138000",
  "expireDate": "2024-12-31T23:59:59Z",
  "appConfig": {
    "appName": "测试应用",
    "appLogo": "https://example.com/app-logo.png",
    "loginBgImage": "https://example.com/bg.jpg",
    "homePageConfig": "{\"layout\":\"default\"}"
  }
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "测试公司",
    "code": "test_company",
    "logo": "https://example.com/logo.png",
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "status": true,
    "contactName": "张三",
    "contactPhone": "13800138000",
    "expireDate": "2024-12-31T23:59:59Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "msg": "创建成功"
}
```

**错误响应**：
```json
{
  "code": 7001,
  "msg": "租户编码已存在"
}
```

### 1.2 获取租户列表

**接口地址**：`GET /api/tenant/getTenantList`

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
name: "公司"     // 租户名称（可选）
code: "test"     // 租户编码（可选）
status: true     // 状态（可选）
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "测试公司",
        "code": "test_company",
        "logo": "https://example.com/logo.png",
        "status": true,
        "contactName": "张三",
        "contactPhone": "13800138000",
        "expireDate": "2024-12-31T23:59:59Z",
        "appConfig": {
          "id": 1,
          "tenantId": 1,
          "appName": "测试应用",
          "appLogo": "https://example.com/app-logo.png"
        },
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 1.3 更新租户信息

**接口地址**：`PUT /api/tenant/updateTenant`

**请求参数**：
```json
{
  "id": 1,
  "name": "更新后的公司名",
  "primaryColor": "#28a745",
  "contactName": "李四",
  "contactPhone": "13900139000"
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "更新成功"
}
```

### 1.4 删除租户

**接口地址**：`DELETE /api/tenant/deleteTenant`

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "删除成功"
}
```

### 1.5 获取租户详情

**接口地址**：`GET /api/tenant/findTenant`

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "测试公司",
    "code": "test_company",
    "logo": "https://example.com/logo.png",
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "status": true,
    "contactName": "张三",
    "contactPhone": "13800138000",
    "expireDate": "2024-12-31T23:59:59Z",
    "appConfig": {
      "id": 1,
      "tenantId": 1,
      "appName": "测试应用",
      "appLogo": "https://example.com/app-logo.png",
      "loginBgImage": "https://example.com/bg.jpg",
      "homePageConfig": "{\"layout\":\"default\"}"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "msg": "获取成功"
}
```

## 2. 加盟商多租户登录API

> **📖 详细设计**：完整的登录流程设计请参考 [`multi_tenant_detailed_design.md`](./multi_tenant_detailed_design.md)

### 2.1 加盟商登录

**接口地址**：`POST /app/auth/login`

**请求参数**：
```json
{
  "username": "user123",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "captcha-uuid"
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [
      {
        "tenantId": 1,
        "tenantCode": "tenant_001",
        "tenantName": "租户A",
        "franchiseeId": 123,
        "franchiseeName": "加盟商A"
      },
      {
        "tenantId": 2,
        "tenantCode": "tenant_002",
        "tenantName": "租户B",
        "franchiseeId": 456,
        "franchiseeName": "加盟商B"
      }
    ],
    "tempToken": "temp-jwt-token"
  },
  "msg": "请选择要登录的租户"
}
```

### 2.2 确认租户登录

**接口地址**：`POST /app/auth/confirm-tenant`

**请求参数**：
```json
{
  "tempToken": "temp-jwt-token",
  "tenantId": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "token": "final-jwt-token",
    "user": {
      "id": 1,
      "username": "user123",
      "nickName": "用户123"
    },
    "tenant": {
      "id": 1,
      "code": "tenant_001",
      "name": "租户A"
    },
    "franchisee": {
      "id": 123,
      "code": "F001",
      "name": "加盟商A"
    }
  },
  "msg": "登录成功"
}
```

### 2.3 切换租户

**接口地址**：`POST /app/auth/switch-tenant`

**请求参数**：
```json
{
  "tenantId": 2
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "token": "new-jwt-token",
    "tenant": {
      "id": 2,
      "code": "tenant_002",
      "name": "租户B"
    },
    "franchisee": {
      "id": 456,
      "code": "F002",
      "name": "加盟商B"
    }
  },
  "msg": "切换成功"
}
```

### 2.4 获取我的租户列表

**接口地址**：`GET /app/auth/my-tenants`

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "tenantId": 1,
      "tenantCode": "tenant_001",
      "tenantName": "租户A",
      "franchiseeId": 123,
      "franchiseeName": "加盟商A",
      "isCurrent": true
    },
    {
      "tenantId": 2,
      "tenantCode": "tenant_002",
      "tenantName": "租户B",
      "franchiseeId": 456,
      "franchiseeName": "加盟商B",
      "isCurrent": false
    }
  ],
  "msg": "获取成功"
}
```

---

## ❌ 已废弃的用户租户关联API

> **⚠️ 废弃说明**：以下API基于M:N关系设计，与新的加盟商1:1关系设计冲突，已废弃使用。

### ~~2.1 添加用户到租户~~ ❌ **已废弃**

**接口地址**：~~`POST /api/tenant/addUserToTenant`~~

**废弃原因**：新设计中用户通过加盟商身份自动关联租户，无需手动添加。

### ~~2.2 从租户移除用户~~ ❌ **已废弃**

**接口地址**：~~`POST /api/tenant/removeUserFromTenant`~~

**废弃原因**：新设计中用户租户关系通过加盟商管理，无需单独移除。

### ~~2.3 设置默认租户~~ ❌ **已废弃**

**接口地址**：~~`POST /api/tenant/setDefaultTenant`~~

**废弃原因**：新设计中无需默认租户概念，用户通过加盟商身份直接关联租户。

### ~~2.4 获取用户关联的租户~~ ❌ **已废弃**

**接口地址**：~~`GET /api/tenant/getUserTenants`~~

**废弃原因**：已被新的 `GET /app/auth/my-tenants` 接口替代。
  ],
  "msg": "获取成功"
}
```

### 2.5 切换租户

**接口地址**：`POST /api/tenant/switchTenant/{tenantId}`

**路径参数**：
- `tenantId`: 要切换到的租户ID

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": "2024-01-08T00:00:00Z"
  },
  "msg": "切换成功"
}
```

## 3. 超级管理员API

### 3.1 获取所有租户

**接口地址**：`GET /api/super/tenants`

**权限要求**：超级管理员

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "租户A",
        "code": "tenant_a",
        "status": true,
        "contactName": "张三",
        "contactPhone": "13800138000",
        "expireDate": "2024-12-31T23:59:59Z",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 3.2 获取所有用户

**接口地址**：`GET /api/super/users`

**权限要求**：超级管理员

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "username": "admin",
        "nickName": "管理员",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "userType": 1,
        "isSuperAdmin": false,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 3.3 禁用租户

**接口地址**：`POST /api/super/tenants/disable`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "禁用成功"
}
```

### 3.4 启用租户

**接口地址**：`POST /api/super/tenants/enable`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "启用成功"
}
```

### 3.5 提升用户权限

**接口地址**：`POST /api/super/users/promote`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "userId": 1,
  "userType": 3
}
```

**用户类型说明**：
- 0: 普通用户
- 1: 租户管理员
- 2: 系统管理员
- 3: 超级管理员

**响应示例**：
```json
{
  "code": 0,
  "msg": "提升成功"
}
```

### 3.6 降级用户权限

**接口地址**：`POST /api/super/users/demote`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "降级成功"
}
```

### 3.7 获取系统统计信息

**接口地址**：`GET /api/super/system/stats`

**权限要求**：超级管理员

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "tenantCount": 10,
    "activeTenantCount": 8,
    "userCount": 100,
    "superAdminCount": 2,
    "orderCount": 1000,
    "productCount": 500
  },
  "msg": "获取成功"
}
```

### 3.8 获取操作日志

**接口地址**：`GET /api/super/logs/operations`

**权限要求**：超级管理员

**请求参数**：
```
page: 1                    // 页码
pageSize: 10              // 每页大小
operationType: "tenant_create"  // 操作类型（可选）
targetType: "tenant"      // 目标类型（可选）
userId: 1                 // 用户ID（可选）
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "userId": 1,
        "operationType": "tenant_create",
        "targetType": "tenant",
        "targetId": 1,
        "tenantId": null,
        "operationDesc": "创建租户",
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0...",
        "status": "success",
        "duration": 150,
        "createdAt": "2024-01-01T00:00:00Z",
        "user": {
          "id": 1,
          "username": "admin",
          "nickName": "管理员"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 3.9 获取操作统计

**接口地址**：`GET /api/super/logs/stats`

**权限要求**：超级管理员

**请求参数**：
```
days: 7  // 统计天数，默认7天
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "totalOperations": 100,
    "successOperations": 95,
    "failedOperations": 5,
    "operationsByType": {
      "tenant_create": 10,
      "tenant_update": 20,
      "user_promote": 5
    },
    "operationsByUser": {
      "admin": 50,
      "super_admin": 50
    },
    "recentOperations": [
      {
        "id": 1,
        "operationType": "tenant_create",
        "operationDesc": "创建租户",
        "status": "success",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  },
  "msg": "获取成功"
}
```

### 3.10 获取跨租户数据

**接口地址**：`GET /api/super/data/{dataType}`

**权限要求**：超级管理员

**路径参数**：
- `dataType`: 数据类型（orders, products, franchisees）

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "tenantId": 1,
        "orderNo": "ORD20240101001",
        "amount": 100.00,
        "status": "completed",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 7001 | 参数错误 |
| 7002 | 数据库错误 |
| 7003 | 权限不足 |
| 7004 | 资源不存在 |

### 4.2 租户相关错误码

| 错误码 | 说明 |
|--------|------|
| 8001 | 租户不存在 |
| 8002 | 租户已禁用 |
| 8003 | 租户已过期 |
| 8004 | 租户编码已存在 |
| 8005 | 用户不属于该租户 |
| 8006 | 无法删除默认租户 |

### 4.3 用户相关错误码

| 错误码 | 说明 |
|--------|------|
| 9001 | 用户不存在 |
| 9002 | 用户已存在 |
| 9003 | 密码错误 |
| 9004 | 用户已禁用 |
| 9005 | 权限不足 |

## 5. 使用示例

### 5.1 创建租户并添加用户

```javascript
// 1. 创建租户
const createTenantResponse = await fetch('/api/tenant/createTenant', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    name: '新公司',
    code: 'new_company',
    contactName: '张三',
    contactPhone: '13800138000'
  })
});

const tenant = await createTenantResponse.json();
console.log('租户创建成功:', tenant.data);

// 2. 添加用户到租户
const addUserResponse = await fetch('/api/tenant/addUserToTenant', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    userId: 1,
    tenantId: tenant.data.id,
    role: 'admin'
  })
});

console.log('用户添加成功');
```

### 5.2 切换租户

```javascript
// 获取用户的租户列表
const tenantsResponse = await fetch('/api/tenant/getUserTenants', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const tenants = await tenantsResponse.json();
console.log('用户租户列表:', tenants.data);

// 切换到指定租户
const switchResponse = await fetch(`/api/tenant/switchTenant/${tenants.data[1].id}`, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const newToken = await switchResponse.json();
console.log('新令牌:', newToken.data.token);

// 使用新令牌进行后续请求
localStorage.setItem('token', newToken.data.token);
```

### 5.3 超级管理员操作

```javascript
// 获取所有租户（超级管理员权限）
const allTenantsResponse = await fetch('/api/super/tenants?page=1&pageSize=10', {
  headers: {
    'Authorization': 'Bearer ' + superAdminToken
  }
});

const allTenants = await allTenantsResponse.json();
console.log('所有租户:', allTenants.data);

// 禁用租户
const disableResponse = await fetch('/api/super/tenants/disable', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + superAdminToken
  },
  body: JSON.stringify({
    id: 1
  })
});

console.log('租户已禁用');

// 查看操作日志
const logsResponse = await fetch('/api/super/logs/operations?page=1&pageSize=10', {
  headers: {
    'Authorization': 'Bearer ' + superAdminToken
  }
});

const logs = await logsResponse.json();
console.log('操作日志:', logs.data);
```

## 6. SDK示例

### 6.1 JavaScript SDK

```javascript
class TenantAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(method, url, data = null) {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      }
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${this.baseURL}${url}`, options);
    return await response.json();
  }

  // 租户管理
  async createTenant(tenantData) {
    return await this.request('POST', '/api/tenant/createTenant', tenantData);
  }

  async getTenantList(page = 1, pageSize = 10) {
    return await this.request('GET', `/api/tenant/getTenantList?page=${page}&pageSize=${pageSize}`);
  }

  async updateTenant(tenantData) {
    return await this.request('PUT', '/api/tenant/updateTenant', tenantData);
  }

  async deleteTenant(id) {
    return await this.request('DELETE', '/api/tenant/deleteTenant', { id });
  }

  // 用户租户关联
  async addUserToTenant(userId, tenantId, role) {
    return await this.request('POST', '/api/tenant/addUserToTenant', {
      userId, tenantId, role
    });
  }

  async getUserTenants() {
    return await this.request('GET', '/api/tenant/getUserTenants');
  }

  async switchTenant(tenantId) {
    return await this.request('POST', `/api/tenant/switchTenant/${tenantId}`);
  }
}

// 使用示例
const api = new TenantAPI('https://api.example.com', 'your-jwt-token');

// 创建租户
const tenant = await api.createTenant({
  name: '测试公司',
  code: 'test_company',
  contactName: '张三',
  contactPhone: '13800138000'
});

console.log('租户创建成功:', tenant);
```

### 6.2 Python SDK

```python
import requests
import json

class TenantAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }

    def request(self, method, url, data=None):
        full_url = f"{self.base_url}{url}"
        
        if method == 'GET':
            response = requests.get(full_url, headers=self.headers, params=data)
        elif method == 'POST':
            response = requests.post(full_url, headers=self.headers, json=data)
        elif method == 'PUT':
            response = requests.put(full_url, headers=self.headers, json=data)
        elif method == 'DELETE':
            response = requests.delete(full_url, headers=self.headers, json=data)
        
        return response.json()

    def create_tenant(self, tenant_data):
        return self.request('POST', '/api/tenant/createTenant', tenant_data)

    def get_tenant_list(self, page=1, page_size=10):
        params = {'page': page, 'pageSize': page_size}
        return self.request('GET', '/api/tenant/getTenantList', params)

    def switch_tenant(self, tenant_id):
        return self.request('POST', f'/api/tenant/switchTenant/{tenant_id}')

# 使用示例
api = TenantAPI('https://api.example.com', 'your-jwt-token')

# 创建租户
tenant = api.create_tenant({
    'name': '测试公司',
    'code': 'test_company',
    'contactName': '张三',
    'contactPhone': '13800138000'
})

print('租户创建成功:', tenant)
```

## 7. 最佳实践

### 7.1 错误处理

```javascript
async function handleTenantOperation() {
  try {
    const response = await api.createTenant(tenantData);
    
    if (response.code === 0) {
      console.log('操作成功:', response.data);
    } else {
      // 根据错误码进行不同处理
      switch (response.code) {
        case 8004:
          alert('租户编码已存在，请使用其他编码');
          break;
        case 7003:
          alert('权限不足，请联系管理员');
          break;
        default:
          alert(`操作失败: ${response.msg}`);
      }
    }
  } catch (error) {
    console.error('网络错误:', error);
    alert('网络连接失败，请稍后重试');
  }
}
```

### 7.2 令牌管理

```javascript
class TokenManager {
  constructor() {
    this.token = localStorage.getItem('token');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  setToken(token, refreshToken) {
    this.token = token;
    this.refreshToken = refreshToken;
    localStorage.setItem('token', token);
    localStorage.setItem('refreshToken', refreshToken);
  }

  async refreshTokenIfNeeded() {
    // 检查令牌是否即将过期
    const payload = JSON.parse(atob(this.token.split('.')[1]));
    const exp = payload.exp * 1000;
    const now = Date.now();
    
    if (exp - now < 5 * 60 * 1000) { // 5分钟内过期
      await this.refreshToken();
    }
  }

  async switchTenant(tenantId) {
    const response = await fetch(`/api/tenant/switchTenant/${tenantId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });
    
    const result = await response.json();
    if (result.code === 0) {
      this.setToken(result.data.token, this.refreshToken);
    }
    
    return result;
  }
}
```

### 7.3 权限检查

```javascript
function checkPermission(userType, requiredLevel) {
  const levels = {
    'normal': 0,
    'tenant_admin': 1,
    'system_admin': 2,
    'super_admin': 3
  };
  
  return levels[userType] >= levels[requiredLevel];
}

// 使用示例
if (checkPermission(user.userType, 'tenant_admin')) {
  // 显示租户管理功能
  showTenantManagement();
}

if (user.isSuperAdmin) {
  // 显示超级管理员功能
  showSuperAdminPanel();
}
```

## 总结

本API文档提供了多租户系统的完整接口说明，包括：

1. **租户管理**：创建、查询、更新、删除租户
2. **用户管理**：用户租户关联、权限管理、租户切换
3. **超级管理员**：跨租户管理、操作审计、系统统计
4. **错误处理**：详细的错误码和处理建议
5. **SDK示例**：JavaScript和Python的SDK实现
6. **最佳实践**：错误处理、令牌管理、权限检查

通过这些API，开发者可以轻松集成多租户功能到自己的应用中，实现完整的SaaS平台能力。