# 多租户系统API文档

## 概述

本文档详细描述了多租户系统的所有API接口，包括租户管理、用户管理、超级管理员功能等。

## 认证机制

### JWT令牌结构

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "uuid": "user-uuid",
    "id": 1,
    "username": "admin",
    "nickName": "管理员",
    "authorityId": 1,
    "userType": 1,
    "isSuperAdmin": false,
    "tenantId": 1,
    "managedTenants": [1, 2],
    "exp": 1640995200,
    "iat": 1640908800,
    "nbf": 1640908800
  }
}
```

### 请求头

所有需要认证的接口都需要在请求头中包含JWT令牌：

```
Authorization: Bearer <JWT_TOKEN>
```

## 1. 租户管理API

### 1.1 创建租户

**接口地址**：`POST /api/tenant/createTenant`

**请求参数**：
```json
{
  "name": "测试公司",
  "code": "test_company",
  "logo": "https://example.com/logo.png",
  "primaryColor": "#007bff",
  "secondaryColor": "#6c757d",
  "contactName": "张三",
  "contactPhone": "13800138000",
  "expireDate": "2024-12-31T23:59:59Z",
  "appConfig": {
    "appName": "测试应用",
    "appLogo": "https://example.com/app-logo.png",
    "loginBgImage": "https://example.com/bg.jpg",
    "homePageConfig": "{\"layout\":\"default\"}"
  }
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "测试公司",
    "code": "test_company",
    "logo": "https://example.com/logo.png",
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "status": true,
    "contactName": "张三",
    "contactPhone": "13800138000",
    "expireDate": "2024-12-31T23:59:59Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "msg": "创建成功"
}
```

**错误响应**：
```json
{
  "code": 7001,
  "msg": "租户编码已存在"
}
```

### 1.2 获取租户列表

**接口地址**：`GET /api/tenant/getTenantList`

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
name: "公司"     // 租户名称（可选）
code: "test"     // 租户编码（可选）
status: true     // 状态（可选）
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "测试公司",
        "code": "test_company",
        "logo": "https://example.com/logo.png",
        "status": true,
        "contactName": "张三",
        "contactPhone": "13800138000",
        "expireDate": "2024-12-31T23:59:59Z",
        "appConfig": {
          "id": 1,
          "tenantId": 1,
          "appName": "测试应用",
          "appLogo": "https://example.com/app-logo.png"
        },
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 1.3 更新租户信息

**接口地址**：`PUT /api/tenant/updateTenant`

**请求参数**：
```json
{
  "id": 1,
  "name": "更新后的公司名",
  "primaryColor": "#28a745",
  "contactName": "李四",
  "contactPhone": "13900139000"
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "更新成功"
}
```

### 1.4 删除租户

**接口地址**：`DELETE /api/tenant/deleteTenant`

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "删除成功"
}
```

### 1.5 获取租户详情

**接口地址**：`GET /api/tenant/findTenant`

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "name": "测试公司",
    "code": "test_company",
    "logo": "https://example.com/logo.png",
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "status": true,
    "contactName": "张三",
    "contactPhone": "13800138000",
    "expireDate": "2024-12-31T23:59:59Z",
    "appConfig": {
      "id": 1,
      "tenantId": 1,
      "appName": "测试应用",
      "appLogo": "https://example.com/app-logo.png",
      "loginBgImage": "https://example.com/bg.jpg",
      "homePageConfig": "{\"layout\":\"default\"}"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "msg": "获取成功"
}
```

## 2. 用户租户关联API

### 2.1 添加用户到租户

**接口地址**：`POST /api/tenant/addUserToTenant`

**请求参数**：
```json
{
  "userId": 1,
  "tenantId": 2,
  "role": "admin"
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "添加成功"
}
```

### 2.2 从租户移除用户

**接口地址**：`POST /api/tenant/removeUserFromTenant`

**请求参数**：
```json
{
  "userId": 1,
  "tenantId": 2
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "移除成功"
}
```

### 2.3 设置默认租户

**接口地址**：`POST /api/tenant/setDefaultTenant`

**请求参数**：
```json
{
  "userId": 1,
  "tenantId": 2
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "设置成功"
}
```

### 2.4 获取用户关联的租户

**接口地址**：`GET /api/tenant/getUserTenants`

**响应示例**：
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "公司A",
      "code": "company_a",
      "logo": "https://example.com/logo-a.png",
      "isDefault": true,
      "role": "admin",
      "joinTime": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "公司B",
      "code": "company_b",
      "logo": "https://example.com/logo-b.png",
      "isDefault": false,
      "role": "user",
      "joinTime": "2024-01-02T00:00:00Z"
    }
  ],
  "msg": "获取成功"
}
```

### 2.5 切换租户

**接口地址**：`POST /api/tenant/switchTenant/{tenantId}`

**路径参数**：
- `tenantId`: 要切换到的租户ID

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": "2024-01-08T00:00:00Z"
  },
  "msg": "切换成功"
}
```

## 3. 超级管理员API

### 3.1 获取所有租户

**接口地址**：`GET /api/super/tenants`

**权限要求**：超级管理员

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "租户A",
        "code": "tenant_a",
        "status": true,
        "contactName": "张三",
        "contactPhone": "13800138000",
        "expireDate": "2024-12-31T23:59:59Z",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 3.2 获取所有用户

**接口地址**：`GET /api/super/users`

**权限要求**：超级管理员

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "username": "admin",
        "nickName": "管理员",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "userType": 1,
        "isSuperAdmin": false,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 3.3 禁用租户

**接口地址**：`POST /api/super/tenants/disable`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "禁用成功"
}
```

### 3.4 启用租户

**接口地址**：`POST /api/super/tenants/enable`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "启用成功"
}
```

### 3.5 提升用户权限

**接口地址**：`POST /api/super/users/promote`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "userId": 1,
  "userType": 3
}
```

**用户类型说明**：
- 0: 普通用户
- 1: 租户管理员
- 2: 系统管理员
- 3: 超级管理员

**响应示例**：
```json
{
  "code": 0,
  "msg": "提升成功"
}
```

### 3.6 降级用户权限

**接口地址**：`POST /api/super/users/demote`

**权限要求**：超级管理员

**请求参数**：
```json
{
  "id": 1
}
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "降级成功"
}
```

### 3.7 获取系统统计信息

**接口地址**：`GET /api/super/system/stats`

**权限要求**：超级管理员

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "tenantCount": 10,
    "activeTenantCount": 8,
    "userCount": 100,
    "superAdminCount": 2,
    "orderCount": 1000,
    "productCount": 500
  },
  "msg": "获取成功"
}
```

### 3.8 获取操作日志

**接口地址**：`GET /api/super/logs/operations`

**权限要求**：超级管理员

**请求参数**：
```
page: 1                    // 页码
pageSize: 10              // 每页大小
operationType: "tenant_create"  // 操作类型（可选）
targetType: "tenant"      // 目标类型（可选）
userId: 1                 // 用户ID（可选）
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "userId": 1,
        "operationType": "tenant_create",
        "targetType": "tenant",
        "targetId": 1,
        "tenantId": null,
        "operationDesc": "创建租户",
        "ipAddress": "*************",
        "userAgent": "Mozilla/5.0...",
        "status": "success",
        "duration": 150,
        "createdAt": "2024-01-01T00:00:00Z",
        "user": {
          "id": 1,
          "username": "admin",
          "nickName": "管理员"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

### 3.9 获取操作统计

**接口地址**：`GET /api/super/logs/stats`

**权限要求**：超级管理员

**请求参数**：
```
days: 7  // 统计天数，默认7天
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "totalOperations": 100,
    "successOperations": 95,
    "failedOperations": 5,
    "operationsByType": {
      "tenant_create": 10,
      "tenant_update": 20,
      "user_promote": 5
    },
    "operationsByUser": {
      "admin": 50,
      "super_admin": 50
    },
    "recentOperations": [
      {
        "id": 1,
        "operationType": "tenant_create",
        "operationDesc": "创建租户",
        "status": "success",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  },
  "msg": "获取成功"
}
```

### 3.10 获取跨租户数据

**接口地址**：`GET /api/super/data/{dataType}`

**权限要求**：超级管理员

**路径参数**：
- `dataType`: 数据类型（orders, products, franchisees）

**请求参数**：
```
page: 1          // 页码
pageSize: 10     // 每页大小
```

**响应示例**：
```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "tenantId": 1,
        "orderNo": "ORD20240101001",
        "amount": 100.00,
        "status": "completed",
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  },
  "msg": "获取成功"
}
```

## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 7001 | 参数错误 |
| 7002 | 数据库错误 |
| 7003 | 权限不足 |
| 7004 | 资源不存在 |

### 4.2 租户相关错误码

| 错误码 | 说明 |
|--------|------|
| 8001 | 租户不存在 |
| 8002 | 租户已禁用 |
| 8003 | 租户已过期 |
| 8004 | 租户编码已存在 |
| 8005 | 用户不属于该租户 |
| 8006 | 无法删除默认租户 |

### 4.3 用户相关错误码

| 错误码 | 说明 |
|--------|------|
| 9001 | 用户不存在 |
| 9002 | 用户已存在 |
| 9003 | 密码错误 |
| 9004 | 用户已禁用 |
| 9005 | 权限不足 |

## 5. 使用示例

### 5.1 创建租户并添加用户

```javascript
// 1. 创建租户
const createTenantResponse = await fetch('/api/tenant/createTenant', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    name: '新公司',
    code: 'new_company',
    contactName: '张三',
    contactPhone: '13800138000'
  })
});

const tenant = await createTenantResponse.json();
console.log('租户创建成功:', tenant.data);

// 2. 添加用户到租户
const addUserResponse = await fetch('/api/tenant/addUserToTenant', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    userId: 1,
    tenantId: tenant.data.id,
    role: 'admin'
  })
});

console.log('用户添加成功');
```

### 5.2 切换租户

```javascript
// 获取用户的租户列表
const tenantsResponse = await fetch('/api/tenant/getUserTenants', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const tenants = await tenantsResponse.json();
console.log('用户租户列表:', tenants.data);

// 切换到指定租户
const switchResponse = await fetch(`/api/tenant/switchTenant/${tenants.data[1].id}`, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  }
});

const newToken = await switchResponse.json();
console.log('新令牌:', newToken.data.token);

// 使用新令牌进行后续请求
localStorage.setItem('token', newToken.data.token);
```

### 5.3 超级管理员操作

```javascript
// 获取所有租户（超级管理员权限）
const allTenantsResponse = await fetch('/api/super/tenants?page=1&pageSize=10', {
  headers: {
    'Authorization': 'Bearer ' + superAdminToken
  }
});

const allTenants = await allTenantsResponse.json();
console.log('所有租户:', allTenants.data);

// 禁用租户
const disableResponse = await fetch('/api/super/tenants/disable', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + superAdminToken
  },
  body: JSON.stringify({
    id: 1
  })
});

console.log('租户已禁用');

// 查看操作日志
const logsResponse = await fetch('/api/super/logs/operations?page=1&pageSize=10', {
  headers: {
    'Authorization': 'Bearer ' + superAdminToken
  }
});

const logs = await logsResponse.json();
console.log('操作日志:', logs.data);
```

## 6. SDK示例

### 6.1 JavaScript SDK

```javascript
class TenantAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(method, url, data = null) {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`
      }
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${this.baseURL}${url}`, options);
    return await response.json();
  }

  // 租户管理
  async createTenant(tenantData) {
    return await this.request('POST', '/api/tenant/createTenant', tenantData);
  }

  async getTenantList(page = 1, pageSize = 10) {
    return await this.request('GET', `/api/tenant/getTenantList?page=${page}&pageSize=${pageSize}`);
  }

  async updateTenant(tenantData) {
    return await this.request('PUT', '/api/tenant/updateTenant', tenantData);
  }

  async deleteTenant(id) {
    return await this.request('DELETE', '/api/tenant/deleteTenant', { id });
  }

  // 用户租户关联
  async addUserToTenant(userId, tenantId, role) {
    return await this.request('POST', '/api/tenant/addUserToTenant', {
      userId, tenantId, role
    });
  }

  async getUserTenants() {
    return await this.request('GET', '/api/tenant/getUserTenants');
  }

  async switchTenant(tenantId) {
    return await this.request('POST', `/api/tenant/switchTenant/${tenantId}`);
  }
}

// 使用示例
const api = new TenantAPI('https://api.example.com', 'your-jwt-token');

// 创建租户
const tenant = await api.createTenant({
  name: '测试公司',
  code: 'test_company',
  contactName: '张三',
  contactPhone: '13800138000'
});

console.log('租户创建成功:', tenant);
```

### 6.2 Python SDK

```python
import requests
import json

class TenantAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }

    def request(self, method, url, data=None):
        full_url = f"{self.base_url}{url}"
        
        if method == 'GET':
            response = requests.get(full_url, headers=self.headers, params=data)
        elif method == 'POST':
            response = requests.post(full_url, headers=self.headers, json=data)
        elif method == 'PUT':
            response = requests.put(full_url, headers=self.headers, json=data)
        elif method == 'DELETE':
            response = requests.delete(full_url, headers=self.headers, json=data)
        
        return response.json()

    def create_tenant(self, tenant_data):
        return self.request('POST', '/api/tenant/createTenant', tenant_data)

    def get_tenant_list(self, page=1, page_size=10):
        params = {'page': page, 'pageSize': page_size}
        return self.request('GET', '/api/tenant/getTenantList', params)

    def switch_tenant(self, tenant_id):
        return self.request('POST', f'/api/tenant/switchTenant/{tenant_id}')

# 使用示例
api = TenantAPI('https://api.example.com', 'your-jwt-token')

# 创建租户
tenant = api.create_tenant({
    'name': '测试公司',
    'code': 'test_company',
    'contactName': '张三',
    'contactPhone': '13800138000'
})

print('租户创建成功:', tenant)
```

## 7. 最佳实践

### 7.1 错误处理

```javascript
async function handleTenantOperation() {
  try {
    const response = await api.createTenant(tenantData);
    
    if (response.code === 0) {
      console.log('操作成功:', response.data);
    } else {
      // 根据错误码进行不同处理
      switch (response.code) {
        case 8004:
          alert('租户编码已存在，请使用其他编码');
          break;
        case 7003:
          alert('权限不足，请联系管理员');
          break;
        default:
          alert(`操作失败: ${response.msg}`);
      }
    }
  } catch (error) {
    console.error('网络错误:', error);
    alert('网络连接失败，请稍后重试');
  }
}
```

### 7.2 令牌管理

```javascript
class TokenManager {
  constructor() {
    this.token = localStorage.getItem('token');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  setToken(token, refreshToken) {
    this.token = token;
    this.refreshToken = refreshToken;
    localStorage.setItem('token', token);
    localStorage.setItem('refreshToken', refreshToken);
  }

  async refreshTokenIfNeeded() {
    // 检查令牌是否即将过期
    const payload = JSON.parse(atob(this.token.split('.')[1]));
    const exp = payload.exp * 1000;
    const now = Date.now();
    
    if (exp - now < 5 * 60 * 1000) { // 5分钟内过期
      await this.refreshToken();
    }
  }

  async switchTenant(tenantId) {
    const response = await fetch(`/api/tenant/switchTenant/${tenantId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    });
    
    const result = await response.json();
    if (result.code === 0) {
      this.setToken(result.data.token, this.refreshToken);
    }
    
    return result;
  }
}
```

### 7.3 权限检查

```javascript
function checkPermission(userType, requiredLevel) {
  const levels = {
    'normal': 0,
    'tenant_admin': 1,
    'system_admin': 2,
    'super_admin': 3
  };
  
  return levels[userType] >= levels[requiredLevel];
}

// 使用示例
if (checkPermission(user.userType, 'tenant_admin')) {
  // 显示租户管理功能
  showTenantManagement();
}

if (user.isSuperAdmin) {
  // 显示超级管理员功能
  showSuperAdminPanel();
}
```

## 总结

本API文档提供了多租户系统的完整接口说明，包括：

1. **租户管理**：创建、查询、更新、删除租户
2. **用户管理**：用户租户关联、权限管理、租户切换
3. **超级管理员**：跨租户管理、操作审计、系统统计
4. **错误处理**：详细的错误码和处理建议
5. **SDK示例**：JavaScript和Python的SDK实现
6. **最佳实践**：错误处理、令牌管理、权限检查

通过这些API，开发者可以轻松集成多租户功能到自己的应用中，实现完整的SaaS平台能力。