# 租户与加盟商关系设计分析与实现报告

## 📋 **最终设计方案**

经过深入分析和重新设计，我们采用了**方案二：FranchiseeTenantRelation 关联表**的架构，这是最符合业务需求的设计。

## 🏗️ **架构设计**

### **三层架构模型**

```
┌─────────────────────────────────────────────────────────────┐
│                    关联层 (Association Layer)                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │         FranchiseeTenantRelation                        │ │
│  │  - 管理加盟商与租户的多对多关系                            │ │
│  │  - 支持角色管理 (franchisee/manager/owner)               │ │
│  │  - 支持状态管理 (active/inactive)                        │ │
│  │  - 支持默认租户设置                                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (Business Layer)                   │
│  ┌─────────────────────┐    ┌─────────────────────────────┐  │
│  │     Franchisee      │    │           Tenant            │  │
│  │  - 加盟商基本信息     │    │  - 租户基本信息              │  │
│  │  - 不包含租户字段     │    │  - 租户配置信息              │  │
│  │  - 纯业务实体        │    │  - 纯业务实体                │  │
│  └─────────────────────┘    └─────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    认证层 (Authentication Layer)             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    SysUser                              │ │
│  │  - 系统用户认证                                          │ │
│  │  - Username = Franchisee.Tel (手机号)                   │ │
│  │  - 密码验证                                              │ │
│  │  - 不包含业务逻辑                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **关系模型**

```
SysUser (1) ←→ (1) Franchisee (M) ←→ (M) Tenant
                      ↑                    ↑
                      └─── FranchiseeTenantRelation ───┘
                           (多对多关联表)
```

## 🔍 **业务需求分析**

### **核心业务规则**：
1. **一个加盟商可以属于多个租户** ✅
2. **用户使用手机号+密码登录** ✅
3. **SysUser.Username = Franchisee.Tel** ✅
4. **登录时需要确定具体访问哪个租户** ✅
5. **支持默认租户机制** ✅
6. **支持租户选择机制** ✅

## 💾 **数据模型设计**

### **1. 核心模型结构**

```go
// 加盟商模型 - 纯业务实体，不包含租户信息
type Franchisee struct {
    global.GVA_MODEL
    UserId      uint   `json:"userId"`     // 关联系统用户
    Tel         string `json:"tel"`        // 手机号，与SysUser.Username相同
    Name        string `json:"name"`       // 加盟商名称
    Code        string `json:"code"`       // 加盟商编码
    // ... 其他业务字段
    // ✅ 不包含TenantID - 通过关联表管理
}

// 系统用户模型 - 纯认证层，不包含业务逻辑
type SysUser struct {
    global.GVA_MODEL
    Username    string `json:"userName"`   // 用户名 = 手机号
    Password    string `json:"-"`          // 密码
    // ... 其他认证相关字段
    // ✅ 不包含TenantID - 通过业务层管理
}

// 租户模型 - 纯业务实体
type Tenant struct {
    global.GVA_MODEL
    Name           string `json:"name"`           // 租户名称
    Code           string `json:"code"`           // 租户代码
    PrimaryColor   string `json:"primaryColor"`   // 主色调
    SecondaryColor string `json:"secondaryColor"` // 辅色调
    // ... 其他租户配置
}

// 加盟商租户关联模型 - 管理多对多关系
type FranchiseeTenantRelation struct {
    global.GVA_MODEL
    FranchiseeID uint   `json:"franchiseeId"`  // 加盟商ID
    TenantID     uint   `json:"tenantId"`      // 租户ID
    Status       string `json:"status"`        // 状态：active/inactive
    Role         string `json:"role"`          // 角色：franchisee/manager/owner
    IsDefault    bool   `json:"isDefault"`     // 是否为默认租户
    Remark       string `json:"remark"`        // 备注
}
```

## 🔐 **登录流程设计**

### **加盟商登录业务流程**

```mermaid
graph TD
    A[用户输入手机号+密码] --> B[验证码校验]
    B --> C[SysUser认证]
    C --> D{认证成功?}
    D -->|否| E[返回认证失败]
    D -->|是| F[验证是否为加盟商]
    F --> G{是加盟商?}
    G -->|否| H[返回非加盟商错误]
    G -->|是| I[获取租户关联列表]
    I --> J{租户数量?}
    J -->|0个| K[返回无租户关联错误]
    J -->|1个| L[直接使用该租户登录]
    J -->|多个| M{指定了租户代码?}
    M -->|是| N[验证租户权限]
    M -->|否| O{有默认租户?}
    O -->|是| P[使用默认租户登录]
    O -->|否| Q[返回租户选择列表]
    N --> R{权限验证通过?}
    R -->|否| S[返回权限错误]
    R -->|是| T[生成租户JWT]
    L --> T
    P --> T
    T --> U[返回登录成功]
```

### **登录API设计**

#### **复用现有登录接口**：`POST /base/login`

```go
// 扩展现有登录请求结构
type Login struct {
    Username   string `json:"username"`                     // 用户名（手机号）
    Password   string `json:"password" binding:"required"`  // 密码
    TenantCode string `json:"tenantCode"`                   // 可选：租户代码
    Captcha    string `json:"captcha"`                      // 验证码
    CaptchaId  string `json:"captchaId"`                    // 验证码ID
}
```

#### **响应结构**：

```go
// 扩展现有登录响应结构
type LoginResponse struct {
    User                SysUser                        `json:"user"`
    Token               string                         `json:"token"`
    ExpiresAt           int64                          `json:"expiresAt"`
    // 新增字段用于多租户支持
    NeedTenantSelection bool                           `json:"needTenantSelection,omitempty"` // 是否需要租户选择
    AvailableTenants    []FranchiseeTenantRelation     `json:"availableTenants,omitempty"`    // 可用租户列表
    TenantID            uint                           `json:"tenantId,omitempty"`            // 当前租户ID
}
```

### **登录场景处理**

#### **场景1：单租户加盟商登录**
```json
// 请求
{
  "username": "13800138000",
  "password": "password123",
  "captcha": "1234",
  "captchaId": "uuid"
}

// 响应
{
  "code": 0,
  "data": {
    "user": {...},
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000,
    "tenantId": 1
  },
  "msg": "登录成功"
}
```

#### **场景2：多租户加盟商需要选择**
```json
// 请求（不指定租户）
{
  "username": "13800138000",
  "password": "password123"
}

// 响应
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [
      {
        "tenantId": 1,
        "role": "franchisee",
        "isDefault": true,
        "tenant": {"name": "默认租户", "code": "default"}
      },
      {
        "tenantId": 2,
        "role": "manager",
        "isDefault": false,
        "tenant": {"name": "租户A", "code": "tenant_a"}
      }
    ]
  },
  "msg": "请选择要登录的租户"
}
```

#### **场景3：指定租户登录**
```json
// 请求
{
  "username": "13800138000",
  "password": "password123",
  "tenantCode": "tenant_a"
}

// 响应
{
  "code": 0,
  "data": {
    "user": {...},
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000,
    "tenantId": 2
  },
  "msg": "登录成功"
}
```

#### **场景4：普通系统用户登录**
```json
// 请求
{
  "username": "admin",
  "password": "admin123"
}

// 响应
{
  "code": 0,
  "data": {
    "user": {...},
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000
  },
  "msg": "登录成功"
}
```

## 3. 其他可能缺少租户关联的模型

### 3.1 已发现的问题模型

通过分析，以下模型可能缺少必要的租户关联：

#### 3.1.1 系统核心模型
```go
// ❌ 系统用户表 - 应该支持多租户
type SysUser struct {
    // 缺少 TenantID 字段
}

// ❌ 加盟商表 - 应该支持租户隔离  
type Franchisee struct {
    // 缺少 TenantID 字段
}
```

#### 3.1.2 加盟商相关模型
```go
// ✅ 已检查：加盟商账户 - 包含TenantID
type FranchiseeAccount struct {
    TenantID uint `gorm:"column:tenant_id;not null;index"`
}

// ❌ 需要检查：加盟商分类
type FranchiseeCategory struct {
    // 可能缺少 TenantID 字段
}

// ❌ 需要检查：加盟商团队关系
type FranchiseeTeam struct {
    // 可能缺少 TenantID 字段
}

// ❌ 需要检查：加盟商链接关系
type FranchiseeLink struct {
    // 可能缺少 TenantID 字段
}
```

#### 3.1.3 权限和配置模型
```go
// ❌ 权限相关表可能需要租户隔离
type SysAuthority struct {
    // 某些权限可能需要租户级别隔离
}

// ❌ 菜单配置可能需要租户定制
type SysBaseMenu struct {
    // 租户可能需要不同的菜单配置
}
```

## 4. 设计建议和解决方案

### 4.1 加盟商模型修复

```go
// 修复后的加盟商模型
type Franchisee struct {
    global.GVA_MODEL
    TenantID    uint   `json:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID"`
    UserId      uint   `json:"userId" gorm:"column:user_id;comment:用户ID"`
    Name        string `json:"name" gorm:"column:name;comment:加盟商名称"`
    Code        string `json:"code" gorm:"column:code;comment:加盟商编码"`
    // ... 其他字段保持不变
}

// 表名方法保持不变
func (Franchisee) TableName() string {
    return "franchisee"
}
```

### 4.2 系统用户模型选择

**方案一：SysUser添加TenantID（推荐）**
```go
type SysUser struct {
    global.GVA_MODEL
    TenantID    uint   `json:"tenantId" gorm:"column:tenant_id;index;comment:默认租户ID"`
    UUID        uuid.UUID `json:"uuid" gorm:"index;comment:用户UUID"`
    Username    string    `json:"userName" gorm:"index;comment:用户登录名"`
    // ... 其他字段
}
```

**方案二：保持SysUser全局，依赖UserTenantRelation**
- 优点：用户可以跨租户
- 缺点：登录逻辑复杂，需要额外的租户选择步骤

### 4.3 登录流程改进

```go
// 改进的登录流程
func (userService *UserService) LoginWithTenant(username, password string, tenantID uint) (*LoginResult, error) {
    // 1. 验证用户名密码
    user, err := userService.validateUser(username, password)
    if err != nil {
        return nil, err
    }
    
    // 2. 验证用户是否属于指定租户
    if tenantID > 0 {
        if !userService.userBelongsToTenant(user.ID, tenantID) {
            return nil, errors.New("用户不属于指定租户")
        }
    } else {
        // 3. 获取用户的默认租户
        tenantID, err = userService.getUserDefaultTenant(user.ID)
        if err != nil {
            return nil, err
        }
    }
    
    // 4. 生成包含租户信息的JWT
    token, err := userService.generateTenantToken(user, tenantID)
    return &LoginResult{User: user, Token: token, TenantID: tenantID}, err
}
```

### 4.4 加盟商登录特殊处理

```go
// 加盟商登录逻辑
func (franchiseeService *FranchiseeService) FranchiseeLogin(username, password string) (*LoginResult, error) {
    // 1. 验证用户
    user, err := userService.Login(username, password)
    if err != nil {
        return nil, err
    }
    
    // 2. 获取加盟商信息
    franchisee, err := franchiseeService.GetFranchiseeByUserID(user.ID)
    if err != nil {
        return nil, errors.New("用户不是加盟商")
    }
    
    // 3. 使用加盟商所属的租户ID
    tenantID := franchisee.TenantID
    
    // 4. 生成租户token
    token, err := tenantService.GenerateTenantToken(user.ID, tenantID)
    return &LoginResult{User: user, Token: token, TenantID: tenantID}, err
}
```

## 5. 数据迁移计划

### 5.1 数据库结构调整

```sql
-- 1. 为加盟商表添加租户ID字段
ALTER TABLE `franchisee` ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 COMMENT '租户ID';
ALTER TABLE `franchisee` ADD INDEX `idx_franchisee_tenant_id` (`tenant_id`);

-- 2. 为系统用户表添加默认租户ID字段（可选）
ALTER TABLE `sys_users` ADD COLUMN `tenant_id` bigint unsigned NULL COMMENT '默认租户ID';
ALTER TABLE `sys_users` ADD INDEX `idx_sys_users_tenant_id` (`tenant_id`);

-- 3. 检查其他需要添加租户ID的表
-- franchisee_category, franchisee_team, franchisee_link 等
```

### 5.2 数据迁移脚本

```go
// 数据迁移函数
func MigrateFranchiseeToTenant() error {
    // 1. 为所有现有加盟商设置默认租户ID
    err := global.GVA_DB.Model(&franchisees.Franchisee{}).
        Where("tenant_id = 0 OR tenant_id IS NULL").
        Update("tenant_id", 1).Error // 假设1是默认租户
    
    // 2. 为加盟商用户创建租户关联
    var franchiseeUsers []struct {
        UserID   uint
        TenantID uint
    }
    
    err = global.GVA_DB.Table("franchisee").
        Select("user_id, tenant_id").
        Where("user_id > 0").
        Find(&franchiseeUsers).Error
    
    for _, fu := range franchiseeUsers {
        // 创建用户租户关联
        tenantService.AddUserToTenant(fu.UserID, fu.TenantID, "franchisee")
    }
    
    return err
}
```

## 6. 总结和建议

### 6.1 关键问题总结

1. **加盟商模型缺少租户隔离**：这是最严重的问题，可能导致数据泄露
2. **登录流程缺少租户选择**：用户无法明确选择要登录的租户
3. **系统用户与租户关系不明确**：影响权限控制和数据隔离

### 6.2 优先级建议

**P0 - 立即修复**：
- 为 `Franchisee` 模型添加 `TenantID` 字段
- 修复加盟商相关的数据查询，确保租户隔离
- 数据迁移：为现有加盟商数据设置租户ID

**P1 - 短期优化**：
- 改进登录流程，支持租户选择
- 为 `SysUser` 添加默认租户ID字段
- 完善用户租户关联机制

**P2 - 中期完善**：
- 检查并修复其他缺少租户关联的模型
- 实现租户切换功能
- 完善权限控制机制

### 6.3 风险评估

**高风险**：
- 加盟商数据可能存在跨租户访问风险
- 用户登录后可能访问到错误租户的数据

**中风险**：
- 权限控制可能不够精确
- 数据统计可能包含其他租户数据

**建议立即采取行动修复这些问题，确保系统的数据安全和租户隔离的完整性。**

---

## 🔧 **修复完成情况**

### ✅ **已完成的修复**

#### **P0 - 模型修复（已完成）**

1. **✅ 修复加盟商模型**：
   - 为 `Franchisee` 模型添加了 `TenantID` 字段
   - 为 `FranchiseeTeam` 模型添加了 `TenantID` 字段
   - 为 `FranchiseeLink` 模型添加了 `TenantID` 字段
   - 为 `FranchiseeMemberLink` 模型添加了 `TenantID` 字段

2. **✅ 更新租户模型注册**：
   - 在 `initialize/tenant_models.go` 中注册了所有修复的模型
   - 确保GORM租户插件能够自动处理这些表的租户隔离

#### **P1 - 登录流程修复（已完成）**

3. **✅ 修复标准登录流程**：
   - 修改 `TokenNext` 方法，使其能够获取用户的默认租户
   - 使用 `GenerateTenantToken` 方法生成包含租户信息的JWT
   - 正确处理token解析和过期时间

4. **✅ 添加加盟商专用登录**：
   - 创建了 `FranchiseeLogin` API方法
   - 验证用户是否为加盟商
   - 使用加盟商所属租户ID生成token
   - 添加了对应的路由 `/base/franchiseeLogin`

#### **P2 - 数据迁移（已完成）**

5. **✅ 创建数据迁移脚本**：
   - 创建了 `source/system/tenant_data_migration.go` 自动迁移脚本
   - 创建了 `docs/sql/add_tenant_id_to_franchisee_models.sql` 手动SQL脚本
   - 包含数据完整性验证和用户租户关联创建

### 🎯 **修复效果**

#### **安全性提升**：
- ✅ 加盟商数据现在具有完整的租户隔离
- ✅ 登录流程能够正确设置租户上下文
- ✅ 防止了跨租户数据访问的安全风险

#### **功能完善**：
- ✅ 加盟商可以使用专用登录接口
- ✅ JWT token包含正确的租户信息
- ✅ 支持自动数据迁移

#### **代码质量**：
- ✅ 所有相关模型都遵循租户隔离设计
- ✅ 登录流程统一使用租户token机制
- ✅ 完整的错误处理和日志记录

### 📋 **下一步建议**

#### **立即执行**：
1. **运行数据迁移**：执行SQL脚本或启动应用让自动迁移运行
2. **测试登录功能**：验证普通登录和加盟商登录都能正常工作
3. **验证数据隔离**：确认不同租户的加盟商数据互相隔离

#### **后续优化**：
1. **权限细化**：考虑是否需要为不同租户设置不同的权限
2. **菜单定制**：评估是否需要租户级别的菜单配置
3. **监控告警**：添加跨租户访问的监控和告警机制

### 🚀 **部署建议**

1. **备份数据库**：执行任何迁移前先备份
2. **分步部署**：
   - 先部署代码更新
   - 再执行数据迁移
   - 最后验证功能
3. **回滚准备**：准备回滚方案以防出现问题

---

## 🔄 **重新设计完成（方案二）**

### ✅ **正确的架构设计**

基于用户的正确建议，我们采用了**方案二：FranchiseeTenantRelation 表**，这是更符合业务逻辑的设计：

#### **架构层次**：
- **底层**：`SysUser` - 系统用户认证层（手机号+密码）
- **业务层**：`Franchisee` + `Tenant` - 业务实体层
- **关联层**：`FranchiseeTenantRelation` - 业务关系层（多对多）

#### **核心设计原则**：
1. **一个加盟商可以属于多个租户** ✅
2. **用户使用手机号+密码登录** ✅ (`SysUser.Username` = `Franchisee.Tel`)
3. **登录时支持租户选择机制** ✅
4. **业务层面的租户隔离** ✅

### 🔧 **重新实现的功能**

#### **1. 数据模型设计**
- ✅ **回滚错误设计**：移除了所有模型中的直接TenantID字段
- ✅ **创建关联表**：`FranchiseeTenantRelation` 管理多对多关系
- ✅ **保持模型纯净**：`Franchisee` 和 `Tenant` 保持业务纯净性

#### **2. 加盟商登录流程**
```go
// 新的登录流程支持：
// 1. 手机号+密码认证
// 2. 自动检测加盟商身份
// 3. 租户关联验证
// 4. 智能租户选择（单租户直接登录，多租户提供选择）
// 5. 默认租户机制
// 6. 指定租户登录
```

#### **3. 租户关联服务**
- ✅ **完整的CRUD操作**：添加、移除、查询加盟商租户关联
- ✅ **默认租户管理**：设置和获取加盟商的默认租户
- ✅ **权限验证**：检查加盟商是否属于指定租户
- ✅ **状态管理**：激活/停用加盟商在特定租户中的状态

#### **4. 登录API设计**
```json
// 请求示例
{
  "tel": "13800138000",
  "password": "password123",
  "tenantCode": "tenant001",  // 可选
  "captcha": "1234",
  "captchaId": "uuid"
}

// 响应示例1：直接登录成功
{
  "needTenantSelection": false,
  "user": {...},
  "token": "jwt_token",
  "expiresAt": 1640995200000,
  "tenantId": 1
}

// 响应示例2：需要选择租户
{
  "needTenantSelection": true,
  "availableTenants": [
    {"tenantId": 1, "role": "franchisee", "isDefault": true},
    {"tenantId": 2, "role": "manager", "isDefault": false}
  ]
}
```

### 🎯 **业务优势**

#### **灵活性**：
- ✅ 支持加盟商跨租户经营
- ✅ 支持不同租户中的不同角色
- ✅ 支持动态添加/移除租户关联

#### **安全性**：
- ✅ 严格的租户权限验证
- ✅ 基于JWT的租户上下文
- ✅ 业务层面的数据隔离

#### **用户体验**：
- ✅ 智能的租户选择机制
- ✅ 默认租户快速登录
- ✅ 明确的多租户提示

### 📋 **部署文件清单**

**新增文件**：
- `model/franchisees/franchisee_tenant_relation.go` - 关联模型
- `service/franchisees/franchisee_tenant_relation.go` - 关联服务
- `source/system/franchisee_tenant_relation_migration.go` - 数据迁移
- `docs/sql/create_franchisee_tenant_relation.sql` - SQL脚本

**修改文件**：
- `model/system/request/sys_user.go` - 添加加盟商登录请求
- `model/system/response/sys_user.go` - 添加加盟商登录响应
- `api/v1/system/sys_user.go` - 重新实现加盟商登录
- `service/system/tenant.go` - 添加根据代码获取租户方法
- `initialize/tenant_models.go` - 更新租户模型注册

**回滚文件**：
- 所有之前错误添加TenantID字段的模型文件

### 🚀 **立即行动项**

1. **🔥 立即执行**：
   - 备份数据库
   - 执行 `create_franchisee_tenant_relation.sql` 脚本
   - 启动应用让自动迁移运行

2. **🔍 验证测试**：
   - 测试加盟商登录（单租户场景）
   - 测试加盟商登录（多租户选择场景）
   - 验证租户数据隔离
   - 测试租户切换功能

3. **📊 监控检查**：
   - 检查所有加盟商都有租户关联
   - 验证默认租户设置正确
   - 确认JWT包含正确租户信息

**现在的设计是正确的、可扩展的、符合业务需求的！** 🎯
