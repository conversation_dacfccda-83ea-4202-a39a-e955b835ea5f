-- 为加盟商相关表添加租户ID字段的SQL脚本
-- 执行前请备份数据库！

-- 1. 为加盟商表添加租户ID字段
ALTER TABLE `franchisee` 
ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 COMMENT '租户ID' AFTER `id`;

-- 为加盟商表添加租户ID索引
ALTER TABLE `franchisee` 
ADD INDEX `idx_franchisee_tenant_id` (`tenant_id`);

-- 2. 为加盟商团队表添加租户ID字段
ALTER TABLE `franchisee_team` 
ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 COMMENT '租户ID' AFTER `id`;

-- 为加盟商团队表添加租户ID索引
ALTER TABLE `franchisee_team` 
ADD INDEX `idx_franchisee_team_tenant_id` (`tenant_id`);

-- 3. 为加盟商链接表添加租户ID字段
ALTER TABLE `franchisee_link` 
ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 COMMENT '租户ID' AFTER `id`;

-- 为加盟商链接表添加租户ID索引
ALTER TABLE `franchisee_link` 
ADD INDEX `idx_franchisee_link_tenant_id` (`tenant_id`);

-- 4. 为加盟商成员链接表添加租户ID字段
ALTER TABLE `franchisee_member_link` 
ADD COLUMN `tenant_id` bigint unsigned NOT NULL DEFAULT 1 COMMENT '租户ID' AFTER `id`;

-- 为加盟商成员链接表添加租户ID索引
ALTER TABLE `franchisee_member_link` 
ADD INDEX `idx_franchisee_member_link_tenant_id` (`tenant_id`);

-- 5. 确保默认租户存在
INSERT IGNORE INTO `tenant` (`id`, `name`, `code`, `logo`, `primary_color`, `secondary_color`, `status`, `contact_name`, `contact_phone`, `contact_email`, `created_at`, `updated_at`) 
VALUES (1, '默认租户', 'default', '', '#409EFF', '#67C23A', 1, '系统管理员', '', '', NOW(), NOW());

-- 6. 为现有加盟商数据设置默认租户ID（如果还没有设置的话）
UPDATE `franchisee` SET `tenant_id` = 1 WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;
UPDATE `franchisee_team` SET `tenant_id` = 1 WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;
UPDATE `franchisee_link` SET `tenant_id` = 1 WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;
UPDATE `franchisee_member_link` SET `tenant_id` = 1 WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;

-- 7. 为加盟商用户创建租户关联（如果user_tenant_relation表存在的话）
-- 注意：这个步骤需要根据实际的表结构进行调整
INSERT IGNORE INTO `user_tenant_relation` (`user_id`, `tenant_id`, `role`, `is_default`, `created_at`, `updated_at`)
SELECT DISTINCT f.`user_id`, f.`tenant_id`, 'franchisee', 1, NOW(), NOW()
FROM `franchisee` f 
WHERE f.`user_id` > 0 
AND f.`user_id` NOT IN (
    SELECT `user_id` FROM `user_tenant_relation` WHERE `tenant_id` = f.`tenant_id`
);

-- 验证数据完整性
-- 检查是否所有加盟商都有租户ID
SELECT COUNT(*) as franchisee_without_tenant FROM `franchisee` WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;
SELECT COUNT(*) as franchisee_team_without_tenant FROM `franchisee_team` WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;
SELECT COUNT(*) as franchisee_link_without_tenant FROM `franchisee_link` WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;
SELECT COUNT(*) as franchisee_member_link_without_tenant FROM `franchisee_member_link` WHERE `tenant_id` = 0 OR `tenant_id` IS NULL;

-- 检查租户关联情况
SELECT 
    COUNT(DISTINCT f.user_id) as total_franchisee_users,
    COUNT(DISTINCT utr.user_id) as users_with_tenant_relation
FROM `franchisee` f
LEFT JOIN `user_tenant_relation` utr ON f.user_id = utr.user_id AND f.tenant_id = utr.tenant_id
WHERE f.user_id > 0;
