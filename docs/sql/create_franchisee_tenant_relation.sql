-- 创建加盟商租户关联表的SQL脚本
-- 执行前请备份数据库！

-- 1. 创建加盟商租户关联表
CREATE TABLE IF NOT EXISTS `franchisee_tenant_relation` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `franchisee_id` bigint unsigned NOT NULL COMMENT '加盟商ID',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active/inactive',
  `role` varchar(50) DEFAULT NULL COMMENT '在该租户中的角色',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认租户',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_franchisee_tenant_relation_deleted_at` (`deleted_at`),
  KEY `idx_franchisee_tenant_relation_franchisee_id` (`franchisee_id`),
  KEY `idx_franchisee_tenant_relation_tenant_id` (`tenant_id`),
  KEY `idx_franchisee_tenant_relation_status` (`status`),
  UNIQUE KEY `uk_franchisee_tenant` (`franchisee_id`, `tenant_id`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='加盟商租户关联表';

-- 2. 确保默认租户存在
INSERT IGNORE INTO `tenant` (`id`, `name`, `code`, `logo`, `primary_color`, `secondary_color`, `status`, `contact_name`, `contact_phone`, `created_at`, `updated_at`) 
VALUES (1, '默认租户', 'default', '', '#409EFF', '#67C23A', 1, '系统管理员', '', NOW(), NOW());

-- 3. 为现有加盟商创建租户关联（如果还没有的话）
INSERT IGNORE INTO `franchisee_tenant_relation` 
(`franchisee_id`, `tenant_id`, `status`, `role`, `is_default`, `remark`, `created_at`, `updated_at`)
SELECT 
    f.`id` as franchisee_id,
    1 as tenant_id,
    'active' as status,
    'franchisee' as role,
    1 as is_default,
    '系统迁移时自动创建' as remark,
    NOW() as created_at,
    NOW() as updated_at
FROM `franchisee` f
WHERE f.`id` NOT IN (
    SELECT DISTINCT `franchisee_id` 
    FROM `franchisee_tenant_relation` 
    WHERE `tenant_id` = 1
);

-- 4. 验证数据完整性
-- 检查是否所有加盟商都有租户关联
SELECT 
    COUNT(*) as total_franchisees,
    (SELECT COUNT(DISTINCT franchisee_id) FROM franchisee_tenant_relation WHERE tenant_id = 1) as franchisees_with_tenant_relation
FROM franchisee;

-- 检查是否有重复的默认租户设置
SELECT 
    franchisee_id,
    COUNT(*) as default_tenant_count
FROM franchisee_tenant_relation 
WHERE is_default = 1 
GROUP BY franchisee_id 
HAVING COUNT(*) > 1;

-- 检查租户关联状态分布
SELECT 
    status,
    COUNT(*) as count
FROM franchisee_tenant_relation 
GROUP BY status;

-- 检查角色分布
SELECT 
    role,
    COUNT(*) as count
FROM franchisee_tenant_relation 
GROUP BY role;
