# 加盟商多租户登录设计方案

## 📋 概述

本文档详细描述加盟商在多租户环境下的登录流程设计，包括单租户和多租户场景的处理逻辑。

## 🎯 核心问题

1. **用户身份认证**：使用 `sysUser` 的 `userName + password` 进行登录
2. **多租户选择**：用户可能在多个租户中都有加盟商身份
3. **单租户简化**：只属于一个租户时的自动处理
4. **租户切换**：运行时切换租户身份

## 🏗️ 数据模型设计

### **核心关系**
```
SysUser (1) ←→ (N) Franchisee (N) ←→ (1) Tenant
```

### **数据结构**
```go
// 用户表（身份认证）
type SysUser struct {
    ID       uint   `gorm:"primarykey"`
    Username string `gorm:"unique;not null"`
    Password string `gorm:"not null"`
    // 其他用户基础信息...
}

// 加盟商表（业务身份）
type Franchisee struct {
    global.GVA_MODEL
    TenantID uint   `gorm:"column:tenant_id;not null;index"`
    UserID   uint   `gorm:"column:user_id;not null;index"`
    Code     string `gorm:"column:code;not null"`
    Name     string `gorm:"column:name;not null"`
    // 所有业务字段都按租户隔离...
    
    // 复合唯一索引
    // UNIQUE KEY `uk_tenant_user` (`tenant_id`, `user_id`)
    // UNIQUE KEY `uk_tenant_code` (`tenant_id`, `code`)
}

// 租户表
type Tenant struct {
    ID   uint   `gorm:"primarykey"`
    Name string `gorm:"not null"`
    Code string `gorm:"unique;not null"`
    // 租户配置信息...
}
```

## 🔐 登录流程设计

### **1. 用户认证阶段**

```go
// 第一步：验证用户名密码
func AuthenticateUser(username, password string) (*SysUser, error) {
    var user SysUser
    err := db.Where("username = ?", username).First(&user).Error
    if err != nil {
        return nil, errors.New("用户不存在")
    }
    
    if !checkPassword(user.Password, password) {
        return nil, errors.New("密码错误")
    }
    
    return &user, nil
}
```

### **2. 租户身份查询阶段**

```go
// 第二步：查询用户的加盟商身份
func GetUserFranchisees(userID uint) ([]FranchiseeWithTenant, error) {
    var franchisees []FranchiseeWithTenant
    err := db.Table("franchisee f").
        Select("f.*, t.name as tenant_name, t.code as tenant_code").
        Joins("LEFT JOIN tenant t ON f.tenant_id = t.id").
        Where("f.user_id = ?", userID).
        Find(&franchisees).Error
    
    return franchisees, err
}

type FranchiseeWithTenant struct {
    Franchisee
    TenantName string `json:"tenantName"`
    TenantCode string `json:"tenantCode"`
}
```

### **3. 登录场景处理**

#### **场景1：单租户加盟商**
```go
func HandleSingleTenantLogin(user *SysUser, franchisees []FranchiseeWithTenant) (*LoginResponse, error) {
    if len(franchisees) == 1 {
        franchisee := franchisees[0]
        
        // 直接生成包含租户信息的JWT
        token, err := generateJWTWithTenant(user, &franchisee)
        if err != nil {
            return nil, err
        }
        
        return &LoginResponse{
            Token:       token,
            User:        user,
            Franchisee:  &franchisee,
            TenantID:    franchisee.TenantID,
            NeedChoose:  false,
        }, nil
    }
    
    return nil, errors.New("不是单租户场景")
}
```

#### **场景2：多租户加盟商**
```go
func HandleMultiTenantLogin(user *SysUser, franchisees []FranchiseeWithTenant) (*LoginResponse, error) {
    if len(franchisees) > 1 {
        // 查找默认租户
        defaultFranchisee := findDefaultFranchisee(franchisees)
        
        return &LoginResponse{
            User:              user,
            AvailableTenants:  franchisees,
            DefaultTenant:     defaultFranchisee,
            NeedChoose:        true,
        }, nil
    }
    
    return nil, errors.New("不是多租户场景")
}

func findDefaultFranchisee(franchisees []FranchiseeWithTenant) *FranchiseeWithTenant {
    // 1. 查找用户设置的默认租户
    // 2. 如果没有设置，返回最近登录的租户
    // 3. 如果都没有，返回第一个
    for _, f := range franchisees {
        if f.IsDefault { // 假设有默认标记字段
            return &f
        }
    }
    return &franchisees[0] // 返回第一个作为默认
}
```

#### **场景3：无加盟商身份**
```go
func HandleNoFranchiseeLogin(user *SysUser) (*LoginResponse, error) {
    return &LoginResponse{
        User:       user,
        Message:    "该用户不是加盟商，请联系管理员",
        NeedChoose: false,
        CanLogin:   false,
    }, nil
}
```

### **4. 租户选择确认**

```go
// 用户选择租户后的确认登录
func ConfirmTenantLogin(userID, tenantID uint) (*LoginResponse, error) {
    // 验证用户在该租户中确实有加盟商身份
    var franchisee Franchisee
    err := db.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&franchisee).Error
    if err != nil {
        return nil, errors.New("用户在该租户中没有加盟商身份")
    }
    
    // 获取用户信息
    var user SysUser
    db.First(&user, userID)
    
    // 生成JWT
    token, err := generateJWTWithTenant(&user, &franchisee)
    if err != nil {
        return nil, err
    }
    
    // 记录用户选择（用于下次默认）
    recordUserTenantChoice(userID, tenantID)
    
    return &LoginResponse{
        Token:      token,
        User:       &user,
        Franchisee: &franchisee,
        TenantID:   tenantID,
        NeedChoose: false,
    }, nil
}
```

## 🎨 前端交互设计

### **登录流程图**
```
用户输入用户名密码
        ↓
    验证用户身份
        ↓
   查询加盟商身份
        ↓
    ┌─────────────┐
    │ 判断租户数量 │
    └─────────────┘
         ↓
    ┌────┴────┐
    │ 0个租户  │ → 显示错误信息
    └─────────┘
         ↓
    ┌────┴────┐
    │ 1个租户  │ → 直接登录
    └─────────┘
         ↓
    ┌────┴────┐
    │ 多个租户 │ → 显示租户选择界面
    └─────────┘
         ↓
    用户选择租户
         ↓
    确认登录成功
```

### **租户选择界面设计**
```json
{
  "needChoose": true,
  "user": {
    "id": 123,
    "username": "franchisee001"
  },
  "availableTenants": [
    {
      "tenantId": 1,
      "tenantName": "租户A",
      "tenantCode": "TENANT_A",
      "franchiseeId": 456,
      "franchiseeName": "张三的店铺",
      "franchiseeCode": "F001",
      "isDefault": true
    },
    {
      "tenantId": 2,
      "tenantName": "租户B", 
      "tenantCode": "TENANT_B",
      "franchiseeId": 789,
      "franchiseeName": "张三的分店",
      "franchiseeCode": "F002",
      "isDefault": false
    }
  ]
}
```

## 🔧 JWT Token 设计

### **Token 结构**
```go
type JWTClaims struct {
    UserID       uint   `json:"user_id"`
    Username     string `json:"username"`
    TenantID     uint   `json:"tenant_id"`
    FranchiseeID uint   `json:"franchisee_id"`
    TenantCode   string `json:"tenant_code"`
    
    jwt.StandardClaims
}

func generateJWTWithTenant(user *SysUser, franchisee *Franchisee) (string, error) {
    claims := JWTClaims{
        UserID:       user.ID,
        Username:     user.Username,
        TenantID:     franchisee.TenantID,
        FranchiseeID: franchisee.ID,
        TenantCode:   franchisee.TenantCode, // 需要关联查询
        StandardClaims: jwt.StandardClaims{
            ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
            Issuer:    "guanpu-server",
        },
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(jwtSecret))
}
```

## 🔄 租户切换功能

### **运行时切换**
```go
// 切换租户接口
func SwitchTenant(userID, newTenantID uint) (*LoginResponse, error) {
    // 验证用户在新租户中有加盟商身份
    var franchisee Franchisee
    err := db.Where("user_id = ? AND tenant_id = ?", userID, newTenantID).First(&franchisee).Error
    if err != nil {
        return nil, errors.New("用户在该租户中没有加盟商身份")
    }
    
    // 重新生成JWT
    var user SysUser
    db.First(&user, userID)
    
    token, err := generateJWTWithTenant(&user, &franchisee)
    if err != nil {
        return nil, err
    }
    
    return &LoginResponse{
        Token:      token,
        User:       &user,
        Franchisee: &franchisee,
        TenantID:   newTenantID,
        Switched:   true,
    }, nil
}
```

## 📱 移动端实现细节

### **APP登录流程**
```javascript
// 1. 用户登录
async function login(username, password) {
    const response = await api.post('/app/auth/login', {
        username,
        password
    });

    if (response.needChoose) {
        // 显示租户选择界面
        showTenantSelector(response.availableTenants, response.defaultTenant);
    } else {
        // 直接登录成功
        saveToken(response.token);
        navigateToHome();
    }
}

// 2. 租户选择
async function selectTenant(tenantId) {
    const response = await api.post('/app/auth/confirm-tenant', {
        tenantId
    });

    saveToken(response.token);
    navigateToHome();
}

// 3. 租户切换
async function switchTenant(tenantId) {
    const response = await api.post('/app/auth/switch-tenant', {
        tenantId
    });

    saveToken(response.token);
    // 刷新当前页面数据
    refreshCurrentPage();
}
```

### **本地存储策略**
```javascript
// 存储用户的租户选择偏好
const UserPreference = {
    saveDefaultTenant(userId, tenantId) {
        localStorage.setItem(`default_tenant_${userId}`, tenantId);
    },

    getDefaultTenant(userId) {
        return localStorage.getItem(`default_tenant_${userId}`);
    },

    saveUserTenants(userId, tenants) {
        localStorage.setItem(`user_tenants_${userId}`, JSON.stringify(tenants));
    }
};
```

## 🔒 安全考虑

### **1. 租户隔离验证**
```go
// 中间件：验证用户访问权限
func TenantAccessMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        claims, exists := c.Get("claims")
        if !exists {
            c.JSON(401, gin.H{"error": "未授权访问"})
            c.Abort()
            return
        }

        jwtClaims := claims.(*JWTClaims)

        // 验证用户在当前租户中确实有加盟商身份
        var count int64
        db.Model(&Franchisee{}).
            Where("user_id = ? AND tenant_id = ?", jwtClaims.UserID, jwtClaims.TenantID).
            Count(&count)

        if count == 0 {
            c.JSON(403, gin.H{"error": "无权访问该租户数据"})
            c.Abort()
            return
        }

        // 设置租户上下文
        c.Set("tenant_id", jwtClaims.TenantID)
        c.Set("franchisee_id", jwtClaims.FranchiseeID)
        c.Next()
    }
}
```

### **2. 防止租户数据泄露**
```go
// 所有查询都必须包含租户过滤
func GetFranchiseeOrders(franchiseeID, tenantID uint) ([]Order, error) {
    var orders []Order
    err := db.Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).
        Find(&orders).Error
    return orders, err
}
```

## 🎯 用户体验优化

### **1. 智能默认选择**
```go
// 根据用户行为智能选择默认租户
func getSmartDefaultTenant(userID uint, franchisees []Franchisee) *Franchisee {
    // 1. 用户手动设置的默认租户
    if defaultTenant := getUserSetDefaultTenant(userID); defaultTenant != nil {
        return defaultTenant
    }

    // 2. 最近登录的租户
    if recentTenant := getRecentLoginTenant(userID); recentTenant != nil {
        return recentTenant
    }

    // 3. 业务活跃度最高的租户
    if activeTenant := getMostActiveTenant(userID, franchisees); activeTenant != nil {
        return activeTenant
    }

    // 4. 默认返回第一个
    if len(franchisees) > 0 {
        return &franchisees[0]
    }

    return nil
}
```

### **2. 租户切换提示**
```go
type TenantSwitchResponse struct {
    Success     bool   `json:"success"`
    NewToken    string `json:"newToken"`
    TenantName  string `json:"tenantName"`
    Message     string `json:"message"`
    NeedRefresh bool   `json:"needRefresh"` // 是否需要刷新页面数据
}
```

## 🔧 API接口设计

### **登录相关接口**
```go
// POST /app/auth/login
type LoginRequest struct {
    Username string `json:"username" binding:"required"`
    Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
    Success          bool                    `json:"success"`
    Token            string                  `json:"token,omitempty"`
    User             *SysUser               `json:"user,omitempty"`
    Franchisee       *Franchisee            `json:"franchisee,omitempty"`
    TenantID         uint                   `json:"tenantId,omitempty"`
    NeedChoose       bool                   `json:"needChoose"`
    AvailableTenants []FranchiseeWithTenant `json:"availableTenants,omitempty"`
    DefaultTenant    *FranchiseeWithTenant  `json:"defaultTenant,omitempty"`
    Message          string                 `json:"message,omitempty"`
}

// POST /app/auth/confirm-tenant
type ConfirmTenantRequest struct {
    TenantID uint `json:"tenantId" binding:"required"`
}

// POST /app/auth/switch-tenant
type SwitchTenantRequest struct {
    TenantID uint `json:"tenantId" binding:"required"`
}

// GET /app/auth/my-tenants
type MyTenantsResponse struct {
    Tenants []FranchiseeWithTenant `json:"tenants"`
    Current *FranchiseeWithTenant  `json:"current"`
}
```

## 📊 数据库设计补充

### **用户租户偏好表**
```sql
CREATE TABLE user_tenant_preference (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    default_tenant_id INT,
    last_login_tenant_id INT,
    last_login_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_user (user_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id),
    FOREIGN KEY (default_tenant_id) REFERENCES tenant(id),
    FOREIGN KEY (last_login_tenant_id) REFERENCES tenant(id)
);
```

### **登录日志表**
```sql
CREATE TABLE franchisee_login_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tenant_id INT NOT NULL,
    franchisee_id INT NOT NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,

    INDEX idx_user_time (user_id, login_time),
    INDEX idx_tenant_time (tenant_id, login_time),
    FOREIGN KEY (user_id) REFERENCES sys_user(id),
    FOREIGN KEY (tenant_id) REFERENCES tenant(id),
    FOREIGN KEY (franchisee_id) REFERENCES franchisee(id)
);
```

## 🚀 实施建议

### **分阶段实施**
1. **第一阶段**：实现基础的多租户登录逻辑
2. **第二阶段**：添加智能默认选择和用户偏好
3. **第三阶段**：完善用户体验和安全加固

### **测试用例**
1. 单租户用户登录测试
2. 多租户用户登录测试
3. 租户切换功能测试
4. 安全边界测试
5. 并发登录测试

---

**文档版本**: v1.1
**创建日期**: 2024-12-25
**最后更新**: 2024-12-25
**维护团队**: 技术团队
**审核状态**: 详细设计方案
