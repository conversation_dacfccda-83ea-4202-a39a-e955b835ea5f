# 加盟商租户关系业务流程验证

## 📋 **验证概述**

本文档提供了完整的业务流程验证步骤，用于验证加盟商租户关系设计的正确性和完整性。

## 🔄 **核心业务流程验证**

### **流程1：新加盟商注册流程**

#### **步骤说明**
1. 创建系统用户账号（手机号+密码）
2. 创建加盟商信息
3. 建立加盟商与租户的关联关系
4. 验证登录功能

#### **验证脚本**
```bash
#!/bin/bash
# 新加盟商注册流程验证

echo "=== 新加盟商注册流程验证 ==="

# 1. 创建系统用户
curl -X POST http://localhost:8888/user/admin \
  -H "Content-Type: application/json" \
  -d '{
    "username": "13900139999",
    "password": "test123456",
    "enable": 1
  }'

# 2. 创建加盟商
curl -X POST http://localhost:8888/franchisee/createFranchisee \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "tel": "13900139999",
    "name": "测试加盟商",
    "code": "TEST001"
  }'

# 3. 建立租户关联
curl -X POST http://localhost:8888/franchisee/addToTenant \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "franchiseeId": 999,
    "tenantId": 1,
    "role": "franchisee",
    "isDefault": true
  }'

# 4. 验证登录
curl -X POST http://localhost:8888/base/franchiseeLogin \
  -H "Content-Type: application/json" \
  -d '{
    "tel": "13900139999",
    "password": "test123456",
    "captcha": "1234",
    "captchaId": "test"
  }'

echo "新加盟商注册流程验证完成"
```

### **流程2：多租户加盟商管理流程**

#### **步骤说明**
1. 将现有加盟商添加到新租户
2. 设置不同租户中的不同角色
3. 验证多租户登录选择
4. 验证租户切换功能

#### **验证脚本**
```bash
#!/bin/bash
# 多租户加盟商管理流程验证

echo "=== 多租户加盟商管理流程验证 ==="

FRANCHISEE_ID=2  # 李四的ID

# 1. 添加到新租户
curl -X POST http://localhost:8888/franchisee/addToTenant \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "franchiseeId": '$FRANCHISEE_ID',
    "tenantId": 4,
    "role": "manager",
    "isDefault": false
  }'

# 2. 查询加盟商的所有租户关联
curl -X GET "http://localhost:8888/franchisee/tenants?franchiseeId=$FRANCHISEE_ID" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 3. 测试多租户登录（不指定租户）
echo "测试多租户登录（应使用默认租户）:"
curl -X POST http://localhost:8888/base/franchiseeLogin \
  -H "Content-Type: application/json" \
  -d '{
    "tel": "13800138002",
    "password": "password123"
  }'

# 4. 测试指定租户登录
echo "测试指定租户登录:"
curl -X POST http://localhost:8888/base/franchiseeLogin \
  -H "Content-Type: application/json" \
  -d '{
    "tel": "13800138002",
    "password": "password123",
    "tenantCode": "tenant_a"
  }'

# 5. 更改默认租户
curl -X PUT http://localhost:8888/franchisee/setDefaultTenant \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{
    "franchiseeId": '$FRANCHISEE_ID',
    "tenantId": 2
  }'

# 6. 再次测试默认登录
echo "测试更改默认租户后的登录:"
curl -X POST http://localhost:8888/base/franchiseeLogin \
  -H "Content-Type: application/json" \
  -d '{
    "tel": "13800138002",
    "password": "password123"
  }'

echo "多租户加盟商管理流程验证完成"
```

### **流程3：租户数据隔离验证**

#### **步骤说明**
1. 使用不同租户的token访问数据
2. 验证数据隔离效果
3. 验证跨租户访问被阻止

#### **验证脚本**
```bash
#!/bin/bash
# 租户数据隔离验证

echo "=== 租户数据隔离验证 ==="

# 1. 获取租户1的token
TENANT1_TOKEN=$(curl -s -X POST http://localhost:8888/base/franchiseeLogin \
  -H "Content-Type: application/json" \
  -d '{
    "tel": "13800138002",
    "password": "password123",
    "tenantCode": "default"
  }' | jq -r '.data.token')

# 2. 获取租户2的token
TENANT2_TOKEN=$(curl -s -X POST http://localhost:8888/base/franchiseeLogin \
  -H "Content-Type: application/json" \
  -d '{
    "tel": "13800138002",
    "password": "password123",
    "tenantCode": "tenant_a"
  }' | jq -r '.data.token')

# 3. 使用租户1 token访问加盟商列表
echo "使用租户1 token访问加盟商列表:"
curl -X GET http://localhost:8888/franchisee/getFranchiseeList \
  -H "Authorization: Bearer $TENANT1_TOKEN"

# 4. 使用租户2 token访问加盟商列表
echo "使用租户2 token访问加盟商列表:"
curl -X GET http://localhost:8888/franchisee/getFranchiseeList \
  -H "Authorization: Bearer $TENANT2_TOKEN"

# 5. 验证订单数据隔离
echo "验证订单数据隔离:"
curl -X GET http://localhost:8888/orders/getOrderList \
  -H "Authorization: Bearer $TENANT1_TOKEN"

curl -X GET http://localhost:8888/orders/getOrderList \
  -H "Authorization: Bearer $TENANT2_TOKEN"

echo "租户数据隔离验证完成"
```

## 🧪 **手动验证步骤**

### **验证1：数据库数据一致性检查**

```sql
-- 1. 检查所有加盟商都有租户关联
SELECT 
    f.id as franchisee_id,
    f.name as franchisee_name,
    COUNT(ftr.id) as tenant_count
FROM franchisee f
LEFT JOIN franchisee_tenant_relation ftr ON f.id = ftr.franchisee_id 
    AND ftr.status = 'active'
GROUP BY f.id, f.name
ORDER BY tenant_count;

-- 2. 检查默认租户设置
SELECT 
    franchisee_id,
    COUNT(*) as default_count
FROM franchisee_tenant_relation 
WHERE is_default = 1 
GROUP BY franchisee_id 
HAVING COUNT(*) > 1;

-- 3. 检查租户关联状态分布
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM franchisee_tenant_relation), 2) as percentage
FROM franchisee_tenant_relation 
GROUP BY status;

-- 4. 检查角色分布
SELECT 
    role,
    COUNT(*) as count
FROM franchisee_tenant_relation 
GROUP BY role
ORDER BY count DESC;
```

### **验证2：API响应格式验证**

#### **单租户登录响应验证**
```json
{
  "code": 0,
  "data": {
    "needTenantSelection": false,
    "user": {
      "id": 1,
      "username": "13800138001",
      "nickName": "张三"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1640995200000,
    "tenantId": 1
  },
  "msg": "加盟商登录成功"
}
```

#### **多租户选择响应验证**
```json
{
  "code": 0,
  "data": {
    "needTenantSelection": true,
    "availableTenants": [
      {
        "id": 1,
        "franchiseeId": 2,
        "tenantId": 1,
        "status": "active",
        "role": "franchisee",
        "isDefault": true,
        "tenant": {
          "id": 1,
          "name": "默认租户",
          "code": "default"
        }
      },
      {
        "id": 2,
        "franchiseeId": 2,
        "tenantId": 2,
        "status": "active",
        "role": "manager",
        "isDefault": false,
        "tenant": {
          "id": 2,
          "name": "租户A",
          "code": "tenant_a"
        }
      }
    ]
  },
  "msg": "请选择要登录的租户"
}
```

### **验证3：JWT Token内容验证**

```bash
# 解析JWT token验证租户信息
echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | \
  cut -d'.' -f2 | \
  base64 -d | \
  jq '.'

# 期望输出包含:
# {
#   "userId": 2,
#   "tenantId": 1,
#   "username": "13800138002",
#   "exp": 1640995200,
#   ...
# }
```

## ✅ **验证检查清单**

### **功能验证**
- [ ] 单租户加盟商可以正常登录
- [ ] 多租户加盟商可以选择租户登录
- [ ] 默认租户机制工作正常
- [ ] 指定租户登录功能正常
- [ ] 租户切换功能正常
- [ ] 非加盟商用户无法使用加盟商登录
- [ ] 无租户关联的加盟商无法登录
- [ ] 停用状态的租户关联无法登录

### **数据验证**
- [ ] 所有加盟商都有至少一个租户关联
- [ ] 每个加盟商最多只有一个默认租户
- [ ] 租户关联状态正确
- [ ] 角色分配合理
- [ ] 数据库约束正常工作

### **安全验证**
- [ ] JWT包含正确的租户信息
- [ ] 不同租户的token不能互相使用
- [ ] 租户数据完全隔离
- [ ] 权限验证正常工作
- [ ] 跨租户访问被正确阻止

### **性能验证**
- [ ] 登录响应时间 < 500ms
- [ ] 租户关联查询性能良好
- [ ] 并发登录处理正常
- [ ] 大量数据下性能稳定

### **错误处理验证**
- [ ] 错误信息清晰明确
- [ ] 异常情况处理完善
- [ ] 日志记录完整
- [ ] 回滚机制正常

## 📊 **验证报告模板**

```markdown
# 加盟商租户关系验证报告

## 验证环境
- 测试时间: YYYY-MM-DD HH:mm:ss
- 测试环境: [开发/测试/预生产]
- 数据库版本: MySQL 8.0
- 应用版本: v1.0.0

## 验证结果
### 功能验证: ✅ 通过 / ❌ 失败
### 数据验证: ✅ 通过 / ❌ 失败  
### 安全验证: ✅ 通过 / ❌ 失败
### 性能验证: ✅ 通过 / ❌ 失败

## 发现问题
1. [问题描述]
   - 严重程度: [高/中/低]
   - 影响范围: [描述]
   - 解决方案: [描述]

## 验证结论
[总体评估和建议]
```
