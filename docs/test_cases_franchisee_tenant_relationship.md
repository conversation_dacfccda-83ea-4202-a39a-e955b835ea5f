# 加盟商租户关系测试用例

## 📋 **测试概述**

本文档包含了验证加盟商租户关系设计的完整测试用例，涵盖数据模型、业务逻辑、API接口和边界条件等各个方面。

## 🗃️ **测试数据准备**

### **基础数据**

```sql
-- 1. 创建测试租户
INSERT INTO `tenant` (`id`, `name`, `code`, `status`, `contact_name`) VALUES
(1, '默认租户', 'default', 1, '系统管理员'),
(2, '租户A', 'tenant_a', 1, '租户A管理员'),
(3, '租户B', 'tenant_b', 1, '租户B管理员'),
(4, '停用租户', 'tenant_disabled', 0, '停用租户管理员');

-- 2. 创建测试用户
INSERT INTO `sys_users` (`id`, `username`, `password`, `enable`) VALUES
(1, '13800138001', '$2a$10$hashed_password1', 1),  -- 单租户加盟商
(2, '13800138002', '$2a$10$hashed_password2', 1),  -- 多租户加盟商
(3, '13800138003', '$2a$10$hashed_password3', 1),  -- 无租户关联加盟商
(4, '13800138004', '$2a$10$hashed_password4', 0),  -- 被禁用用户
(5, '13800138005', '$2a$10$hashed_password5', 1);  -- 非加盟商用户

-- 3. 创建测试加盟商
INSERT INTO `franchisee` (`id`, `user_id`, `tel`, `name`, `code`) VALUES
(1, 1, '13800138001', '张三加盟商', 'F001'),
(2, 2, '13800138002', '李四加盟商', 'F002'),
(3, 3, '13800138003', '王五加盟商', 'F003'),
(4, 4, '13800138004', '赵六加盟商', 'F004');

-- 4. 创建加盟商租户关联
INSERT INTO `franchisee_tenant_relation` 
(`franchisee_id`, `tenant_id`, `status`, `role`, `is_default`, `remark`) VALUES
-- 张三：只属于默认租户
(1, 1, 'active', 'franchisee', 1, '单租户测试用户'),

-- 李四：属于多个租户，有默认租户
(2, 1, 'active', 'franchisee', 1, '多租户测试用户-默认租户'),
(2, 2, 'active', 'manager', 0, '多租户测试用户-租户A'),
(2, 3, 'active', 'franchisee', 0, '多租户测试用户-租户B'),

-- 王五：无租户关联（用于测试错误场景）

-- 赵六：属于停用租户
(4, 4, 'active', 'franchisee', 1, '停用租户测试用户');
```

## 🧪 **单元测试用例**

### **1. 数据模型测试**

#### **测试用例 1.1：FranchiseeTenantRelation 模型创建**

```go
func TestFranchiseeTenantRelationCreate(t *testing.T) {
    relation := franchisees.FranchiseeTenantRelation{
        FranchiseeID: 1,
        TenantID:     1,
        Status:       franchisees.StatusActive,
        Role:         franchisees.RoleFranchisee,
        IsDefault:    true,
        Remark:       "测试关联",
    }

    err := global.GVA_DB.Create(&relation).Error
    assert.NoError(t, err)
    assert.NotZero(t, relation.ID)
}
```

#### **测试用例 1.2：唯一约束测试**

```go
func TestFranchiseeTenantRelationUniqueConstraint(t *testing.T) {
    // 尝试创建重复的关联
    relation1 := franchisees.FranchiseeTenantRelation{
        FranchiseeID: 1,
        TenantID:     1,
        Status:       franchisees.StatusActive,
        Role:         franchisees.RoleFranchisee,
    }

    relation2 := franchisees.FranchiseeTenantRelation{
        FranchiseeID: 1,
        TenantID:     1,
        Status:       franchisees.StatusActive,
        Role:         franchisees.RoleManager,
    }

    err1 := global.GVA_DB.Create(&relation1).Error
    assert.NoError(t, err1)

    err2 := global.GVA_DB.Create(&relation2).Error
    assert.Error(t, err2) // 应该失败，违反唯一约束
}
```

### **2. 业务服务测试**

#### **测试用例 2.1：添加加盟商到租户**

```go
func TestAddFranchiseeToTenant(t *testing.T) {
    service := &franchisees.FranchiseeTenantRelationService{}

    // 正常添加
    err := service.AddFranchiseeToTenant(1, 2, franchisees.RoleFranchisee, false)
    assert.NoError(t, err)

    // 重复添加应该失败
    err = service.AddFranchiseeToTenant(1, 2, franchisees.RoleFranchisee, false)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "已属于该租户")
}
```

#### **测试用例 2.2：获取加盟商的租户列表**

```go
func TestGetTenantsByFranchisee(t *testing.T) {
    service := &franchisees.FranchiseeTenantRelationService{}

    // 获取多租户加盟商的租户列表
    relations, err := service.GetTenantsByFranchisee(2)
    assert.NoError(t, err)
    assert.Len(t, relations, 3) // 李四属于3个租户

    // 获取单租户加盟商的租户列表
    relations, err = service.GetTenantsByFranchisee(1)
    assert.NoError(t, err)
    assert.Len(t, relations, 1) // 张三只属于1个租户

    // 获取无租户关联加盟商的租户列表
    relations, err = service.GetTenantsByFranchisee(3)
    assert.NoError(t, err)
    assert.Len(t, relations, 0) // 王五没有租户关联
}
```

#### **测试用例 2.3：默认租户管理**

```go
func TestDefaultTenantManagement(t *testing.T) {
    service := &franchisees.FranchiseeTenantRelationService{}

    // 获取默认租户
    defaultRelation, err := service.GetFranchiseeDefaultTenant(2)
    assert.NoError(t, err)
    assert.Equal(t, uint(1), defaultRelation.TenantID)
    assert.True(t, defaultRelation.IsDefault)

    // 设置新的默认租户
    err = service.SetFranchiseeDefaultTenant(2, 2)
    assert.NoError(t, err)

    // 验证默认租户已更改
    defaultRelation, err = service.GetFranchiseeDefaultTenant(2)
    assert.NoError(t, err)
    assert.Equal(t, uint(2), defaultRelation.TenantID)
}
```

## 🌐 **API接口测试**

### **3. 加盟商登录API测试**

#### **测试用例 3.1：单租户加盟商登录**

```go
func TestSingleTenantFranchiseeLogin(t *testing.T) {
    // 准备请求
    loginReq := systemReq.FranchiseeLogin{
        Tel:      "13800138001",
        Password: "password123",
        Captcha:  "1234",
        CaptchaId: "test-captcha-id",
    }

    // 发送请求
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    // 验证响应
    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int `json:"code"`
        Data systemRes.FranchiseeLoginResponse `json:"data"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.Equal(t, 0, response.Code)
    assert.False(t, response.Data.NeedTenantSelection)
    assert.NotEmpty(t, response.Data.Token)
    assert.Equal(t, uint(1), response.Data.TenantID)
    assert.NotNil(t, response.Data.User)
}
```

#### **测试用例 3.2：多租户加盟商登录（无指定租户）**

```go
func TestMultiTenantFranchiseeLoginWithoutTenantCode(t *testing.T) {
    // 准备请求（不指定租户代码）
    loginReq := systemReq.FranchiseeLogin{
        Tel:      "13800138002",
        Password: "password123",
    }

    // 发送请求
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    // 验证响应
    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int `json:"code"`
        Data systemRes.FranchiseeLoginResponse `json:"data"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.Equal(t, 0, response.Code)

    // 由于李四有默认租户，应该直接登录成功
    assert.False(t, response.Data.NeedTenantSelection)
    assert.NotEmpty(t, response.Data.Token)
    assert.Equal(t, uint(1), response.Data.TenantID) // 默认租户
}
```

#### **测试用例 3.3：多租户加盟商登录（指定租户）**

```go
func TestMultiTenantFranchiseeLoginWithTenantCode(t *testing.T) {
    // 准备请求（指定租户代码）
    loginReq := systemReq.FranchiseeLogin{
        Tel:        "13800138002",
        Password:   "password123",
        TenantCode: "tenant_a",
    }

    // 发送请求
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    // 验证响应
    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int `json:"code"`
        Data systemRes.FranchiseeLoginResponse `json:"data"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.Equal(t, 0, response.Code)
    assert.False(t, response.Data.NeedTenantSelection)
    assert.NotEmpty(t, response.Data.Token)
    assert.Equal(t, uint(2), response.Data.TenantID) // 租户A
}
```

## ❌ **错误场景测试**

### **4. 异常情况测试**

#### **测试用例 4.1：非加盟商用户登录**

```go
func TestNonFranchiseeUserLogin(t *testing.T) {
    loginReq := systemReq.FranchiseeLogin{
        Tel:      "13800138005", // 非加盟商用户
        Password: "password123",
    }

    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int    `json:"code"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.NotEqual(t, 0, response.Code)
    assert.Contains(t, response.Msg, "不是加盟商")
}
```

#### **测试用例 4.2：无租户关联的加盟商登录**

```go
func TestFranchiseeWithoutTenantLogin(t *testing.T) {
    loginReq := systemReq.FranchiseeLogin{
        Tel:      "13800138003", // 王五，无租户关联
        Password: "password123",
    }

    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int    `json:"code"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.NotEqual(t, 0, response.Code)
    assert.Contains(t, response.Msg, "没有关联任何租户")
}
```

#### **测试用例 4.3：指定不存在的租户登录**

```go
func TestLoginWithNonExistentTenant(t *testing.T) {
    loginReq := systemReq.FranchiseeLogin{
        Tel:        "13800138002",
        Password:   "password123",
        TenantCode: "non_existent_tenant",
    }

    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int    `json:"code"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.NotEqual(t, 0, response.Code)
    assert.Contains(t, response.Msg, "租户不存在")
}
```

#### **测试用例 4.4：指定无权限的租户登录**

```go
func TestLoginWithUnauthorizedTenant(t *testing.T) {
    loginReq := systemReq.FranchiseeLogin{
        Tel:        "13800138001", // 张三只属于默认租户
        Password:   "password123",
        TenantCode: "tenant_a",    // 尝试登录租户A
    }

    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin", 
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int    `json:"code"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.NotEqual(t, 0, response.Code)
    assert.Contains(t, response.Msg, "不属于指定租户")
}
```

## 🔄 **集成测试用例**

### **5. 完整业务流程测试**

#### **测试用例 5.1：新加盟商注册到登录完整流程**

```go
func TestCompleteNewFranchiseeFlow(t *testing.T) {
    // 1. 创建系统用户
    user := system.SysUser{
        Username: "13900139001",
        Password: hashPassword("newpassword"),
        Enable:   1,
    }
    err := global.GVA_DB.Create(&user).Error
    assert.NoError(t, err)

    // 2. 创建加盟商
    franchisee := franchisees.Franchisee{
        UserId: user.ID,
        Tel:    "13900139001",
        Name:   "新加盟商",
        Code:   "F999",
    }
    err = global.GVA_DB.Create(&franchisee).Error
    assert.NoError(t, err)

    // 3. 添加到租户
    ftrService := &franchisees.FranchiseeTenantRelationService{}
    err = ftrService.AddFranchiseeToTenant(franchisee.ID, 1,
        franchisees.RoleFranchisee, true)
    assert.NoError(t, err)

    // 4. 测试登录
    loginReq := systemReq.FranchiseeLogin{
        Tel:      "13900139001",
        Password: "newpassword",
    }

    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin",
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    assert.Equal(t, 200, w.Code)

    var response struct {
        Code int `json:"code"`
        Data systemRes.FranchiseeLoginResponse `json:"data"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.Equal(t, 0, response.Code)
    assert.False(t, response.Data.NeedTenantSelection)
    assert.NotEmpty(t, response.Data.Token)
}
```

#### **测试用例 5.2：加盟商跨租户切换流程**

```go
func TestFranchiseeTenantSwitchFlow(t *testing.T) {
    // 1. 首次登录默认租户
    loginReq := systemReq.FranchiseeLogin{
        Tel:      "13800138002",
        Password: "password123",
    }

    w1 := httptest.NewRecorder()
    req1, _ := http.NewRequest("POST", "/base/franchiseeLogin",
        strings.NewReader(toJSON(loginReq)))
    req1.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w1, req1)

    var response1 struct {
        Data systemRes.FranchiseeLoginResponse `json:"data"`
    }
    json.Unmarshal(w1.Body.Bytes(), &response1)

    assert.Equal(t, uint(1), response1.Data.TenantID) // 默认租户

    // 2. 切换到租户A
    loginReq.TenantCode = "tenant_a"

    w2 := httptest.NewRecorder()
    req2, _ := http.NewRequest("POST", "/base/franchiseeLogin",
        strings.NewReader(toJSON(loginReq)))
    req2.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w2, req2)

    var response2 struct {
        Data systemRes.FranchiseeLoginResponse `json:"data"`
    }
    json.Unmarshal(w2.Body.Bytes(), &response2)

    assert.Equal(t, uint(2), response2.Data.TenantID) // 租户A

    // 3. 验证JWT中的租户信息
    j := &utils.JWT{SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey)}
    claims, err := j.ParseToken(response2.Data.Token)
    assert.NoError(t, err)
    assert.Equal(t, uint(2), claims.TenantID)
}
```

## 📊 **性能测试用例**

### **6. 性能和并发测试**

#### **测试用例 6.1：并发登录测试**

```go
func TestConcurrentFranchiseeLogin(t *testing.T) {
    const concurrency = 100
    const iterations = 10

    var wg sync.WaitGroup
    errors := make(chan error, concurrency*iterations)

    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(goroutineID int) {
            defer wg.Done()

            for j := 0; j < iterations; j++ {
                loginReq := systemReq.FranchiseeLogin{
                    Tel:      "13800138001",
                    Password: "password123",
                }

                w := httptest.NewRecorder()
                req, _ := http.NewRequest("POST", "/base/franchiseeLogin",
                    strings.NewReader(toJSON(loginReq)))
                req.Header.Set("Content-Type", "application/json")

                router.ServeHTTP(w, req)

                if w.Code != 200 {
                    errors <- fmt.Errorf("goroutine %d iteration %d failed with code %d",
                        goroutineID, j, w.Code)
                }
            }
        }(i)
    }

    wg.Wait()
    close(errors)

    errorCount := 0
    for err := range errors {
        t.Logf("Concurrent test error: %v", err)
        errorCount++
    }

    assert.Equal(t, 0, errorCount, "并发登录测试应该没有错误")
}
```

#### **测试用例 6.2：大量租户关联查询性能测试**

```go
func TestLargeTenantRelationQueryPerformance(t *testing.T) {
    // 创建大量测试数据
    const testFranchiseeCount = 1000
    const testTenantCount = 50

    // 批量创建测试数据
    var relations []franchisees.FranchiseeTenantRelation
    for i := 1; i <= testFranchiseeCount; i++ {
        for j := 1; j <= testTenantCount; j++ {
            if rand.Float32() < 0.1 { // 10%的概率创建关联
                relations = append(relations, franchisees.FranchiseeTenantRelation{
                    FranchiseeID: uint(i),
                    TenantID:     uint(j),
                    Status:       franchisees.StatusActive,
                    Role:         franchisees.RoleFranchisee,
                    IsDefault:    j == 1, // 第一个租户为默认
                })
            }
        }
    }

    err := global.GVA_DB.CreateInBatches(&relations, 100).Error
    assert.NoError(t, err)

    // 性能测试
    service := &franchisees.FranchiseeTenantRelationService{}

    start := time.Now()
    for i := 1; i <= 100; i++ {
        _, err := service.GetTenantsByFranchisee(uint(i))
        assert.NoError(t, err)
    }
    duration := time.Since(start)

    t.Logf("查询100个加盟商的租户关联耗时: %v", duration)
    assert.Less(t, duration, time.Second, "查询性能应该在1秒内")

    // 清理测试数据
    global.GVA_DB.Where("1=1").Delete(&franchisees.FranchiseeTenantRelation{})
}
```

## 🛡️ **安全测试用例**

### **7. 安全性测试**

#### **测试用例 7.1：JWT租户隔离验证**

```go
func TestJWTTenantIsolation(t *testing.T) {
    // 1. 获取租户1的token
    token1 := loginAndGetToken("13800138002", "tenant_a") // 租户A
    token2 := loginAndGetToken("13800138002", "tenant_b") // 租户B

    // 2. 解析token验证租户信息
    j := &utils.JWT{SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey)}

    claims1, err := j.ParseToken(token1)
    assert.NoError(t, err)
    assert.Equal(t, uint(2), claims1.TenantID)

    claims2, err := j.ParseToken(token2)
    assert.NoError(t, err)
    assert.Equal(t, uint(3), claims2.TenantID)

    // 3. 验证token不能跨租户使用
    assert.NotEqual(t, claims1.TenantID, claims2.TenantID)
}
```

#### **测试用例 7.2：权限边界测试**

```go
func TestPermissionBoundaryTest(t *testing.T) {
    service := &franchisees.FranchiseeTenantRelationService{}

    // 测试停用状态的租户关联
    err := service.DeactivateFranchiseeInTenant(2, 2)
    assert.NoError(t, err)

    // 验证停用后无法登录该租户
    loginReq := systemReq.FranchiseeLogin{
        Tel:        "13800138002",
        Password:   "password123",
        TenantCode: "tenant_a",
    }

    w := httptest.NewRecorder()
    req, _ := http.NewRequest("POST", "/base/franchiseeLogin",
        strings.NewReader(toJSON(loginReq)))
    req.Header.Set("Content-Type", "application/json")

    router.ServeHTTP(w, req)

    var response struct {
        Code int    `json:"code"`
        Msg  string `json:"msg"`
    }
    json.Unmarshal(w.Body.Bytes(), &response)

    assert.NotEqual(t, 0, response.Code)
    assert.Contains(t, response.Msg, "不属于指定租户")

    // 恢复激活状态
    err = service.ActivateFranchiseeInTenant(2, 2)
    assert.NoError(t, err)
}
```

## 📈 **测试执行计划**

### **测试执行顺序**

1. **单元测试** → 验证基础功能
2. **API测试** → 验证接口功能
3. **集成测试** → 验证业务流程
4. **错误场景测试** → 验证异常处理
5. **性能测试** → 验证系统性能
6. **安全测试** → 验证安全机制

### **测试覆盖率目标**

- **代码覆盖率**: ≥ 90%
- **分支覆盖率**: ≥ 85%
- **功能覆盖率**: 100%

### **测试环境要求**

- 独立的测试数据库
- 模拟的验证码服务
- 完整的应用配置
- 测试数据自动清理机制
  
  ```
  
  ```
