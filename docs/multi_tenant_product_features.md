# 多租户功能产品需求文档

## 📋 **文档概述**

本文档从产品功能角度全面梳理多租户功能对现有商城系统的影响，包括新增功能、功能变更和影响分析。

## 🎯 **多租户功能总览**

### **核心概念**

- **租户（Tenant）**：独立的业务实体，拥有独立的数据空间和配置
- **加盟商（Franchisee）**：在每个租户中拥有唯一身份的业务用户
- **1:1关系**：每个用户在每个租户中只有一个加盟商身份，简化业务逻辑

### **业务价值**

- 支持多品牌/多区域运营
- 实现数据隔离和独立管理
- 提供灵活的加盟商管理机制
- 支持差异化的业务配置

## 📊 **功能影响分析表**

### **1. 用户认证与权限管理**

| 功能模块 | 原有功能           | 多租户变更                                                   | 影响等级 | 变更类型     |
| -------- | ------------------ | ------------------------------------------------------------ | -------- | ------------ |
| 用户登录 | 单一登录入口       | 智能识别用户类型，支持租户选择                               | 🔴 高     | 功能增强     |
| 权限验证 | 基于角色的权限控制 | 增加租户维度的权限隔离                                       | 🔴 高     | 架构变更     |
| JWT令牌  | 标准用户信息       | <u>**增加租户ID和租户权限信息（需关注加盟商在多租户之间进行切换时令牌的切换）**</u> | 🟡 中     | 数据结构变更 |
| 用户管理 | 全局用户管理       | 按租户隔离的用户管理                                         | 🔴 高     | 功能重构     |

### **2. 加盟商管理系统**

| 功能模块        | 原有功能    | 多租户变更          | 影响等级 | 变更类型   |
| ----------- | ------- | -------------- | ---- | ------ |
| 加盟商注册/申请/审批 | 直接注册到系统 | 注册时需要指定所属租户    | 🟡 中 | 流程调整   |
| 加盟商列表       | 显示所有加盟商 | 按当前租户过滤显示      | 🔴 高 | 数据过滤   |
| 加盟商详情       | 全局加盟商信息 | 显示在当前租户中的信息和角色 | 🟡 中 | 信息调整   |
| 加盟商搜索       | 全局搜索    | 租户内搜索          | 🟡 中 | 搜索范围调整 |
| 加盟商导入导出     | 全局数据操作  | 租户内数据操作        | 🟡 中 | 数据范围调整 |

#### **2.1 加盟商数据模型重构**

| 变更项目 | 原有设计 | 新设计 | 影响等级 | 变更说明 |
|---------|---------|--------|---------|---------|
| 数据模型关系 | 加盟商与租户M:N关系 | 加盟商与租户1:1关系 | 🔴 高 | 删除FranchiseeTenantRelation表 |
| 租户字段 | 通过关联表管理 | 直接在franchisee表添加tenant_id | 🔴 高 | 数据结构重构 |
| 唯一性约束 | 全局唯一Code | 租户内唯一Code | 🔴 高 | 复合唯一索引 |
| 跨租户用户 | 复杂关联查询 | 同一用户多条加盟商记录 | 🟡 中 | 数据冗余换取简单性 |
| 字段隔离范围 | 部分字段共享 | 几乎所有字段租户隔离 | 🔴 高 | FCategoryId、Linkman、Code、Balance、Points等 |

#### **2.2 加盟商登录流程重构**

| 登录场景 | 处理逻辑 | 用户体验 | 技术实现 |
|---------|---------|---------|---------|
| 单租户加盟商 | 直接登录到唯一租户 | 无感知，直接进入系统 | 自动设置租户上下文 |
| 多租户加盟商 | 显示租户选择界面 | 选择要登录的租户身份 | 租户列表+默认租户 |
| 首次多租户登录 | 引导设置默认租户 | 友好的选择引导 | 记住用户选择 |
| 租户切换 | 重新验证租户权限 | 确认切换提示 | 重新加载租户上下文 |

### **3. 订单管理系统**

| 功能模块 | 原有功能     | 多租户变更           | 影响等级 | 变更类型     |
| -------- | ------------ | -------------------- | -------- | ------------ |
| 订单列表 | 显示所有订单 | 按租户隔离显示订单   | 🔴 高     | 数据隔离     |
| 订单创建 | 全局订单创建 | 在当前租户下创建订单 | 🟡 中     | 数据归属     |
| 订单查询 | 全局订单查询 | 租户内订单查询       | 🔴 高     | 查询范围调整 |
| 订单统计 | 全局统计数据 | 租户维度统计数据     | 🔴 高     | 统计维度调整 |
| 订单导出 | 全局数据导出 | 租户数据导出         | 🟡 中     | 导出范围调整 |
| 订单详情 | 全局订单详情 | 租户内订单详情验证   | 🔴 高     | 权限验证     |
| 订单备注 | 全局订单备注 | 租户内订单备注管理   | 🟡 中     | 数据隔离     |

#### **3.1 订单商品管理（OrderGoods）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 订单商品列表 | 显示所有订单商品 | 只显示当前租户且当前加盟商的订单商品 | 🔴 高 | 复合数据隔离 |
| 商品明细查询 | 全局商品明细 | 通过订单关联验证租户权限 | 🔴 高 | 关联权限验证 |
| 子单状态管理 | 全局子单状态 | 租户内子单状态管理 | 🟡 中 | 状态隔离 |
| 积分返利计算 | 全局积分规则 | 按租户积分规则计算 | 🟡 中 | 规则隔离 |
| 专属账户扣款 | 全局账户管理 | 租户内专属账户管理 | 🔴 高 | 账户隔离 |

#### **3.2 发货单管理（OrderDelivery）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 发货单列表 | 显示所有发货单 | 按租户和加盟商隔离显示 | 🔴 高 | 数据隔离 |
| 发货商品明细 | 全局商品明细 | 验证商品属于当前租户订单 | 🔴 高 | 关联验证 |
| 物流信息管理 | 全局物流模板 | 租户独立物流配置 | 🟡 中 | 配置隔离 |
| 发货状态跟踪 | 全局状态管理 | 租户内状态跟踪 | 🟡 中 | 状态隔离 |

#### **3.3 退货单管理（OrderReturn）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 退货单列表 | 显示所有退货单 | 按租户和加盟商隔离显示 | 🔴 高 | 数据隔离 |
| 退货商品验证 | 全局商品验证 | 验证退货商品属于当前租户 | 🔴 高 | 权限验证 |
| 退款处理 | 全局退款流程 | 按租户退款政策处理 | 🟡 中 | 流程隔离 |
| 换货单管理 | 全局换货流程 | 租户内换货流程 | 🟡 中 | 流程隔离 |

#### **3.4 零元订单管理（ZeroOrder）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 零元订单列表 | 显示所有零元订单 | 按租户隔离显示零元订单 | 🔴 高 | 数据隔离 |
| 零元订单创建 | 全局零元订单 | 租户内零元订单创建 | 🟡 中 | 数据归属 |
| 零元订单审核 | 全局审核流程 | 租户内审核流程 | 🟡 中 | 流程隔离 |


# **4. 商品管理系统**

| 功能模块 | 原有功能   | 多租户变更    | 影响等级 | 变更类型   |
| ---- | ------ | -------- | ---- | ------ |
| 商品列表 | 全局商品管理 | 租户独立商品管理 | 🔴 高 | 数据隔离   |
| 商品分类 | 全局分类体系 | 租户独立分类体系 | 🔴 高 | 数据结构调整 |
| 库存管理 | 全局库存统计 | 租户独立库存管理 | 🔴 高 | 库存隔离   |
| 价格管理 | 统一价格体系 | 租户独立定价策略 | 🟡 中 | 定价策略调整 |
| 商品品牌 | 全局品牌管理 | 租户独立品牌管理 | 🟡 中 | 品牌隔离   |

#### **4.1 专区商城管理（SpecialMall）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 专区商城列表 | 全局专区商城 | 租户独立专区商城 | 🔴 高 | 数据隔离 |
| 专区商品配置 | 全局商品配置 | 租户内商品配置 | 🟡 中 | 配置隔离 |
| 专区权限管理 | 全局权限控制 | 租户内权限控制 | 🟡 中 | 权限隔离 |

#### **4.2 购物车管理（Cart）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 购物车列表 | 显示所有购物车 | 按租户和用户隔离显示 | 🔴 高 | 数据隔离 |
| 商品类型管理 | 全局商品类型 | 租户内商品类型（余额/积分商品） | 🟡 中 | 类型隔离 |
| 购物车商品验证 | 全局商品验证 | 验证商品属于当前租户 | 🔴 高 | 权限验证 |

### **5. 财务管理系统**

| 功能模块 | 原有功能   | 多租户变更    | 影响等级 | 变更类型   |
| ---- | ------ | -------- | ---- | ------ |
| 加盟商业绩 | 全局业绩统计 | 租户内业绩统计 | 🔴 高 | 数据隔离 |
| 历史业绩 | 全局历史数据 | 租户历史数据 | 🔴 高 | 数据隔离 |
| 月度业绩 | 全局月度统计 | 租户月度统计 | 🔴 高 | 统计隔离 |
| 充值记录 | 全局充值记录 | 租户内充值记录 | 🔴 高 | 记录隔离 |

#### **5.1 充值记录管理（RechargeRecord）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 充值记录列表 | 显示所有充值记录 | 按租户和加盟商隔离显示 | 🔴 高 | 数据隔离 |
| 余额充值 | 全局余额管理 | 租户内余额管理 | 🔴 高 | 账户隔离 |
| 积分充值 | 全局积分管理 | 租户内积分管理 | 🔴 高 | 积分隔离 |
| 专属账户管理 | 全局账户管理 | 租户内专属账户管理 | 🔴 高 | 账户隔离 |
| 操作记录审计 | 全局操作记录 | 租户内操作记录 | 🟡 中 | 审计隔离 |

#### **5.2 加盟商账户管理（FranchiseeAccount）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 专属账户列表 | 显示所有专属账户 | 按租户隔离显示账户 | 🔴 高 | 数据隔离 |
| 账户余额管理 | 全局余额管理 | 租户内余额管理 | 🔴 高 | 余额隔离 |
| 账户状态控制 | 全局状态管理 | 租户内状态管理 | 🟡 中 | 状态隔离 |

#### **5.3 业绩统计管理（FranchiseePerformance）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 实时业绩统计 | 全局业绩数据 | 租户内业绩统计 | 🔴 高 | 统计隔离 |
| 历史业绩查询 | 全局历史数据 | 租户历史业绩数据 | 🔴 高 | 数据隔离 |
| 月度业绩报表 | 全局月度报表 | 租户月度业绩报表 | 🔴 高 | 报表隔离 |
| 业绩排行榜 | 全局排行榜 | 租户内排行榜 | 🟡 中 | 排名隔离 |

### **6. 仓储管理系统**

| 功能模块 | 原有功能   | 多租户变更    | 影响等级 | 变更类型   |
| ---- | ------ | -------- | ---- | ------ |
| 大仓管理 | 全局大仓管理 | 租户独立大仓管理 | 🔴 高 | 仓储隔离 |
| 云仓管理 | 全局云仓管理 | 租户独立云仓管理 | 🔴 高 | 仓储隔离 |
| 库存分配 | 全局库存分配 | 租户内库存分配 | 🔴 高 | 分配隔离 |
| 仓储记录 | 全局仓储记录 | 租户内仓储记录 | 🟡 中 | 记录隔离 |

#### **6.1 大仓管理（BigWarehouse）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 大仓列表 | 显示所有大仓 | 按租户隔离显示大仓 | 🔴 高 | 数据隔离 |
| 大仓商品管理 | 全局商品管理 | 租户内大仓商品管理 | 🔴 高 | 商品隔离 |
| 大仓库存管理 | 全局库存管理 | 租户内库存管理 | 🔴 高 | 库存隔离 |
| 大仓订单管理 | 全局订单管理 | 租户内大仓订单管理 | 🔴 高 | 订单隔离 |
| 大仓分配管理 | 全局分配管理 | 租户内分配管理 | 🟡 中 | 分配隔离 |
| 大仓覆盖区域 | 全局覆盖管理 | 租户内覆盖区域管理 | 🟡 中 | 区域隔离 |
| 大仓管理员 | 全局管理员 | 租户内大仓管理员 | 🟡 中 | 管理员隔离 |

#### **6.2 云仓管理（CloudWarehouse）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 云仓列表 | 显示所有云仓 | 按租户隔离显示云仓 | 🔴 高 | 数据隔离 |
| 云仓记录管理 | 全局记录管理 | 租户内云仓记录管理 | 🔴 高 | 记录隔离 |
| 子云仓管理 | 全局子云仓 | 租户内子云仓管理 | 🟡 中 | 子仓隔离 |

### **7. 通用数据管理系统**

| 功能模块 | 原有功能   | 多租户变更    | 影响等级 | 变更类型   |
| ---- | ------ | -------- | ---- | ------ |
| 物流模板 | 全局物流模板 | 租户独立物流模板 | 🟡 中 | 模板隔离 |
| 城市数据 | 全局城市数据 | 共享城市数据（无需隔离） | 🟢 低 | 无需变更 |
| 商品赠品规则 | 全局赠品规则 | 租户独立赠品规则 | 🟡 中 | 规则隔离 |

#### **7.1 物流模板管理（LogisticsTemplate）**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 物流模板列表 | 显示所有模板 | 按租户隔离显示模板 | 🟡 中 | 数据隔离 |
| 快递公司配置 | 全局快递配置 | 租户独立快递配置 | 🟡 中 | 配置隔离 |
| 适用地区管理 | 全局地区管理 | 租户内地区管理 | 🟡 中 | 地区隔离 |

### **8. 支付管理系统**

| 功能模块 | 原有功能   | 多租户变更    | 影响等级 | 变更类型   |
| ---- | ------ | -------- | ---- | ------ |
| 在线支付 | 全局支付管理 | 租户独立支付配置 | 🔴 高 | 支付隔离 |
| 汇友支付 | 全局汇友支付 | 租户独立汇友支付 | 🔴 高 | 支付隔离 |
| 支付记录 | 全局支付记录 | 租户内支付记录 | 🔴 高 | 记录隔离 |

### **9. 系统配置管理**

| 功能模块 | 原有功能   | 多租户变更         | 影响等级 | 变更类型   |
| ---- | ------ | ------------- | ---- | ------ |
| 系统配置 | 全局系统配置 | 租户独立配置 + 全局配置 | 🔴 高 | 配置体系重构 |
| 主题设置 | 统一主题风格 | 租户独立主题定制      | 🟡 中 | 个性化功能  |
| 支付配置 | 全局支付配置 | 租户独立支付配置      | 🟡 中 | 配置隔离   |
| 物流配置 | 全局物流设置 | 租户独立物流配置      | 🟡 中 | 配置隔离   |

## 🆕 **新增功能模块**

### **1. 租户管理**

| 功能点    | 功能描述             | 优先级 | 用户角色  |
| ------ | ---------------- | --- | ----- |
| 租户创建   | 创建新的租户实体，设置基本信息  | P0  | 超级管理员 |
| 租户配置   | 配置租户的主题、支付、物流等设置 | P0  | 租户管理员 |
| 租户状态管理 | 启用/停用租户，控制租户访问权限 | P0  | 超级管理员 |
| 租户信息编辑 | 修改租户基本信息和配置      | P1  | 租户管理员 |
| 租户数据统计 | 查看租户的业务数据统计      | P1  | 租户管理员 |

### **2. 加盟商租户关联管理**

| 功能点    | 功能描述            | 优先级 | 用户角色  |
| ------ | --------------- | --- | ----- |
| 关联关系创建 | 将加盟商添加到指定租户     | P0  | 租户管理员 |
| 关联关系管理 | 查看、编辑、删除加盟商租户关联 | P0  | 租户管理员 |
| 角色权限设置 | 设置加盟商在租户中的角色和权限 | P0  | 租户管理员 |
| 默认租户设置 | 为加盟商设置默认登录租户    | P1  | 租户管理员 |
| 关联状态管理 | 激活/停用加盟商在租户中的状态 | P1  | 租户管理员 |

### **3. 租户切换功能**

| 功能点    | 功能描述            | 优先级 | 用户角色 |
| ------ | --------------- | --- | ---- |
| 租户选择界面 | 登录时显示可用租户列表供选择  | P0  | 加盟商  |
| 租户切换   | 在系统中快速切换当前操作的租户 | P0  | 加盟商  |
| 租户信息显示 | 在界面中显示当前所在的租户信息 | P1  | 加盟商  |
| 切换历史记录 | 记录和显示租户切换历史     | P2  | 加盟商  |

## 📱 **移动端功能变更**

### **APP功能影响分析**

| 功能模块 | 原有功能     | 多租户变更       | 影响等级 | 实现方案     |
| ---- | -------- | ----------- | ---- | -------- |
| 登录界面 | 手机号+密码登录 | 增加租户选择步骤    | 🔴 高 | 新增租户选择页面 |
| 主界面  | 统一的主界面风格 | 根据租户配置动态调整  | 🟡 中 | 主题配置动态加载 |
| 商品浏览 | 显示所有商品   | 显示当前租户的商品   | 🔴 高 | API数据过滤  |
| 订单管理 | 显示所有订单   | 显示当前租户的订单   | 🔴 高 | API数据过滤  |
| 个人中心 | 统一的个人信息  | 显示在当前租户中的信息 | 🟡 中 | 信息展示调整   |

## 🔄 **业务流程变更**

### **1. 加盟商注册流程**

#### **原有流程**

```
填写基本信息 → 提交审核 → 审核通过 → 账号激活 → 开始使用
```

#### **多租户流程**

```
选择加入租户 → 填写基本信息 → 提交审核 → 租户管理员审核 → 设置角色权限 → 账号激活 → 开始使用
```

### **2. 加盟商登录流程**

#### **原有流程**

```
输入手机号密码 → 验证通过 → 进入系统
```

#### **多租户流程**

```
输入手机号密码 → 验证通过 → 检查租户关联 → 选择租户（如需要） → 进入对应租户系统
```

### **3. 订单处理流程**

#### **原有流程**

```
客户下单 → 订单分配 → 加盟商处理 → 订单完成
```

#### **多租户流程**

```
客户下单 → 租户内订单分配 → 租户内加盟商处理 → 租户内订单完成 → 租户统计更新
```

## 📊 **数据迁移影响**

### **需要迁移的数据**

| 数据类型  | 迁移方式         | 影响范围    | 风险等级 |
| ----- | ------------ | ------- | ---- |
| 现有加盟商 | 自动关联到默认租户    | 全部加盟商数据 | 🟡 中 |
| 历史订单  | 根据加盟商关联分配到租户 | 全部订单数据  | 🔴 高 |
| 商品数据  | 复制到各租户或指定归属  | 全部商品数据  | 🔴 高 |
| 财务数据  | 按租户重新归类统计    | 全部财务数据  | 🔴 高 |
| 系统配置  | 迁移为默认租户配置    | 全部配置数据  | 🟡 中 |

### **数据一致性保证**

| 检查项        | 检查方法      | 修复方案      |
| ---------- | --------- | --------- |
| 加盟商租户关联完整性 | 查询无关联的加盟商 | 自动关联到默认租户 |
| 订单数据归属正确性  | 验证订单租户归属  | 根据加盟商关联修正 |
| 权限数据一致性    | 检查权限配置完整性 | 重新生成权限数据  |
| 统计数据准确性    | 重新计算各项统计  | 执行数据重算脚本  |

## 🎨 **用户界面变更**

### **管理后台界面**

| 界面模块 | 变更内容          | 变更类型   |
| ---- | ------------- | ------ |
| 顶部导航 | 增加当前租户显示和切换入口 | 新增功能   |
| 侧边菜单 | 增加租户管理相关菜单项   | 新增功能   |
| 数据列表 | 所有列表增加租户过滤功能  | 功能增强   |
| 表单页面 | 相关表单增加租户选择字段  | 字段调整   |
| 统计图表 | 增加租户维度的数据统计   | 数据维度调整 |

### **移动端界面**

| 界面模块 | 变更内容         | 变更类型   |
| ---- | ------------ | ------ |
| 登录页面 | 可能需要增加租户选择步骤 | 流程调整   |
| 主界面  | 根据租户配置调整主题风格 | 样式动态化  |
| 个人中心 | 显示当前租户信息     | 信息展示调整 |
| 设置页面 | 增加租户切换功能     | 新增功能   |

## 📈 **性能影响评估**

### **数据库性能**

| 影响项   | 影响程度     | 优化方案      |
| ----- | -------- | --------- |
| 查询复杂度 | 增加租户过滤条件 | 添加租户相关索引  |
| 数据量增长 | 关联表数据增加  | 定期数据清理和归档 |
| 并发访问  | 多租户并发查询  | 数据库连接池优化  |
| 存储空间  | 租户配置数据增加 | 配置数据压缩存储  |

### **应用性能**

| 影响项  | 影响程度     | 优化方案     |
| ---- | -------- | -------- |
| 内存使用 | 租户配置缓存   | 合理的缓存策略  |
| 响应时间 | 租户权限验证开销 | 权限缓存优化   |
| 并发处理 | 租户隔离处理逻辑 | 异步处理优化   |
| 网络传输 | 租户信息传输   | 数据压缩和CDN |

## 🔒 **安全性考虑**

### **数据安全**

| 安全项     | 风险等级 | 防护措施         |
| ------- | ---- | ------------ |
| 跨租户数据泄露 | 🔴 高 | 严格的数据过滤和权限验证 |
| 租户权限越权  | 🔴 高 | 多层权限验证机制     |
| 敏感信息暴露  | 🟡 中 | 数据脱敏和加密存储    |
| 审计日志完整性 | 🟡 中 | 完整的操作日志记录    |

### **访问控制**

| 控制项   | 实现方式        | 验证机制       |
| ----- | ----------- | ---------- |
| 租户隔离  | JWT令牌包含租户信息 | 每次请求验证租户权限 |
| 角色权限  | 基于角色的访问控制   | 动态权限验证     |
| API安全 | 接口级别的租户验证   | 中间件统一验证    |
| 数据加密  | 敏感数据加密存储    | 加密算法和密钥管理  |

## 💼 **运营管理功能变更**

### **营销活动管理**

| 功能模块  | 原有功能    | 多租户变更     | 影响等级 | 变更说明            |
| ----- | ------- | --------- | ---- | --------------- |
| 优惠券管理 | 全局优惠券系统 | 租户独立优惠券管理 | 🔴 高 | 每个租户可独立创建和管理优惠券 |
| 促销活动  | 统一促销活动  | 租户独立促销策略  | 🔴 高 | 支持租户差异化营销策略     |
| 积分系统  | 全局积分体系  | 租户独立积分规则  | 🟡 中 | 租户可自定义积分获取和消费规则 |
| 会员等级  | 统一会员体系  | 租户独立会员等级  | 🟡 中 | 支持租户个性化会员权益     |

### **客服管理系统**

| 功能模块  | 原有功能   | 多租户变更    | 影响等级 | 变更说明            |
| ----- | ------ | -------- | ---- | --------------- |
| 客服工单  | 全局工单管理 | 租户内工单隔离  | 🔴 高 | 工单只在租户内可见和处理    |
| 客服知识库 | 统一知识库  | 租户独立知识库  | 🟡 中 | 支持租户自定义FAQ和帮助文档 |
| 客服统计  | 全局客服数据 | 租户维度客服统计 | 🔴 高 | 按租户统计客服工作量和满意度  |

## 📊 **详细功能对比表**

### **核心业务功能对比**

| 功能领域     | 子功能  | 单租户模式 | 多租户模式    | 开发工作量 | 测试复杂度 |
| -------- | ---- | ----- | -------- | ----- | ----- |
| **用户管理** | 用户注册 | 直接注册  | 选择租户注册   | 🟡 中  | 🟡 中  |
|          | 用户登录 | 简单登录  | 智能租户识别登录 | 🔴 高  | 🔴 高  |
|          | 权限控制 | 角色权限  | 租户+角色权限  | 🔴 高  | 🔴 高  |
|          | 用户列表 | 全局列表  | 租户过滤列表   | 🟡 中  | 🟡 中  |
| **商品管理** | 商品创建 | 全局商品  | 租户商品     | 🟡 中  | 🟡 中  |
|          | 商品分类 | 全局分类  | 租户分类     | 🔴 高  | 🔴 高  |
|          | 库存管理 | 全局库存  | 租户库存     | 🔴 高  | 🔴 高  |
|          | 价格策略 | 统一定价  | 租户定价     | 🟡 中  | 🟡 中  |
| **订单管理** | 订单创建 | 全局订单  | 租户订单     | 🟡 中  | 🟡 中  |
|          | 订单查询 | 全局查询  | 租户查询     | 🔴 高  | 🔴 高  |
|          | 订单统计 | 全局统计  | 租户统计     | 🔴 高  | 🔴 高  |
|          | 订单导出 | 全局导出  | 租户导出     | 🟡 中  | 🟡 中  |

### **管理功能对比**

| 管理模块       | 原有权限  | 多租户权限    | 权限变更     | 影响用户   |
| ---------- | ----- | -------- | -------- | ------ |
| **超级管理员**  | 全系统管理 | 全系统+租户管理 | 增加租户管理权限 | 系统管理员  |
| **租户管理员**  | -     | 租户内全部管理  | 新增角色     | 租户负责人  |
| **加盟商管理员** | 加盟商管理 | 租户内加盟商管理 | 权限范围缩小   | 加盟商管理员 |
| **普通加盟商**  | 基础操作  | 租户内基础操作  | 权限范围缩小   | 普通加盟商  |
| **客服人员**   | 全局客服  | 租户内客服    | 权限范围缩小   | 客服人员   |

## 🔄 **业务流程重构分析**

### **关键业务流程变更**

#### **1. 商品上架流程**

**原有流程**：

```
商品信息录入 → 商品审核 → 上架销售 → 全平台可见
```

**多租户流程**：

```
选择目标租户 → 商品信息录入 → 租户内审核 → 租户内上架 → 租户内可见
```

**变更影响**：

- 需要增加租户选择步骤
- 审核流程需要租户隔离
- 商品可见性限制在租户内

#### **2. 订单分配流程**

**原有流程**：

```
客户下单 → 系统分配 → 就近加盟商 → 订单处理
```

**多租户流程**：

```
客户下单 → 确定租户 → 租户内分配 → 租户内加盟商 → 订单处理
```

**变更影响**：

- 订单分配范围限制在租户内
- 需要考虑租户边界问题
- 可能影响配送效率

#### **3. 财务结算流程**

**原有流程**：

```
订单完成 → 收入统计 → 成本核算 → 利润分配 → 财务结算
```

**多租户流程**：

```
订单完成 → 租户收入统计 → 租户成本核算 → 租户内利润分配 → 租户财务结算 → 平台抽成结算
```

**变更影响**：

- 财务数据需要按租户隔离
- 增加平台与租户的结算环节
- 财务报表需要多维度展示

## 📱 **移动端产品功能变更详情**

### **APP端功能变更**

| 页面/功能    | 原有设计   | 多租户设计       | UI变更     | 交互变更     |
| -------- | ------ | ----------- | -------- | -------- |
| **启动页**  | 统一品牌展示 | 动态品牌展示      | Logo动态加载 | 无        |
| **登录页**  | 手机号+密码 | 手机号+密码+租户选择 | 增加租户选择UI | 增加租户选择流程 |
| **首页**   | 统一首页布局 | 租户定制首页      | 布局配置化    | 内容动态加载   |
| **商品列表** | 全部商品   | 租户商品        | 无变更      | 数据源调整    |
| **购物车**  | 统一购物车  | 租户内购物车      | 无变更      | 数据隔离     |
| **订单页**  | 全部订单   | 租户订单        | 无变更      | 数据过滤     |
| **个人中心** | 统一个人信息 | 租户相关信息      | 增加租户信息展示 | 增加租户切换   |

### **小程序端功能变更**

| 功能模块     | 原有功能   | 多租户功能     | 开发难度 | 用户体验影响   |
| -------- | ------ | --------- | ---- | -------- |
| **授权登录** | 微信一键登录 | 微信登录+租户绑定 | 🟡 中 | 增加一步操作   |
| **商品浏览** | 全商品浏览  | 租户商品浏览    | 🟡 中 | 商品范围变化   |
| **分享功能** | 统一分享样式 | 租户品牌分享    | 🟡 中 | 分享内容个性化  |
| **支付流程** | 统一支付配置 | 租户支付配置    | 🔴 高 | 支付方式可能不同 |

## 💰 **成本效益分析**

### **开发成本评估**

| 功能模块   | 开发工作量（人天） | 测试工作量（人天） | 总成本评估    |
| ------ | --------- | --------- | -------- |
| 租户管理系统 | 15        | 8         | 🔴 高     |
| 用户登录改造 | 10        | 6         | 🟡 中     |
| 数据隔离改造 | 25        | 15        | 🔴 高     |
| 权限系统重构 | 20        | 12        | 🔴 高     |
| 移动端适配  | 12        | 8         | 🟡 中     |
| 数据迁移   | 8         | 5         | 🟡 中     |
| **总计** | **90**    | **54**    | **🔴 高** |

### **业务价值评估**

| 价值维度      | 价值描述    | 量化指标    | 预期收益   |
| --------- | ------- | ------- | ------ |
| **市场扩展**  | 支持多品牌运营 | 可支持租户数量 | 无限扩展   |
| **运营效率**  | 独立运营管理  | 运营效率提升  | 30%+   |
| **数据安全**  | 数据隔离保护  | 数据泄露风险  | 降低90%  |
| **个性化服务** | 租户定制化   | 客户满意度   | 提升25%  |
| **收入增长**  | 多租户收费   | 平台收入    | 增长50%+ |

## 🎯 **实施优先级建议**

### **P0 - 核心功能（必须实现）**

| 功能     | 实施周期 | 依赖关系 | 风险等级 |
| ------ | ---- | ---- | ---- |
| 租户基础管理 | 2周   | 无    | 🟡 中 |
| 用户登录改造 | 2周   | 租户管理 | 🔴 高 |
| 数据隔离机制 | 3周   | 租户管理 | 🔴 高 |
| 权限系统重构 | 3周   | 用户登录 | 🔴 高 |

### **P1 - 重要功能（优先实现）**

| 功能     | 实施周期 | 依赖关系   | 风险等级 |
| ------ | ---- | ------ | ---- |
| 租户配置管理 | 2周   | 租户基础管理 | 🟡 中 |
| 移动端适配  | 3周   | 用户登录改造 | 🟡 中 |
| 数据迁移工具 | 1周   | 数据隔离机制 | 🟡 中 |
| 租户切换功能 | 1周   | 权限系统   | 🟡 中 |

### **P2 - 增强功能（后续实现）**

| 功能     | 实施周期 | 依赖关系   | 风险等级 |
| ------ | ---- | ------ | ---- |
| 租户主题定制 | 2周   | 租户配置管理 | 🟢 低 |
| 高级权限控制 | 2周   | 权限系统重构 | 🟡 中 |
| 租户数据分析 | 1周   | 数据隔离机制 | 🟢 低 |
| 批量操作功能 | 1周   | 基础功能完成 | 🟢 低 |

## 📋 **验收标准**

### **功能验收标准**

| 验收项  | 验收标准             | 测试方法    |
| ---- | ---------------- | ------- |
| 租户隔离 | 不同租户数据完全隔离，无交叉访问 | 数据访问测试  |
| 用户登录 | 支持智能租户识别和选择      | 登录流程测试  |
| 权限控制 | 租户内权限正确，跨租户访问被阻止 | 权限边界测试  |
| 数据迁移 | 现有数据正确迁移，无数据丢失   | 数据一致性测试 |
| 性能表现 | 多租户功能不影响系统性能     | 性能压力测试  |

### **用户体验验收标准**

| 体验项   | 验收标准            | 评估方法    |
| ----- | --------------- | ------- |
| 登录体验  | 登录流程简洁，租户选择直观   | 用户体验测试  |
| 界面一致性 | 多租户界面风格保持一致性    | UI一致性检查 |
| 操作便捷性 | 租户切换操作简单快捷      | 操作效率测试  |
| 错误处理  | 错误提示清晰，引导用户正确操作 | 异常场景测试  |

## 💼 **运营管理功能变更**

### **营销活动管理**

| 功能模块  | 原有功能    | 多租户变更     | 影响等级 | 变更说明            |
| ----- | ------- | --------- | ---- | --------------- |
| 优惠券管理 | 全局优惠券系统 | 租户独立优惠券管理 | 🔴 高 | 每个租户可独立创建和管理优惠券 |
| 促销活动  | 统一促销活动  | 租户独立促销策略  | 🔴 高 | 支持租户差异化营销策略     |
| 积分系统  | 全局积分体系  | 租户独立积分规则  | 🟡 中 | 租户可自定义积分获取和消费规则 |
| 会员等级  | 统一会员体系  | 租户独立会员等级  | 🟡 中 | 支持租户个性化会员权益     |

### **客服管理系统**

| 功能模块  | 原有功能   | 多租户变更    | 影响等级 | 变更说明            |
| ----- | ------ | -------- | ---- | --------------- |
| 客服工单  | 全局工单管理 | 租户内工单隔离  | 🔴 高 | 工单只在租户内可见和处理    |
| 客服知识库 | 统一知识库  | 租户独立知识库  | 🟡 中 | 支持租户自定义FAQ和帮助文档 |
| 客服统计  | 全局客服数据 | 租户维度客服统计 | 🔴 高 | 按租户统计客服工作量和满意度  |

## 📊 **详细功能对比表**

### **核心业务功能对比**

| 功能领域     | 子功能  | 单租户模式 | 多租户模式    | 开发工作量 | 测试复杂度 |
| -------- | ---- | ----- | -------- | ----- | ----- |
| **用户管理** | 用户注册 | 直接注册  | 选择租户注册   | 🟡 中  | 🟡 中  |
|          | 用户登录 | 简单登录  | 智能租户识别登录 | 🔴 高  | 🔴 高  |
|          | 权限控制 | 角色权限  | 租户+角色权限  | 🔴 高  | 🔴 高  |
|          | 用户列表 | 全局列表  | 租户过滤列表   | 🟡 中  | 🟡 中  |
| **商品管理** | 商品创建 | 全局商品  | 租户商品     | 🟡 中  | 🟡 中  |
|          | 商品分类 | 全局分类  | 租户分类     | 🔴 高  | 🔴 高  |
|          | 库存管理 | 全局库存  | 租户库存     | 🔴 高  | 🔴 高  |
|          | 价格策略 | 统一定价  | 租户定价     | 🟡 中  | 🟡 中  |
| **订单管理** | 订单创建 | 全局订单  | 租户订单     | 🟡 中  | 🟡 中  |
|          | 订单查询 | 全局查询  | 租户查询     | 🔴 高  | 🔴 高  |
|          | 订单统计 | 全局统计  | 租户统计     | 🔴 高  | 🔴 高  |
|          | 订单导出 | 全局导出  | 租户导出     | 🟡 中  | 🟡 中  |

### **管理功能对比**

| 管理模块       | 原有权限  | 多租户权限    | 权限变更     | 影响用户   |
| ---------- | ----- | -------- | -------- | ------ |
| **超级管理员**  | 全系统管理 | 全系统+租户管理 | 增加租户管理权限 | 系统管理员  |
| **租户管理员**  | -     | 租户内全部管理  | 新增角色     | 租户负责人  |
| **加盟商管理员** | 加盟商管理 | 租户内加盟商管理 | 权限范围缩小   | 加盟商管理员 |
| **普通加盟商**  | 基础操作  | 租户内基础操作  | 权限范围缩小   | 普通加盟商  |
| **客服人员**   | 全局客服  | 租户内客服    | 权限范围缩小   | 客服人员   |

## 🔄 **业务流程重构分析**

### **关键业务流程变更**

#### **1. 商品上架流程**

**原有流程**：

```
商品信息录入 → 商品审核 → 上架销售 → 全平台可见
```

**多租户流程**：

```
选择目标租户 → 商品信息录入 → 租户内审核 → 租户内上架 → 租户内可见
```

**变更影响**：

- 需要增加租户选择步骤
- 审核流程需要租户隔离
- 商品可见性限制在租户内

#### **2. 订单分配流程**

**原有流程**：

```
客户下单 → 系统分配 → 就近加盟商 → 订单处理
```

**多租户流程**：

```
客户下单 → 确定租户 → 租户内分配 → 租户内加盟商 → 订单处理
```

**变更影响**：

- 订单分配范围限制在租户内
- 需要考虑租户边界问题
- 可能影响配送效率

#### **3. 财务结算流程**

**原有流程**：

```
订单完成 → 收入统计 → 成本核算 → 利润分配 → 财务结算
```

**多租户流程**：

```
订单完成 → 租户收入统计 → 租户成本核算 → 租户内利润分配 → 租户财务结算 → 平台抽成结算
```

**变更影响**：

- 财务数据需要按租户隔离
- 增加平台与租户的结算环节
- 财务报表需要多维度展示

## 📱 **移动端产品功能变更详情**

### **APP端功能变更**

| 页面/功能    | 原有设计   | 多租户设计       | UI变更     | 交互变更     |
| -------- | ------ | ----------- | -------- | -------- |
| **启动页**  | 统一品牌展示 | 动态品牌展示      | Logo动态加载 | 无        |
| **登录页**  | 手机号+密码 | 手机号+密码+租户选择 | 增加租户选择UI | 增加租户选择流程 |
| **首页**   | 统一首页布局 | 租户定制首页      | 布局配置化    | 内容动态加载   |
| **商品列表** | 全部商品   | 租户商品        | 无变更      | 数据源调整    |
| **购物车**  | 统一购物车  | 租户内购物车      | 无变更      | 数据隔离     |
| **订单页**  | 全部订单   | 租户订单        | 无变更      | 数据过滤     |
| **个人中心** | 统一个人信息 | 租户相关信息      | 增加租户信息展示 | 增加租户切换   |

### **小程序端功能变更**

| 功能模块     | 原有功能   | 多租户功能     | 开发难度 | 用户体验影响   |
| -------- | ------ | --------- | ---- | -------- |
| **授权登录** | 微信一键登录 | 微信登录+租户绑定 | 🟡 中 | 增加一步操作   |
| **商品浏览** | 全商品浏览  | 租户商品浏览    | 🟡 中 | 商品范围变化   |
| **分享功能** | 统一分享样式 | 租户品牌分享    | 🟡 中 | 分享内容个性化  |
| **支付流程** | 统一支付配置 | 租户支付配置    | 🔴 高 | 支付方式可能不同 |

## 💰 **成本效益分析**

### **开发成本评估**

| 功能模块   | 开发工作量（人天） | 测试工作量（人天） | 总成本评估    |
| ------ | --------- | --------- | -------- |
| 租户管理系统 | 15        | 8         | 🔴 高     |
| 用户登录改造 | 10        | 6         | 🟡 中     |
| 数据隔离改造 | 25        | 15        | 🔴 高     |
| 权限系统重构 | 20        | 12        | 🔴 高     |
| 移动端适配  | 12        | 8         | 🟡 中     |
| 数据迁移   | 8         | 5         | 🟡 中     |
| **总计** | **90**    | **54**    | **🔴 高** |

### **业务价值评估**

| 价值维度      | 价值描述    | 量化指标    | 预期收益   |
| --------- | ------- | ------- | ------ |
| **市场扩展**  | 支持多品牌运营 | 可支持租户数量 | 无限扩展   |
| **运营效率**  | 独立运营管理  | 运营效率提升  | 30%+   |
| **数据安全**  | 数据隔离保护  | 数据泄露风险  | 降低90%  |
| **个性化服务** | 租户定制化   | 客户满意度   | 提升25%  |
| **收入增长**  | 多租户收费   | 平台收入    | 增长50%+ |

## 🎯 **实施优先级建议**

### **P0 - 核心功能（必须实现）**

| 功能     | 实施周期 | 依赖关系 | 风险等级 |
| ------ | ---- | ---- | ---- |
| 租户基础管理 | 2周   | 无    | 🟡 中 |
| 用户登录改造 | 2周   | 租户管理 | 🔴 高 |
| 数据隔离机制 | 3周   | 租户管理 | 🔴 高 |
| 权限系统重构 | 3周   | 用户登录 | 🔴 高 |

### **P1 - 重要功能（优先实现）**

| 功能     | 实施周期 | 依赖关系   | 风险等级 |
| ------ | ---- | ------ | ---- |
| 租户配置管理 | 2周   | 租户基础管理 | 🟡 中 |
| 移动端适配  | 3周   | 用户登录改造 | 🟡 中 |
| 数据迁移工具 | 1周   | 数据隔离机制 | 🟡 中 |
| 租户切换功能 | 1周   | 权限系统   | 🟡 中 |

### **P2 - 增强功能（后续实现）**

| 功能     | 实施周期 | 依赖关系   | 风险等级 |
| ------ | ---- | ------ | ---- |
| 租户主题定制 | 2周   | 租户配置管理 | 🟢 低 |
| 高级权限控制 | 2周   | 权限系统重构 | 🟡 中 |
| 租户数据分析 | 1周   | 数据隔离机制 | 🟢 低 |
| 批量操作功能 | 1周   | 基础功能完成 | 🟢 低 |

## 📋 **验收标准**

### **功能验收标准**

| 验收项  | 验收标准             | 测试方法    |
| ---- | ---------------- | ------- |
| 租户隔离 | 不同租户数据完全隔离，无交叉访问 | 数据访问测试  |
| 用户登录 | 支持智能租户识别和选择      | 登录流程测试  |
| 权限控制 | 租户内权限正确，跨租户访问被阻止 | 权限边界测试  |
| 数据迁移 | 现有数据正确迁移，无数据丢失   | 数据一致性测试 |
| 性能表现 | 多租户功能不影响系统性能     | 性能压力测试  |

### **用户体验验收标准**

| 体验项   | 验收标准            | 评估方法    |
| ----- | --------------- | ------- |
| 登录体验  | 登录流程简洁，租户选择直观   | 用户体验测试  |
| 界面一致性 | 多租户界面风格保持一致性    | UI一致性检查 |
| 操作便捷性 | 租户切换操作简单快捷      | 操作效率测试  |
| 错误处理  | 错误提示清晰，引导用户正确操作 | 异常场景测试  |

## 🚀 **上线计划建议**

### **分阶段上线策略**

| 阶段        | 功能范围      | 上线时间 | 用户范围  | 风险控制   |
| --------- | --------- | ---- | ----- | ------ |
| **Alpha** | 租户管理+基础登录 | 第4周  | 内部测试  | 完整回滚方案 |
| **Beta**  | 数据隔离+权限控制 | 第8周  | 小范围试点 | 灰度发布   |
| **RC**    | 移动端+完整功能  | 第12周 | 部分用户  | 监控告警   |
| **GA**    | 全功能上线     | 第16周 | 全部用户  | 7x24监控 |

### **风险控制措施**

| 风险类型       | 风险描述      | 控制措施    | 应急预案    |
| ---------- | --------- | ------- | ------- |
| **数据风险**   | 数据迁移失败或丢失 | 多重备份+验证 | 立即回滚到备份 |
| **性能风险**   | 多租户影响系统性能 | 性能监控+优化 | 降级处理    |
| **安全风险**   | 租户数据泄露    | 安全测试+审计 | 紧急修复    |
| **用户体验风险** | 登录流程复杂化   | 用户测试+优化 | 简化流程    |

## 🎯 **详细产品功能规格**

### **1. 租户管理功能详细规格**

#### **1.1 租户创建功能**

| 功能项        | 详细描述            | 字段规格          | 验证规则              |
| ---------- | --------------- | ------------- | ----------------- |
| **租户名称**   | 租户的显示名称，用于界面展示  | 字符串，最大56字符    | 必填，不能重复           |
| **租户编码**   | 租户的唯一标识码，用于系统识别 | 字符串，最大56字符    | 必填，唯一，只能包含字母数字下划线 |
| **租户Logo** | 租户的品牌标识图片       | 图片URL，最大255字符 | 可选，支持jpg/png格式    |
| **主色调**    | 租户界面的主要颜色       | 颜色代码，最大20字符   | 可选，十六进制颜色码        |
| **次色调**    | 租户界面的辅助颜色       | 颜色代码，最大20字符   | 可选，十六进制颜色码        |
| **联系人**    | 租户的主要联系人姓名      | 字符串，最大50字符    | 必填                |
| **联系电话**   | 租户的联系电话         | 字符串，最大20字符    | 必填，手机号格式验证        |
| **租约到期日期** | 租户服务的到期时间       | 日期时间          | 可选，不能早于当前时间       |

#### **1.2 租户应用配置功能**

| 配置项        | 功能描述             | 配置选项             | 默认值       |
| ---------- | ---------------- | ---------------- | --------- |
| **应用名称**   | 在APP和Web端显示的应用名称 | 自定义文本，最大100字符    | "加盟商管理系统" |
| **应用Logo** | APP图标和Web端Logo   | 图片上传，支持多尺寸       | 系统默认Logo  |
| **登录背景图**  | 登录页面的背景图片        | 图片上传，推荐1920x1080 | 系统默认背景    |
| **首页配置**   | 首页布局和组件配置        | JSON配置，可视化编辑     | 标准首页布局    |
| **主题风格**   | 整体界面风格设置         | 预设主题+自定义         | 默认主题      |
| **功能模块开关** | 控制各功能模块的显示       | 开关配置             | 全部开启      |

#### **1.3 租户状态管理功能**

| 状态     | 状态描述     | 影响范围        | 操作权限  |
| ------ | -------- | ----------- | ----- |
| **启用** | 租户正常运行状态 | 所有功能可用      | 超级管理员 |
| **禁用** | 租户暂停服务状态 | 用户无法登录，数据保留 | 超级管理员 |
| **试用** | 租户试用期状态  | 功能受限，有时间限制  | 超级管理员 |
| **过期** | 租户服务已过期  | 只读访问，无法操作   | 系统自动  |

### **2. 加盟商多租户关联功能详细规格**

#### **2.1 关联关系管理**

| 功能项      | 详细描述         | 业务规则             | 界面展示       |
| -------- | ------------ | ---------------- | ---------- |
| **添加关联** | 将加盟商添加到指定租户  | 一个加盟商可属于多个租户     | 下拉选择租户+加盟商 |
| **移除关联** | 从租户中移除加盟商    | 需要确认操作，不删除加盟商数据  | 确认对话框      |
| **角色设置** | 设置加盟商在租户中的角色 | 普通加盟商/管理员加盟商/所有者 | 角色下拉选择     |
| **状态管理** | 控制关联的激活状态    | 激活/停用，影响登录权限     | 开关控件       |
| **默认租户** | 设置加盟商的默认登录租户 | 每个加盟商只能有一个默认租户   | 单选按钮       |

#### **2.2 权限配置功能**

| 权限类型     | 权限描述       | 配置方式    | 继承规则     |
| -------- | ---------- | ------- | -------- |
| **数据权限** | 控制可访问的数据范围 | 按租户自动隔离 | 自动继承租户权限 |
| **功能权限** | 控制可使用的功能模块 | 菜单权限配置  | 基于角色继承   |
| **操作权限** | 控制具体的操作权限  | 按钮级权限控制 | 基于角色+自定义 |
| **审批权限** | 控制审批流程权限   | 审批节点配置  | 基于角色配置   |

### **3. 租户切换功能详细规格**

#### **3.1 登录时租户选择**

| 场景        | 处理逻辑      | 界面展示        | 用户体验     |
| --------- | --------- | ----------- | -------- |
| **单租户用户** | 直接登录到唯一租户 | 无需选择，直接进入   | 无感知切换    |
| **多租户用户** | 显示租户选择界面  | 租户列表+默认租户标识 | 一键选择默认租户 |
| **首次登录**  | 引导设置默认租户  | 租户介绍+设置向导   | 友好的引导流程  |
| **记住选择**  | 记住上次选择的租户 | 自动选中上次租户    | 减少重复选择   |

#### **3.2 运行时租户切换**

| 功能项      | 实现方式        | 数据处理     | 界面反馈    |
| -------- | ----------- | -------- | ------- |
| **切换入口** | 顶部导航栏租户信息区域 | 点击显示租户列表 | 下拉菜单形式  |
| **切换确认** | 提示当前操作将丢失   | 确认对话框    | 明确的风险提示 |
| **切换过程** | 重新加载用户权限和数据 | 后台重新认证   | 加载动画提示  |
| **切换完成** | 刷新界面显示新租户信息 | 更新所有相关数据 | 成功提示    |

### **4. 数据隔离功能详细规格**

#### **4.1 自动数据过滤**

| 数据类型      | 过滤方式           | 实现机制          | 性能优化          |
| --------- | -------------- | ------------- | ------------- |
| **加盟商数据** | 按tenant_id自动过滤 | GORM插件自动注入    | tenant_id索引优化 |
| **订单数据**  | 关联加盟商的租户过滤     | 多表关联查询        | 复合索引优化        |
| **商品数据**  | 租户独立商品库        | 直接tenant_id过滤 | 分区表优化         |
| **财务数据**  | 严格按租户隔离        | 双重验证机制        | 缓存优化          |

#### **4.2 跨租户访问控制**

| 访问类型      | 控制策略     | 检查机制            | 异常处理    |
| --------- | -------- | --------------- | ------- |
| **API访问** | 中间件自动验证  | JWT token租户ID检查 | 返回403错误 |
| **数据库查询** | 自动注入租户条件 | GORM插件拦截        | 记录安全日志  |
| **文件访问**  | 按租户目录隔离  | 路径验证            | 拒绝访问    |
| **缓存访问**  | 租户前缀隔离   | Key前缀验证         | 清空相关缓存  |

### **5. 业务流程多租户适配详细规格**

#### **5.1 加盟商注册流程**

| 流程步骤        | 原有流程    | 多租户流程     | 新增功能点    |
| ----------- | ------- | --------- | -------- |
| **1. 注册入口** | 统一注册页面  | 租户专属注册页面  | 租户品牌展示   |
| **2. 信息填写** | 基本信息表单  | 基本信息+租户选择 | 租户介绍和选择  |
| **3. 提交审核** | 系统管理员审核 | 租户管理员审核   | 租户内部审核流程 |
| **4. 审核通过** | 全局账号激活  | 租户内账号激活   | 租户欢迎信息   |
| **5. 首次登录** | 直接进入系统  | 租户引导流程    | 租户功能介绍   |

#### **5.2 订单处理流程**

| 流程步骤     | 多租户处理    | 数据隔离      | 业务规则   |
| -------- | -------- | --------- | ------ |
| **订单创建** | 自动设置租户ID | 只能选择租户内商品 | 租户定价策略 |
| **订单分配** | 租户内加盟商分配 | 按租户区域分配   | 租户配送规则 |
| **订单处理** | 租户管理员监控  | 租户内数据可见   | 租户审批流程 |
| **订单完成** | 租户业绩统计   | 租户财务核算    | 租户结算规则 |

#### **5.3 商品管理流程**

| 管理环节      | 租户独立性   | 共享机制    | 权限控制    |
| --------- | ------- | ------- | ------- |
| **商品创建**  | 租户独立商品库 | 可选择共享商品 | 租户管理员权限 |
| **价格设置**  | 租户独立定价  | 参考建议价格  | 价格审批流程  |
| **库存管理**  | 租户独立库存  | 总库存分配   | 库存预警设置  |
| **上下架管理** | 租户独立控制  | 不影响其他租户 | 商品审核流程  |

### **6. 移动端多租户功能详细规格**

#### **6.1 APP登录体验**

| 功能点      | 设计方案     | 交互流程       | 技术实现     |
| -------- | -------- | ---------- | -------- |
| **租户识别** | 扫码或输入租户码 | 二维码扫描+手动输入 | 租户码验证API |
| **品牌展示** | 动态加载租户主题 | Logo+色彩+背景 | 配置文件下载   |
| **登录表单** | 租户定制化表单  | 字段配置+验证规则  | 动态表单渲染   |
| **记住租户** | 本地存储租户信息 | 下次自动选择     | 本地缓存机制   |

#### **6.2 APP功能适配**

| 功能模块     | 适配方案   | 数据处理    | 界面调整   |
| -------- | ------ | ------- | ------ |
| **首页展示** | 租户定制首页 | 动态组件配置  | 布局配置化  |
| **商品浏览** | 租户商品库  | API自动过滤 | 分类体系适配 |
| **订单管理** | 租户订单数据 | 权限自动控制  | 状态流程适配 |
| **个人中心** | 租户身份信息 | 多租户角色展示 | 切换入口设计 |

### **7. 财务管理多租户功能详细规格**

#### **7.1 业绩统计功能**

| 统计维度        | 数据范围          | 计算规则      | 展示方式     |
| ----------- | ------------- | --------- | -------- |
| **租户总业绩**   | 租户内所有加盟商业绩汇总  | 按时间段累计计算  | 仪表盘+趋势图  |
| **加盟商个人业绩** | 单个加盟商在当前租户的业绩 | 订单金额+返利积分 | 排行榜+详细报表 |
| **区域业绩**    | 按地区划分的业绩统计    | 地理位置聚合计算  | 地图可视化    |
| **商品业绩**    | 各商品在租户内的销售情况  | 销量+销售额统计  | 商品排行榜    |
| **时间对比**    | 同比环比业绩分析      | 多时间段对比计算  | 对比图表     |

#### **7.2 财务结算功能**

| 结算类型      | 结算范围       | 结算规则      | 审批流程      |
| --------- | ---------- | --------- | --------- |
| **加盟商结算** | 租户内加盟商佣金结算 | 按租户佣金政策计算 | 租户财务审批    |
| **租户分润**  | 平台与租户的收益分成 | 按合作协议分成比例 | 平台财务审批    |
| **退款处理**  | 租户内订单退款    | 按租户退款政策执行 | 租户管理员审批   |
| **积分结算**  | 积分兑换和消耗结算  | 按租户积分规则计算 | 自动结算+人工审核 |

#### **7.3 财务报表功能**

| 报表类型      | 报表内容       | 数据来源      | 生成周期    |
| --------- | ---------- | --------- | ------- |
| **收入报表**  | 租户总收入、分类收入 | 订单数据+支付数据 | 日/周/月/年 |
| **成本报表**  | 商品成本、运营成本  | 商品成本+费用数据 | 月/季/年   |
| **利润报表**  | 毛利润、净利润分析  | 收入-成本计算   | 月/季/年   |
| **现金流报表** | 资金流入流出情况   | 支付+结算数据   | 日/周/月   |
| **加盟商报表** | 各加盟商经营情况   | 加盟商业绩数据   | 月/季     |

### **8. 库存管理多租户功能详细规格**

#### **8.1 库存隔离机制**

| 隔离类型     | 隔离方式     | 管理规则      | 同步机制    |
| -------- | -------- | --------- | ------- |
| **虚拟库存** | 租户独立库存池  | 从总库存分配给租户 | 实时同步    |
| **实体库存** | 租户专属仓库   | 物理隔离存储    | 定期盘点同步  |
| **云仓库存** | 租户在云仓的库存 | 按租户标识管理   | API实时同步 |
| **预留库存** | 为租户预留的库存 | 优先分配机制    | 自动释放机制  |

#### **8.2 库存分配功能**

| 分配策略     | 分配规则       | 优先级     | 调整机制   |
| -------- | ---------- | ------- | ------ |
| **按需分配** | 根据租户历史销量分配 | 高活跃租户优先 | 动态调整   |
| **固定分配** | 按合同约定固定分配  | 合同条款优先  | 合同期内固定 |
| **竞价分配** | 租户竞价获得库存   | 价高者得    | 实时竞价   |
| **紧急分配** | 紧急情况下的库存调配 | 紧急程度优先  | 人工干预   |

#### **8.3 库存预警功能**

| 预警类型      | 触发条件     | 预警方式    | 处理建议    |
| --------- | -------- | ------- | ------- |
| **低库存预警** | 库存低于安全库存 | 系统通知+邮件 | 及时补货    |
| **零库存预警** | 库存为零     | 紧急通知+短信 | 立即补货或下架 |
| **过期预警**  | 商品即将过期   | 提前通知    | 促销处理    |
| **滞销预警**  | 长期无销量    | 定期报告    | 调整策略或清仓 |

### **9. 支付管理多租户功能详细规格**

#### **9.1 支付配置功能**

| 配置项      | 配置内容       | 支持方式         | 安全要求   |
| -------- | ---------- | ------------ | ------ |
| **支付通道** | 微信、支付宝、银联等 | 租户独立配置       | 密钥加密存储 |
| **商户信息** | 租户的商户号和密钥  | 独立商户账号       | 敏感信息加密 |
| **费率设置** | 不同支付方式的费率  | 按租户差异化设置     | 权限控制   |
| **结算周期** | 资金结算的时间周期  | T+0/T+1/T+7等 | 合规要求   |

#### **9.2 资金管理功能**

| 管理功能      | 功能描述     | 操作权限    | 风控措施   |
| --------- | -------- | ------- | ------ |
| **资金池管理** | 租户独立资金账户 | 租户财务管理员 | 资金隔离   |
| **分账管理**  | 订单资金自动分账 | 系统自动执行  | 分账规则验证 |
| **提现管理**  | 租户资金提现申请 | 租户管理员申请 | 多级审批   |
| **对账管理**  | 与第三方支付对账 | 系统自动对账  | 差异报警   |

### **10. 数据分析多租户功能详细规格**

#### **10.1 经营分析功能**

| 分析维度     | 分析内容       | 数据指标       | 展示方式    |
| -------- | ---------- | ---------- | ------- |
| **销售分析** | 销售趋势、热销商品  | 销售额、销量、增长率 | 图表+报表   |
| **用户分析** | 加盟商活跃度、留存率 | 活跃用户数、留存率  | 漏斗图+趋势图 |
| **商品分析** | 商品表现、库存周转  | 销售排行、周转率   | 排行榜+分析图 |
| **区域分析** | 各地区经营情况    | 区域销售额、增长率  | 地图+柱状图  |

#### **10.2 对比分析功能**

| 对比类型      | 对比维度     | 对比指标   | 应用场景 |
| --------- | -------- | ------ | ---- |
| **时间对比**  | 同比、环比分析  | 各项经营指标 | 趋势分析 |
| **租户对比**  | 不同租户间对比  | 经营效率指标 | 标杆学习 |
| **加盟商对比** | 租户内加盟商对比 | 业绩排名   | 激励机制 |
| **商品对比**  | 不同商品表现对比 | 销售表现   | 商品优化 |

### **11. 用户体验优化功能详细规格**

#### **11.1 个性化界面功能**

| 个性化项       | 自定义内容       | 配置方式       | 生效范围 |
| ---------- | ----------- | ---------- | ---- |
| **主题色彩**   | 主色调、辅助色、背景色 | 色彩选择器+预设主题 | 整个租户 |
| **Logo展示** | 租户Logo、应用图标 | 图片上传+尺寸适配  | 所有界面 |
| **导航菜单**   | 菜单结构、图标、名称  | 拖拽配置+权限控制  | 租户用户 |
| **首页布局**   | 组件排列、数据展示   | 可视化编辑器     | 租户首页 |
| **语言本地化**  | 界面语言、时区设置   | 语言包+时区选择   | 租户全局 |

#### **11.2 消息通知功能**

| 通知类型     | 触发条件        | 通知方式   | 接收对象 |
| -------- | ----------- | ------ | ---- |
| **业务通知** | 订单状态变更、审批结果 | 站内信+推送 | 相关用户 |
| **系统通知** | 系统维护、功能更新   | 公告+邮件  | 全体用户 |
| **预警通知** | 库存不足、异常订单   | 短信+电话  | 管理员  |

### **12. 系统集成功能详细规格**

#### **12.1 第三方系统集成**

| 集成系统      | 集成目的      | 集成方式        | 数据同步  |
| --------- | --------- | ----------- | ----- |
| **ERP系统** | 库存、财务数据同步 | API接口+定时同步  | 实时+批量 |
| **CRM系统** | 客户信息管理    | 数据导入导出      | 定期同步  |
| **物流系统**  | 订单配送跟踪    | Webhook+API | 实时推送  |
| **财务系统**  | 财务数据对接    | 标准财务接口      | 日结算同步 |

#### **12.2 开放API功能**

| API类型       | 功能范围        | 权限控制    | 使用场景  |
| ----------- | ----------- | ------- | ----- |
| **租户管理API** | 租户CRUD操作    | 超级管理员权限 | 系统集成  |
| **业务数据API** | 订单、商品、加盟商数据 | 租户权限隔离  | 第三方应用 |
| **统计分析API** | 业绩、财务统计数据   | 数据权限控制  | 报表系统  |

#### **12.3 数据导入导出功能**

| 数据类型      | 导入格式       | 导出格式      | 批量处理   |
| --------- | ---------- | --------- | ------ |
| **加盟商数据** | Excel、CSV  | Excel、PDF | 支持万级数据 |
| **商品数据**  | Excel、JSON | Excel、XML | 支持图片批量 |
| **订单数据**  | CSV、API    | Excel、PDF | 支持历史数据 |
| **财务数据**  | 标准财务格式     | 财务报表格式    | 支持加密导出 |

### **13. 安全与合规功能详细规格**

#### **13.1 数据安全功能**

| 安全措施     | 实现方式      | 保护范围   | 监控机制    |
| -------- | --------- | ------ | ------- |
| **数据加密** | AES-256加密 | 敏感数据字段 | 加密状态监控  |
| **访问控制** | RBAC权限模型  | 所有业务功能 | 权限变更日志  |
| **数据备份** | 增量+全量备份   | 全部业务数据 | 备份完整性检查 |
| **审计日志** | 操作日志记录    | 关键业务操作 | 异常行为分析  |

#### **13.2 合规管理功能**

| 合规要求       | 实现措施      | 检查机制    | 报告输出 |
| ---------- | --------- | ------- | ---- |
| **数据保护法规** | 数据脱敏、权限控制 | 定期合规检查  | 合规报告 |
| **财务合规**   | 财务数据规范化   | 财务审计接口  | 审计报告 |
| **行业标准**   | 标准化接口实现   | 标准符合性测试 | 认证报告 |
| **安全等保**   | 安全防护措施    | 安全评估    | 等保报告 |

### **14. 运营管理功能详细规格**

#### **14.1 租户运营监控**

| 监控指标       | 监控内容      | 预警阈值      | 处理措施   |
| ---------- | --------- | --------- | ------ |
| **活跃度监控**  | 用户登录、操作频率 | 连续7天无活跃   | 客服主动联系 |
| **业务量监控**  | 订单量、交易额   | 环比下降30%   | 业务分析报告 |
| **系统使用监控** | 功能使用情况    | 功能使用率<10% | 功能优化建议 |
| **满意度监控**  | 用户反馈、投诉   | 满意度<80%   | 服务改进计划 |

#### **14.2 平台运营功能**

| 运营功能     | 功能描述      | 操作权限  | 效果评估  |
| -------- | --------- | ----- | ----- |
| **租户管理** | 租户创建和配置管理 | 超级管理员 | 租户活跃度 |
| **系统监控** | 系统运行状态监控  | 运维团队  | 系统稳定性 |

---

**文档版本**: v2.2
**创建日期**: 2024-01-01
**最后更新**: 2024-12-25
**维护团队**: 产品团队 & 技术团队
**审核状态**: 基于实际代码模型完善功能细节
