# 多租户功能产品需求文档

## 📋 **文档概述**

本文档从产品功能角度全面梳理多租户功能对现有商城系统的影响，包括新增功能、功能变更和影响分析。

## 🎯 **多租户功能总览**

### **核心概念**
- **租户（Tenant）**：独立的业务实体，拥有独立的数据空间和配置
- **加盟商（Franchisee）**：可以属于一个或多个租户的业务用户
- **租户关联**：加盟商与租户之间的多对多关系管理

### **业务价值**
- 支持多品牌/多区域运营
- 实现数据隔离和独立管理
- 提供灵活的加盟商管理机制
- 支持差异化的业务配置

## 📊 **功能影响分析表**

### **1. 用户认证与权限管理**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 用户登录 | 单一登录入口 | 智能识别用户类型，支持租户选择 | 🔴 高 | 功能增强 |
| 权限验证 | 基于角色的权限控制 | 增加租户维度的权限隔离 | 🔴 高 | 架构变更 |
| JWT令牌 | 标准用户信息 | 增加租户ID和租户权限信息 | 🟡 中 | 数据结构变更 |
| 用户管理 | 全局用户管理 | 按租户隔离的用户管理 | 🔴 高 | 功能重构 |

### **2. 加盟商管理系统**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 加盟商注册 | 直接注册到系统 | 注册时需要指定所属租户 | 🟡 中 | 流程调整 |
| 加盟商列表 | 显示所有加盟商 | 按当前租户过滤显示 | 🔴 高 | 数据过滤 |
| 加盟商详情 | 全局加盟商信息 | 显示在当前租户中的信息和角色 | 🟡 中 | 信息调整 |
| 加盟商搜索 | 全局搜索 | 租户内搜索 | 🟡 中 | 搜索范围调整 |
| 加盟商导入导出 | 全局数据操作 | 租户内数据操作 | 🟡 中 | 数据范围调整 |

### **3. 订单管理系统**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 订单列表 | 显示所有订单 | 按租户隔离显示订单 | 🔴 高 | 数据隔离 |
| 订单创建 | 全局订单创建 | 在当前租户下创建订单 | 🟡 中 | 数据归属 |
| 订单查询 | 全局订单查询 | 租户内订单查询 | 🔴 高 | 查询范围调整 |
| 订单统计 | 全局统计数据 | 租户维度统计数据 | 🔴 高 | 统计维度调整 |
| 订单导出 | 全局数据导出 | 租户数据导出 | 🟡 中 | 导出范围调整 |

### **4. 商品管理系统**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 商品列表 | 全局商品管理 | 租户独立商品管理 | 🔴 高 | 数据隔离 |
| 商品分类 | 全局分类体系 | 租户独立分类体系 | 🔴 高 | 数据结构调整 |
| 库存管理 | 全局库存统计 | 租户独立库存管理 | 🔴 高 | 库存隔离 |
| 价格管理 | 统一价格体系 | 租户独立定价策略 | 🟡 中 | 定价策略调整 |

### **5. 财务管理系统**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 收入统计 | 全局收入统计 | 租户维度收入统计 | 🔴 高 | 统计维度调整 |
| 成本核算 | 全局成本管理 | 租户独立成本核算 | 🔴 高 | 核算体系调整 |
| 财务报表 | 全局财务报表 | 租户独立财务报表 | 🔴 高 | 报表体系重构 |
| 结算管理 | 统一结算流程 | 租户独立结算流程 | 🟡 中 | 流程调整 |

### **6. 系统配置管理**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更类型 |
|---------|---------|-----------|---------|---------|
| 系统配置 | 全局系统配置 | 租户独立配置 + 全局配置 | 🔴 高 | 配置体系重构 |
| 主题设置 | 统一主题风格 | 租户独立主题定制 | 🟡 中 | 个性化功能 |
| 支付配置 | 全局支付配置 | 租户独立支付配置 | 🟡 中 | 配置隔离 |
| 物流配置 | 全局物流设置 | 租户独立物流配置 | 🟡 中 | 配置隔离 |

## 🆕 **新增功能模块**

### **1. 租户管理**

| 功能点 | 功能描述 | 优先级 | 用户角色 |
|-------|---------|-------|---------|
| 租户创建 | 创建新的租户实体，设置基本信息 | P0 | 超级管理员 |
| 租户配置 | 配置租户的主题、支付、物流等设置 | P0 | 租户管理员 |
| 租户状态管理 | 启用/停用租户，控制租户访问权限 | P0 | 超级管理员 |
| 租户信息编辑 | 修改租户基本信息和配置 | P1 | 租户管理员 |
| 租户数据统计 | 查看租户的业务数据统计 | P1 | 租户管理员 |

### **2. 加盟商租户关联管理**

| 功能点 | 功能描述 | 优先级 | 用户角色 |
|-------|---------|-------|---------|
| 关联关系创建 | 将加盟商添加到指定租户 | P0 | 租户管理员 |
| 关联关系管理 | 查看、编辑、删除加盟商租户关联 | P0 | 租户管理员 |
| 角色权限设置 | 设置加盟商在租户中的角色和权限 | P0 | 租户管理员 |
| 默认租户设置 | 为加盟商设置默认登录租户 | P1 | 租户管理员 |
| 关联状态管理 | 激活/停用加盟商在租户中的状态 | P1 | 租户管理员 |

### **3. 租户切换功能**

| 功能点 | 功能描述 | 优先级 | 用户角色 |
|-------|---------|-------|---------|
| 租户选择界面 | 登录时显示可用租户列表供选择 | P0 | 加盟商 |
| 租户切换 | 在系统中快速切换当前操作的租户 | P0 | 加盟商 |
| 租户信息显示 | 在界面中显示当前所在的租户信息 | P1 | 加盟商 |
| 切换历史记录 | 记录和显示租户切换历史 | P2 | 加盟商 |

## 📱 **移动端功能变更**

### **APP功能影响分析**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 实现方案 |
|---------|---------|-----------|---------|---------|
| 登录界面 | 手机号+密码登录 | 增加租户选择步骤 | 🔴 高 | 新增租户选择页面 |
| 主界面 | 统一的主界面风格 | 根据租户配置动态调整 | 🟡 中 | 主题配置动态加载 |
| 商品浏览 | 显示所有商品 | 显示当前租户的商品 | 🔴 高 | API数据过滤 |
| 订单管理 | 显示所有订单 | 显示当前租户的订单 | 🔴 高 | API数据过滤 |
| 个人中心 | 统一的个人信息 | 显示在当前租户中的信息 | 🟡 中 | 信息展示调整 |

### **小程序功能影响**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 实现方案 |
|---------|---------|-----------|---------|---------|
| 授权登录 | 微信授权登录 | 增加租户绑定流程 | 🟡 中 | 新增租户绑定页面 |
| 商品展示 | 统一商品展示 | 租户独立商品展示 | 🔴 高 | 数据源调整 |
| 支付流程 | 统一支付配置 | 使用租户支付配置 | 🟡 中 | 支付配置动态获取 |
| 分享功能 | 统一分享样式 | 租户品牌化分享 | 🟡 中 | 分享内容定制 |

## 🔄 **业务流程变更**

### **1. 加盟商注册流程**

#### **原有流程**
```
填写基本信息 → 提交审核 → 审核通过 → 账号激活 → 开始使用
```

#### **多租户流程**
```
选择加入租户 → 填写基本信息 → 提交审核 → 租户管理员审核 → 设置角色权限 → 账号激活 → 开始使用
```

### **2. 加盟商登录流程**

#### **原有流程**
```
输入手机号密码 → 验证通过 → 进入系统
```

#### **多租户流程**
```
输入手机号密码 → 验证通过 → 检查租户关联 → 选择租户（如需要） → 进入对应租户系统
```

### **3. 订单处理流程**

#### **原有流程**
```
客户下单 → 订单分配 → 加盟商处理 → 订单完成
```

#### **多租户流程**
```
客户下单 → 租户内订单分配 → 租户内加盟商处理 → 租户内订单完成 → 租户统计更新
```

## 📊 **数据迁移影响**

### **需要迁移的数据**

| 数据类型 | 迁移方式 | 影响范围 | 风险等级 |
|---------|---------|---------|---------|
| 现有加盟商 | 自动关联到默认租户 | 全部加盟商数据 | 🟡 中 |
| 历史订单 | 根据加盟商关联分配到租户 | 全部订单数据 | 🔴 高 |
| 商品数据 | 复制到各租户或指定归属 | 全部商品数据 | 🔴 高 |
| 财务数据 | 按租户重新归类统计 | 全部财务数据 | 🔴 高 |
| 系统配置 | 迁移为默认租户配置 | 全部配置数据 | 🟡 中 |

### **数据一致性保证**

| 检查项 | 检查方法 | 修复方案 |
|-------|---------|---------|
| 加盟商租户关联完整性 | 查询无关联的加盟商 | 自动关联到默认租户 |
| 订单数据归属正确性 | 验证订单租户归属 | 根据加盟商关联修正 |
| 权限数据一致性 | 检查权限配置完整性 | 重新生成权限数据 |
| 统计数据准确性 | 重新计算各项统计 | 执行数据重算脚本 |

## 🎨 **用户界面变更**

### **管理后台界面**

| 界面模块 | 变更内容 | 变更类型 |
|---------|---------|---------|
| 顶部导航 | 增加当前租户显示和切换入口 | 新增功能 |
| 侧边菜单 | 增加租户管理相关菜单项 | 新增功能 |
| 数据列表 | 所有列表增加租户过滤功能 | 功能增强 |
| 表单页面 | 相关表单增加租户选择字段 | 字段调整 |
| 统计图表 | 增加租户维度的数据统计 | 数据维度调整 |

### **移动端界面**

| 界面模块 | 变更内容 | 变更类型 |
|---------|---------|---------|
| 登录页面 | 可能需要增加租户选择步骤 | 流程调整 |
| 主界面 | 根据租户配置调整主题风格 | 样式动态化 |
| 个人中心 | 显示当前租户信息 | 信息展示调整 |
| 设置页面 | 增加租户切换功能 | 新增功能 |

## 📈 **性能影响评估**

### **数据库性能**

| 影响项 | 影响程度 | 优化方案 |
|-------|---------|---------|
| 查询复杂度 | 增加租户过滤条件 | 添加租户相关索引 |
| 数据量增长 | 关联表数据增加 | 定期数据清理和归档 |
| 并发访问 | 多租户并发查询 | 数据库连接池优化 |
| 存储空间 | 租户配置数据增加 | 配置数据压缩存储 |

### **应用性能**

| 影响项 | 影响程度 | 优化方案 |
|-------|---------|---------|
| 内存使用 | 租户配置缓存 | 合理的缓存策略 |
| 响应时间 | 租户权限验证开销 | 权限缓存优化 |
| 并发处理 | 租户隔离处理逻辑 | 异步处理优化 |
| 网络传输 | 租户信息传输 | 数据压缩和CDN |

## 🔒 **安全性考虑**

### **数据安全**

| 安全项 | 风险等级 | 防护措施 |
|-------|---------|---------|
| 跨租户数据泄露 | 🔴 高 | 严格的数据过滤和权限验证 |
| 租户权限越权 | 🔴 高 | 多层权限验证机制 |
| 敏感信息暴露 | 🟡 中 | 数据脱敏和加密存储 |
| 审计日志完整性 | 🟡 中 | 完整的操作日志记录 |

### **访问控制**

| 控制项 | 实现方式 | 验证机制 |
|-------|---------|---------|
| 租户隔离 | JWT令牌包含租户信息 | 每次请求验证租户权限 |
| 角色权限 | 基于角色的访问控制 | 动态权限验证 |
| API安全 | 接口级别的租户验证 | 中间件统一验证 |
| 数据加密 | 敏感数据加密存储 | 加密算法和密钥管理 |

## 💼 **运营管理功能变更**

### **营销活动管理**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更说明 |
|---------|---------|-----------|---------|---------|
| 优惠券管理 | 全局优惠券系统 | 租户独立优惠券管理 | 🔴 高 | 每个租户可独立创建和管理优惠券 |
| 促销活动 | 统一促销活动 | 租户独立促销策略 | 🔴 高 | 支持租户差异化营销策略 |
| 积分系统 | 全局积分体系 | 租户独立积分规则 | 🟡 中 | 租户可自定义积分获取和消费规则 |
| 会员等级 | 统一会员体系 | 租户独立会员等级 | 🟡 中 | 支持租户个性化会员权益 |

### **客服管理系统**

| 功能模块 | 原有功能 | 多租户变更 | 影响等级 | 变更说明 |
|---------|---------|-----------|---------|---------|
| 客服工单 | 全局工单管理 | 租户内工单隔离 | 🔴 高 | 工单只在租户内可见和处理 |
| 客服知识库 | 统一知识库 | 租户独立知识库 | 🟡 中 | 支持租户自定义FAQ和帮助文档 |
| 客服统计 | 全局客服数据 | 租户维度客服统计 | 🔴 高 | 按租户统计客服工作量和满意度 |

## 📊 **详细功能对比表**

### **核心业务功能对比**

| 功能领域 | 子功能 | 单租户模式 | 多租户模式 | 开发工作量 | 测试复杂度 |
|---------|-------|-----------|-----------|-----------|-----------|
| **用户管理** | 用户注册 | 直接注册 | 选择租户注册 | 🟡 中 | 🟡 中 |
| | 用户登录 | 简单登录 | 智能租户识别登录 | 🔴 高 | 🔴 高 |
| | 权限控制 | 角色权限 | 租户+角色权限 | 🔴 高 | 🔴 高 |
| | 用户列表 | 全局列表 | 租户过滤列表 | 🟡 中 | 🟡 中 |
| **商品管理** | 商品创建 | 全局商品 | 租户商品 | 🟡 中 | 🟡 中 |
| | 商品分类 | 全局分类 | 租户分类 | 🔴 高 | 🔴 高 |
| | 库存管理 | 全局库存 | 租户库存 | 🔴 高 | 🔴 高 |
| | 价格策略 | 统一定价 | 租户定价 | 🟡 中 | 🟡 中 |
| **订单管理** | 订单创建 | 全局订单 | 租户订单 | 🟡 中 | 🟡 中 |
| | 订单查询 | 全局查询 | 租户查询 | 🔴 高 | 🔴 高 |
| | 订单统计 | 全局统计 | 租户统计 | 🔴 高 | 🔴 高 |
| | 订单导出 | 全局导出 | 租户导出 | 🟡 中 | 🟡 中 |

### **管理功能对比**

| 管理模块 | 原有权限 | 多租户权限 | 权限变更 | 影响用户 |
|---------|---------|-----------|---------|---------|
| **超级管理员** | 全系统管理 | 全系统+租户管理 | 增加租户管理权限 | 系统管理员 |
| **租户管理员** | - | 租户内全部管理 | 新增角色 | 租户负责人 |
| **加盟商管理员** | 加盟商管理 | 租户内加盟商管理 | 权限范围缩小 | 加盟商管理员 |
| **普通加盟商** | 基础操作 | 租户内基础操作 | 权限范围缩小 | 普通加盟商 |
| **客服人员** | 全局客服 | 租户内客服 | 权限范围缩小 | 客服人员 |

## 🔄 **业务流程重构分析**

### **关键业务流程变更**

#### **1. 商品上架流程**

**原有流程**：
```
商品信息录入 → 商品审核 → 上架销售 → 全平台可见
```

**多租户流程**：
```
选择目标租户 → 商品信息录入 → 租户内审核 → 租户内上架 → 租户内可见
```

**变更影响**：
- 需要增加租户选择步骤
- 审核流程需要租户隔离
- 商品可见性限制在租户内

#### **2. 订单分配流程**

**原有流程**：
```
客户下单 → 系统分配 → 就近加盟商 → 订单处理
```

**多租户流程**：
```
客户下单 → 确定租户 → 租户内分配 → 租户内加盟商 → 订单处理
```

**变更影响**：
- 订单分配范围限制在租户内
- 需要考虑租户边界问题
- 可能影响配送效率

#### **3. 财务结算流程**

**原有流程**：
```
订单完成 → 收入统计 → 成本核算 → 利润分配 → 财务结算
```

**多租户流程**：
```
订单完成 → 租户收入统计 → 租户成本核算 → 租户内利润分配 → 租户财务结算 → 平台抽成结算
```

**变更影响**：
- 财务数据需要按租户隔离
- 增加平台与租户的结算环节
- 财务报表需要多维度展示

## 📱 **移动端产品功能变更详情**

### **APP端功能变更**

| 页面/功能 | 原有设计 | 多租户设计 | UI变更 | 交互变更 |
|----------|---------|-----------|-------|---------|
| **启动页** | 统一品牌展示 | 动态品牌展示 | Logo动态加载 | 无 |
| **登录页** | 手机号+密码 | 手机号+密码+租户选择 | 增加租户选择UI | 增加租户选择流程 |
| **首页** | 统一首页布局 | 租户定制首页 | 布局配置化 | 内容动态加载 |
| **商品列表** | 全部商品 | 租户商品 | 无变更 | 数据源调整 |
| **购物车** | 统一购物车 | 租户内购物车 | 无变更 | 数据隔离 |
| **订单页** | 全部订单 | 租户订单 | 无变更 | 数据过滤 |
| **个人中心** | 统一个人信息 | 租户相关信息 | 增加租户信息展示 | 增加租户切换 |

### **小程序端功能变更**

| 功能模块 | 原有功能 | 多租户功能 | 开发难度 | 用户体验影响 |
|---------|---------|-----------|---------|-------------|
| **授权登录** | 微信一键登录 | 微信登录+租户绑定 | 🟡 中 | 增加一步操作 |
| **商品浏览** | 全商品浏览 | 租户商品浏览 | 🟡 中 | 商品范围变化 |
| **分享功能** | 统一分享样式 | 租户品牌分享 | 🟡 中 | 分享内容个性化 |
| **支付流程** | 统一支付配置 | 租户支付配置 | 🔴 高 | 支付方式可能不同 |

## 💰 **成本效益分析**

### **开发成本评估**

| 功能模块 | 开发工作量（人天） | 测试工作量（人天） | 总成本评估 |
|---------|------------------|------------------|-----------|
| 租户管理系统 | 15 | 8 | 🔴 高 |
| 用户登录改造 | 10 | 6 | 🟡 中 |
| 数据隔离改造 | 25 | 15 | 🔴 高 |
| 权限系统重构 | 20 | 12 | 🔴 高 |
| 移动端适配 | 12 | 8 | 🟡 中 |
| 数据迁移 | 8 | 5 | 🟡 中 |
| **总计** | **90** | **54** | **🔴 高** |

### **业务价值评估**

| 价值维度 | 价值描述 | 量化指标 | 预期收益 |
|---------|---------|---------|---------|
| **市场扩展** | 支持多品牌运营 | 可支持租户数量 | 无限扩展 |
| **运营效率** | 独立运营管理 | 运营效率提升 | 30%+ |
| **数据安全** | 数据隔离保护 | 数据泄露风险 | 降低90% |
| **个性化服务** | 租户定制化 | 客户满意度 | 提升25% |
| **收入增长** | 多租户收费 | 平台收入 | 增长50%+ |

## 🎯 **实施优先级建议**

### **P0 - 核心功能（必须实现）**

| 功能 | 实施周期 | 依赖关系 | 风险等级 |
|-----|---------|---------|---------|
| 租户基础管理 | 2周 | 无 | 🟡 中 |
| 用户登录改造 | 2周 | 租户管理 | 🔴 高 |
| 数据隔离机制 | 3周 | 租户管理 | 🔴 高 |
| 权限系统重构 | 3周 | 用户登录 | 🔴 高 |

### **P1 - 重要功能（优先实现）**

| 功能 | 实施周期 | 依赖关系 | 风险等级 |
|-----|---------|---------|---------|
| 租户配置管理 | 2周 | 租户基础管理 | 🟡 中 |
| 移动端适配 | 3周 | 用户登录改造 | 🟡 中 |
| 数据迁移工具 | 1周 | 数据隔离机制 | 🟡 中 |
| 租户切换功能 | 1周 | 权限系统 | 🟡 中 |

### **P2 - 增强功能（后续实现）**

| 功能 | 实施周期 | 依赖关系 | 风险等级 |
|-----|---------|---------|---------|
| 租户主题定制 | 2周 | 租户配置管理 | 🟢 低 |
| 高级权限控制 | 2周 | 权限系统重构 | 🟡 中 |
| 租户数据分析 | 1周 | 数据隔离机制 | 🟢 低 |
| 批量操作功能 | 1周 | 基础功能完成 | 🟢 低 |

## 📋 **验收标准**

### **功能验收标准**

| 验收项 | 验收标准 | 测试方法 |
|-------|---------|---------|
| 租户隔离 | 不同租户数据完全隔离，无交叉访问 | 数据访问测试 |
| 用户登录 | 支持智能租户识别和选择 | 登录流程测试 |
| 权限控制 | 租户内权限正确，跨租户访问被阻止 | 权限边界测试 |
| 数据迁移 | 现有数据正确迁移，无数据丢失 | 数据一致性测试 |
| 性能表现 | 多租户功能不影响系统性能 | 性能压力测试 |

### **用户体验验收标准**

| 体验项 | 验收标准 | 评估方法 |
|-------|---------|---------|
| 登录体验 | 登录流程简洁，租户选择直观 | 用户体验测试 |
| 界面一致性 | 多租户界面风格保持一致性 | UI一致性检查 |
| 操作便捷性 | 租户切换操作简单快捷 | 操作效率测试 |
| 错误处理 | 错误提示清晰，引导用户正确操作 | 异常场景测试 |

---

**文档版本**: v1.0
**创建日期**: 2024-01-01
**维护团队**: 产品团队 & 技术团队
**审核状态**: 待审核
