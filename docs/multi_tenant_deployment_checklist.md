# 多租户系统部署检查清单

## 📋 概述

本检查清单用于确保多租户系统的安全部署，包括数据库迁移、代码部署、功能验证和回滚准备。

## 🔧 部署前准备

### **1. 环境检查**
- [ ] **测试环境验证**
  - [ ] 测试环境已完成完整的多租户功能测试
  - [ ] 所有单元测试通过
  - [ ] 集成测试通过
  - [ ] 性能测试满足要求

- [ ] **生产环境准备**
  - [ ] 生产数据库已备份
  - [ ] 应用服务器资源充足
  - [ ] 监控系统正常运行
  - [ ] 日志系统正常运行

### **2. 代码检查**
- [ ] **核心功能实现**
  - [ ] JWT结构包含所有必要字段（UserID、TenantID、FranchiseeID、TenantCode）
  - [ ] 加盟商模型包含TenantID字段
  - [ ] FranchiseeMultiTenantService完整实现
  - [ ] TenantAccessMiddleware正确实现
  - [ ] 多租户登录API完整实现

- [ ] **安全检查**
  - [ ] 数据隔离机制正确实现
  - [ ] 跨租户访问防护到位
  - [ ] JWT令牌安全配置
  - [ ] 敏感信息不在日志中暴露

### **3. 数据库准备**
- [ ] **迁移脚本验证**
  - [ ] SQL迁移脚本在测试环境验证通过
  - [ ] 复合唯一索引创建正确
  - [ ] 外键约束配置合理
  - [ ] 数据完整性验证通过

- [ ] **备份策略**
  - [ ] 完整数据库备份已创建
  - [ ] 备份文件已验证可恢复
  - [ ] 回滚脚本已准备并测试

## 🚀 部署执行步骤

### **第一阶段：数据库迁移**

#### **1. 执行数据库迁移**
```bash
# 1. 连接到生产数据库
mysql -u [username] -p [database_name]

# 2. 执行迁移脚本
source sql/add_tenant_id_to_franchisee.sql

# 3. 验证迁移结果
SELECT COUNT(*) FROM franchisee WHERE tenant_id IS NOT NULL;
SELECT COUNT(*) FROM franchisee WHERE tenant_id = 0;
```

#### **2. 验证数据完整性**
- [ ] **字段检查**
  - [ ] 所有加盟商记录都有tenant_id字段
  - [ ] tenant_id默认值设置正确（通常为1）
  - [ ] 复合唯一索引创建成功

- [ ] **索引检查**
  ```sql
  SHOW INDEX FROM franchisee WHERE Key_name IN ('idx_tenant_user', 'idx_tenant_code');
  ```

- [ ] **数据一致性检查**
  ```sql
  -- 检查重复的租户-用户组合
  SELECT tenant_id, user_id, COUNT(*) 
  FROM franchisee 
  GROUP BY tenant_id, user_id 
  HAVING COUNT(*) > 1;
  
  -- 检查重复的租户-编码组合
  SELECT tenant_id, code, COUNT(*) 
  FROM franchisee 
  GROUP BY tenant_id, code 
  HAVING COUNT(*) > 1;
  ```

### **第二阶段：应用部署**

#### **1. 代码部署**
- [ ] **停止应用服务**
  ```bash
  # 停止应用服务器
  systemctl stop guanpu-server
  ```

- [ ] **部署新代码**
  ```bash
  # 备份当前版本
  cp -r /path/to/current /path/to/backup/$(date +%Y%m%d_%H%M%S)
  
  # 部署新版本
  git pull origin main
  go build -o guanpu-server main.go
  ```

- [ ] **配置检查**
  - [ ] JWT配置正确
  - [ ] 数据库连接配置正确
  - [ ] 日志配置适当

#### **2. 启动应用**
- [ ] **启动服务**
  ```bash
  systemctl start guanpu-server
  systemctl status guanpu-server
  ```

- [ ] **健康检查**
  ```bash
  curl -f http://localhost:8888/health || exit 1
  ```

### **第三阶段：功能验证**

#### **1. 基础功能测试**
- [ ] **单租户登录测试**
  ```bash
  # 测试单租户用户登录
  curl -X POST http://localhost:8888/app/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username":"single_tenant_user","password":"password","captcha":"1234","captchaId":"test"}'
  ```

- [ ] **多租户登录测试**
  ```bash
  # 测试多租户用户登录
  curl -X POST http://localhost:8888/app/auth/login \
    -H "Content-Type: application/json" \
    -d '{"username":"multi_tenant_user","password":"password","captcha":"1234","captchaId":"test"}'
  ```

#### **2. 数据隔离验证**
- [ ] **租户数据隔离**
  - [ ] 用户只能看到自己租户的数据
  - [ ] 跨租户访问被正确阻止
  - [ ] GORM插件自动过滤生效

- [ ] **加盟商身份验证**
  - [ ] TenantAccessMiddleware正确验证身份
  - [ ] 无效的加盟商ID被拒绝
  - [ ] JWT令牌包含正确的租户和加盟商信息

#### **3. 性能验证**
- [ ] **响应时间检查**
  - [ ] 登录接口响应时间 < 500ms
  - [ ] 租户切换响应时间 < 200ms
  - [ ] 数据查询性能无明显下降

- [ ] **并发测试**
  - [ ] 多用户同时登录正常
  - [ ] 租户切换并发安全
  - [ ] 数据库连接池正常

## 🔍 监控和验证

### **1. 日志监控**
- [ ] **应用日志**
  ```bash
  # 检查应用启动日志
  tail -f /var/log/guanpu-server/app.log | grep -E "(ERROR|WARN|租户)"
  ```

- [ ] **数据库日志**
  ```bash
  # 检查数据库慢查询
  tail -f /var/log/mysql/slow.log
  ```

### **2. 性能监控**
- [ ] **系统资源**
  - [ ] CPU使用率正常
  - [ ] 内存使用率正常
  - [ ] 数据库连接数正常

- [ ] **业务指标**
  - [ ] 登录成功率 > 99%
  - [ ] API响应时间正常
  - [ ] 错误率 < 1%

### **3. 安全监控**
- [ ] **安全事件**
  - [ ] 无跨租户数据访问尝试
  - [ ] 无异常的JWT令牌
  - [ ] 无SQL注入尝试

## ⚠️ 回滚计划

### **1. 回滚触发条件**
- [ ] 登录功能异常
- [ ] 数据隔离失效
- [ ] 性能严重下降
- [ ] 安全问题发现

### **2. 回滚步骤**
#### **应用回滚**
```bash
# 1. 停止当前服务
systemctl stop guanpu-server

# 2. 恢复备份版本
cp /path/to/backup/[timestamp]/guanpu-server /path/to/current/

# 3. 启动服务
systemctl start guanpu-server
```

#### **数据库回滚**
```sql
-- 1. 删除新增的索引
ALTER TABLE franchisee DROP INDEX idx_tenant_user;
ALTER TABLE franchisee DROP INDEX idx_tenant_code;

-- 2. 删除tenant_id字段
ALTER TABLE franchisee DROP COLUMN tenant_id;
```

### **3. 回滚验证**
- [ ] 应用正常启动
- [ ] 原有功能正常
- [ ] 数据完整性保持
- [ ] 性能恢复正常

## 📊 部署后验证

### **1. 功能验证清单**
- [ ] 用户可以正常登录
- [ ] 单租户用户直接登录成功
- [ ] 多租户用户可以选择租户
- [ ] 租户切换功能正常
- [ ] 数据按租户正确隔离
- [ ] API接口响应正确

### **2. 性能验证清单**
- [ ] 登录响应时间正常
- [ ] 数据查询性能正常
- [ ] 并发处理能力正常
- [ ] 内存使用稳定

### **3. 安全验证清单**
- [ ] 跨租户访问被阻止
- [ ] JWT令牌安全有效
- [ ] 敏感数据不泄露
- [ ] 审计日志正常记录

## ✅ 部署完成确认

### **最终检查**
- [ ] 所有功能测试通过
- [ ] 性能指标正常
- [ ] 安全检查通过
- [ ] 监控系统正常
- [ ] 文档已更新
- [ ] 团队已通知

### **部署记录**
- **部署时间**: _______________
- **部署版本**: _______________
- **执行人员**: _______________
- **验证人员**: _______________
- **备注**: _______________

---

**检查清单版本**: v1.0  
**最后更新**: 2024-12-25  
**适用版本**: 多租户系统 v2.0（基于加盟商1:1关系）
