package dao

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/account"
	"github.com/OSQianXing/guanpu-server/model/app"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/utils"
)

var Product = new(product)

type product struct {
}

func (p *product) GetCombinationProductDetial(productID int) ([]*products.CombinationDetail, error) {
	// 组合商品信息
	var out []*products.CombinationDetail
	err := global.GVA_DB.Preload("Product").Table("combination_product_detail as cp").
		Joins("left join product as p on p.id = cp.product_id").
		Where("cp.combination_product_id = ?", productID).
		Select("cp.product_id,cp.quantity,p.name as product_name,p.image_addr,p.spec").
		Find(&out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (p *product) FirstProduct(productID int) (*products.Product, error) {
	var out products.Product
	err := global.GVA_DB.Model(products.Product{}).Unscoped().Where("id = ?", productID).
		First(&out).Error
	if err != nil {
		return nil, err
	}
	if utils.BoolIsTrue(out.IsCombination) {
		out.CombinationDetail, err = p.GetCombinationProductDetial(productID)
		if err != nil {
			return nil, err
		}
	}
	// 获取商品所属分区
	if out.SpecialMallID != 0 {
		var sm products.SpecialMall
		err = global.GVA_DB.Model(products.SpecialMall{}).Unscoped().Where("id = ?", out.SpecialMallID).First(&sm).Error
		if err != nil {
			return nil, err
		}
		out.SpecialMall = &sm
	}
	return &out, nil
}

func (p *product) FirstAppProduct(productID int) (*app.Product, error) {
	var out app.Product
	err := global.GVA_DB.Model(app.Product{}).Unscoped().Where("id = ?", productID).
		First(&out).Error
	if err != nil {
		return nil, err
	}
	if utils.BoolIsTrue(out.IsCombination) {
		out.CombinationDetail, err = p.GetCombinationProductDetial(productID)
		if err != nil {
			return nil, err
		}
	}
	if out.SpecialMallID != 0 {
		var sm products.SpecialMall
		err = global.GVA_DB.Model(products.SpecialMall{}).Unscoped().Where("id = ?", out.SpecialMallID).First(&sm).Error
		if err != nil {
			return nil, err
		}
		out.SpecailMallName = sm.Name
		// 查询账户信息
		var ea account.ExclusiveAccount
		err = global.GVA_DB.Model(account.ExclusiveAccount{}).Unscoped().Where("id = ?", sm.WalletID).First(&ea).Error
		if err != nil {
			return nil, err
		}
		out.ExclusiveAccount = ea
	}
	return &out, nil
}

func (p *product) GetAllSpecialMallID() ([]int, error) {
	var out []int
	err := global.GVA_DB.Model(products.Product{}).
		Where("special_mall_id>0").Distinct("special_mall_id").
		Pluck("special_mall_id", &out).Error
	if err != nil {
		return nil, err
	}
	err = global.GVA_DB.Model(products.SpecialMall{}).
		Where("id in (?)", out).
		Order("sort desc").
		Pluck("id", &out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (p *product) GetAllPCategoryIDs() ([]int, error) {
	var out []int
	err := global.GVA_DB.Model(products.Product{}).
		Where("deleted_at is null").Distinct("p_category_id").
		Pluck("p_category_id", &out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (p *product) GetOnSaleProduct(id uint) (products.Product, error) {
	var out products.Product
	err := global.GVA_DB.Model(products.Product{}).Where("id = ?", id).
		Where("on_sale = ?", true).
		First(&out).Error
	if err != nil {
		return out, err
	}
	return out, nil
}
