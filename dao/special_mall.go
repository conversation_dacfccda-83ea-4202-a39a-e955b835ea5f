package dao

import (
	"fmt"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/model/products/response"
	"strings"
)

var SpecialMall = new(specialMall)

type specialMall struct {
}

func (s *specialMall) GetSpecialMallByID(id int) (*response.SpecialMallWithWallet, error) {
	var out response.SpecialMallWithWallet
	err := global.GVA_DB.Table("special_mall as sm").
		Joins("left join exclusive_account as w on w.id = sm.wallet_id").
		Where("sm.id = ?", id).
		Select("sm.*,w.name as wallet_name,w.code as wallet_code,w.exclusive_type,w.f_category_ids,w.is_banned").
		First(&out).Error
	if err != nil {
		return nil, err
	}

	var fcs []string
	err = global.GVA_DB.Model(&franchisees.FranchiseeCategory{}).Where("id in (?)", *out.FCategoryIds).Select("name").Scan(&fcs).Error
	if err != nil {
		return nil, err
	}
	var count int64
	err = global.GVA_DB.Model(&products.Product{}).Where("special_mall_id = ?", out.ID).Count(&count).Error
	if err != nil {
		return nil, err
	}
	out.FCategoryNames = fcs
	out.ProductCount = count
	return &out, nil
}

func (s *specialMall) GetSpecialMall(id int) (*products.SpecialMall, error) {
	var out products.SpecialMall
	err := global.GVA_DB.Model(products.SpecialMall{}).Unscoped().Where("id = ?", id).Order("sort desc").First(&out).Error
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (s *specialMall) GetSpecialMallByIDs(ids []int) ([]*products.SpecialMall, error) {
	var out []*products.SpecialMall
	err := global.GVA_DB.Model(products.SpecialMall{}).Unscoped().
		Where("id in ?", ids).
		Order("sort desc").
		Find(&out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (s *specialMall) GetAllSpecialMallIDs() ([]int, error) {
	var out []int
	err := global.GVA_DB.Model(products.SpecialMall{}).
		Where("enable = 1 and deleted_at is null").
		Pluck("id", &out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (s *specialMall) GetCaclPerformanceSpecialMallIDs() ([]int, error) {
	var out []int
	err := global.GVA_DB.Model(&products.SpecialMall{}).
		Where("calc_performance = 1").
		Pluck("id", &out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (s *specialMall) IsGeneralMall(id int) (bool, error) {
	var out products.SpecialMall
	err := global.GVA_DB.Model(products.SpecialMall{}).Unscoped().Where("id = ?", id).Order("sort desc").First(&out).Error
	if err != nil {
		return false, err
	}
	return strings.Contains(out.Name, "通用专区"), nil
}

// GetgeneralMallID 查询通用专区ID
func (s *specialMall) GetgeneralMallID(specialMallIDs []int) (int, error) {
	if len(specialMallIDs) == 0 {
		return 0, nil
	}
	var generalMallID int
	err := global.GVA_DB.Model(&products.SpecialMall{}).
		Where("id in (?)", specialMallIDs).
		Where("name like '%通用专区%'").
		Select("id").
		Scan(&generalMallID).Error

	if err != nil {
		return 0, err
	}
	return generalMallID, nil
}

// GetSpecialMallIDsByFCategoryId 根据加盟商分类查询专区商城
func (s *specialMall) GetSpecialMallIDsByFCategoryId(fCategoryId int) ([]*products.SpecialMall, error) {
	var out []*products.SpecialMall
	err := global.GVA_DB.Model(&products.SpecialMall{}).
		Joins("left join exclusive_account as ea on special_mall.wallet_id = ea.id ").
		Where("ea.is_banned = 0").
		Where("special_mall.enable = 1").
		Where("special_mall.calc_performance = 1").
		Where(fmt.Sprintf(`JSON_CONTAINS(f_category_ids, "%v")`, fCategoryId)).
		Order("special_mall.sort desc").
		Find(&out).Error
	if err != nil {
		return nil, err
	}
	return out, nil
}
