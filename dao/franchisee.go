package dao

import (
	"fmt"
	"strings"

	"github.com/OSQianXing/guanpu-server/types"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
)

type franchisee struct{}

var Franchisee = new(franchisee)

// GetFranchiseeIDByName
func (f *franchisee) GetFranchiseeIDByName(name string) ([]int, error) {
	var franchiseeIDs []int
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Where("name like ?", fmt.Sprintf("%%%s%%", name)).
		Pluck("id", &franchiseeIDs).Error
	return franchiseeIDs, err
}

func (f *franchisee) GetFranchiseeByUserID(userID uint) (franchisee franchisees.Franchisee, err error) {
	err = global.GVA_DB.Model(franchisees.Franchisee{}).Where("user_id=?", userID).First(&franchisee).Error
	return
}

func (f *franchisee) GetFranchiseeIDByFCategoryID(fCategoryID int) (franchiseeIDs []uint, err error) {
	err = global.GVA_DB.Model(franchisees.Franchisee{}).
		Where("f_category_id=?", fCategoryID).
		Pluck("id", &franchiseeIDs).Error
	return
}

func (f *franchisee) GetFranchiseeIDByArea(area []string) (franchiseeIDs []uint, err error) {
	query := global.GVA_DB.Model(franchisees.Franchisee{})
	for i, a := range area {
		oneAreas := strings.Split(a, "|")
		if len(oneAreas) != 3 {
			continue
		}
		if i == 0 {
			query = query.Where("province=? and city=? and county=?", oneAreas[0], oneAreas[1], oneAreas[2])
		} else {
			query = query.Or("province=? and city=? and county=?", oneAreas[0], oneAreas[1], oneAreas[2])
		}
	}

	err = query.Pluck("id", &franchiseeIDs).Error
	return
}

// GetCategoryNameByFranchiseeId 根据加盟商 id 获取加盟商分类名称
func (f *franchisee) GetCategoryNameByFranchiseeId(franchiseeID uint) (categoryName string, err error) {
	err = global.GVA_DB.Model(franchisees.Franchisee{}).
		Select("f_category.name").
		Joins("left join franchisee_category as f_category on f_category.id=franchisee.f_category_id").
		Where("franchisee.id=?", franchiseeID).
		Find(&categoryName).Error

	if err != nil {
		return "", err
	}

	return
}

// GetFranchiseeByFranchiseeID 根据加盟商 id 获取加盟商信息
func (f *franchisee) GetFranchiseeByFranchiseeID(franchiseeID int) (franchisee franchisees.Franchisee, err error) {
	err = global.GVA_DB.Where("id=?", franchiseeID).First(&franchisee).Error
	return
}

// GetFranchiseeWxOpenid 根据加盟商 id 获取加盟商微信 openid
func (f *franchisee) GetFranchiseeWxOpenid(franchiseeID int) (wxOpenid string, err error) {
	err = global.GVA_DB.Model(&franchisees.FranchiseeWxInfo{}).
		Select("openid").
		Where("franchisee_id=? AND wx_type=?", franchiseeID, types.WxTypeMiniProgram).
		Find(&wxOpenid).Error
	return
}
