package dao

import (
	"errors"
	"time"

	"github.com/OSQianXing/guanpu-server/types"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/orders/response"
	productRes "github.com/OSQianXing/guanpu-server/model/products/response"
	"github.com/OSQianXing/guanpu-server/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var Order = new(order)

type order struct{}

func (o *order) GetWalletPayedDetail(orderID int) ([]*response.WalletPay, error) {
	var out []*response.WalletPay
	err := global.GVA_DB.Table("order_goods as og").
		Joins("left join franchisee_account as a on a.id = og.account_id").
		Where("og.order_id = ? and og.account_payed > 0 and og.account_id > 0", orderID).
		Group("a.id,a.exclusive_account_name").
		Select("sum(og.account_payed) as total_payed,a.exclusive_account_name as wallet_name,a.exclusive_account_id as wallet_id").
		Find(&out).Error
	if err != nil {
		return nil, err
	}
	// 查询通户支付金额
	var generalAccountPayed *response.WalletPay
	err = global.GVA_DB.Table("order_goods").
		Joins("left join `orders` as o on o.id = order_goods.order_id").
		Where("order_goods.order_id = ?", orderID).
		Where("o.payment_type != ?", types.PaymentTypeOnlinePay).
		// Where("o.ayment_type != ?", types.ZeroPayOrder).
		Select("SUM(CASE WHEN points_quantity > 0 THEN points_quantity * points_price end) as  total_points_payed," +
			" SUM(CASE WHEN quantity > 0 THEN COALESCE(unit_price * quantity - account_payed, 0)  END ) AS  total_payed").
		Scan(&generalAccountPayed).Error
	if err != nil {
		return nil, err
	}
	generalAccountCashPayed := &response.WalletPay{
		TotalPayed: generalAccountPayed.TotalPayed,
		WalletName: "通用账户余额支付",
	}
	generalAccountPointsPayed := &response.WalletPay{
		TotalPointsPayed: generalAccountPayed.TotalPointsPayed,
		WalletName:       "通用账户积分支付",
	}
	out = append(out, generalAccountCashPayed, generalAccountPointsPayed)
	return out, nil
}

func (o *order) GetSpecialMallPerformanceBySpecialMallIDsAndFranchiseeID(specialMallIDs []int, franchiseeID int, generalSpecialMallID int, targetTime time.Time) ([]*productRes.SpecialMallPerformance, error) {
	start, end := utils.GetStartAndEndByTargetTime(targetTime)
	var outCharge []*productRes.SpecialMallPerformance
	err := global.GVA_DB.Table("order_goods as og").
		Joins("left join `"+orders.Order{}.TableName()+"` as o on o.id = og.order_id").
		Select("og.special_mall_id,sum(og.quantity*og.unit_price) as performance").
		Where("o.franchisee_id = ? and og.special_mall_id in (?) and og.quantity > 0 and og.unit_price>0", franchiseeID, specialMallIDs).
		Where("og.created_at between ? and ?", start, end).
		Where("o.order_type in (?,?)", types.Franchisee, types.InPlaceOfOrder).
		Where("o.order_sub_type in (?,?)", types.Ordinary, types.Presentation).
		Where("o.status in (?,?,?,?)", types.OrderAwaitingDelivery, types.OrderDelivery, types.OrderPartDelivery, types.OrderCompleted). // 2 3 为退款的. 订单只有全额退款.把退款的订单排除掉
		Group("og.special_mall_id").
		Find(&outCharge).Error
	if err != nil {
		return nil, err
	}

	// 查询通户支付金额
	if generalSpecialMallID > 0 {
		var generalSpecialMallPerformance productRes.SpecialMallPerformance
		err = global.GVA_DB.Table("order_goods as og").
			Joins("left join `"+orders.Order{}.TableName()+"` as o on o.id = og.order_id").
			Where("o.franchisee_id = ? and og.special_mall_id = 0 and og.quantity > 0", franchiseeID).
			Where("og.created_at between ? and ?", start, end).
			Where("o.order_type in (?,?)", types.Franchisee, types.InPlaceOfOrder).
			Where("o.order_sub_type in(0,1)").
			Where("o.status in (0,1,4,5)"). // 2 3 为退款的. 订单只有全额退款.把退款的订单排除掉
			Group("og.special_mall_id").
			Select("og.special_mall_id,sum(og.quantity*og.unit_price) as performance").
			Find(&generalSpecialMallPerformance).Error
		if err != nil {
			return nil, err
		}

		generalSpecialMallPerformance.SpecialMallID = generalSpecialMallID
		if generalSpecialMallPerformance.Performance > 0 {
			outCharge = append(outCharge, &generalSpecialMallPerformance)
		}
	}
	return outCharge, nil
}

func (o *order) GetSpecialMallPerformanceBySpecialMallIDsAndFranchiseeIDBetweenTimes(specialMallIDs []int, franchiseeID int, generalSpecialMallID int, start, end time.Time) ([]*productRes.SpecialMallPerformance, error) {
	//start, end := utils.GetStartAndEndByTargetTime(targetTime)
	var out []*productRes.SpecialMallPerformance
	err := global.GVA_DB.Table("order_goods as og").
		Joins("left join `"+orders.Order{}.TableName()+"` as o on o.id = og.order_id").
		Select("og.special_mall_id,sum(og.quantity*og.unit_price) as performance").
		Where("o.franchisee_id = ? and og.special_mall_id in (?) and og.quantity > 0 and og.unit_price>0", franchiseeID, specialMallIDs).
		Where("og.created_at between ? and ?", start, end).
		Where("o.order_type in (?,?)", types.Franchisee, types.InPlaceOfOrder).
		Where("o.order_sub_type in(0,1)").
		Where("o.status in (0,1,4,5)"). // 2 3 为退款的. 订单只有全额退款.把退款的订单排除掉
		Group("og.special_mall_id").
		Find(&out).Error
	if err != nil {
		return nil, err
	}

	// 查询通户支付金额
	if generalSpecialMallID > 0 {
		var generalSpecialMallPerformance productRes.SpecialMallPerformance
		err = global.GVA_DB.Table("order_goods as og").
			Joins("left join `"+orders.Order{}.TableName()+"` as o on o.id = og.order_id").
			Where("o.franchisee_id = ? and og.special_mall_id = 0 and og.quantity > 0", franchiseeID).
			Where("og.created_at between ? and ?", start, end).
			Where("o.order_type in (?,?)", types.Franchisee, types.InPlaceOfOrder).
			Where("o.order_sub_type in(0,1)").
			Where("o.status in (0,1,4,5)"). // 2 3 为退款的. 订单只有全额退款.把退款的订单排除掉
			Group("og.special_mall_id").
			Select("og.special_mall_id,sum(og.quantity*og.unit_price) as performance").
			Find(&generalSpecialMallPerformance).Error
		if err != nil {
			return nil, err
		}

		generalSpecialMallPerformance.SpecialMallID = generalSpecialMallID
		if generalSpecialMallPerformance.Performance > 0 {
			out = append(out, &generalSpecialMallPerformance)
		}
	}
	return out, nil
}

func (o *order) GetOrderGoodsByOrderNo(orderNo string) ([]*orders.OrderGoods, error) {
	var og []*orders.OrderGoods
	err := global.GVA_DB.Model(&orders.OrderGoods{}).
		Joins("left join `"+orders.Order{}.TableName()+"` on orders.id = order_goods.order_id").
		Where("orders.order_no = ?", orderNo).Find(&og).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("service.GetOrderGoodsByOrderNo err:", zap.Error(err), zap.Any("orderNo", orderNo))
	}
	return og, err

}

// SimpleGetOrderByOrderNo 根据订单号获取订单
func (o *order) SimpleGetOrderByOrderNo(orderNo string) (order *orders.Order, err error) {
	var res orders.Order
	err = global.GVA_DB.Where("order_no = ?", orderNo).First(&res).Error
	return &res, err
}

// SimpleGetOrders 获取多条 orders  记录
func (o *order) SimpleGetOrders(orderIds []uint) ([]*orders.Order, error) {
	// use SimpleGetOrdersWithinTx
	return o.SimpleGetOrdersWithinTx(global.GVA_DB, orderIds)
}

// SimpleGetOrdersWithinTx 在事务中获取多条 orders  记录
func (o *order) SimpleGetOrdersWithinTx(tx *gorm.DB, orderIds []uint) ([]*orders.Order, error) {
	var oList []*orders.Order
	err := tx.Model(&orders.Order{}).Where("id in ?", orderIds).Find(&oList).Error
	if err != nil {
		return nil, err
	}
	if len(oList) == 0 {
		return nil, nil
	}
	return oList, nil
}
