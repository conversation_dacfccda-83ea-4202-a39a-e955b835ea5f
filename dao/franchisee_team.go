package dao

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesRes "github.com/OSQianXing/guanpu-server/model/franchisees/response"
	"github.com/OSQianXing/guanpu-server/types"
)

var FranchiseeTeam = new(franchiseeTeam)

type franchiseeTeam struct {
}

// GetAllFranchiseeIDs 获取所有加盟商ID
func (franchiseeTeam *franchiseeTeam) GetAllFranchiseeIDs(userID uint) ([]int, error) {
	var franchiseeIDs []int
	err := global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).
		Where("user_id = ?", userID).
		Pluck("franchisee_id", &franchiseeIDs).Error
	return franchiseeIDs, err
}

func (franchiseeTeam *franchiseeTeam) GetFranchiseeMarketLead(franchiseeID uint) (*franchiseesRes.FranchiseeTeamMarketInfo, error) {
	var marketLead franchiseesRes.FranchiseeTeamMarketInfo
	err := global.GVA_DB.Preload("MarketLead").Model(&franchisees.FranchiseeTeam{}).Where("franchisee_id = ?", franchiseeID).Where("type = ?", types.MarketLead).First(&marketLead).Error
	if err != nil {
		return nil, err
	}
	return &marketLead, err
}

func (franchiseeTeam *franchiseeTeam) GetFranchiseeSupervisionLead(franchiseeID uint) (*franchiseesRes.FranchiseeTeamSupervisionInfo, error) {
	var supervisionLead franchiseesRes.FranchiseeTeamSupervisionInfo
	err := global.GVA_DB.Preload("SupervisionLead").Model(&franchisees.FranchiseeTeam{}).Where("franchisee_id = ?", franchiseeID).Where("type = ?", types.SupervisionLead).First(&supervisionLead).Error
	if err != nil {
		return nil, err
	}
	return &supervisionLead, err
}
