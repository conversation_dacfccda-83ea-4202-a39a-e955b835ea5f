package dao

import (
	"errors"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/warehouse"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type cloudWarehouse struct {
}

var CloudWarehouse = new(cloudWarehouse)

func (c *cloudWarehouse) GetSubCloudWarehouseListByOrderNo(orderNo string) ([]*warehouse.SubCloudWarehouse, error) {
	var subCloudWarehouseList []*warehouse.SubCloudWarehouse
	err := global.GVA_DB.Table((&warehouse.SubCloudWarehouse{}).TableName()+" as scw").
		Joins("left join "+(&warehouse.CloudWarehouse{}).TableName()+" as cw on cw.id = scw.cloud_warehouse_id").
		Where("cw.order_no = ?", orderNo).
		Find(&subCloudWarehouseList).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("service.GetSubCloudWarehouseListByOrderNo err:", zap.Error(err), zap.Any("orderNo", orderNo))
	}
	return subCloudWarehouseList, err
}

// ISCloudWarehouseWhole 查看云仓订单是否完整
func (c *cloudWarehouse) ISCloudWarehouseOrderWhole(orderNo string) bool {
	subList, err := c.GetSubCloudWarehouseListByOrderNo(orderNo)
	if err != nil {
		return false
	}

	og, err := Order.GetOrderGoodsByOrderNo(orderNo)
	if err != nil {
		return false
	}
	if len(subList) != len(og) || len(og) == 0 {
		return false
	}

	for _, v := range subList {
		_, re := lo.Find[*orders.OrderGoods](og, func(item *orders.OrderGoods) bool {
			return (item.ProductId == v.ProductId && item.Quantity == v.Inventory) || item.ProductId == v.ProductId && (item.Quantity+item.PointsQuantity) == v.Inventory
		})
		if !re {
			global.GVA_LOG.Debug("service.ISCloudWarehouseOrderWhole err:", zap.Any("v", v), zap.Any("orderNo", orderNo))
			return false
		}
	}
	return true
}
