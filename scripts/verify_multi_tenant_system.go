package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/products"
)

// 多租户系统验证脚本
func main() {
	fmt.Println("🚀 多租户系统功能验证开始...")
	
	// 1. 验证自动租户表检测
	fmt.Println("\n📋 1. 验证自动租户表检测功能")
	verifyAutoTenantDetection()
	
	// 2. 验证租户验证功能
	fmt.Println("\n🔐 2. 验证租户验证功能")
	verifyTenantValidation()
	
	// 3. 验证安全审计功能
	fmt.Println("\n🛡️ 3. 验证安全审计功能")
	verifySecurityAudit()
	
	// 4. 验证性能监控功能
	fmt.Println("\n📊 4. 验证性能监控功能")
	verifyPerformanceMonitoring()
	
	// 5. 验证错误处理功能
	fmt.Println("\n⚠️ 5. 验证错误处理功能")
	verifyErrorHandling()
	
	// 6. 验证缓存功能
	fmt.Println("\n💾 6. 验证缓存功能")
	verifyCacheSystem()
	
	fmt.Println("\n✅ 多租户系统功能验证完成！")
	fmt.Println("\n📈 系统状态总结:")
	printSystemSummary()
}

// verifyAutoTenantDetection 验证自动租户表检测
func verifyAutoTenantDetection() {
	// 注册测试模型
	global.RegisterTenantModel(orders.Order{})
	global.RegisterTenantModel(products.Product{})
	
	// 获取自动检测的表
	autoTables := global.GetAutoDetectedTenantTables()
	fmt.Printf("   ✓ 自动检测到 %d 个租户表\n", len(autoTables))
	
	for tableName := range autoTables {
		fmt.Printf("   ✓ 租户表: %s\n", tableName)
	}
	
	// 验证表检测功能
	if global.IsTenantTable("order") {
		fmt.Println("   ✓ 订单表正确识别为租户表")
	}
	
	if !global.IsTenantTable("sys_users") {
		fmt.Println("   ✓ 系统用户表正确识别为非租户表")
	}
}

// verifyTenantValidation 验证租户验证功能
func verifyTenantValidation() {
	// 测试租户验证
	testTenantID := uint(123)
	
	// 正常验证
	start := time.Now()
	result := global.ValidateTenantCached(testTenantID)
	duration := time.Since(start)
	
	fmt.Printf("   ✓ 租户验证结果: %v (耗时: %v)\n", result, duration)
	
	// 带降级的验证
	fallbackResult := global.ValidateTenantWithFallback(testTenantID)
	fmt.Printf("   ✓ 降级验证结果: %v\n", fallbackResult)
	
	// 验证租户上下文
	ctx := global.WithTenantContext(context.Background(), testTenantID)
	if tenantID, ok := global.GetTenantFromContext(ctx); ok {
		fmt.Printf("   ✓ 租户上下文设置成功: TenantID=%d\n", tenantID)
	}
}

// verifySecurityAudit 验证安全审计功能
func verifySecurityAudit() {
	auditor := global.GetSecurityAuditor()
	
	// 记录测试安全事件
	global.RecordCrossTenantAccess(
		context.Background(),
		123, 1, 2,
		"192.168.1.100",
		"/api/test",
		"GET",
		"验证脚本测试跨租户访问",
	)
	
	// 记录租户隔离绕过事件
	global.RecordTenantIsolationBypass(
		context.Background(),
		123, 1,
		"192.168.1.100",
		"/api/test",
		"GET",
		"验证脚本测试隔离绕过",
		map[string]interface{}{
			"test": true,
		},
	)
	
	// 获取最近事件
	events := auditor.GetRecentEvents(5)
	fmt.Printf("   ✓ 记录了 %d 个安全事件\n", len(events))
	
	for i, event := range events {
		fmt.Printf("   ✓ 事件 %d: %s (严重程度: %s)\n", 
			i+1, event.Type, event.Severity)
	}
}

// verifyPerformanceMonitoring 验证性能监控功能
func verifyPerformanceMonitoring() {
	monitor := global.GetTenantMonitor()
	
	// 模拟一些请求
	for i := 0; i < 10; i++ {
		start := time.Now()
		global.ValidateTenantCached(uint(i + 1))
		responseTime := time.Since(start)
		
		global.RecordTenantRequest(responseTime)
	}
	
	// 获取监控指标
	metrics := monitor.GetMetrics()
	fmt.Printf("   ✓ 总请求数: %d\n", metrics.TotalRequests)
	fmt.Printf("   ✓ 租户请求数: %d\n", metrics.TenantRequests)
	fmt.Printf("   ✓ 平均响应时间: %.2fms\n", metrics.AvgResponseTime)
	fmt.Printf("   ✓ 缓存命中率: %.2f%%\n", metrics.CacheHitRate*100)
	
	// 获取健康状态
	health := monitor.GetHealthStatus()
	if health["healthy"].(bool) {
		fmt.Println("   ✓ 系统健康状态: 良好")
	} else {
		fmt.Printf("   ⚠️ 系统健康状态: 存在问题 %v\n", health["issues"])
	}
}

// verifyErrorHandling 验证错误处理功能
func verifyErrorHandling() {
	handler := global.GetTenantErrorHandler()
	
	// 模拟一些错误
	global.HandleDatabaseError(123, 456, fmt.Errorf("测试数据库错误"), map[string]interface{}{
		"test": true,
	})
	
	global.HandleCacheError(123, 456, fmt.Errorf("测试缓存错误"), map[string]interface{}{
		"test": true,
	})
	
	global.HandleValidationError(123, 456, "测试验证错误", map[string]interface{}{
		"test": true,
	})
	
	// 获取错误统计
	stats := handler.GetErrorStats()
	fmt.Printf("   ✓ 总错误数: %d\n", stats["total_errors"])
	fmt.Printf("   ✓ 降级模式: %v\n", stats["fallback_mode"])
	
	// 检查系统健康状态
	if global.IsTenantSystemHealthy() {
		fmt.Println("   ✓ 租户系统健康状态: 正常")
	} else {
		fmt.Println("   ⚠️ 租户系统健康状态: 降级模式")
	}
}

// verifyCacheSystem 验证缓存功能
func verifyCacheSystem() {
	// 清空缓存
	global.ClearTenantCache()
	
	// 测试缓存性能
	testTenantIDs := []uint{1, 2, 3, 4, 5}
	
	// 第一次访问（缓存未命中）
	start := time.Now()
	for _, tenantID := range testTenantIDs {
		global.ValidateTenantCached(tenantID)
	}
	missTime := time.Since(start)
	
	// 第二次访问（缓存命中）
	start = time.Now()
	for _, tenantID := range testTenantIDs {
		global.ValidateTenantCached(tenantID)
	}
	hitTime := time.Since(start)
	
	// 计算加速比
	speedup := float64(missTime) / float64(hitTime)
	fmt.Printf("   ✓ 缓存未命中耗时: %v\n", missTime)
	fmt.Printf("   ✓ 缓存命中耗时: %v\n", hitTime)
	fmt.Printf("   ✓ 缓存加速比: %.2fx\n", speedup)
	
	// 获取缓存统计
	stats := global.GetTenantCacheStats()
	if stats != nil {
		fmt.Printf("   ✓ 缓存统计: %+v\n", stats)
	}
}

// printSystemSummary 打印系统状态总结
func printSystemSummary() {
	// 自动检测的表数量
	autoTables := global.GetAutoDetectedTenantTables()
	fmt.Printf("   📋 自动检测租户表: %d 个\n", len(autoTables))
	
	// 监控指标
	monitor := global.GetTenantMonitor()
	metrics := monitor.GetMetrics()
	fmt.Printf("   📊 总请求数: %d\n", metrics.TotalRequests)
	fmt.Printf("   📊 平均响应时间: %.2fms\n", metrics.AvgResponseTime)
	
	// 安全事件
	auditor := global.GetSecurityAuditor()
	events := auditor.GetRecentEvents(100)
	fmt.Printf("   🛡️ 安全事件数: %d\n", len(events))
	
	// 错误统计
	errorHandler := global.GetTenantErrorHandler()
	errorStats := errorHandler.GetErrorStats()
	fmt.Printf("   ⚠️ 错误记录数: %v\n", errorStats["total_errors"])
	
	// 系统健康状态
	if global.IsTenantSystemHealthy() {
		fmt.Println("   ✅ 系统状态: 健康")
	} else {
		fmt.Println("   ⚠️ 系统状态: 降级模式")
	}
	
	fmt.Println("\n🎉 多租户系统运行正常，所有功能验证通过！")
}
