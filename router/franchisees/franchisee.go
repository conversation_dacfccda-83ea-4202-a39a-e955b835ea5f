package franchisees

import (
	v1 "github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type FranchiseeRouter struct {
}

// InitFranchiseeRouter 初始化 Franchisee 路由信息
func (s *FranchiseeRouter) InitFranchiseeRouter(Router *gin.RouterGroup) {
	franchiseeRouter := Router.Group("franchisee").Use(middleware.OperationRecord()).Use(middleware.TenantAccessMiddleware())
	franchiseeRouterWithoutRecord := Router.Group("franchisee").Use(middleware.TenantAccessMiddleware())
	var franchiseeApi = v1.ApiGroupApp.FranchiseesApiGroup.FranchiseeApi
	{
		franchiseeRouter.POST("createFranchisee", franchiseeApi.CreateFranchisee)               // 新建Franchisee
		franchiseeRouter.DELETE("deleteFranchisee", franchiseeApi.DeleteFranchisee)             // 删除Franchisee
		franchiseeRouter.DELETE("deleteFranchiseeByIds", franchiseeApi.DeleteFranchiseeByIds)   // 批量删除Franchisee
		franchiseeRouter.PUT("updateFranchisee", franchiseeApi.UpdateFranchisee)                // 更新Franchisee
		franchiseeRouter.POST("baned", franchiseeApi.Baned)                                     // 禁用Franchisee
		franchiseeRouter.POST("unbaned", franchiseeApi.Unbaned)                                 // 解禁Franchisee
		franchiseeRouter.GET("exportFranchisee", franchiseeApi.ExportFranchisee)                // 导出Franchisee
		franchiseeRouter.POST("approveFranchisee", franchiseeApi.ApproveFranchisee)             // 审核FranchiseeStandby
		franchiseeRouter.POST("resetFranchiseePassword", franchiseeApi.ResetFranchiseePassword) // 重置加盟商密码
	}
	{
		franchiseeRouterWithoutRecord.GET("findFranchisee", franchiseeApi.FindFranchisee)                          // 根据ID获取Franchisee
		franchiseeRouterWithoutRecord.GET("getFranchiseeList", franchiseeApi.GetFranchiseeList)                    // 获取Franchisee列表
		franchiseeRouterWithoutRecord.GET("getFranchiseeBaseList", franchiseeApi.GetFranchiseeBaseList)            // 获取Franchisee基础信息列表
		franchiseeRouterWithoutRecord.GET("/franchiseeInfo/:franchiseeId", franchiseeApi.GetFranchiseeInfo)        // 获取Franchisee简介信息,包括今日充值 消费统计
		franchiseeRouterWithoutRecord.GET("topFranchisee", franchiseeApi.GetTopFranchisee)                         // 获取一级分销加盟商
		franchiseeRouterWithoutRecord.GET("topFranchiseeWithCustomer", franchiseeApi.GetTopFranchiseeWithCustomer) // 获取一级分销加盟商
		franchiseeRouterWithoutRecord.GET("getFranchiseeStatistics", franchiseeApi.GetFranchiseeStatistics)
		franchiseeRouterWithoutRecord.GET("franchiseeStatistics", franchiseeApi.GetFranchiseeStatistics)         // Franchisee统计
		franchiseeRouterWithoutRecord.GET("directCustomers", franchiseeApi.GetDirectCustomers)                   // 获取直属客户
		franchiseeRouterWithoutRecord.GET("upTopFranchisee", franchiseeApi.GetUpTopFranchisee)                   // 获取上游中的一级分销加盟商
		franchiseeRouterWithoutRecord.GET("franchiseeAccountSummary", franchiseeApi.GetFranchiseeAccountSummary) // 获取加盟商账户摘要
	}
}
