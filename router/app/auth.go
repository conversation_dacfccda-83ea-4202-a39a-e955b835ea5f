package app

import (
	v1 "github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type AuthRouter struct{}

// InitAuthRouter 初始化加盟商认证路由
func (s *AuthRouter) InitAuthRouter(Router *gin.RouterGroup) {
	authApi := v1.ApiGroupApp.AppApiGroup.AuthApi

	// 不需要认证的路由
	appAuthRouter := Router.Group("auth")
	{
		appAuthRouter.POST("login", authApi.FranchiseeLogin)           // 加盟商登录
		appAuthRouter.POST("confirm-tenant", authApi.ConfirmTenantLogin) // 确认租户登录
	}

	// 需要JWT认证的路由
	appAuthRouterWithAuth := Router.Group("auth").Use(middleware.JWTAuth())
	{
		appAuthRouterWithAuth.POST("switch-tenant", authApi.SwitchTenant) // 切换租户
		appAuthRouterWithAuth.GET("my-tenants", authApi.GetMyTenants)     // 获取我的租户列表
	}
}
