package system

import (
	"github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type UnifiedUserRouter struct{}

// InitUnifiedUserRouter 初始化统一用户管理路由
func (s *UnifiedUserRouter) InitUnifiedUserRouter(Router *gin.RouterGroup) {
	unifiedUserRouter := Router.Group("users").Use(middleware.OperationRecord())
	unifiedUserRouterWithoutRecord := Router.Group("users")
	unifiedUserApi := v1.ApiGroupApp.SystemApiGroup.UnifiedUserApi
	{
		// 需要租户隔离的接口
		unifiedUserRouter.Use(middleware.TenantIsolationMiddleware())
		unifiedUserRouter.POST("list", unifiedUserApi.GetUserList)       // 获取用户列表
		unifiedUserRouter.POST("create", unifiedUserApi.CreateUser)      // 创建用户
		unifiedUserRouter.PUT(":id", unifiedUserApi.UpdateUser)          // 更新用户
		unifiedUserRouter.DELETE(":id", unifiedUserApi.DeleteUser)       // 删除用户
	}
	{
		// 不需要操作记录的接口
		unifiedUserRouterWithoutRecord.Use(middleware.TenantIsolationMiddleware())
		// 这里可以添加不需要操作记录的接口
	}
}