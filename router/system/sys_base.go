package system

import (
	v1 "github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type BaseRouter struct{}

func (s *BaseRouter) InitBaseRouter(Router *gin.RouterGroup) (R gin.IRoutes) {
	baseRouter := Router.Group("base")
	baseApi := v1.ApiGroupApp.SystemApiGroup.BaseApi
	{
		baseRouter.POST("login", baseApi.Login)
		baseRouter.POST("franchiseeLogin", baseApi.FranchiseeLogin)
		baseRouter.POST("captcha", baseApi.Captcha)
	}
	baseRouter.Use(middleware.JWTAuth()).POST("refreshToken", baseApi.RefreshToken)
	return baseRouter
}
