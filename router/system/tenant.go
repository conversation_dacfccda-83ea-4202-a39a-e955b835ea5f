package system

import (
	"github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type TenantRouter struct{}

// InitTenantRouter 初始化租户路由信息
func (s *TenantRouter) InitTenantRouter(Router *gin.RouterGroup) {
	tenantRouter := Router.Group("tenant").Use(middleware.OperationRecord())
	tenantRouterWithoutRecord := Router.Group("tenant")
	tenantApi := v1.ApiGroupApp.SystemApiGroup.TenantApi
	{
		tenantRouter.POST("createTenant", tenantApi.CreateTenant)   // 新建租户
		tenantRouter.DELETE("deleteTenant", tenantApi.DeleteTenant) // 删除租户
		tenantRouter.PUT("updateTenant", tenantApi.UpdateTenant)    // 更新租户
		tenantRouter.POST("addUserToTenant", tenantApi.AddUserToTenant) // 添加用户到租户
		tenantRouter.DELETE("removeUserFromTenant", tenantApi.RemoveUserFromTenant) // 从租户中移除用户
		tenantRouter.PUT("setDefaultTenant", tenantApi.SetDefaultTenant) // 设置默认租户
	}
	{
		tenantRouterWithoutRecord.GET("findTenant", tenantApi.FindTenant)       // 根据ID获取租户
		tenantRouterWithoutRecord.GET("getTenantList", tenantApi.GetTenantList) // 获取租户列表
		tenantRouterWithoutRecord.GET("getUserTenants", tenantApi.GetUserTenants) // 获取用户关联的所有租户
		tenantRouterWithoutRecord.POST("switchTenant/:tenantId", tenantApi.SwitchTenant) // 切换租户
	}
}