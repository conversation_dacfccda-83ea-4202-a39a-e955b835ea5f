package system

import (
	"github.com/gin-gonic/gin"
	apis "github.com/OSQianXing/guanpu-server/api/v1/system"
	"github.com/OSQianXing/guanpu-server/middleware"
)

type SuperAdminRouter struct{}

func (s *SuperAdminRouter) InitSuperAdminRouter(Router *gin.RouterGroup) {
	api := &apis.SuperAdminApi{}
	superAdminRouter := Router.Group("super").Use(middleware.SuperAdminAuth())
	{
		superAdminRouter.GET("/tenants", api.GetAllTenants)
		superAdminRouter.GET("/users", api.GetAllUsers)
		superAdminRouter.POST("/tenants/disable", api.DisableTenant)
		superAdminRouter.POST("/tenants/enable", api.EnableTenant)
		superAdminRouter.POST("/users/promote", api.PromoteUser)
		superAdminRouter.POST("/users/demote", api.DemoteUser)
		superAdminRouter.GET("/system/stats", api.GetSystemStats)
		superAdminRouter.GET("/data/:dataType", api.GetCrossTenantsData)
		
		// 操作日志路由
		superAdminRouter.GET("/logs/operations", api.GetOperationLogs)
		superAdminRouter.GET("/logs/stats", api.GetOperationLogStats)
	}
}