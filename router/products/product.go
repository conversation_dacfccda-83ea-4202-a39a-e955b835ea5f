package products

import (
	"github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type ProductRouter struct {
}

// InitProductRouter 初始化 Product 路由信息
func (s *ProductRouter) InitProductRouter(Router *gin.RouterGroup) {
	productRouter := Router.Group("product").Use(middleware.OperationRecord()).Use(middleware.TenantIsolationMiddleware())
	productRouterWithoutRecord := Router.Group("product").Use(middleware.TenantIsolationMiddleware())
	var productApi = v1.ApiGroupApp.ProductsApiGroup.ProductApi
	{
		productRouter.POST("createProduct", productApi.CreateProduct)                  // 新建Product
		productRouter.DELETE("deleteProduct", productApi.DeleteProduct)                // 删除Product
		productRouter.DELETE("deleteProductByIds", productApi.DeleteProductByIds)      // 批量删除Product
		productRouter.PUT("updateProduct", productApi.UpdateProduct)                   // 更新Product
		productRouter.PUT("updateProductInventory", productApi.UpdateProductInventory) // 更新Product库存
		productRouter.POST("onSale", productApi.OnSale)                                // 操作上下架商品
		productRouter.GET("exportProductList", productApi.ExportProductList)           // 导出Product列表
	}
	{
		productRouterWithoutRecord.GET("findProduct", productApi.FindProduct)                                 // 根据ID获取Product
		productRouterWithoutRecord.GET("getProductList", productApi.GetProductList)                           // 获取Product列表
		productRouterWithoutRecord.GET("checkProductCouldBeModified", productApi.CheckProductCouldBeModified) // 检查商品是否可以修改
	}
}
