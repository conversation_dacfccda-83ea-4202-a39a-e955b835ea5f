package orders

import (
	"github.com/OSQianXing/guanpu-server/api/v1"
	"github.com/OSQianXing/guanpu-server/middleware"
	"github.com/gin-gonic/gin"
)

type OrderRouter struct {
}

// InitOrderRouter 初始化 Order 路由信息
func (s *OrderRouter) InitOrderRouter(Router *gin.RouterGroup) {
	orderRouter := Router.Group("order").Use(middleware.OperationRecord()).Use(middleware.TenantIsolationMiddleware())
	orderRouterWithoutRecord := Router.Group("order").Use(middleware.TenantIsolationMiddleware())
	var orderApi = v1.ApiGroupApp.OrdersApiGroup.OrderApi
	{
		orderRouter.POST("createOrder", orderApi.CreateOrder)                  // 新建Order
		orderRouter.PUT("updateOrder", orderApi.UpdateOrder)                   // 更新Order
		orderRouter.GET("exportOrderList", orderApi.ExportOrderList)           //  导出Order
		orderRouter.PUT("updateOrderWarehouse", orderApi.UpdateOrderWarehouse) // 更新订单所属仓库
	}
	{
		orderRouterWithoutRecord.GET("findOrder", orderApi.FindOrder)       // 根据ID获取Order
		orderRouterWithoutRecord.GET("getOrderList", orderApi.GetOrderList) // 获取Order列表
	}
}
