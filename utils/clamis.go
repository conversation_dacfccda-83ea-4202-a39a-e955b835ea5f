package utils

import (
	"bytes"
	"io"
	"strconv"

	"github.com/OSQianXing/guanpu-server/global"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/gin-gonic/gin"
	"github.com/gofrs/uuid"
)

func GetClaims(c *gin.Context) (*systemReq.CustomClaims, error) {
	token := c.Request.Header.Get("x-token")
	j := NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		global.GVA_LOG.Error("从Gin的Context中获取从jwt解析信息失败, 请检查请求头是否存在x-token且claims是否为规定结构")
	}
	return claims, err
}

// GetUserID 从Gin的Context中获取从jwt解析出来的用户ID
func GetUserID(c *gin.Context) uint {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return 0
		} else {
			return cl.BaseClaims.ID
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.BaseClaims.ID
	}
}

// GetUserUuid 从Gin的Context中获取从jwt解析出来的用户UUID
func GetUserUuid(c *gin.Context) uuid.UUID {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return uuid.UUID{}
		} else {
			return cl.UUID
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.UUID
	}
}

// GetUserAuthorityId 从Gin的Context中获取从jwt解析出来的用户角色id
func GetUserAuthorityId(c *gin.Context) uint {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return 0
		} else {
			return cl.AuthorityId
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.AuthorityId
	}
}

// GetUserInfo 从Gin的Context中获取从jwt解析出来的用户角色id
func GetUserInfo(c *gin.Context) *systemReq.CustomClaims {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return nil
		} else {
			return cl
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse
	}
}

// GetUserName 从Gin的Context中获取从jwt解析出来的用户名
func GetUserName(c *gin.Context) string {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return ""
		} else {
			return cl.Username
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.Username
	}
}

func GetUserIP(c *gin.Context) string {
	return c.ClientIP()
}

func GetUserAgent(c *gin.Context) string {
	return c.Request.UserAgent()
}

// GetTenantID 从Gin的Context中获取从jwt解析出来的租户ID
func GetTenantID(c *gin.Context) uint {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return 0
		} else {
			return cl.TenantID
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.TenantID
	}
}

// GetFranchiseeID 从Gin的Context中获取从jwt解析出来的加盟商ID
func GetFranchiseeID(c *gin.Context) uint {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return 0
		} else {
			return cl.FranchiseeID
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.FranchiseeID
	}
}

// GetTenantCode 从Gin的Context中获取从jwt解析出来的租户编码
func GetTenantCode(c *gin.Context) string {
	if claims, exists := c.Get("claims"); !exists {
		if cl, err := GetClaims(c); err != nil {
			return ""
		} else {
			return cl.TenantCode
		}
	} else {
		waitUse := claims.(*systemReq.CustomClaims)
		return waitUse.TenantCode
	}
}

// StringToUint 字符串转换为uint
func StringToUint(s string) (uint, error) {
	if s == "" {
		return 0, nil
	}

	var result uint64
	var err error

	// 尝试解析为uint64
	result, err = strconv.ParseUint(s, 10, 32)
	if err != nil {
		return 0, err
	}

	return uint(result), nil
}

// GetSuperAdminClaims 获取超级管理员Claims
func GetSuperAdminClaims(c *gin.Context) (*systemReq.CustomClaims, error) {
	return GetClaims(c)
}

// NewReadCloser 创建一个新的ReadCloser
func NewReadCloser(data []byte) io.ReadCloser {
	return io.NopCloser(bytes.NewReader(data))
}
