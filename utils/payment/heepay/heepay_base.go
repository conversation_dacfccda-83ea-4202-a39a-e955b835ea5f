package heepay

import (
	"bytes"
	"context"
	"crypto"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/go-resty/resty/v2"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"golang.org/x/text/encoding/simplifiedchinese"
)

const (
	HeepayOpenApiUrl  = "https://openapi.heepay.com"
	HeepayPayApiUrl   = "https://pay.heepay.com"
	HeepayQueryApiUrl = "https://query.heepay.com"
)
const (
	HeepayPayApiPathWithHeepayMiniProgram = "/Phone/SDK/PayInit.aspx"
	HeepayRefundApiPath                   = "/API/Payment/PaymentRefund.aspx"
	HeepayPaymentQueryApiPath             = "/Payment/Query.aspx"
	HeepayAllotApi                        = "/API/Allot/tradesubmit.aspx"
	HeepayAllotQueryApi                   = "/API/Allot/tradequery.aspx"
)

const (
	HeepayApiVersion               = "1"
	HeepayWechatMiniProgramPayType = "30"
	HeepayAliPayMiniProgramPayType = "22"
	HeepayAgentId                  = "2225143"
	HeepayGuanpuRefAgentId         = "2236099"
)

const (
	HeepayMetaOptionS                  = "微信小程序"
	HeepayMetaOptionN                  = "观朴酒道馆"
	HeepayMetaOptionId                 = "https://www.guanpujiu.com.cn"
	HeepayMetaOptionIsMinipg           = "1"
	HeepayMetaOptionWxMiniprogramAppid = "" // 小程序appid
	HeepayMetaOptionWxSubAppid         = "" // 小程序appid
)
const (
	HeepaySignType = "MD5"
)

const (
	HeepaySignKeyForPay     = "9BC393AB308449E7AB64CDEC" // for create order pay
	HeepaySignKeyForControl = "984B6465EFCF49968B62D52F" // for refund,gateway etc.
	Heepay3DESKey           = "DB5E16084F934831860EC7E0" // for 3des

)

var HeepayGuanpuRsaPubKey = []byte(`
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtxh9fJI/mNMaOrwoOEWCQ2CdTCSoNkUzBpYcTGjIDpHjCTv7yvz7MAURX65I1W2ieuFMFqZv+/abDXJJN7tio09hBR/5hSLo6tREsTJ4fBQ+S4vMOcdn2Vozzf3NeOkcHvSCBPKG6U5LrBdbOBTp96pVqmOO8VtLfMaiKLsEXBXMKNp+nVrLfQH1cT9y26eV8Uc+bFzenWT4yLRmj0qv9DNaatyOdtp2ClKguAaT6fKUCmjSEIWsuf8R1uCoAQRVKC1glRhjVeefw1EN/LmcD8YHSocmSh1TJr2658PMB0hNAznJpVt62+nZtBGzvTa5/A7JPc+aT87XiwxS3K3qqwIDAQAB
-----END PUBLIC KEY-----`)

var HeepayPublicKey = []byte(`
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzPwuqCWmEOVntUTCQJu0nrMgMtCQtadUZKJm6Re3h4fShGD9cyRxfp9oUgIUSB7m5p8apgW3b7wlKYp7PMLYIAbCDagjimbs+Pb1zm8ES5JNgco5g0NoCri3U2IlKxk2wU6pYL2kSG8X/75Y77YkvHS3nZD3wgcbK2Mlpv2iDyg5QSnQzWGYMYx2SnCHucw0LZCMERKh5sMT8T+O3wrTTJWJk1BGVa72FGtfe4wEQpZvEUbriXAJbwyUDs5qbok3328bGD96WkN8OvHq9ETcsyba2MAEL8x+i75cUYIHBq79zba4zC7esWJoktpDQNy9q1IKK3P0osm1k1Kmgmb1LwIDAQAB
-----END PUBLIC KEY-----`)

var HeepayGuanpuRsaPrivateKey = []byte(`
-----BEGIN RSA PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3GH18kj+Y0xo6vCg4RYJDYJ1MJKg2RTMGlhxMaMgOkeMJO/vK/PswBRFfrkjVbaJ64UwWpm/79psNckk3u2KjT2EFH/mFIujq1ESxMnh8FD5Li8w5x2fZWjPN/c146Rwe9IIE8obpTkusF1s4FOn3qlWqY47xW0t8xqIouwRcFcwo2n6dWst9AfVxP3Lbp5XxRz5sXN6dZPjItGaPSq/0M1pq3I522nYKUqC4BpPp8pQKaNIQhay5/xHW4KgBBFUoLWCVGGNV55/DUQ38uZwPxgdKhyZKHVMmvbrnw8wHSE0DOcmlW3rb6dm0EbO9Nrn8Dsk9z5pPzteLDFLcreqrAgMBAAECggEAUuD1kIlc208XbOXV6N7h36DgouJm+CXrunwBdD7r69hypi/uQoywISTyH9by5hVZ+nh4KjqNr0aKGhSU+vLJ6/WXC6xVFMRgHUgXjgQ0EKpTJnwx5ieSpQrVL1XHCAew8HwqTJYBpqMH0u7BnkmtPKvwWg/12N/D9EeviHNbPljlj0STiksdhkibGYc8fJiW61tIeQKR7y5e6kmdWUzUWALScaspnVRYo4y9eU605kvYfrn4LFCxdC0qkYmpG1IsTGOM+EVvVOds8InFA6deDOerEuoDvY51rU5h4LNFObLPJwVDWvaqRG9myQdtamJGgg+jUtNtfW86Z/KVB8speQKBgQDZ3B6Fdxl9bvuRG0gJwrDRYyOL5CZCKTgs/zAfVwiYou56hKwWwxPHAOj3YoA8j+nEy9nQLieSY4tNKk6Mhp/svpODLHF0awnSC9i4zLzkcjiGBTQCv+40eAt5xvHuE1BofVSzi5ZjAjXXNLqlbzSyM9n9hII4tq40aShVHATkaQKBgQDXJlX9p7otIkges+nadyRTz4LImYGvNliOQLZPqQKnbHoB1KYaa9GkvzZrQcmUtkyqX71TzVje/TRQI4x4kukiOByiT5NPDDjDxXyo99cLDV9jiXaDrCGI8Ksxn98VdEkAzJoSGVIr4g82rlFIftP2Y/6J2beOeRe7oDf90YDj8wKBgQCn6PaBKVU1DufHY/52v15mU4GfHR27mmdYjuo5DWxrsBAbz8uO05WXqITlvj5V7xleezsKL7iCJETGGZhmyI/T+OJalFNxRdpQ0J6dd/cSH1KfQL1gbSsbt3YoyoPGWY58qjQxGaGz940lq9c7EGXN/xbBRXLUYUAM1wul5S2EGQKBgGsWXb6/eV91rMf7bO6cVqyQoNwP7Ai12CYV96h2yydiSugWXAbSm6qQshl4yUkH4+j5dlb74rw684rf5s8s8HVj0HzJdtzljGuFJKNFmPebVFtK1H59csOiZtNwa61WD/Ah5p4TlF7bSg9BfJvE5OPCqbMbds2nYDS3bqcbXDgZAoGABmxf1cy/NGitmbqLtTQOwCf/CoTjYH52gk61HJyPYfTcdu/g+H37Fhu2J4t0LU4Wlm+iVpRVfCuoxjdqFoMSjLup6EKO2rrAG5x+kCdP1s5NT8arzv34QxYwfoZe0pJRETV9Jk8AzWdVllRNmhI8dw7mMwlC/aUZae5g1Ac/FN4=
-----END RSA PRIVATE KEY-----
`)

var TestPrviateKye = []byte(`
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)

const (
	HeepayGoodsNum           = 1
	HeepayAllotBatchNoPrefix = "HYALLOT-"
)

const HeepayWechatMiniProgramPayResultSuccess = "1"

var (
	HeepayNotifyPath = "/pay/onlinePayCallbackHeepay"
	// HeepayReturnUrl = global.GVA_CONFIG.System.CallBackUrl + ""
	HeepayReturnUrl        = global.GVA_CONFIG.System.CallBackUrl + "/pay/onlinePayNotify"
	HeepayRefundNotifyPath = "/pay/heepayRefundOnlinePayCallback"
)

type Client struct {
	restyClient *resty.Client
}

func NewClient() *Client {
	return &Client{
		restyClient: resty.New(),
	}
}

func (c *Client) Get(path string, params map[string]string) (*resty.Response, error) {
	req := c.restyClient.R().SetQueryParams(params)

	return req.Get(path)
}
func MD5Sign(str string) string {
	data := md5.Sum([]byte(str))
	return fmt.Sprintf("%x", data)
}

// Todo: generate md5 signature
func MD5V(str string) string {
	hash := md5.Sum([]byte(str))
	return hex.EncodeToString(hash[:])
}

func SHA1Sign(str string) string {
	data := sha1.Sum([]byte(str))
	return fmt.Sprintf("%x", data)
}
func SHA256Sign(str string) string {
	data := sha256.Sum256([]byte(str))
	return fmt.Sprintf("%x", data)
}

func HeePayCrypt(str string) string {
	return MD5Sign(str)
}

// transfer utf8 to gbk
func Utf8ToGbk(str string) (string, error) {
	gbkEncorder := simplifiedchinese.GBK.NewEncoder()
	gbkStr, err := gbkEncorder.String(str)
	if err != nil {
		return "", err
	}
	return gbkStr, nil
}

func GbkToUtf8(str string) (string, error) {
	decoder := simplifiedchinese.GBK.NewDecoder()
	utf8Str, err := decoder.String(str)
	if err != nil {
		return "", err
	}
	return utf8Str, nil
}

func Base64Encode(str string) string {
	return base64.StdEncoding.EncodeToString([]byte(str))
}

func Base64Decode(str string) string {
	bytes, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return ""
	}
	return string(bytes)
}

func GenOrderTransNo(orderNo string) string {
	return orderNo + "_" + strconv.FormatInt(time.Now().UnixMicro(), 10)
}

func HeepayCallCheckSign(req *HeepayCallbackRequest) (bool, error) {
	if req == nil {
		return false, fmt.Errorf("req is nil")
	}
	if req.Sign == "" {
		return false, fmt.Errorf("sign is empty")
	}
	return true, nil
	checkSignStringBuilder := strings.Builder{}
	checkSignStringBuilder.WriteString("result")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.Result)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("agent_id")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.AgentId)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("jnet_bill_no")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.JnetBillNo)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("agent_bill_id")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.AgentBillId)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("pay_type")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.PayType)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("pay_amt")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.PayAmt)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("remark")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(req.Remark)
	checkSignStringBuilder.WriteString("&")
	checkSignStringBuilder.WriteString("key")
	checkSignStringBuilder.WriteString("=")
	checkSignStringBuilder.WriteString(HeepaySignKeyForPay)
	finalString, err := Utf8ToGbk(checkSignStringBuilder.String())
	if err != nil {
		return false, err
	}
	global.GVA_LOG.Info("utils.payment.heepay.checkSignStringBuilder", zap.String("finalString", finalString))
	if MD5Sign(finalString) != req.Sign {
		return false, fmt.Errorf("sign not match")
	}
	return true, nil
}

func HeepayRefundSign(req *HeepayRefundRequest) (*HeepayRefundRequest, error) {
	if req == nil {
		return nil, fmt.Errorf("req is nil")
	}
	builder := strings.Builder{}
	builder.WriteString("agent_bill_id")
	builder.WriteString("=")
	builder.WriteString(req.AgentBillId)
	builder.WriteString("&")
	builder.WriteString("agent_id")
	builder.WriteString("=")
	builder.WriteString(req.AgentId)
	builder.WriteString("&")
	builder.WriteString("key")
	builder.WriteString("=")
	builder.WriteString(strings.ToLower(HeepaySignKeyForControl))
	builder.WriteString("&")
	builder.WriteString("notify_url")
	builder.WriteString("=")
	builder.WriteString(req.NotifyUrl)
	finalString := builder.String()

	req.Sign = MD5Sign(finalString)
	return req, nil
}

func HeepayAllotCheckSign(sign string, plainText []byte, publicKey []byte) (bool, error) {
	if sign == "" {
		return false, fmt.Errorf("sign is empty")
	}
	if plainText == nil {
		return false, fmt.Errorf("plainText is empty")
	}
	if publicKey == nil {
		return false, fmt.Errorf("publicKey is empty")
	}
	hashedPlainText := sha1.Sum(plainText)
	fmt.Println("hashedPlainText: ", hashedPlainText)
	// reqsign, err := url.QueryUnescape(sign)
	reqsign, err := GbkToUtf8(sign)

	if err != nil {
		return false, errors.Join(err, errors.New("url.QueryUnescape error "+reqsign))
	}
	fmt.Println("reqsign: ", reqsign)
	reqsign, err = GbkToUtf8(reqsign)
	if err != nil {
		return false, errors.Join(err, errors.New("GbkToUtf8 error "+reqsign))
	}
	reqsignbyte, err := base64.StdEncoding.DecodeString(reqsign)
	if err != nil {
		return false, errors.Join(err, errors.New("base64.StdEncoding.DecodeString error "+reqsign))
	}
	block, _ := pem.Decode(publicKey)

	if block == nil {
		return false, fmt.Errorf("Heepay public key error")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return false, errors.Join(err, errors.New("ParsePKIXPublicKey error"))
	}
	if xx, ok := pub.(*rsa.PublicKey); !ok {
		return false, fmt.Errorf("public key error: %T", pub)
	} else {
		if rsa.VerifyPKCS1v15(xx, crypto.SHA1, hashedPlainText[:], reqsignbyte) == nil {
			return true, nil
		} else {
			return false, fmt.Errorf("rsa.VerifyPKCS1v15 error")
		}
	}
}

func RsaDecryptAndCheckSign(req *HeepayAllotTradeCallbakReq, privateKey []byte, publicKey []byte) ([]byte, error) {
	// data, err := GbkToUtf8(data)
	// fmt.Println("data :", data)
	// data, err := url.QueryUnescape(data)
	// if err != nil {
	// 	return nil, errors.Join(err, errors.New("url.QueryUnescape error "+data))
	// }

	block, _ := pem.Decode(privateKey)

	if block == nil {
		return nil, fmt.Errorf("private key error")
	}
	if block.Type != "RSA PRIVATE KEY" {
		return nil, fmt.Errorf("private key error: %s", block.Type)
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, errors.Join(err, errors.New("ParsePKCS8PrivateKey error"))
	}
	if priv == nil {
		return nil, fmt.Errorf("private key error")
	}
	// var x *rsa.PrivateKey
	if x, ok := priv.(*rsa.PrivateKey); !ok {
		return nil, fmt.Errorf("private key error: %T", priv)
	} else {
		// unEscapeData, err := url.QueryUnescape(req.EncryptData)
		// if err != nil {
		// 	return nil, errors.Join(err, errors.New("url.QueryUnescape error "+req.EncryptData))
		// }
		// unEscapeData := req.EncryptData
		// fmt.Println("unEscapeData: ", unEscapeData)
		unEscapeData, err := GbkToUtf8(req.EncryptData)
		if err != nil {
			return nil, errors.Join(err, errors.New("GbkToUtf8 error "+unEscapeData))
		}
		// bytes, err := base64.StdEncoding.DecodeString(unEscapeData) // url.QueryUnescape(req.EncryptData)
		// if err != nil {
		// 	return "", err
		// }
		dataByte, err := base64.StdEncoding.DecodeString(unEscapeData)
		if err != nil {
			return nil, err
		}
		fmt.Println("dataByte :", string(dataByte))
		// fmt.Println("priv", x.PublicKey)
		plainText, err := rsa.DecryptPKCS1v15(rand.Reader, x, dataByte)
		if err != nil {
			return nil, errors.Join(err, errors.New("DecryptPKCS1v15 error"))
		}
		fmt.Println("plainText: ", string(plainText))

		if ok, err := HeepayAllotCheckSign(req.Sign, plainText, publicKey); ok && err == nil {
			return plainText, nil
		} else {
			return nil, errors.New("rsa.VerifyPKCS1v15 error:" + err.Error())
		}

	}
}

func HeepayAllotCallBackDecrypt(req *HeepayAllotTradeCallbakReq) (string, error) {
	if req == nil {
		return "", fmt.Errorf("req is nil")
	}
	if req.EncryptData == "" {
		return "", fmt.Errorf("encrypt data is empty")
	}

	decryptData, err := RsaDecryptAndCheckSign(req, HeepayGuanpuRsaPrivateKey, HeepayPublicKey)
	if err != nil {
		return "", err
	}
	return string(decryptData), nil
}

func TransformToHeepayAllotTradeDa(t string) (HeepayAllotTradeData, error) {
	// agent_id=2113688&allot_amt=173.9900&hy_bill_no=Z201029051811810&real_amt=200.00&ref_agent_id=2113688&trade_amt=200.00&trade_time=20201029153626&version=1

	if t == "" {
		return HeepayAllotTradeData{}, fmt.Errorf("t is empty")
	}

	strs := strings.Split(t, "&")
	req := HeepayAllotTradeData{}
	req.PlainData = t
	for _, v := range strs {
		s := strings.Split(v, "=")
		if len(s) == 2 {
			switch s[0] {
			case "agent_id":
				req.AgentId = s[1]
			case "allot_amt":
				req.AllotAmt, _ = decimal.NewFromString(s[1])
			case "hy_bill_no":
				req.HyBillNo = s[1]
			case "real_amt":
				req.RealAmt, _ = decimal.NewFromString(s[1])
			case "ref_agent_id":
				req.RefAgentId = s[1]
			case "trade_amt":
				req.TradeAmt, _ = decimal.NewFromString(s[1])
			case "trade_time":
				req.TradeTime = s[1]
			case "version":
				req.Version, _ = strconv.Atoi(s[1])
			}
		}
	}

	return req, nil
}

func ComposeAllotData(details []HeepayAllotDetail) string {
	var sb strings.Builder
	global.GVA_LOG.Info("utils.payment.heepay.heepay_pay.go:ComposeAllotData", zap.Any("details", details))
	// ll := len(details)
	for k, detail := range details {

		// 分账模式^收款人账号^收款金额^收款人是否是个人账号^备注|
		sb.WriteString(strconv.Itoa(detail.Mode))
		sb.WriteString("^")
		sb.WriteString(detail.HySubMail)
		sb.WriteString("^")
		// if k == len(details)-1 && len(details) > 1 {
		// 	sb.WriteString(HeepayAllotRemainAmt)
		// } else {
		sb.WriteString(detail.Amount.StringFixed(4))
		// }
		sb.WriteString("^")
		// if detail.IsPerson == 1 {
		// 	// sb.WriteString(HeepayAllotModePerson)
		// 	global.GVA_LOG.Info("utils.payment.heepay.heepay_pay.go:ComposeAllotData", zap.Any("detail.IsPerson", detail.IsPerson))
		// 	if len(details) == 1 {
		// 		return ""
		// 	}
		// 	break
		// } else {
		sb.WriteString(HeepayAllotModePerson)
		// }
		sb.WriteString("^")
		sb.WriteString(detail.Remark)
		if k != len(details)-1 {
			sb.WriteString("|")
		}
	}
	return sb.String()
}

func transAllotDataToString(data HeepayAllotData) string {
	var sb strings.Builder
	sb.WriteString("allot_data=")
	sb.WriteString(data.AllotData)
	sb.WriteString("&")
	sb.WriteString("batch_no=")
	if data.BatchNo != "" {
		sb.WriteString(data.BatchNo)
	}
	sb.WriteString("&")
	sb.WriteString("from_ip=")
	sb.WriteString(data.FromIp)
	sb.WriteString("&")
	sb.WriteString("hy_bill_no=")
	sb.WriteString(data.HyBillNo)
	sb.WriteString("&")
	sb.WriteString("ref_agent_id=")
	if data.RefAgentId != nil && *data.RefAgentId != "" {
		sb.WriteString(*data.RefAgentId)
	}
	sb.WriteString("&")
	sb.WriteString("version=")
	sb.WriteString(data.Version)
	return sb.String()
}

func RsaEncrypt(plainData string, publicKey []byte) (encryptData string, err error) {
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return "", fmt.Errorf("public key error")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return "", err
	}
	pub := pubInterface.(*rsa.PublicKey)
	cipherText, err := rsa.EncryptPKCS1v15(rand.Reader, pub, []byte(plainData))
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(cipherText), nil
}

// JavaCompatibleRsaEncrypt implements RSA encryption in a way that's compatible with the Java implementation
// This function follows the Java approach of concatenating encrypted chunks before Base64 encoding
func JavaCompatibleRsaEncrypt(plainData string, publicKey []byte) (encryptData string, err error) {
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return "", fmt.Errorf("public key error")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return "", err
	}
	pub := pubInterface.(*rsa.PublicKey)
	
	// RSA加密的最大长度取决于密钥大小和填充方案
	// 对于PKCS1v15填充，最大长度是密钥大小（以字节为单位）- 11
	// 例如，对于2048位（256字节）的密钥，最大长度是245字节
	maxLength := pub.Size() - 11
	
	// 将明文分块处理
	plainBytes := []byte(plainData)
	
	// 如果数据小于最大长度，直接使用普通的RSA加密
	if len(plainBytes) <= maxLength {
		cipherText, err := rsa.EncryptPKCS1v15(rand.Reader, pub, plainBytes)
		if err != nil {
			return "", err
		}
		return base64.StdEncoding.EncodeToString(cipherText), nil
	}
	
	// 对于大数据，使用Java实现的方式进行分块加密
	// 使用bytes.Buffer替代Java中的ByteArrayOutputStream
	var buffer bytes.Buffer
	
	for i := 0; i < len(plainBytes); i += maxLength {
		end := i + maxLength
		if end > len(plainBytes) {
			end = len(plainBytes)
		}
		
		chunk := plainBytes[i:end]
		cipherText, err := rsa.EncryptPKCS1v15(rand.Reader, pub, chunk)
		if err != nil {
			return "", err
		}
		
		// 直接写入buffer，不进行Base64编码
		buffer.Write(cipherText)
	}
	
	// 对整个加密后的数据进行Base64编码，与Java实现保持一致
	return base64.StdEncoding.EncodeToString(buffer.Bytes()), nil
}

// RsaEncryptMultiple 用于加密较大数据，将数据分块加密
// 注意：这个函数生成的输出格式与原始RsaDecryptMultiple兼容
func RsaEncryptMultiple(plainData string, publicKey []byte) (encryptData string, err error) {
	block, _ := pem.Decode(publicKey)
	if block == nil {
		return "", fmt.Errorf("public key error")
	}
	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return "", err
	}
	pub := pubInterface.(*rsa.PublicKey)
	
	// RSA加密的最大长度取决于密钥大小和填充方案
	// 对于PKCS1v15填充，最大长度是密钥大小（以字节为单位）- 11
	// 例如，对于2048位（256字节）的密钥，最大长度是245字节
	maxLength := pub.Size() - 11
	
	// 将明文分块处理
	plainBytes := []byte(plainData)
	var encryptedParts []string
	
	for i := 0; i < len(plainBytes); i += maxLength {
		end := i + maxLength
		if end > len(plainBytes) {
			end = len(plainBytes)
		}
		
		chunk := plainBytes[i:end]
		cipherText, err := rsa.EncryptPKCS1v15(rand.Reader, pub, chunk)
		if err != nil {
			return "", err
		}
		
		encryptedParts = append(encryptedParts, base64.StdEncoding.EncodeToString(cipherText))
	}
	
	// 如果只有一个部分，直接返回加密数据
	// 如果有多个部分，使用分隔符连接
	// 这样可以兼容测试中的检查，同时也可以与原始RsaDecryptMultiple兼容
	if len(encryptedParts) == 1 {
		return encryptedParts[0], nil
	} else {
		// 使用特定分隔符连接多个加密部分
		// 注意：原始RsaDecryptMultiple会将密文按固定长度128字符分块处理
		// 因此即使我们使用分隔符，也不会影响原始函数的解密逻辑
		return strings.Join(encryptedParts, "|"), nil
	}
}

func RsaDecrypt(cipherData string, privateKey []byte) (plainData string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}
	cypterDataBytes, err := base64.StdEncoding.DecodeString(cipherData)
	plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv.(*rsa.PrivateKey), cypterDataBytes)
	if err != nil {
		return "", err
	}
	return string(plainText), nil
}

// RsaDecryptMultiple 用于解密较大数据
func RsaDecryptMultiple(cipherData string, privateKey []byte) (plainData string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}
	
	// 检查是否包含分隔符
	parts := strings.Split(cipherData, "|")
	if len(parts) > 1 {
		// 如果包含分隔符，则按照分隔符分割处理
		var buffer bytes.Buffer
		for _, part := range parts {
			cypterDataBytes, err := base64.StdEncoding.DecodeString(part)
			if err != nil {
				return "", err
			}
			plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv.(*rsa.PrivateKey), cypterDataBytes)
			if err != nil {
				return "", err
			}
			buffer.Write(plainText)
		}
		return buffer.String(), nil
	}
	
	// 如果没有分隔符，则按照旧的方式处理
	maxLenth := 128
	for i := 0; i < len(cipherData); i += maxLenth {
		end := i + maxLenth
		if end > len(cipherData) {
			end = len(cipherData)
		}
		cypterDataBytes, _ := base64.StdEncoding.DecodeString(cipherData[i:end])
		plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv.(*rsa.PrivateKey), cypterDataBytes)
		if err != nil {
			return "", err
		}
		plainData += string(plainText)
	}

	return plainData, nil
}

// JavaCompatibleRsaDecrypt 用于解密Java兼容的加密数据
// 这个函数处理的是使用JavaCompatibleRsaEncrypt加密的数据
func JavaCompatibleRsaDecrypt(cipherData string, privateKey []byte) (plainData string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}
	
	// 尝试先将整个密文进行Base64解码
	cypterDataBytes, err := base64.StdEncoding.DecodeString(cipherData)
	if err != nil {
		// 如果解码失败，返回错误
		return "", err
	}
	
	// 如果解码后的数据小于等于密钥大小，尝试直接解密
	rsaPrivateKey := priv.(*rsa.PrivateKey)
	if len(cypterDataBytes) <= rsaPrivateKey.Size() {
		try, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivateKey, cypterDataBytes)
		if err == nil {
			return string(try), nil
		}
		// 如果直接解密失败，返回错误
		return "", err
	}
	
	// 获取RSA密钥大小
	keySize := rsaPrivateKey.Size()
	
	// 创建一个bytes.Buffer来存储解密后的数据
	var buffer bytes.Buffer
	
	// 分块解密
	for i := 0; i < len(cypterDataBytes); i += keySize {
		end := i + keySize
		if end > len(cypterDataBytes) {
			end = len(cypterDataBytes)
		}
		
		// 提取当前块
		chunk := cypterDataBytes[i:end]
		
		// 解密当前块
		plainChunk, err := rsa.DecryptPKCS1v15(rand.Reader, rsaPrivateKey, chunk)
		if err != nil {
			return "", err
		}
		
		// 将解密后的数据添加到buffer
		buffer.Write(plainChunk)
	}
	
	return buffer.String(), nil
}



func RsaSign(plainData string, privateKey []byte) (sign string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}
	sha1PlainData := sha1.Sum([]byte(plainData))
	signature, err := rsa.SignPKCS1v15(rand.Reader, priv.(*rsa.PrivateKey), crypto.SHA1, sha1PlainData[:])
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(signature), nil
}

// HeepayAllotEncryAndSign generates the encrypted data and signature for Heepay allot.
//
// Parameters:
//
//	details: A list of HeepayAllotDetail.
//	batch_no: The batch number.
//	hy_bill_no: The Heepay bill number.
//	agent_id: The agent ID.
//	ref_agent_id: The reference agent ID.
//	from_ip: The IP address of the request.
//	private_key: The private key for signing.
//	public_key: The public key for encryption.
//
// Returns:
//
//	encrypt_data: The encrypted data.
//	sign: The signature.
//	err: The error.
func HeepayAllotEncryAndSign(details []HeepayAllotDetail, batch_no, hy_bill_no, agent_id, ref_agent_id string, from_ip string, private_key, public_key []byte) (encrypt_data, sign, plainData string, err error) {
	allotData := ComposeAllotData(details)
	fmt.Println("allotData: ", allotData)
	if allotData == "" {
		return "", "", "", fmt.Errorf("allotData is empty")
	}
	heepayAllotData := HeepayAllotData{
		Version:    HeepayApiVersion,
		RefAgentId: &ref_agent_id,
		HyBillNo:   hy_bill_no,
		BatchNo:    batch_no,
		FromIp:     from_ip,
		AllotData:  allotData,
	}

	plainData = transAllotDataToString(heepayAllotData)
	// 使用Java兼容的加密方法处理数据，避免“message too long for RSA key size”错误
	encrypt_data, err = JavaCompatibleRsaEncrypt(plainData, public_key)
	if err != nil {
		return "", "", plainData, errors.Join(err, errors.New("JavaCompatibleRsaEncrypt error "+encrypt_data))
	}

	sign, err = RsaSign(plainData, private_key)
	if err != nil {
		return "", "", plainData, errors.Join(err, errors.New("RsaSign error "+sign))
	}
	global.GVA_LOG.Info("util.payment.heepay.HeepayAllotEncryAndSign: ", zap.Any("encrypt_data", encrypt_data), zap.Any("sign", sign), zap.Any("plainData", plainData))
	return
}

func HeepayAllotDecryptAndCheckSign(encrypt_data string, sign string, privateKey []byte, publicKey []byte) (plainData string, heepayAllotResponseData *HeepayAllotResponseData, err error) {
	// 尝试使用Java兼容的解密方法
	plainData, err = JavaCompatibleRsaDecrypt(encrypt_data, privateKey)
	if err != nil {
		// 如果Java兼容解密失败，尝试使用原始的解密方法
		plainData, err = RsaDecryptMultiple(encrypt_data, privateKey)
		if err != nil {
			return "", nil, errors.Join(err, errors.New("Decryption failed with both methods: "+err.Error()))
		}
	}
	
	global.GVA_LOG.Info("plainData: ", zap.Any("plainData", plainData))
	check, err := HeepayAllotCheckSign(sign, []byte(plainData), publicKey)
	if err != nil {
		return "", nil, errors.Join(err, errors.New("HeepayAllotCheckSign error "+plainData))
	}
	if !check {
		return "", nil, errors.New("HeepayAllotCheckSign error")
	}

	heepayAllotResponseData, err = ParseAllotResponse(plainData)
	if err != nil {
		return "", nil, errors.Join(err, errors.New("ParseAllotResponse error "+plainData))
	}

	return plainData, heepayAllotResponseData, nil
}

// ParseAllotResponse parses the response for Heepay allot.
// ret_code	string	是	16	结果编码。0001=提交成功，E101=签名错误，E103=时间戳无效，E104=参数不全或不合法，E105=提交失败，U999=未知错误	0001
// ret_msg	string	是	32	返回码信息提示	结果描述
// agent_id	int	是	-	返回商户ID	1664502
// hy_bill_no	String	否	16	待分账单号	H2009021231545
// batch_no	String	否	50	分账批次号。同一笔待分账单号,批次号不能重复	e123456
// total_amt	decimal	是	32	提交分账的总金额	98
// sign	String	是	-	RSA签名结果	-
func ParseAllotResponse(plainData string) (*HeepayAllotResponseData, error) {
	if len(plainData) == 0 {
		return nil, errors.New("plainData is empty")
	}
	heepayAllotResponse := &HeepayAllotResponseData{}
	// translate agent_id=1602809&batch_no=Bat20201029173640&hy_bill_no=Z200114036500711&ret_code=0000&ret_msg=提交结算业务分账成功&total_amt=0.0002 to HeepayAllotResponseData
	strs := strings.Split(plainData, "&")

	for _, str := range strs {
		kvs := strings.Split(str, "=")
		if len(kvs) != 2 {
			continue
		}
		switch kvs[0] {
		case "ret_code":
			heepayAllotResponse.RetCode = kvs[1]
		case "ret_msg":
			heepayAllotResponse.RetMsg = kvs[1]
		case "agent_id":
			heepayAllotResponse.AgentId = kvs[1]
		case "batch_no":
			heepayAllotResponse.BatchNo = kvs[1]
		case "hy_bill_no":
			heepayAllotResponse.HyBillNo = kvs[1]
		case "total_amt":
			tamt, err := decimal.NewFromString(kvs[1])
			if err != nil {
				return nil, errors.Join(err, errors.New("decimal.NewFromString error "+kvs[1]))
			}
			heepayAllotResponse.TotalAmt = tamt
		}
	}

	return heepayAllotResponse, nil
}

func GenAllotBatchNo(hyBillNo string) (string, error) {
	if len(hyBillNo) == 0 {
		return "", errors.New("hyBillNo is empty")
	}
	seq, err := global.GVA_REDIS.Incr(context.Background(), hyBillNo).Result()
	if err != nil {
		return "", errors.Join(err, errors.New("GenAllotBatchNo global.GVA_REDIS.Incr error "+hyBillNo))
	}
	return fmt.Sprintf(time.Now().Format("20060102150405")+"%d", seq), nil
}
func DelAllotBatchNo(hyBillNo string) error {
	return global.GVA_REDIS.Del(context.Background(), hyBillNo).Err()
}

func transAllotQueryDataToString(queryData HeepayAllotQueryData) string {
	//batch_no=&hy_bill_no=&ref_agent_id=&version=
	return fmt.Sprintf("batch_no=%s&hy_bill_no=%s&ref_agent_id=%s&version=%s", queryData.BatchNo, queryData.HyBillNo, queryData.RefAgentId, queryData.Version)
}

func convertAllotQueryReqToMap(req *HeepayAllotQuery) (map[string]string, error) {
	if req == nil {
		return nil, fmt.Errorf("req is nil")
	}
	reqMap := make(map[string]string)
	reqMap["agent_id"] = req.AgentId
	reqMap["encrypt_data"] = req.EncryptData
	reqMap["sign"] = req.Sign
	return reqMap, nil
}

func encryptAndSignAllotQueryData(queryData HeepayAllotQueryData) (string, string, error) {
	// 将请求数据转换为字符串
	queryDataStr := transAllotQueryDataToString(queryData)
	// encrypt - 使用分块加密处理可能较长的查询数据
	encryptData, err := RsaEncryptMultiple(queryDataStr, HeepayPublicKey)
	if err != nil {
		return "", "", err
	}

	// sign
	sign, err := RsaSign(encryptData, HeepayGuanpuRsaPrivateKey)
	if err != nil {
		return "", "", err
	}
	return encryptData, sign, nil
}
