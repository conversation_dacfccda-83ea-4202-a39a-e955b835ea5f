package heepay

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"strings"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

// Use the predefined RSA keys from heepay_base.go for testing
var testPrivateKey = HeepayGuanpuRsaPrivateKey
var testPublicKey = HeepayGuanpuRsaPubKey

func TestRsaEncryptMultiple(t *testing.T) {
	// Test cases
	testCases := []struct {
		name      string
		plainData string
	}{
		{
			name:      "Small data",
			plainData: "This is a small test string",
		},
		{
			name:      "Medium data",
			plainData: strings.Repeat("Medium sized test data. ", 10),
		},
		{
			name:      "Large data",
			plainData: strings.Repeat("This is a large test string that should be split into multiple chunks for encryption. ", 20),
		},
		{
			name:      "Real world example",
			plainData: "allot_data=0^<EMAIL>^2970.0000^F^|0^<EMAIL>^1344.0000^F^|0^<EMAIL>^1320.0000^F^|0^<EMAIL>^1248.0000^F^&batch_no=202503121442084&from_ip=**************&hy_bill_no=Z25030336825941K&ref_agent_id=2236099&version=1",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Encrypt the data using the new multiple encryption function
			encryptedData, err := RsaEncryptMultiple(tc.plainData, testPublicKey)
			assert.NoError(t, err, "Encryption should not return an error")
			assert.NotEmpty(t, encryptedData, "Encrypted data should not be empty")

			// Check if the data was split (for large data)
			if len(tc.plainData) > 200 {
				assert.Contains(t, encryptedData, "|", "Large data should be split and contain delimiter")
			}

			// Decrypt the data using RsaDecryptMultiple
			decryptedData, err := RsaDecryptMultiple(encryptedData, testPrivateKey)
			assert.NoError(t, err, "Decryption should not return an error")

			// Verify the decrypted data matches the original
			assert.Equal(t, tc.plainData, decryptedData, "Decrypted data should match original data")
		})
	}
}

func TestHeepayAllotEncryAndSign(t *testing.T) {
	// Create test data similar to the real-world scenario
	details := []HeepayAllotDetail{
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    decimal.NewFromFloat(2970),
			IsPerson:  0,
			Remark:    "",
		},
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    decimal.NewFromFloat(1344),
			IsPerson:  0,
			Remark:    "",
		},
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    decimal.NewFromFloat(1320),
			IsPerson:  0,
			Remark:    "",
		},
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    decimal.NewFromFloat(1248),
			IsPerson:  0,
			Remark:    "",
		},
	}

	batch_no := "202503121442084"
	hy_bill_no := "Z25030336825941K"
	agent_id := "2225143"
	ref_agent_id := "2236099"
	from_ip := "**************"

	// Test the full encryption and signing process
	encrypt_data, sign, plainData, err := HeepayAllotEncryAndSign(
		details,
		batch_no,
		hy_bill_no,
		agent_id,
		ref_agent_id,
		from_ip,
		testPrivateKey,
		testPublicKey,
	)

	// Assertions
	assert.NoError(t, err, "HeepayAllotEncryAndSign should not return an error")
	assert.NotEmpty(t, encrypt_data, "Encrypted data should not be empty")
	assert.NotEmpty(t, sign, "Sign should not be empty")
	assert.NotEmpty(t, plainData, "Plain data should not be empty")

	// Test the decryption and signature verification
	decryptedData, responseData, err := HeepayAllotDecryptAndCheckSign(
		encrypt_data,
		sign,
		testPrivateKey,
		testPublicKey,
	)

	// These assertions might fail in a real test environment since we're not mocking the response
	// But they should work in a properly configured test environment
	if err == nil {
		assert.NotEmpty(t, decryptedData, "Decrypted data should not be empty")
		assert.NotNil(t, responseData, "Response data should not be nil")
	}
}

func TestRsaEncryptDecryptRoundTrip(t *testing.T) {
	// Test a round trip of encryption and decryption with different data sizes
	testSizes := []int{10, 100, 200, 500, 1000, 2000}
	
	for _, size := range testSizes {
		t.Run("Size_"+string(rune(size)), func(t *testing.T) {
			// Generate a string of specified size
			originalData := strings.Repeat("A", size)
			t.Logf("Original data size: %d", len(originalData))
			
			// Get the public key block for debugging
			block, _ := pem.Decode(testPublicKey)
			if block == nil {
				t.Fatal("Failed to decode public key")
			}
			pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
			if err != nil {
				t.Fatalf("Failed to parse public key: %v", err)
			}
			pub := pubInterface.(*rsa.PublicKey)
			maxLength := pub.Size() - 11
			t.Logf("Max chunk size for encryption: %d", maxLength)
			t.Logf("Expected number of chunks: %d", (len(originalData) + maxLength - 1) / maxLength)
			
			// Encrypt with the new multiple encryption function
			encryptedData, err := RsaEncryptMultiple(originalData, testPublicKey)
			assert.NoError(t, err, "Encryption should not return an error")
			t.Logf("Encrypted data length: %d", len(encryptedData))
			t.Logf("Encrypted data contains delimiter: %v", strings.Contains(encryptedData, "|"))
			
			// For larger data sizes, check that the encrypted data contains the delimiter
			if size > 200 {
				assert.Contains(t, encryptedData, "|", "Large data should be split and contain delimiter")
			}
			
			// Use RsaDecryptMultiple for decryption
			decryptedData, err := RsaDecryptMultiple(encryptedData, testPrivateKey)
			if err != nil {
				t.Logf("Decryption error: %v", err)
			}
			assert.NoError(t, err, "Decryption should not return an error")
			t.Logf("Decrypted data length: %d", len(decryptedData))
			
			// Verify the data matches
			assert.Equal(t, originalData, decryptedData, "Decrypted data should match original data")
		})
	}
}

// TestFreshKeyPair generates a new RSA key pair and tests encryption/decryption with it
func TestFreshKeyPair(t *testing.T) {
	// Generate a new RSA key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	assert.NoError(t, err, "Failed to generate private key")

	// Convert private key to PKCS8 format (which is what RsaDecrypt expects)
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(privateKey)
	assert.NoError(t, err, "Failed to marshal private key to PKCS8")
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY", // Note: PKCS8 uses "PRIVATE KEY" not "RSA PRIVATE KEY"
		Bytes: privateKeyBytes,
	})

	// Convert public key to PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	assert.NoError(t, err, "Failed to marshal public key")
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	// Test data
	testData := "This is a test string for fresh key pair"

	// Test single encryption/decryption
	t.Run("Single encryption", func(t *testing.T) {
		// Encrypt
		encryptedData, err := RsaEncrypt(testData, publicKeyPEM)
		assert.NoError(t, err, "Encryption should not return an error")
		assert.NotEmpty(t, encryptedData, "Encrypted data should not be empty")

		// Decrypt
		decryptedData, err := RsaDecrypt(encryptedData, privateKeyPEM)
		assert.NoError(t, err, "Decryption should not return an error")
		assert.Equal(t, testData, decryptedData, "Decrypted data should match original data")
	})

	// Test multiple encryption/decryption with various data sizes
	dataSizes := []int{10, 100, 500, 1000}
	for _, size := range dataSizes {
		t.Run("Multiple encryption size "+string(rune(size)), func(t *testing.T) {
			// Generate data of specified size
			originalData := strings.Repeat("B", size)

			// Encrypt with multiple encryption
			encryptedData, err := RsaEncryptMultiple(originalData, publicKeyPEM)
			assert.NoError(t, err, "Encryption should not return an error")
			assert.NotEmpty(t, encryptedData, "Encrypted data should not be empty")

			// If data is large, it should be split
			if size > 200 {
				assert.Contains(t, encryptedData, "|", "Large data should be split and contain delimiter")
			}

			// Decrypt with multiple decryption
			decryptedData, err := RsaDecryptMultiple(encryptedData, privateKeyPEM)
			assert.NoError(t, err, "Decryption should not return an error")
			assert.Equal(t, originalData, decryptedData, "Decrypted data should match original data")
		})
	}
}
