package heepay

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"strings"
	"testing"
)

// TestJavaCompatibleRsaEncryption tests the Java-compatible RSA encryption implementation
func TestJavaCompatibleRsaEncryption(t *testing.T) {
	// Test data with different sizes
	testCases := []struct {
		name       string
		plainData  string
		shouldFail bool // Whether standard RsaEncrypt should fail (for large data)
	}{
		{
			name:       "Small data",
			plainData:  "This is a small test string for RSA encryption",
			shouldFail: false,
		},
		{
			name:       "Medium data",
			plainData:  strings.Repeat("Medium ", 20), // About 140 bytes
			shouldFail: false,
		},
		{
			name:       "Large data",
			plainData:  strings.Repeat("Large data that exceeds RSA encryption block size. ", 10), // About 500 bytes
			shouldFail: true,
		},
	}

	// Generate test keys for encryption/decryption
	privateKey, publicKey := generateTestKeyPair(t)

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test standard RSA encryption (should fail for large data)
			encrypted1, err1 := RsaEncrypt(tc.plainData, publicKey)
			if tc.shouldFail {
				if err1 == nil {
					t.Errorf("RsaEncrypt should have failed for large data but succeeded")
				} else {
					t.Logf("RsaEncrypt correctly failed for large data: %v", err1)
				}
			} else {
				if err1 != nil {
					t.Errorf("RsaEncrypt failed for small data: %v", err1)
				} else {
					// Try to decrypt with custom implementation
					decrypted1, err := testRsaDecrypt(encrypted1, privateKey)
					if err != nil {
						t.Errorf("Custom RsaDecrypt failed: %v", err)
					} else if decrypted1 != tc.plainData {
						t.Errorf("RsaEncrypt/RsaDecrypt roundtrip failed. Got %q, want %q", decrypted1, tc.plainData)
					} else {
						t.Logf("RsaEncrypt/RsaDecrypt roundtrip succeeded")
					}
				}
			}

			// Test RsaEncryptMultiple (should work for all data sizes)
			encrypted2, err2 := RsaEncryptMultiple(tc.plainData, publicKey)
			if err2 != nil {
				t.Errorf("RsaEncryptMultiple failed: %v", err2)
			} else {
				// Check if large data contains the delimiter
				if tc.shouldFail && !strings.Contains(encrypted2, "|") {
					t.Errorf("Large data should be split and contain delimiter")
				}

				// Try to decrypt with custom implementation
				decrypted2, err := testRsaDecryptMultiple(encrypted2, privateKey)
				if err != nil {
					t.Errorf("Custom RsaDecryptMultiple failed: %v", err)
				} else if decrypted2 != tc.plainData {
					t.Errorf("RsaEncryptMultiple/RsaDecryptMultiple roundtrip failed. Got %q, want %q", decrypted2, tc.plainData)
				} else {
					t.Logf("RsaEncryptMultiple/RsaDecryptMultiple roundtrip succeeded")
				}
			}

			// Test JavaCompatibleRsaEncrypt (should work for all data sizes)
			encrypted3, err3 := JavaCompatibleRsaEncrypt(tc.plainData, publicKey)
			if err3 != nil {
				t.Errorf("JavaCompatibleRsaEncrypt failed: %v", err3)
			} else {
				// Try to decrypt with custom implementation
				decrypted3, err := testJavaCompatibleRsaDecrypt(encrypted3, privateKey)
				if err != nil {
					t.Errorf("Custom JavaCompatibleRsaDecrypt failed: %v", err)
				} else if decrypted3 != tc.plainData {
					t.Errorf("JavaCompatibleRsaEncrypt/JavaCompatibleRsaDecrypt roundtrip failed. Got %q, want %q", decrypted3, tc.plainData)
				} else {
					t.Logf("JavaCompatibleRsaEncrypt/JavaCompatibleRsaDecrypt roundtrip succeeded")
				}
			}
		})
	}
}

// Helper function to generate a test key pair
func generateTestKeyPair(t *testing.T) ([]byte, []byte) {
	// Generate a new RSA key pair for testing
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("Failed to generate test key pair: %v", err)
	}

	// Convert private key to PEM format
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	// Convert public key to PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		t.Fatalf("Failed to marshal public key: %v", err)
	}
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return privateKeyPEM, publicKeyPEM
}

// Custom implementation of RsaDecrypt for testing
func testRsaDecrypt(cipherData string, privateKey []byte) (plainData string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}

	// Parse the private key - try both PKCS1 and PKCS8 formats
	var priv *rsa.PrivateKey
	var parseErr error

	// First try PKCS1 (most common for test keys)
	priv, parseErr = x509.ParsePKCS1PrivateKey(block.Bytes)
	if parseErr != nil {
		// If PKCS1 fails, try PKCS8
		pkcs8Key, pkcs8Err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if pkcs8Err != nil {
			return "", fmt.Errorf("failed to parse private key: %v (PKCS1), %v (PKCS8)", parseErr, pkcs8Err)
		}
		var ok bool
		priv, ok = pkcs8Key.(*rsa.PrivateKey)
		if !ok {
			return "", fmt.Errorf("not an RSA private key")
		}
	}

	cypterDataBytes, err := base64.StdEncoding.DecodeString(cipherData)
	if err != nil {
		return "", err
	}

	plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv, cypterDataBytes)
	if err != nil {
		return "", err
	}
	return string(plainText), nil
}

// Custom implementation of RsaDecryptMultiple for testing
func testRsaDecryptMultiple(cipherData string, privateKey []byte) (plainData string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}

	// Parse the private key - try both PKCS1 and PKCS8 formats
	var priv *rsa.PrivateKey
	var parseErr error

	// First try PKCS1 (most common for test keys)
	priv, parseErr = x509.ParsePKCS1PrivateKey(block.Bytes)
	if parseErr != nil {
		// If PKCS1 fails, try PKCS8
		pkcs8Key, pkcs8Err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if pkcs8Err != nil {
			return "", fmt.Errorf("failed to parse private key: %v (PKCS1), %v (PKCS8)", parseErr, pkcs8Err)
		}
		var ok bool
		priv, ok = pkcs8Key.(*rsa.PrivateKey)
		if !ok {
			return "", fmt.Errorf("not an RSA private key")
		}
	}

	// Check if contains delimiter
	parts := strings.Split(cipherData, "|")
	if len(parts) > 1 {
		// Process each part separately
		var buffer strings.Builder
		for _, part := range parts {
			cypterDataBytes, err := base64.StdEncoding.DecodeString(part)
			if err != nil {
				return "", err
			}
			plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv, cypterDataBytes)
			if err != nil {
				return "", err
			}
			buffer.Write(plainText)
		}
		return buffer.String(), nil
	}

	// If no delimiter, handle as a single block
	cypterDataBytes, err := base64.StdEncoding.DecodeString(cipherData)
	if err != nil {
		return "", err
	}
	plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv, cypterDataBytes)
	if err != nil {
		return "", err
	}
	return string(plainText), nil
}

// Custom implementation of JavaCompatibleRsaDecrypt for testing
func testJavaCompatibleRsaDecrypt(cipherData string, privateKey []byte) (plainData string, err error) {
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return "", fmt.Errorf("private key error")
	}

	// Parse the private key - try both PKCS1 and PKCS8 formats
	var priv *rsa.PrivateKey
	var parseErr error

	// First try PKCS1 (most common for test keys)
	priv, parseErr = x509.ParsePKCS1PrivateKey(block.Bytes)
	if parseErr != nil {
		// If PKCS1 fails, try PKCS8
		pkcs8Key, pkcs8Err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if pkcs8Err != nil {
			return "", fmt.Errorf("failed to parse private key: %v (PKCS1), %v (PKCS8)", parseErr, pkcs8Err)
		}
		var ok bool
		priv, ok = pkcs8Key.(*rsa.PrivateKey)
		if !ok {
			return "", fmt.Errorf("not an RSA private key")
		}
	}

	// Decode the base64 data
	cypterDataBytes, err := base64.StdEncoding.DecodeString(cipherData)
	if err != nil {
		return "", err
	}

	// Calculate the block size for decryption
	blockSize := priv.Size()
	var buffer strings.Builder

	// Process each block
	for i := 0; i < len(cypterDataBytes); i += blockSize {
		end := i + blockSize
		if end > len(cypterDataBytes) {
			end = len(cypterDataBytes)
		}

		block := cypterDataBytes[i:end]
		plainText, err := rsa.DecryptPKCS1v15(rand.Reader, priv, block)
		if err != nil {
			return "", err
		}

		buffer.Write(plainText)
	}

	return buffer.String(), nil
}
