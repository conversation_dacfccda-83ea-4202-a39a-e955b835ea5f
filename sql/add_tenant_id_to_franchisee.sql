-- 多租户改造：为加盟商表添加租户ID字段和复合唯一索引
-- 执行时间：2024-12-25
-- 说明：实现加盟商1:1关系设计，确保数据隔离和一致性

-- 1. 添加租户ID字段到加盟商表
ALTER TABLE franchisee 
ADD COLUMN tenant_id INT UNSIGNED NOT NULL DEFAULT 1 
COMMENT '租户ID' AFTER id;

-- 2. 添加租户ID索引
ALTER TABLE franchisee 
ADD INDEX idx_franchisee_tenant_id (tenant_id);

-- 3. 添加复合唯一索引：确保一个用户在一个租户中只能有一个加盟商身份
ALTER TABLE franchisee 
ADD UNIQUE KEY idx_tenant_user (tenant_id, user_id);

-- 4. 添加复合唯一索引：确保一个租户中的加盟商编码唯一
ALTER TABLE franchisee 
ADD UNIQUE KEY idx_tenant_code (tenant_id, code);

-- 5. 添加外键约束（可选，根据实际情况决定是否执行）
-- ALTER TABLE franchisee 
-- ADD CONSTRAINT fk_franchisee_tenant 
-- FOREIGN KEY (tenant_id) REFERENCES tenant (id) ON DELETE RESTRICT;

-- 6. 更新现有数据：将所有现有加盟商分配到默认租户（ID=1）
-- 注意：执行前请确保tenant表中存在ID=1的默认租户
UPDATE franchisee SET tenant_id = 1 WHERE tenant_id = 0;

-- 7. 验证数据完整性
SELECT 
    COUNT(*) as total_franchisees,
    COUNT(DISTINCT tenant_id) as tenant_count,
    COUNT(DISTINCT CONCAT(tenant_id, '-', user_id)) as unique_tenant_user_pairs
FROM franchisee;

-- 8. 检查是否有重复的租户-用户组合（应该为0）
SELECT tenant_id, user_id, COUNT(*) as count
FROM franchisee 
GROUP BY tenant_id, user_id 
HAVING COUNT(*) > 1;

-- 9. 检查是否有重复的租户-编码组合（应该为0）
SELECT tenant_id, code, COUNT(*) as count
FROM franchisee 
GROUP BY tenant_id, code 
HAVING COUNT(*) > 1;
