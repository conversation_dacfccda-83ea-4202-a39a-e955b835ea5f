name: release

on:
  push:
    tags:
      - "v*"

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Unshallow
        run: git fetch --prune --unshallow

      - name: Set up Go 1.18
        uses: actions/setup-go@v1
        with:
          go-version: 1.18
        id: go

      - name: Set Envs
        run: |
          echo GO_VERSION=$(go version | awk '{print $3;}') >> $GITHUB_ENV

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v4.2.0
        with:
          version: latest
          args: release --rm-dist
        env:
          GITHUB_TOKEN: ${{ secrets.TOKEN }}
          GO_VERSION: ${{ env.GITHUB_GO_VERSION }}