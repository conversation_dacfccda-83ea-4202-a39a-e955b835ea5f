on:
  workflow_dispatch: {}
  pull_request: {}
  push:
    branches:
    - main
    - master
    paths:
    - .github/workflows/semgrep.yml
  schedule:
  # random HH:MM to avoid a load spike on GitHub Actions at 00:00
  - cron: 4 16 * * *
name: Semgrep
jobs:
  semgrep:
    name: semgrep/ci
    runs-on: ubuntu-20.04
    env:
      SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
    container:
      image: returntocorp/semgrep
    steps:
    - uses: actions/checkout@v3
    - run: semgrep ci
