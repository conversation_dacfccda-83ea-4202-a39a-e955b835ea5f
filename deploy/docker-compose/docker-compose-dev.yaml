version: "3"

# 声明一个名为network的networks,subnet为network的子网地址,默认网关是*********
networks:
  network:
    ipam:
      driver: default
      config:
        - subnet: '*********/16'
        
# 设置mysql，redis持久化保存
volumes:
  mysql:
  redis:

services:
  web:
    image: node:16
    container_name: gva-web
    hostname: gva-web           #可以通过容器名访问
    restart: always
    ports:
      - '8080:8080'
    depends_on:
      - server
    working_dir: /web       # 如果docker 设置了workdir 则此处不需要设置
    #若网络不太好，请自行换源，如下
    #command: bash -c "yarn config set registry https://registry.npm.taobao.org --global && yarn install && yarn serve"
    command: bash -c "yarn install && yarn serve"
    volumes:
      - ../../web:/web
    networks:
      network:
        ipv4_address: *********1

  server:
    image: golang:1.18
    container_name: gva-server
    hostname: gva-server
    restart: always
    ports:
      - '8888:8888'
    depends_on:
      - mysql
      - redis
    volumes:
      - ../../server:/server
    working_dir: /server    # 如果docker 设置了workdir 则此处不需要设置 
    command: bash -c "go env -w GOPROXY=https://goproxy.cn,direct && go mod tidy && go run main.go"
    links:
      - mysql
      - redis
    networks:
      network:
        ipv4_address: *********2

  mysql:
    image: mysql:8.0.21    # 如果您是 arm64 架构：如 MacOS 的 M1，请修改镜像为 image: mysql/mysql-server:8.0.21
    container_name: gva-mysql
    hostname: gva-mysql
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci #设置utf8字符集
    restart: always
    ports:
      - "13306:3306"  # host物理直接映射端口为13306
    environment:
      #MYSQL_ROOT_PASSWORD: 'Aa@6447985' # root管理员用户密码
      MYSQL_DATABASE: 'qmPlus' # 初始化启动时要创建的数据库的名称
      MYSQL_USER: 'gva'
      MYSQL_PASSWORD: 'Aa@6447985'
    volumes:
      - mysql:/var/lib/mysql
    networks:
      network:
        ipv4_address: **********

  redis:
    image: redis:6.0.6
    container_name: gva-redis # 容器名
    hostname: gva-redis
    restart: always
    ports:
      - '16379:6379'
    volumes:
      - redis:/data
    networks:
      network:
        ipv4_address: **********
