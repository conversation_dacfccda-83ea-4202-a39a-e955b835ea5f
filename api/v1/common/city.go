package common

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	commonReq "github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CityApi struct {
}

var cityService = service.ServiceGroupApp.CommonServiceGroup.CityService

// CreateCity 创建City
// @Tags City
// @Summary 创建City
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body common.City true "创建City"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /city/createCity [post]
func (cityApi *CityApi) CreateCity(c *gin.Context) {
	var city common.City
	err := c.ShouldBindJSON(&city)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cityService.CreateCity(&city); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteCity 删除City
// @Tags City
// @Summary 删除City
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body common.City true "删除City"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /city/deleteCity [delete]
func (cityApi *CityApi) DeleteCity(c *gin.Context) {
	var city common.City
	err := c.ShouldBindJSON(&city)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cityService.DeleteCity(city); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteCityByIds 批量删除City
// @Tags City
// @Summary 批量删除City
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除City"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /city/deleteCityByIds [delete]
func (cityApi *CityApi) DeleteCityByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cityService.DeleteCityByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateCity 更新City
// @Tags City
// @Summary 更新City
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body common.City true "更新City"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /city/updateCity [put]
func (cityApi *CityApi) UpdateCity(c *gin.Context) {
	var city common.City
	err := c.ShouldBindJSON(&city)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cityService.UpdateCity(city); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindCity 用id查询City
// @Tags City
// @Summary 用id查询City
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query common.City true "用id查询City"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /city/findCity [get]
func (cityApi *CityApi) FindCity(c *gin.Context) {
	var city common.City
	err := c.ShouldBindQuery(&city)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if recity, err := cityService.GetCity(city.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"recity": recity}, c)
	}
}

// GetCityList 分页获取City列表
// @Tags City
// @Summary 分页获取City列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query commonReq.CitySearch true "分页获取City列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /city/getCityList [get]
func (cityApi *CityApi) GetCityList(c *gin.Context) {
	var pageInfo commonReq.CitySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := cityService.GetCityInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
