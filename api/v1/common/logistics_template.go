package common

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	commonReq "github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LogisticsTemplateApi struct {
}

var logisticsTemplateService = service.ServiceGroupApp.CommonServiceGroup.LogisticsTemplateService

// CreateLogisticsTemplate 创建物流模版
// @Tags LogisticsTemplate
// @Summary 创建物流模版
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body common.LogisticsTemplate true "创建物流模版"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /logisticsTemplate/createLogisticsTemplate [post]
func (logisticsTemplateApi *LogisticsTemplateApi) CreateLogisticsTemplate(c *gin.Context) {
	var logisticsTemplate common.LogisticsTemplate
	err := c.ShouldBindJSON(&logisticsTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := logisticsTemplateService.CreateLogisticsTemplate(&logisticsTemplate); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteLogisticsTemplate 删除LogisticsTemplate
// @Tags LogisticsTemplate
// @Summary 删除LogisticsTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body common.LogisticsTemplate true "删除LogisticsTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /logisticsTemplate/deleteLogisticsTemplate [delete]
func (logisticsTemplateApi *LogisticsTemplateApi) DeleteLogisticsTemplate(c *gin.Context) {
	var logisticsTemplate common.LogisticsTemplate
	err := c.ShouldBindJSON(&logisticsTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := logisticsTemplateService.DeleteLogisticsTemplate(logisticsTemplate); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteLogisticsTemplateByIds 批量删除LogisticsTemplate
// @Tags LogisticsTemplate
// @Summary 批量删除LogisticsTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除LogisticsTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /logisticsTemplate/deleteLogisticsTemplateByIds [delete]
func (logisticsTemplateApi *LogisticsTemplateApi) DeleteLogisticsTemplateByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := logisticsTemplateService.DeleteLogisticsTemplateByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateLogisticsTemplate 更新LogisticsTemplate
// @Tags LogisticsTemplate
// @Summary 更新LogisticsTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body common.LogisticsTemplate true "更新LogisticsTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /logisticsTemplate/updateLogisticsTemplate [put]
func (logisticsTemplateApi *LogisticsTemplateApi) UpdateLogisticsTemplate(c *gin.Context) {
	var req commonReq.LogisticsTemplateUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := logisticsTemplateService.UpdateLogisticsTemplate(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindLogisticsTemplate 用id查询LogisticsTemplate
// @Tags LogisticsTemplate
// @Summary 用id查询LogisticsTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query common.LogisticsTemplate true "用id查询LogisticsTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /logisticsTemplate/findLogisticsTemplate [get]
func (logisticsTemplateApi *LogisticsTemplateApi) FindLogisticsTemplate(c *gin.Context) {
	var logisticsTemplate common.LogisticsTemplate
	err := c.ShouldBindQuery(&logisticsTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if relogisticsTemplate, err := logisticsTemplateService.GetLogisticsTemplate(logisticsTemplate.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"relogisticsTemplate": relogisticsTemplate}, c)
	}
}

// GetLogisticsTemplateList 分页获取物流模版列表
// @Tags LogisticsTemplate
// @Summary 分页获取物流模版列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query commonReq.LogisticsTemplateSearch true "分页获取物流模版列表"
// @Success 200 {object} response.Response{data=[]common.LogisticsTemplate}
// @Router /logisticsTemplate/getLogisticsTemplateList [get]
func (logisticsTemplateApi *LogisticsTemplateApi) GetLogisticsTemplateList(c *gin.Context) {
	var pageInfo commonReq.LogisticsTemplateSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := logisticsTemplateService.GetLogisticsTemplateInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
