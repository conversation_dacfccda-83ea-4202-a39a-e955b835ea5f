package common

import (
	"github.com/OSQianXing/guanpu-server/global"
	commonReq "github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ProductGift 结构体
type ProductGiftApi struct {
}

var ProductGiftService = service.ServiceGroupApp.CommonServiceGroup.ProductGiftService

// GetProductGift 获取赠品
// @Tags ProductGift
// @Summary 获取赠品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetProductGiftRequset true "获取赠品"
// @Success 200 {object} response.Response{data=[]common.FreeGift} "
// @Router /productGift/getProductGift [post]
func (p *ProductGiftApi) GetProductGift(c *gin.Context) {
	var req commonReq.GetProductGiftRequset
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproduct, err := ProductGiftService.GetProductGifts(req); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproduct": reproduct}, c)
	}
}
