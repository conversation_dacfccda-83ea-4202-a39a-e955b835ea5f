package warehouse

import (
	"fmt"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/warehouse"
	warehouseReq "github.com/OSQianXing/guanpu-server/model/warehouse/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

type CloudWarehouseRecordApi struct {
}

var cloudWarehouseRecordService = service.ServiceGroupApp.WarehouseServiceGroup.CloudWarehouseRecordService

// CreateCloudWarehouseRecord 创建CloudWarehouseRecord
// @Tags CloudWarehouseRecord
// @Summary 创建CloudWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouse.CloudWarehouseRecord true "创建CloudWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /cloudWarehouseRecord/createCloudWarehouseRecord [post]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) CreateCloudWarehouseRecord(c *gin.Context) {
	var cloudWarehouseRecord warehouse.CloudWarehouseRecord
	err := c.ShouldBindJSON(&cloudWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseRecordService.CreateCloudWarehouseRecord(&cloudWarehouseRecord); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteCloudWarehouseRecord 删除CloudWarehouseRecord
// @Tags CloudWarehouseRecord
// @Summary 删除CloudWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouse.CloudWarehouseRecord true "删除CloudWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /cloudWarehouseRecord/deleteCloudWarehouseRecord [delete]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) DeleteCloudWarehouseRecord(c *gin.Context) {
	var cloudWarehouseRecord warehouse.CloudWarehouseRecord
	err := c.ShouldBindJSON(&cloudWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseRecordService.DeleteCloudWarehouseRecord(cloudWarehouseRecord); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteCloudWarehouseRecordByIds 批量删除CloudWarehouseRecord
// @Tags CloudWarehouseRecord
// @Summary 批量删除CloudWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除CloudWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /cloudWarehouseRecord/deleteCloudWarehouseRecordByIds [delete]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) DeleteCloudWarehouseRecordByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseRecordService.DeleteCloudWarehouseRecordByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateCloudWarehouseRecord 更新CloudWarehouseRecord
// @Tags CloudWarehouseRecord
// @Summary 更新CloudWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouse.CloudWarehouseRecord true "更新CloudWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /cloudWarehouseRecord/updateCloudWarehouseRecord [put]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) UpdateCloudWarehouseRecord(c *gin.Context) {
	var cloudWarehouseRecord warehouse.CloudWarehouseRecord
	err := c.ShouldBindJSON(&cloudWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseRecordService.UpdateCloudWarehouseRecord(cloudWarehouseRecord); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindCloudWarehouseRecord 用id查询CloudWarehouseRecord
// @Tags CloudWarehouseRecord
// @Summary 用id查询CloudWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouse.CloudWarehouseRecord true "用id查询CloudWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /cloudWarehouseRecord/findCloudWarehouseRecord [get]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) FindCloudWarehouseRecord(c *gin.Context) {
	var cloudWarehouseRecord warehouse.CloudWarehouseRecord
	err := c.ShouldBindQuery(&cloudWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if recloudWarehouseRecord, err := cloudWarehouseRecordService.GetCloudWarehouseRecord(cloudWarehouseRecord.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"recloudWarehouseRecord": recloudWarehouseRecord}, c)
	}
}

// GetCloudWarehouseRecordList 分页获取CloudWarehouseRecord列表
// @Tags CloudWarehouseRecord
// @Summary 分页获取CloudWarehouseRecord列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.CloudWarehouseRecordSearch true "分页获取CloudWarehouseRecord列表"
// @Success 200 {object} response.PageResult{list=[]response.CloudWarehouseRecordPagination}
// @Router /cloudWarehouseRecord/getCloudWarehouseRecordList [get]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) GetCloudWarehouseRecordList(c *gin.Context) {
	var pageInfo warehouseReq.CloudWarehouseRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := cloudWarehouseRecordService.GetCloudWarehouseRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportCloudWarehouseRecord 云仓流水导出
// @Tags CloudWarehouseRecord
// @Summary 云仓流水导出
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.CloudWarehouseRecordSearch true "云仓流水导出"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /cloudWarehouseRecord/exportCloudWarehouseRecord [get]
func (cloudWarehouseRecordApi *CloudWarehouseRecordApi) ExportCloudWarehouseRecord(c *gin.Context) {
	var req warehouseReq.CloudWarehouseRecordSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	fileName := "云仓流水导出.xlsx"
	// 设置响应头
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	f, err := cloudWarehouseRecordService.ExportCloudWarehouseRecordList(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		c.Abort()
		return
	}
	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

}
