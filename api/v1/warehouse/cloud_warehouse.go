package warehouse

import (
	"fmt"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/warehouse"
	warehouseReq "github.com/OSQianXing/guanpu-server/model/warehouse/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

type CloudWarehouseApi struct {
}

var cloudWarehouseService = service.ServiceGroupApp.WarehouseServiceGroup.CloudWarehouseService

// CreateCloudWarehouse 创建CloudWarehouse
// @Tags CloudWarehouse
// @Summary 创建CloudWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouse.CloudWarehouse true "创建CloudWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /cloudWarehouse/createCloudWarehouse [post]
func (cloudWarehouseApi *CloudWarehouseApi) CreateCloudWarehouse(c *gin.Context) {
	var cloudWarehouse warehouse.CloudWarehouse
	err := c.ShouldBindJSON(&cloudWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseService.CreateCloudWarehouse(&cloudWarehouse); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteCloudWarehouse 删除CloudWarehouse
// @Tags CloudWarehouse
// @Summary 删除CloudWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouse.CloudWarehouse true "删除CloudWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /cloudWarehouse/deleteCloudWarehouse [delete]
func (cloudWarehouseApi *CloudWarehouseApi) DeleteCloudWarehouse(c *gin.Context) {
	var cloudWarehouse warehouse.CloudWarehouse
	err := c.ShouldBindJSON(&cloudWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseService.DeleteCloudWarehouse(cloudWarehouse); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteCloudWarehouseByIds 批量删除CloudWarehouse
// @Tags CloudWarehouse
// @Summary 批量删除CloudWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除CloudWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /cloudWarehouse/deleteCloudWarehouseByIds [delete]
func (cloudWarehouseApi *CloudWarehouseApi) DeleteCloudWarehouseByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseService.DeleteCloudWarehouseByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateCloudWarehouse 更新CloudWarehouse
// @Tags CloudWarehouse
// @Summary 更新CloudWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouse.CloudWarehouse true "更新CloudWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /cloudWarehouse/updateCloudWarehouse [put]
func (cloudWarehouseApi *CloudWarehouseApi) UpdateCloudWarehouse(c *gin.Context) {
	var cloudWarehouse warehouse.CloudWarehouse
	err := c.ShouldBindJSON(&cloudWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseService.UpdateCloudWarehouse(cloudWarehouse); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// CloudWareHouseAddProducts 向库存中直接添加商品
// @Tags CloudWarehouse
// @Summary 向云仓库存中直接添加商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CloudWarehouseProductAddReq true "添加商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /cloudWarehouse/cloudWareHouseAddProducts [post]
func (cloudWarehouseApi *CloudWarehouseApi) CloudWareHouseAddProducts(c *gin.Context) {
	var req warehouseReq.CloudWarehouseProductAddReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"FranchiseId":       {utils.NotEmpty()},
		"Products":          {utils.NotEmpty()},
		"CloudBatchOrderNo": {utils.NotEmpty()},
	}
	if err := utils.Verify(req, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseService.CloudWareHouseAddProducts(&req, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("添加失败!", zap.Error(err))
		response.FailWithMessage("添加失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("添加成功", c)
	}
}

// FindCloudWarehouse 用id查询CloudWarehouse
// @Tags CloudWarehouse
// @Summary 用id查询CloudWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouse.CloudWarehouse true "用id查询CloudWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /cloudWarehouse/findCloudWarehouse [get]
func (cloudWarehouseApi *CloudWarehouseApi) FindCloudWarehouse(c *gin.Context) {
	var cloudWarehouse warehouse.CloudWarehouse
	err := c.ShouldBindQuery(&cloudWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if recloudWarehouse, err := cloudWarehouseService.GetCloudWarehouse(cloudWarehouse.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"recloudWarehouse": recloudWarehouse}, c)
	}
}

// GetCloudWarehouseList 分页获取CloudWarehouse列表
// @Tags CloudWarehouse
// @Summary 分页获取CloudWarehouse列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.CloudWarehouseSearch true "分页获取CloudWarehouse列表"
// @Success 200 {object} response.PageResult{list=[]response.CloudWarehousePagination}
// @Router /cloudWarehouse/getCloudWarehouseList [get]
func (cloudWarehouseApi *CloudWarehouseApi) GetCloudWarehouseList(c *gin.Context) {
	var pageInfo warehouseReq.CloudWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := cloudWarehouseService.GetCloudWarehouseInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetFranchiseeCloudWarehouseProductList 加盟商云仓商品列表
// @Tags CloudWarehouse
// @Summary 加盟商云仓商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.FranchiseeCloudWarehouseProductListSearch true "加盟商云仓商品列表"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeCloudWarehouseProductList}
// @Router /cloudWarehouse/getFranchiseeCloudWarehouseProductList [get]
func (cloudWarehouseApi *CloudWarehouseApi) GetFranchiseeCloudWarehouseProductList(c *gin.Context) {
	var req warehouseReq.FranchiseeCloudWarehouseProductListSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := cloudWarehouseService.GetCloudWarehouseProductList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetCloudWarehouseStatistics 云仓统计
// @Tags CloudWarehouse
// @Summary 云仓统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.CloudWarehouseStatisticsSearch true "云仓统计"
// @Success 200 {object} response.CloudWarehouseStatistics
// @Router /cloudWarehouse/getCloudWarehouseStatistics [get]
func (cloudWarehouseApi *CloudWarehouseApi) GetCloudWarehouseStatistics(c *gin.Context) {
	var req warehouseReq.CloudWarehouseStatisticsSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := cloudWarehouseService.GetCloudWarehouseStatistics(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}

// UpdateCloudWarehouseInventory 修改云仓已有商品库存
// @Tags CloudWarehouse
// @Summary 修改云仓已有商品库存
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body warehouseReq.UpdateCloudWarehouseInventoryReq  true "修改云仓库存"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /cloudWarehouse/updateCloudWarehouseInventory [post]
func (cloudWarehouseApi *CloudWarehouseApi) UpdateCloudWarehouseInventory(c *gin.Context) {
	var req warehouseReq.UpdateCloudWarehouseInventoryReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cloudWarehouseService.UpdateCloudWarehouseInventory(req, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("修改失败!,"+err.Error(), zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// ExportCloudWarehouse 云仓总体导出
// @Tags CloudWarehouse
// @Summary 云仓总体导出
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.CloudWarehouseSearch true "云仓总体导出"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"云仓导出成功"}"
// @Router /cloudWarehouse/exportCloudWarehouse [get]
func (cloudWarehouseApi *CloudWarehouseApi) ExportCloudWarehouse(c *gin.Context) {
	var req warehouseReq.CloudWarehouseSearch
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	fileName := "云仓导出.xlsx"
	// 设置响应头
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	f, err := cloudWarehouseService.ExportCloudWarehouse(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		c.Abort()
		return
	}
	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}
