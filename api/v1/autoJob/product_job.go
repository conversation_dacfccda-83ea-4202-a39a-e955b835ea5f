package autoJob

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/autoJob"
	autoJobReq "github.com/OSQianXing/guanpu-server/model/autoJob/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductJobApi struct {
}

var productJobService = service.ServiceGroupApp.AutoJobServiceGroup.ProductJobService

// GetProductAutoJobByProductId 获取ProductAutoJob
// @Tags ProductJob
// @Summary 获取ProductAutoJob
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body autoJob.ProductJob true "获取ProductAutoJob"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
func (productJobApi *ProductJobApi) GetProductAutoJobByProductId(c *gin.Context) {
	var job autoJobReq.ProductAutoOnSaleJob
	err := c.ShouldBindQuery(&job)
	global.GVA_LOG.Info("ProductId", zap.Any("ProductId", job.ProductId))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"ProductId": {utils.NotEmpty()},
	}
	if err := utils.Verify(job, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproductJob, err := productJobService.GetProductJobInfoByProductId(job.ProductId); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		response.OkWithData(gin.H{"reproductJob": reproductJob}, c)
	}
}

// SetProductJobByProductId 创建ProductJob

// Create ProductJob for productId
func (ProductJobApi *ProductJobApi) SetProductJobByProductId(c *gin.Context) {
	var productAutoOnSaleJob autoJobReq.ProductAutoOnSaleJob
	err := c.ShouldBindJSON(&productAutoOnSaleJob)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productJobService.SetAutoOnOffSaleJob(productAutoOnSaleJob.ProductOnSaleStart, productAutoOnSaleJob.ProductOnSaleEnd, int(productAutoOnSaleJob.ProductId)); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// CreateProductJob 创建ProductJob
// @Tags ProductJob
// @Summary 创建ProductJob
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body autoJob.ProductJob true "创建ProductJob"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productJob/createProductJob [post]
func (productJobApi *ProductJobApi) CreateProductJob(c *gin.Context) {
	var productJob autoJob.ProductJob
	err := c.ShouldBindJSON(&productJob)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productJobService.CreateProductJob(&productJob); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteProductJob 删除ProductJob
// @Tags ProductJob
// @Summary 删除ProductJob
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body autoJob.ProductJob true "删除ProductJob"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productJob/deleteProductJob [delete]
func (productJobApi *ProductJobApi) DeleteProductJob(c *gin.Context) {
	var productJob autoJob.ProductJob
	err := c.ShouldBindJSON(&productJob)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productJobService.DeleteProductJob(productJob); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteProductJobByIds 批量删除ProductJob
// @Tags ProductJob
// @Summary 批量删除ProductJob
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ProductJob"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /productJob/deleteProductJobByIds [delete]
func (productJobApi *ProductJobApi) DeleteProductJobByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productJobService.DeleteProductJobByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateProductJob 更新ProductJob
// @Tags ProductJob
// @Summary 更新ProductJob
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body autoJob.ProductJob true "更新ProductJob"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /productJob/updateProductJob [put]
func (productJobApi *ProductJobApi) UpdateProductJob(c *gin.Context) {
	var productJob autoJob.ProductJob
	err := c.ShouldBindJSON(&productJob)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productJobService.UpdateProductJob(productJob); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindProductJob 用id查询ProductJob
// @Tags ProductJob
// @Summary 用id查询ProductJob
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query autoJob.ProductJob true "用id查询ProductJob"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /productJob/findProductJob [get]
func (productJobApi *ProductJobApi) FindProductJob(c *gin.Context) {
	var productJob autoJob.ProductJob
	err := c.ShouldBindQuery(&productJob)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproductJob, err := productJobService.GetProductJob(productJob.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproductJob": reproductJob}, c)
	}
}

// GetProductJobList 分页获取ProductJob列表
// @Tags ProductJob
// @Summary 分页获取ProductJob列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query autoJobReq.ProductJobSearch true "分页获取ProductJob列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productJob/getProductJobList [get]
func (productJobApi *ProductJobApi) GetProductJobList(c *gin.Context) {
	var pageInfo autoJobReq.ProductJobSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productJobService.GetProductJobInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
