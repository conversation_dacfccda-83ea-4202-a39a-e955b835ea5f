package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseManagerApi struct {
}

var bigWarehouseManagerService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseManagerService

// CreateBigWarehouseManager 创建BigWarehouseManager
// @Tags BigWarehouseManager
// @Summary 创建BigWarehouseManager
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseManager true "创建BigWarehouseManager"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseManager/createBigWarehouseManager [post]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) CreateBigWarehouseManager(c *gin.Context) {
	var bigWarehouseManager bigwarehouse.BigWarehouseManager
	err := c.ShouldBindJSON(&bigWarehouseManager)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouseManager, utils.BigWarehouseManagerCreateVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseManagerService.CreateBigWarehouseManager(&bigWarehouseManager); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteBigWarehouseManager 删除BigWarehouseManager
// @Tags BigWarehouseManager
// @Summary 删除BigWarehouseManager
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseManager true "删除BigWarehouseManager"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseManager/deleteBigWarehouseManager [delete]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) DeleteBigWarehouseManager(c *gin.Context) {
	var bigWarehouseManager bigwarehouse.BigWarehouseManager
	err := c.ShouldBindJSON(&bigWarehouseManager)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseManagerService.DeleteBigWarehouseManager(bigWarehouseManager); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteBigWarehouseManagerByIds 批量删除BigWarehouseManager
// @Tags BigWarehouseManager
// @Summary 批量删除BigWarehouseManager
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseManager"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseManager/deleteBigWarehouseManagerByIds [delete]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) DeleteBigWarehouseManagerByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseManagerService.DeleteBigWarehouseManagerByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateBigWarehouseManager 更新BigWarehouseManager
// @Tags BigWarehouseManager
// @Summary 更新BigWarehouseManager
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseManager true "更新BigWarehouseManager"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseManager/updateBigWarehouseManager [put]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) UpdateBigWarehouseManager(c *gin.Context) {
	var bigWarehouseManager bigwarehouse.BigWarehouseManager
	err := c.ShouldBindJSON(&bigWarehouseManager)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouseManager, utils.BigWarehouseManagerCreateVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseManagerService.UpdateBigWarehouseManager(bigWarehouseManager); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseManager 用id查询BigWarehouseManager
// @Tags BigWarehouseManager
// @Summary 用id查询BigWarehouseManager
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "用id查询BigWarehouseManager"
// @Success 200 {object} response.Response{data=response.BigWarehouseManagerRes,msg=string}
// @Router /bigWarehouseManager/findBigWarehouseManager [get]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) FindBigWarehouseManager(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rebigWarehouseManager, err := bigWarehouseManagerService.GetBigWarehouseManager(uint(req.ID)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouseManager": rebigWarehouseManager}, c)
	}
}

// GetBigWarehouseManagerList 分页获取BigWarehouseManager列表
// @Tags BigWarehouseManager
// @Summary 分页获取BigWarehouseManager列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.BigWarehouseManagerSearch true "分页获取BigWarehouseManager列表"
// @Success 200 {object}  response.PageResult{data=[]response.BigWarehouseManagerRes,total=int64}
// @Router /bigWarehouseManager/getBigWarehouseManagerList [get]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) GetBigWarehouseManagerList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseManagerSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.UserId == nil {
		userInfo := utils.GetUserInfo(c)
		if userInfo.AuthorityId == uint(types.KuFangRoleAuthorityId) {
			pageInfo.UserId = &userInfo.BaseClaims.ID
		}
	}
	if list, total, err := bigWarehouseManagerService.GetBigWarehouseManagerInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// SetBigWarehouseManager 设置仓库管理员
// @Tags BigWarehouseManager
// @Summary 设置仓库管理员
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.BigWarehouseManagerSet true "设置仓库管理员"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"设置成功"}"
// @Router /bigWarehouseManager/setBigWarehouseManager [post]
func (bigWarehouseManagerApi *BigWarehouseManagerApi) SetBigWarehouseManager(c *gin.Context) {
	var bigWarehouseManager bigwarehouseReq.BigWarehouseManagerSet
	err := c.ShouldBindJSON(&bigWarehouseManager)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouseManager, utils.BigWarehouseManagerSetVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseManagerService.SetBigWarehouseManager(bigWarehouseManager); err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败"+err.Error(), c)
	} else {
		response.OkWithMessage("设置成功", c)
	}

}
