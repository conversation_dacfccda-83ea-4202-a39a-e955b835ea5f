package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseStockApi struct {
}

var bigWarehouseStockService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseStockService

// CreateBigWarehouseStock 创建BigWarehouseStock
// @Tags BigWarehouseStock
// @Summary 创建BigWarehouseStock
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseStock true "创建BigWarehouseStock"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseStock/createBigWarehouseStock [post]
func (bigWarehouseStockApi *BigWarehouseStockApi) CreateBigWarehouseStock(c *gin.Context) {
	var bigWarehouseStock bigwarehouse.BigWarehouseStock
	err := c.ShouldBindJSON(&bigWarehouseStock)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseStockService.CreateBigWarehouseStock(&bigWarehouseStock); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteBigWarehouseStock 删除BigWarehouseStock
// @Tags BigWarehouseStock
// @Summary 删除BigWarehouseStock
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseStock true "删除BigWarehouseStock"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseStock/deleteBigWarehouseStock [delete]
func (bigWarehouseStockApi *BigWarehouseStockApi) DeleteBigWarehouseStock(c *gin.Context) {
	var bigWarehouseStock bigwarehouse.BigWarehouseStock
	err := c.ShouldBindJSON(&bigWarehouseStock)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseStockService.DeleteBigWarehouseStock(bigWarehouseStock); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteBigWarehouseStockByIds 批量删除BigWarehouseStock
// @Tags BigWarehouseStock
// @Summary 批量删除BigWarehouseStock
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseStock"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseStock/deleteBigWarehouseStockByIds [delete]
func (bigWarehouseStockApi *BigWarehouseStockApi) DeleteBigWarehouseStockByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseStockService.DeleteBigWarehouseStockByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateBigWarehouseStock 更新BigWarehouseStock
// @Tags BigWarehouseStock
// @Summary 更新BigWarehouseStock
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseStock true "更新BigWarehouseStock"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseStock/updateBigWarehouseStock [put]
func (bigWarehouseStockApi *BigWarehouseStockApi) UpdateBigWarehouseStock(c *gin.Context) {
	var bigWarehouseStock bigwarehouse.BigWarehouseStock
	err := c.ShouldBindJSON(&bigWarehouseStock)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseStockService.UpdateBigWarehouseStock(bigWarehouseStock); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseStock 用id查询BigWarehouseStock
// @Tags BigWarehouseStock
// @Summary 用id查询BigWarehouseStock
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouse.BigWarehouseStock true "用id查询BigWarehouseStock"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bigWarehouseStock/findBigWarehouseStock [get]
func (bigWarehouseStockApi *BigWarehouseStockApi) FindBigWarehouseStock(c *gin.Context) {
	var bigWarehouseStock bigwarehouse.BigWarehouseStock
	err := c.ShouldBindQuery(&bigWarehouseStock)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rebigWarehouseStock, err := bigWarehouseStockService.GetBigWarehouseStock(bigWarehouseStock.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouseStock": rebigWarehouseStock}, c)
	}
}

// GetBigWarehouseStockList 分页获取BigWarehouseStock列表
// @Tags BigWarehouseStock
// @Summary 分页获取BigWarehouseStock列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouseReq.BigWarehouseStockSearch true "分页获取BigWarehouseStock列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseStock/getBigWarehouseStockList [get]
func (bigWarehouseStockApi *BigWarehouseStockApi) GetBigWarehouseStockList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseStockSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := bigWarehouseStockService.GetBigWarehouseStockInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
