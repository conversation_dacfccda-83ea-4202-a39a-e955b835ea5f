package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseApi struct {
}

var bigWarehouseService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseService

// CreateBigWarehouse 创建BigWarehouse
// @Tags BigWarehouse
// @Summary 创建BigWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouse true "创建BigWarehouse"
// @Success 200 {object} response.Response{rebigWarehouse=bigwarehouse.BigWarehouse,msg=string}
// @Router /bigWarehouse/createBigWarehouse [post]
func (bigWarehouseApi *BigWarehouseApi) CreateBigWarehouse(c *gin.Context) {
	var bigWarehouse bigwarehouse.BigWarehouse
	err := c.ShouldBindJSON(&bigWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouse, utils.BigWarehouseVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if re, err := bigWarehouseService.CreateBigWarehouse(&bigWarehouse); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouse": re}, c)
	}
}

// DeleteBigWarehouse 删除BigWarehouse
// @Tags BigWarehouse
// @Summary 删除BigWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouse true "删除BigWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouse/deleteBigWarehouse [delete]
func (bigWarehouseApi *BigWarehouseApi) DeleteBigWarehouse(c *gin.Context) {
	var bigWarehouse bigwarehouse.BigWarehouse
	err := c.ShouldBindJSON(&bigWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseService.DeleteBigWarehouse(bigWarehouse); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

//// DeleteBigWarehouseByIds 批量删除BigWarehouse
//// @Tags BigWarehouse
//// @Summary 批量删除BigWarehouse
//// @Security ApiKeyAuth
//// @accept application/json
//// @Produce application/json
//// @Param data body request.IdsReq true "批量删除BigWarehouse"
//// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
//// @Router /bigWarehouse/deleteBigWarehouseByIds [delete]
//func (bigWarehouseApi *BigWarehouseApi) DeleteBigWarehouseByIds(c *gin.Context) {
//	var IDS request.IdsReq
//	err := c.ShouldBindJSON(&IDS)
//	if err != nil {
//		response.FailWithMessage(err.Error(), c)
//		return
//	}
//	if err := bigWarehouseService.DeleteBigWarehouseByIds(IDS); err != nil {
//		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
//		response.FailWithMessage("批量删除失败", c)
//	} else {
//		response.OkWithMessage("批量删除成功", c)
//	}
//}

// UpdateBigWarehouse 更新BigWarehouse
// @Tags BigWarehouse
// @Summary 更新BigWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouse true "更新BigWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouse/updateBigWarehouse [put]
func (bigWarehouseApi *BigWarehouseApi) UpdateBigWarehouse(c *gin.Context) {
	var bigWarehouse bigwarehouse.BigWarehouse
	err := c.ShouldBindJSON(&bigWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouse, utils.BigWarehouseVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseService.UpdateBigWarehouse(bigWarehouse); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouse 用id查询BigWarehouse
// @Tags BigWarehouse
// @Summary 用id查询BigWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "用id查询BigWarehouse"
// @Success 200 {object} response.Response{data=response.BigWarehouseRes,msg=string}
// @Router /bigWarehouse/findBigWarehouse [get]
func (bigWarehouseApi *BigWarehouseApi) FindBigWarehouse(c *gin.Context) {
	var idReq request.GetById
	err := c.ShouldBindQuery(&idReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if rebigWarehouse, err := bigWarehouseService.GetBigWarehouse(uint(idReq.ID)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouse": rebigWarehouse}, c)
	}
}

// GetBigWarehouseList 分页获取BigWarehouse列表
// @Tags BigWarehouse
// @Summary 分页获取BigWarehouse列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.BigWarehouseSearch true "分页获取BigWarehouse列表"
// @Success 200 {object} response.PageResult{data=[]response.BigWarehouseRes,total=int64,page=int,pageSize=int}
// @Router /bigWarehouse/getBigWarehouseList [get]
func (bigWarehouseApi *BigWarehouseApi) GetBigWarehouseList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := bigWarehouseService.GetBigWarehouseInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
