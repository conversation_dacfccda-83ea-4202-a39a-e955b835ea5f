package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseOrderApi struct {
}

var bigWarehouseOrderService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseOrderService

// CreateBigWarehouseOrder 创建BigWarehouseOrder
// @Tags BigWarehouseOrder
// @Summary 创建BigWarehouseOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseOrder true "创建BigWarehouseOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseOrder/createBigWarehouseOrder [post]
func (bigWarehouseOrderApi *BigWarehouseOrderApi) CreateBigWarehouseOrder(c *gin.Context) {
	var bigWarehouseOrder bigwarehouse.BigWarehouseOrder
	err := c.ShouldBindJSON(&bigWarehouseOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if _, err := bigWarehouseOrderService.CreateBigWarehouseOrder(&bigWarehouseOrder); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteBigWarehouseOrder 删除BigWarehouseOrder
// @Tags BigWarehouseOrder
// @Summary 删除BigWarehouseOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseOrder true "删除BigWarehouseOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseOrder/deleteBigWarehouseOrder [delete]
func (bigWarehouseOrderApi *BigWarehouseOrderApi) DeleteBigWarehouseOrder(c *gin.Context) {
	var bigWarehouseOrder bigwarehouse.BigWarehouseOrder
	err := c.ShouldBindJSON(&bigWarehouseOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseOrderService.DeleteBigWarehouseOrder(bigWarehouseOrder); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteBigWarehouseOrderByIds 批量删除BigWarehouseOrder
// @Tags BigWarehouseOrder
// @Summary 批量删除BigWarehouseOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseOrder/deleteBigWarehouseOrderByIds [delete]
func (bigWarehouseOrderApi *BigWarehouseOrderApi) DeleteBigWarehouseOrderByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseOrderService.DeleteBigWarehouseOrderByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateBigWarehouseOrder 更新BigWarehouseOrder
// @Tags BigWarehouseOrder
// @Summary 更新BigWarehouseOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseOrder true "更新BigWarehouseOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseOrder/updateBigWarehouseOrder [put]
func (bigWarehouseOrderApi *BigWarehouseOrderApi) UpdateBigWarehouseOrder(c *gin.Context) {
	var bigWarehouseOrder bigwarehouse.BigWarehouseOrder
	err := c.ShouldBindJSON(&bigWarehouseOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseOrderService.UpdateBigWarehouseOrder(bigWarehouseOrder); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseOrder 用id查询BigWarehouseOrder
// @Tags BigWarehouseOrder
// @Summary 用id查询BigWarehouseOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouse.BigWarehouseOrder true "用id查询BigWarehouseOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bigWarehouseOrder/findBigWarehouseOrder [get]
func (bigWarehouseOrderApi *BigWarehouseOrderApi) FindBigWarehouseOrder(c *gin.Context) {
	var bigWarehouseOrder bigwarehouse.BigWarehouseOrder
	err := c.ShouldBindQuery(&bigWarehouseOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rebigWarehouseOrder, err := bigWarehouseOrderService.GetBigWarehouseOrder(bigWarehouseOrder.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouseOrder": rebigWarehouseOrder}, c)
	}
}

// GetBigWarehouseOrderList 分页获取BigWarehouseOrder列表
// @Tags BigWarehouseOrder
// @Summary 分页获取BigWarehouseOrder列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouseReq.BigWarehouseOrderSearch true "分页获取BigWarehouseOrder列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseOrder/getBigWarehouseOrderList [get]
//func (bigWarehouseOrderApi *BigWarehouseOrderApi) GetBigWarehouseOrderList(c *gin.Context) {
//	var pageInfo bigwarehouseReq.BigWarehouseOrderSearch
//	err := c.ShouldBindQuery(&pageInfo)
//	if err != nil {
//		response.FailWithMessage(err.Error(), c)
//		return
//	}
//	if list, total, err := bigWarehouseOrderService.GetBigWarehouseOrderInfoList(pageInfo); err != nil {
//		global.GVA_LOG.Error("获取失败!", zap.Error(err))
//		response.FailWithMessage("获取失败", c)
//	} else {
//		response.OkWithDetailed(response.PageResult{
//			List:     list,
//			Total:    total,
//			Page:     pageInfo.Page,
//			PageSize: pageInfo.PageSize,
//		}, "获取成功", c)
//	}
//}
