package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseRecordApi struct {
}

var bigWarehouseRecordService = service.ServiceGroupApp.BigWarehouseServiceGroup.BigWarehouseRecordService

// CreateBigWarehouseRecord 创建BigWarehouseRecord
// @Tags BigWarehouseRecord
// @Summary 创建BigWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseRecord true "创建BigWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseRecord/createBigWarehouseRecord [post]
func (bigWarehouseRecordApi *BigWarehouseRecordApi) CreateBigWarehouseRecord(c *gin.Context) {
	var bigWarehouseRecord bigwarehouse.BigWarehouseRecord
	err := c.ShouldBindJSON(&bigWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	bigWarehouseRecord.CreatedBy = utils.GetUserID(c)
	if err := bigWarehouseRecordService.CreateBigWarehouseRecord(&bigWarehouseRecord); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteBigWarehouseRecord 删除BigWarehouseRecord
// @Tags BigWarehouseRecord
// @Summary 删除BigWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseRecord true "删除BigWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseRecord/deleteBigWarehouseRecord [delete]
func (bigWarehouseRecordApi *BigWarehouseRecordApi) DeleteBigWarehouseRecord(c *gin.Context) {
	var bigWarehouseRecord bigwarehouse.BigWarehouseRecord
	err := c.ShouldBindJSON(&bigWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	bigWarehouseRecord.DeletedBy = utils.GetUserID(c)
	if err := bigWarehouseRecordService.DeleteBigWarehouseRecord(bigWarehouseRecord); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteBigWarehouseRecordByIds 批量删除BigWarehouseRecord
// @Tags BigWarehouseRecord
// @Summary 批量删除BigWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseRecord/deleteBigWarehouseRecordByIds [delete]
func (bigWarehouseRecordApi *BigWarehouseRecordApi) DeleteBigWarehouseRecordByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	deletedBy := utils.GetUserID(c)
	if err := bigWarehouseRecordService.DeleteBigWarehouseRecordByIds(IDS, deletedBy); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateBigWarehouseRecord 更新BigWarehouseRecord
// @Tags BigWarehouseRecord
// @Summary 更新BigWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseRecord true "更新BigWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseRecord/updateBigWarehouseRecord [put]
func (bigWarehouseRecordApi *BigWarehouseRecordApi) UpdateBigWarehouseRecord(c *gin.Context) {
	var bigWarehouseRecord bigwarehouse.BigWarehouseRecord
	err := c.ShouldBindJSON(&bigWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	bigWarehouseRecord.UpdatedBy = utils.GetUserID(c)
	if err := bigWarehouseRecordService.UpdateBigWarehouseRecord(bigWarehouseRecord); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseRecord 用id查询BigWarehouseRecord
// @Tags BigWarehouseRecord
// @Summary 用id查询BigWarehouseRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouse.BigWarehouseRecord true "用id查询BigWarehouseRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bigWarehouseRecord/findBigWarehouseRecord [get]
func (bigWarehouseRecordApi *BigWarehouseRecordApi) FindBigWarehouseRecord(c *gin.Context) {
	var bigWarehouseRecord bigwarehouse.BigWarehouseRecord
	err := c.ShouldBindQuery(&bigWarehouseRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rebigWarehouseRecord, err := bigWarehouseRecordService.GetBigWarehouseRecord(bigWarehouseRecord.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouseRecord": rebigWarehouseRecord}, c)
	}
}

// GetBigWarehouseRecordList 分页获取BigWarehouseRecord列表
// @Tags BigWarehouseRecord
// @Summary 分页获取BigWarehouseRecord列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouseReq.BigWarehouseRecordSearch true "分页获取BigWarehouseRecord列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseRecord/getBigWarehouseRecordList [get]
func (bigWarehouseRecordApi *BigWarehouseRecordApi) GetBigWarehouseRecordList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := bigWarehouseRecordService.GetBigWarehouseRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
