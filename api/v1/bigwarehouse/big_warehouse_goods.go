package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseGoodsApi struct {
}

var bigWarehouseGoodsService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseGoodsService

// CreateBigWarehouseGoods 创建BigWarehouseGoods
// @Tags BigWarehouseGoods
// @Summary 创建BigWarehouseGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseGoods true "创建BigWarehouseGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseGoods/createBigWarehouseGoods [post]
func (bigWarehouseGoodsApi *BigWarehouseGoodsApi) CreateBigWarehouseGoods(c *gin.Context) {
	var bigWarehouseGoods bigwarehouse.BigWarehouseGoods
	err := c.ShouldBindJSON(&bigWarehouseGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseGoodsService.CreateBigWarehouseGoods(&bigWarehouseGoods); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteBigWarehouseGoods 删除BigWarehouseGoods
// @Tags BigWarehouseGoods
// @Summary 删除BigWarehouseGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseGoods true "删除BigWarehouseGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseGoods/deleteBigWarehouseGoods [delete]
func (bigWarehouseGoodsApi *BigWarehouseGoodsApi) DeleteBigWarehouseGoods(c *gin.Context) {
	var bigWarehouseGoods bigwarehouse.BigWarehouseGoods
	err := c.ShouldBindJSON(&bigWarehouseGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseGoodsService.DeleteBigWarehouseGoods(bigWarehouseGoods); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteBigWarehouseGoodsByIds 批量删除BigWarehouseGoods
// @Tags BigWarehouseGoods
// @Summary 批量删除BigWarehouseGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseGoods/deleteBigWarehouseGoodsByIds [delete]
func (bigWarehouseGoodsApi *BigWarehouseGoodsApi) DeleteBigWarehouseGoodsByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseGoodsService.DeleteBigWarehouseGoodsByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateBigWarehouseGoods 更新BigWarehouseGoods
// @Tags BigWarehouseGoods
// @Summary 更新BigWarehouseGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseGoods true "更新BigWarehouseGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseGoods/updateBigWarehouseGoods [put]
func (bigWarehouseGoodsApi *BigWarehouseGoodsApi) UpdateBigWarehouseGoods(c *gin.Context) {
	var bigWarehouseGoods bigwarehouse.BigWarehouseGoods
	err := c.ShouldBindJSON(&bigWarehouseGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseGoodsService.UpdateBigWarehouseGoods(bigWarehouseGoods); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseGoods 用id查询BigWarehouseGoods
// @Tags BigWarehouseGoods
// @Summary 用id查询BigWarehouseGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouse.BigWarehouseGoods true "用id查询BigWarehouseGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bigWarehouseGoods/findBigWarehouseGoods [get]
func (bigWarehouseGoodsApi *BigWarehouseGoodsApi) FindBigWarehouseGoods(c *gin.Context) {
	var bigWarehouseGoods bigwarehouse.BigWarehouseGoods
	err := c.ShouldBindQuery(&bigWarehouseGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rebigWarehouseGoods, err := bigWarehouseGoodsService.GetBigWarehouseGoods(bigWarehouseGoods.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouseGoods": rebigWarehouseGoods}, c)
	}
}

// GetBigWarehouseGoodsList 分页获取BigWarehouseGoods列表
// @Tags BigWarehouseGoods
// @Summary 分页获取BigWarehouseGoods列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouseReq.BigWarehouseGoodsSearch true "分页获取BigWarehouseGoods列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseGoods/getBigWarehouseGoodsList [get]
func (bigWarehouseGoodsApi *BigWarehouseGoodsApi) GetBigWarehouseGoodsList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseGoodsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := bigWarehouseGoodsService.GetBigWarehouseGoodsInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
