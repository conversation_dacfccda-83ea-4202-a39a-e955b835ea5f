package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseCoverageApi struct {
}

var bigWarehouseCoverageService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseCoverageService

// CreateBigWarehouseCoverage 创建BigWarehouseCoverage
// @Tags BigWarehouseCoverage
// @Summary 创建BigWarehouseCoverage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseCoverage true "创建BigWarehouseCoverage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseCoverage/createBigWarehouseCoverage [post]
//func (bigWarehouseCoverageApi *BigWarehouseCoverageApi) CreateBigWarehouseCoverage(c *gin.Context) {
//	var bigWarehouseCoverage bigwarehouse.BigWarehouseCoverage
//	err := c.ShouldBindJSON(&bigWarehouseCoverage)
//	if err != nil {
//		response.FailWithMessage(err.Error(), c)
//		return
//	}
//	if err := bigWarehouseCoverageService.CreateBigWarehouseCoverage(&bigWarehouseCoverage); err != nil {
//		global.GVA_LOG.Error("创建失败!", zap.Error(err))
//		response.FailWithMessage("创建失败", c)
//	} else {
//		response.OkWithMessage("创建成功", c)
//	}
//}

// DeleteBigWarehouseCoverage 删除BigWarehouseCoverage
// @Tags BigWarehouseCoverage
// @Summary 删除BigWarehouseCoverage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseCoverage true "删除BigWarehouseCoverage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseCoverage/deleteBigWarehouseCoverage [delete]
//func (bigWarehouseCoverageApi *BigWarehouseCoverageApi) DeleteBigWarehouseCoverage(c *gin.Context) {
//	var bigWarehouseCoverage bigwarehouse.BigWarehouseCoverage
//	err := c.ShouldBindJSON(&bigWarehouseCoverage)
//	if err != nil {
//		response.FailWithMessage(err.Error(), c)
//		return
//	}
//	if err := bigWarehouseCoverageService.DeleteBigWarehouseCoverage(bigWarehouseCoverage); err != nil {
//		global.GVA_LOG.Error("删除失败!", zap.Error(err))
//		response.FailWithMessage("删除失败", c)
//	} else {
//		response.OkWithMessage("删除成功", c)
//	}
//}

// DeleteBigWarehouseCoverageByIds 批量删除BigWarehouseCoverage
// @Tags BigWarehouseCoverage
// @Summary 批量删除BigWarehouseCoverage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseCoverage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseCoverage/deleteBigWarehouseCoverageByIds [delete]
//func (bigWarehouseCoverageApi *BigWarehouseCoverageApi) DeleteBigWarehouseCoverageByIds(c *gin.Context) {
//	var IDS request.IdsReq
//	err := c.ShouldBindJSON(&IDS)
//	if err != nil {
//		response.FailWithMessage(err.Error(), c)
//		return
//	}
//	if err := bigWarehouseCoverageService.DeleteBigWarehouseCoverageByIds(IDS); err != nil {
//		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
//		response.FailWithMessage("批量删除失败", c)
//	} else {
//		response.OkWithMessage("批量删除成功", c)
//	}
//}

// UpdateBigWarehouseCoverage 更新BigWarehouseCoverage
// @Tags BigWarehouseCoverage
// @Summary 更新BigWarehouseCoverage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CoverageRequest true "更新BigWarehouseCoverage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseCoverage/updateBigWarehouseCoverage [put]
func (bigWarehouseCoverageApi *BigWarehouseCoverageApi) UpdateBigWarehouseCoverage(c *gin.Context) {
	var bigWarehouseCoverage bigwarehouseReq.CoverageRequest
	err := c.ShouldBindJSON(&bigWarehouseCoverage)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = utils.Verify(bigWarehouseCoverage, utils.BigWarehouseCoverageVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseCoverageService.SetBigWarehouseCoverage(bigWarehouseCoverage); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseCoverage 用id查询BigWarehouseCoverage
// @Tags BigWarehouseCoverage
// @Summary 用id查询BigWarehouseCoverage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouse.BigWarehouseCoverage true "用id查询BigWarehouseCoverage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bigWarehouseCoverage/findBigWarehouseCoverage [get]
//func (bigWarehouseCoverageApi *BigWarehouseCoverageApi) FindBigWarehouseCoverage(c *gin.Context) {
//	var bigWarehouseCoverage bigwarehouse.BigWarehouseCoverage
//	err := c.ShouldBindQuery(&bigWarehouseCoverage)
//	if err != nil {
//		response.FailWithMessage(err.Error(), c)
//		return
//	}
//	if rebigWarehouseCoverage, err := bigWarehouseCoverageService.GetBigWarehouseCoverage(bigWarehouseCoverage.ID); err != nil {
//		global.GVA_LOG.Error("查询失败!", zap.Error(err))
//		response.FailWithMessage("查询失败", c)
//	} else {
//		response.OkWithData(gin.H{"rebigWarehouseCoverage": rebigWarehouseCoverage}, c)
//	}
//}

// GetBigWarehouseCoverageList 分页获取BigWarehouseCoverage列表
// @Tags BigWarehouseCoverage
// @Summary 分页获取BigWarehouseCoverage列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouseReq.BigWarehouseCoverageSearch true "分页获取BigWarehouseCoverage列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseCoverage/getBigWarehouseCoverageList [get]
func (bigWarehouseCoverageApi *BigWarehouseCoverageApi) GetBigWarehouseCoverageList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseCoverageSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := bigWarehouseCoverageService.GetBigWarehouseCoverageInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
