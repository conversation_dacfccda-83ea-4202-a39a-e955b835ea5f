package bigwarehouse

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type BigWarehouseAllocationApi struct {
}

var bigWarehouseAllocationService = service.ServiceGroupApp.BigWarehouseServiceGroup.WarehouseAllocationService

// CreateBigWarehouseAllocation 创建BigWarehouseAllocation
// @Tags BigWarehouseAllocation
// @Summary 创建BigWarehouseAllocation
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseAllocation true "创建BigWarehouseAllocation"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /bigWarehouseAllocation/createBigWarehouseAllocation [post]
func (bigWarehouseAllocationApi *BigWarehouseAllocationApi) CreateBigWarehouseAllocation(c *gin.Context) {
	var bigWarehouseAllocation bigwarehouse.BigWarehouseAllocation
	err := c.ShouldBindJSON(&bigWarehouseAllocation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouseAllocation, utils.BigWarehouseAllocationVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseAllocationService.CreateBigWarehouseAllocation(&bigWarehouseAllocation); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteBigWarehouseAllocation 删除BigWarehouseAllocation
// @Tags BigWarehouseAllocation
// @Summary 删除BigWarehouseAllocation
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseAllocation true "删除BigWarehouseAllocation"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /bigWarehouseAllocation/deleteBigWarehouseAllocation [delete]
func (bigWarehouseAllocationApi *BigWarehouseAllocationApi) DeleteBigWarehouseAllocation(c *gin.Context) {
	var bigWarehouseAllocation bigwarehouse.BigWarehouseAllocation
	err := c.ShouldBindJSON(&bigWarehouseAllocation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseAllocationService.DeleteBigWarehouseAllocation(bigWarehouseAllocation); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteBigWarehouseAllocationByIds 批量删除BigWarehouseAllocation
// @Tags BigWarehouseAllocation
// @Summary 批量删除BigWarehouseAllocation
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除BigWarehouseAllocation"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /bigWarehouseAllocation/deleteBigWarehouseAllocationByIds [delete]
func (bigWarehouseAllocationApi *BigWarehouseAllocationApi) DeleteBigWarehouseAllocationByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseAllocationService.DeleteBigWarehouseAllocationByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateBigWarehouseAllocation 更新BigWarehouseAllocation
// @Tags BigWarehouseAllocation
// @Summary 更新BigWarehouseAllocation
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body bigwarehouse.BigWarehouseAllocation true "更新BigWarehouseAllocation"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /bigWarehouseAllocation/updateBigWarehouseAllocation [put]
func (bigWarehouseAllocationApi *BigWarehouseAllocationApi) UpdateBigWarehouseAllocation(c *gin.Context) {
	var bigWarehouseAllocation bigwarehouse.BigWarehouseAllocation
	err := c.ShouldBindJSON(&bigWarehouseAllocation)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(bigWarehouseAllocation, utils.BigWarehouseAllocationVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := bigWarehouseAllocationService.UpdateBigWarehouseAllocation(bigWarehouseAllocation); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindBigWarehouseAllocation 用id查询BigWarehouseAllocation
// @Tags BigWarehouseAllocation
// @Summary 用id查询BigWarehouseAllocation
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "用id查询BigWarehouseAllocation"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /bigWarehouseAllocation/findBigWarehouseAllocation [get]
func (bigWarehouseAllocationApi *BigWarehouseAllocationApi) FindBigWarehouseAllocation(c *gin.Context) {
	var idReq request.GetById
	err := c.ShouldBindQuery(&idReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rebigWarehouseAllocation, err := bigWarehouseAllocationService.GetBigWarehouseAllocation(uint(idReq.ID)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rebigWarehouseAllocation": rebigWarehouseAllocation}, c)
	}
}

// GetBigWarehouseAllocationList 分页获取BigWarehouseAllocation列表
// @Tags BigWarehouseAllocation
// @Summary 分页获取BigWarehouseAllocation列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query bigwarehouseReq.BigWarehouseAllocationSearch true "分页获取BigWarehouseAllocation列表"
// @Success 200 {object} response.PageResult{data=[]response.BigWarehouseAllocationRes,total=int64}
// @Router /bigWarehouseAllocation/getBigWarehouseAllocationList [get]
func (bigWarehouseAllocationApi *BigWarehouseAllocationApi) GetBigWarehouseAllocationList(c *gin.Context) {
	var pageInfo bigwarehouseReq.BigWarehouseAllocationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := bigWarehouseAllocationService.GetBigWarehouseAllocationInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
