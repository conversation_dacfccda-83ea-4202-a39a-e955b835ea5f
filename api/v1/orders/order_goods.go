package orders

import (
	"github.com/OSQianXing/guanpu-server/global"
    "github.com/OSQianXing/guanpu-server/model/orders"
    "github.com/OSQianXing/guanpu-server/model/common/request"
    ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
    "github.com/OSQianXing/guanpu-server/model/common/response"
    "github.com/OSQianXing/guanpu-server/service"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type OrderGoodsApi struct {
}

var orderGoodsService = service.ServiceGroupApp.OrdersServiceGroup.OrderGoodsService


// CreateOrderGoods 创建OrderGoods
// @Tags OrderGoods
// @Summary 创建OrderGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderGoods true "创建OrderGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderGoods/createOrderGoods [post]
func (orderGoodsApi *OrderGoodsApi) CreateOrderGoods(c *gin.Context) {
	var orderGoods orders.OrderGoods
	err := c.ShouldBindJSON(&orderGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderGoodsService.CreateOrderGoods(&orderGoods); err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteOrderGoods 删除OrderGoods
// @Tags OrderGoods
// @Summary 删除OrderGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderGoods true "删除OrderGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderGoods/deleteOrderGoods [delete]
func (orderGoodsApi *OrderGoodsApi) DeleteOrderGoods(c *gin.Context) {
	var orderGoods orders.OrderGoods
	err := c.ShouldBindJSON(&orderGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderGoodsService.DeleteOrderGoods(orderGoods); err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteOrderGoodsByIds 批量删除OrderGoods
// @Tags OrderGoods
// @Summary 批量删除OrderGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除OrderGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /orderGoods/deleteOrderGoodsByIds [delete]
func (orderGoodsApi *OrderGoodsApi) DeleteOrderGoodsByIds(c *gin.Context) {
	var IDS request.IdsReq
    err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderGoodsService.DeleteOrderGoodsByIds(IDS); err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateOrderGoods 更新OrderGoods
// @Tags OrderGoods
// @Summary 更新OrderGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderGoods true "更新OrderGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderGoods/updateOrderGoods [put]
func (orderGoodsApi *OrderGoodsApi) UpdateOrderGoods(c *gin.Context) {
	var orderGoods orders.OrderGoods
	err := c.ShouldBindJSON(&orderGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderGoodsService.UpdateOrderGoods(orderGoods); err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOrderGoods 用id查询OrderGoods
// @Tags OrderGoods
// @Summary 用id查询OrderGoods
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orders.OrderGoods true "用id查询OrderGoods"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /orderGoods/findOrderGoods [get]
func (orderGoodsApi *OrderGoodsApi) FindOrderGoods(c *gin.Context) {
	var orderGoods orders.OrderGoods
	err := c.ShouldBindQuery(&orderGoods)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reorderGoods, err := orderGoodsService.GetOrderGoods(orderGoods.ID); err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reorderGoods": reorderGoods}, c)
	}
}

// GetOrderGoodsList 分页获取OrderGoods列表
// @Tags OrderGoods
// @Summary 分页获取OrderGoods列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderGoodsSearch true "分页获取OrderGoods列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderGoods/getOrderGoodsList [get]
func (orderGoodsApi *OrderGoodsApi) GetOrderGoodsList(c *gin.Context) {
	var pageInfo ordersReq.OrderGoodsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := orderGoodsService.GetOrderGoodsInfoList(pageInfo); err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败", c)
    } else {
        response.OkWithDetailed(response.PageResult{
            List:     list,
            Total:    total,
            Page:     pageInfo.Page,
            PageSize: pageInfo.PageSize,
        }, "获取成功", c)
    }
}
