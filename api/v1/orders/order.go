package orders

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	bigwarehouseModel "github.com/OSQianXing/guanpu-server/model/bigwarehouse"
	bigwarehouseReq "github.com/OSQianXing/guanpu-server/model/bigwarehouse/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"

	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderApi struct {
}

var orderService = service.ServiceGroupApp.OrdersServiceGroup.OrderService

// CreateOrder 创建Order
// @Tags Order
// @Summary 创建Order
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.CreateOrderRequest true "创建Order"
// @Success 200 {string} string "{"success":true,"reorderNo":{},"msg":"获取成功"}"
// @Router /order/createOrder [post]
func (orderApi *OrderApi) CreateOrder(c *gin.Context) {
	var order ordersReq.CreateOrderRequest
	err := c.ShouldBindJSON(&order)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息和租户ID
	userInfo := utils.GetUserInfo(c)
	if userInfo == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}

	// 强制设置租户ID，防止客户端伪造
	order.TenantID = userInfo.TenantID

	if order.AddressId == nil {
		response.FailWithMessage("参数有误", c)
		return
	}

	if len(order.Products) == 0 {
		response.FailWithMessage(errors.New("product info cannot be empty").Error(), c)
		return
	}
	// 过滤掉内部订单的无效组合

	if !((order.PaymentType == types.ZeroPayOrder) && (order.OrderType == types.ZeroOrder)) && !((order.PaymentType != types.ZeroPayOrder) && (order.OrderType != types.ZeroOrder)) {
		response.FailWithMessage("参数有误", c)
		return
	}
	var userID uint
	userID = utils.GetUserID(c)

	// 账户锁
	locker := utils.NewDistributedLockEngine(utils.GetFranchiseeAccountKey(order.FranchiseeId))
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		response.FailWithMessage("下单异常请稍后重试", c)
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()

	if orderNo, err := orderService.CreateOrder(&order, userID, utils.GetUserIP(c)); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败, "+err.Error(), c)
	} else {
		// response.OkWithMessage("创建成功", c)
		response.OkWithData(gin.H{"reorderNo": orderNo}, c)
	}
}

// UpdateOrder 更新Order
// @Tags Order
// @Summary 更新Order
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.UpdateOrder true "更新Order"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /order/updateOrder [put]
func (orderApi *OrderApi) UpdateOrder(c *gin.Context) {
	var order ordersReq.UpdateOrder
	err := c.ShouldBindJSON(&order)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息和租户ID
	userInfo := utils.GetUserInfo(c)
	if userInfo == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}

	// 强制设置租户ID，防止跨租户操作
	order.TenantID = userInfo.TenantID

	err = utils.Verify(order, utils.UpdateOrderVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if order.OrderOperate != types.GoodsRefund {
		response.FailWithMessage("仅支持退款操作", c)
		return
	}
	var userID uint
	userID = utils.GetUserID(c)
	// 账户锁
	locker := utils.NewDistributedLockEngine(utils.GetFranchiseeAccountKey(order.FranchiseeId))
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		response.FailWithMessage("操作异常请稍后重试", c)
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()

	if err := orderService.UpdateOrder(order, userID, utils.GetUserIP(c)); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOrder 订单详情
// @Tags Order
// @Summary 订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param orderId query int true "订单ID"
// @Success 200 {object} response.Response{data=response.OrderDetail}
// @Router /order/findOrder [get]
func (orderApi *OrderApi) FindOrder(c *gin.Context) {
	var req ordersReq.OrderDetailRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	// 获取用户租户ID
	userInfo := utils.GetUserInfo(c)
	if userInfo == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}
	
	if reorder, err := orderService.GetOrder(req.OrderID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reorder": reorder}, c)
	}
}

// GetOrderList 分页获取Order列表
// @Tags Order
// @Summary 分页获取Order列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderSearch true "分页获取Order列表"
// @Success 200 {object} response.Response{data=orders.Order,msg=string}
// @Router /order/getOrderList [get]
func (orderApi *OrderApi) GetOrderList(c *gin.Context) {
	var pageInfo ordersReq.OrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userInfo := utils.GetUserInfo(c)
	if userInfo == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}
	
	// 强制设置租户ID，确保租户隔离
	pageInfo.TenantID = userInfo.TenantID
	//// This code snippet checks if the user has the authority of a warehouse role and if the pageInfo.BigWarehouseId is nil or 0 and pageInfo.NeedDelivery is true. If all conditions are met, it retrieves the warehouse ID associated with the user's account and assigns it to pageInfo.BigWarehouseId.
	if userInfo.AuthorityId == uint(types.KuFangRoleAuthorityId) && (pageInfo.BigWarehouseId == nil || *pageInfo.BigWarehouseId == 0) && (pageInfo.NeedDelivery != nil && *pageInfo.NeedDelivery == true) {
		// 获取库房账号对应的仓库 ID
		bigWarehouseManagerList, _, err := service.ServiceGroupApp.BigWarehouseServiceGroup.GetBigWarehouseManagerInfoList(bigwarehouseReq.BigWarehouseManagerSearch{BigWarehouseManager: bigwarehouseModel.BigWarehouseManager{UserId: &userInfo.BaseClaims.ID}, PageInfo: request.PageInfo{Page: 1, PageSize: -1}})
		if err != nil {
			global.GVA_LOG.Error("获取失败!", zap.Error(err))
			response.FailWithMessage("获取失败"+err.Error(), c)
		} else {
			if len(bigWarehouseManagerList) > 0 {
				// 有权限
				pageInfo.BigWarehouseId = bigWarehouseManagerList[0].BigWarehouseId
			}
		}
	}
	if list, total, err := orderService.GetOrderInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportOrderList 导出Order列表
// @Tags Order
// @Summary 导出Order列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderSearch true "导出Order列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /order/exportOrderList [get]
func (orderApi *OrderApi) ExportOrderList(c *gin.Context) {
	var pageInfo ordersReq.OrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		c.Abort()
		return
	}
	
	// 获取用户租户ID
	userInfo := utils.GetUserInfo(c)
	if userInfo == nil {
		response.FailWithMessage("未授权访问", c)
		c.Abort()
		return
	}
	
	// 强制设置租户ID，确保租户隔离
	pageInfo.TenantID = userInfo.TenantID
	
	// 导出 excel
	f, err := orderService.ExportOrderInfoList(pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		c.Abort()
		c.AbortWithStatus(7)
		return
	}
	var fileName string
	if pageInfo.StartCreatedAt != nil && pageInfo.EndCreatedAt != nil {
		fileName = fmt.Sprintf("订单列表-%s-%s.xlsx", pageInfo.StartCreatedAt.Format("2006-01-02"), pageInfo.EndCreatedAt.Format("2006-01-02"))
	} else {
		begin := time.Now().AddDate(0, -3, 0)
		fileName = fmt.Sprintf("订单列表-%s-%s.xlsx", begin.Format("2006-01-02"), time.Now().Format("2006-01-02"))
	}
	// 设置响应头
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}

// UpdateOrderWarehouse 更新订单仓库
// @Tags Order
// @Summary 更新订单仓库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.OrderWarehouseUpdate true "更新订单仓库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /order/updateOrderWarehouse [put]
func (orderApi *OrderApi) UpdateOrderWarehouse(c *gin.Context) {

	var orderWarehouseUpdate ordersReq.OrderWarehouseUpdate
	err := c.ShouldBindJSON(&orderWarehouseUpdate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = orderService.UpdateOrderWarehouse(orderWarehouseUpdate.OrderNo, orderWarehouseUpdate.OrderId, orderWarehouseUpdate.BigWarehouseId)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}
