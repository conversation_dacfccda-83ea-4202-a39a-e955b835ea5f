package orders

import (
	"fmt"
	"net/http"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/orders"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderDeliveryApi struct {
}

var orderDeliveryService = service.ServiceGroupApp.OrdersServiceGroup.OrderDeliveryService

// CreateOrderDelivery 创建发货单
// @Tags OrderDelivery
// @Summary 创建发货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.OrderDeliveryCreate true "创建发货单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderDelivery/createOrderDelivery [post]
func (orderDeliveryApi *OrderDeliveryApi) CreateOrderDelivery(c *gin.Context) {
	var orderDelivery ordersReq.OrderDeliveryCreate
	err := c.ShouldBindJSON(&orderDelivery)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if id, err := orderDeliveryService.CreateOrderDelivery(&orderDelivery, utils.GetUserName(c), utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("发货失败: "+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"id": id}, c)
	}
}

// BatchStocking 批量备货
// @Tags OrderDelivery
// @Summary 批量备货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.BatchStocking true "批量备货"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量备货成功"}"
// @Router /orderDelivery/batchStocking [post]
func (orderDeliveryApi *OrderDeliveryApi) BatchStocking(c *gin.Context) {
	var orderStocking ordersReq.BatchStocking
	err := c.ShouldBindJSON(&orderStocking)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if odMap, err := orderDeliveryService.BatchStocking(&orderStocking, utils.GetUserName(c), utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("发起备货失败!", zap.Error(err))
		response.FailWithMessage("发起备货失败: "+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"odMap": odMap}, c)
	}
}

// BatchConfirmDelivery 批量确认发货
// @Tags OrderDelivery
// @Summary 批量确认发货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.NosReq true "批量确认发货"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量确认发货成功"}"
// @Router /orderDelivery/batchConfirmDelivery [post]
func (orderDeliveryApi *OrderDeliveryApi) BatchConfirmDelivery(c *gin.Context) {
	var Nos request.NosReq
	err := c.ShouldBindJSON(&Nos)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := orderDeliveryService.BatchConfirmDeliveryByDeliveryNos(Nos, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("批量确认发货失败!", zap.Error(err))
		response.FailWithMessage("批量确认发货失败", c)
	} else {
		response.OkWithMessage("批量确认发货成功", c)
	}
}

// DeleteOrderDelivery 删除发货单
// @Tags OrderDelivery
// @Summary 删除发货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderDelivery true "删除发货单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderDelivery/deleteOrderDelivery [delete]
func (orderDeliveryApi *OrderDeliveryApi) DeleteOrderDelivery(c *gin.Context) {
	var orderDelivery orders.OrderDelivery
	err := c.ShouldBindJSON(&orderDelivery)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderDeliveryService.DeleteOrderDelivery(orderDelivery); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteOrderDeliveryByIds 批量删除发货单
// @Tags OrderDelivery
// @Summary 批量删除发货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除发货单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /orderDelivery/deleteOrderDeliveryByIds [delete]
func (orderDeliveryApi *OrderDeliveryApi) DeleteOrderDeliveryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderDeliveryService.DeleteOrderDeliveryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateOrderDelivery 更新发货单
// @Tags OrderDelivery
// @Summary 更新发货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.OrderDeliveryUpdate true "更新发货单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderDelivery/updateOrderDelivery [put]
func (orderDeliveryApi *OrderDeliveryApi) UpdateOrderDelivery(c *gin.Context) {
	var req ordersReq.OrderDeliveryUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderDeliveryService.UpdateOrderDelivery(req, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOrderDelivery 用id查询OrderDelivery
// @Tags OrderDelivery
// @Summary 用id查询OrderDelivery
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderDelivery true "用id查询OrderDelivery"
// @Success 200 {object} response.Response{data=orders.OrderDelivery}
// @Router /orderDelivery/findOrderDelivery [get]
func (orderDeliveryApi *OrderDeliveryApi) FindOrderDelivery(c *gin.Context) {
	var orderDelivery orders.OrderDelivery
	err := c.ShouldBindQuery(&orderDelivery)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reorderDelivery, err := orderDeliveryService.GetOrderDelivery(orderDelivery.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reorderDelivery": reorderDelivery}, c)
	}
}

// FindOrderDeliveryList 根据发货单号获取OrderDeliveryList
// @Tags OrderDelivery
// @Summary 根据发货单号获取OrderDeliveryList
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.NosReq true "根据发货单号获取OrderDeliveryList"
// @Success 200 {object} response.Response{data=response.OrderDeliveryList}
// @Router /orderDelivery/findOrderDeliveryList [post]
func (orderDeliveryApi *OrderDeliveryApi) FindOrderDeliveryList(c *gin.Context) {
	var nos request.NosReq
	err := c.ShouldBindJSON(&nos)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if reorderDelivery, err := orderDeliveryService.GetOrderDeliveryListByDeliveryNos(nos); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败"+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"reorderDelivery": reorderDelivery}, c)
	}
}

// GetOrderDeliveryList 分页获取OrderDelivery列表
// @Tags OrderDelivery
// @Summary 分页获取OrderDelivery列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderDeliverySearch true "分页获取OrderDelivery列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderDelivery/getOrderDeliveryList [get]
func (orderDeliveryApi *OrderDeliveryApi) GetOrderDeliveryList(c *gin.Context) {
	var pageInfo ordersReq.OrderDeliverySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := orderDeliveryService.GetOrderDeliveryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportOrderDelivery 导出发货单
// @Tags OrderDelivery
// @Summary 导出发货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderDeliverySearch true "导出发货单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderDelivery/exportOrderDelivery [get]
func (orderDeliveryApi *OrderDeliveryApi) ExportOrderDelivery(c *gin.Context) {
	var pageInfo ordersReq.OrderDeliverySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	f, err := orderDeliveryService.ExportOrderDelivery(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("导出失败!", zap.Error(err))
		response.FailWithMessage("导出失败,"+err.Error(), c)
		return
	}
	var fileName string
	if pageInfo.StartCreatedAt != nil && pageInfo.EndCreatedAt != nil {
		fileName = fmt.Sprintf("发货单列表-%s-%s.xlsx", pageInfo.StartCreatedAt.Format("2006-01-02"), pageInfo.EndCreatedAt.Format("2006-01-02"))
	} else {
		// 时间范围为当前时间之前的 3 个月
		fileName = fmt.Sprintf("发货单列表-%s-%s.xlsx", time.Now().AddDate(0, -3, 0).Format("2006-01-02"), time.Now().Format("2006-01-02"))
	}

	// 设置响应头
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}
