package orders

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/orders"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
)

type OrderRemarkApi struct {
}

var orderRemarkService = service.ServiceGroupApp.OrdersServiceGroup.OrderRemarkService

// CreateOrderRemark 创建OrderRemark
// @Tags OrderRemark
// @Summary 创建OrderRemark
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderRemark true "创建OrderRemark"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderRemark/createOrderRemark [post]
func (orderRemarkApi *OrderRemarkApi) CreateOrderRemark(c *gin.Context) {
	var orderRemark orders.OrderRemark
	err := c.ShouldBindJSON(&orderRemark)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	orderRemark.CreatedBy = utils.GetUserID(c)

	verify := utils.Rules{
		"OrderID":   {utils.NotEmpty()},
		"Content":   {utils.NotEmpty()},
		"CreatedBy": {utils.Gt(strconv.Itoa(0))},
		"UpdatedBy": {utils.Eq(strconv.Itoa(0))},
		"DeletedBy": {utils.Eq(strconv.Itoa(0))},
	}
	if err := utils.Verify(orderRemark, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if or, err := orderRemarkService.CreateOrderRemark(&orderRemark); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithData(gin.H{"remark:": or}, c)
	}
}

// DeleteOrderRemark 删除OrderRemark
// @Tags OrderRemark
// @Summary 删除OrderRemark
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderRemark true "删除OrderRemark"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderRemark/deleteOrderRemark [delete]
func (orderRemarkApi *OrderRemarkApi) DeleteOrderRemark(c *gin.Context) {
	var orderRemark orders.OrderRemark
	err := c.ShouldBindJSON(&orderRemark)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	orderRemark.DeletedBy = utils.GetUserID(c)
	if err := orderRemarkService.DeleteOrderRemark(orderRemark); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteOrderRemarkByIds 批量删除OrderRemark
// @Tags OrderRemark
// @Summary 批量删除OrderRemark
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除OrderRemark"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /orderRemark/deleteOrderRemarkByIds [delete]
func (orderRemarkApi *OrderRemarkApi) DeleteOrderRemarkByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	deletedBy := utils.GetUserID(c)
	if err := orderRemarkService.DeleteOrderRemarkByIds(IDS, deletedBy); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateOrderRemark 更新OrderRemark
// @Tags OrderRemark
// @Summary 更新OrderRemark
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderRemark true "更新OrderRemark"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderRemark/updateOrderRemark [put]
func (orderRemarkApi *OrderRemarkApi) UpdateOrderRemark(c *gin.Context) {
	var orderRemark orders.OrderRemark
	err := c.ShouldBindJSON(&orderRemark)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	orderRemark.UpdatedBy = utils.GetUserID(c)

	verify := utils.Rules{
		"OrderID":   {utils.NotEmpty()},
		"Content":   {utils.NotEmpty()},
		"UpdatedBy": {utils.Gt(strconv.Itoa(0))},
		"CreatedBy": {utils.Eq(strconv.Itoa(0))},
		"DeletedBy": {utils.Eq(strconv.Itoa(0))},
	}
	if err := utils.Verify(orderRemark, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderRemarkService.UpdateOrderRemark(orderRemark); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOrderRemark 用id查询OrderRemark
// @Tags OrderRemark
// @Summary 用id查询OrderRemark
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orders.OrderRemark true "用id查询OrderRemark"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /orderRemark/findOrderRemark [get]
func (orderRemarkApi *OrderRemarkApi) FindOrderRemark(c *gin.Context) {
	var orderRemark orders.OrderRemark
	err := c.ShouldBindQuery(&orderRemark)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reorderRemark, err := orderRemarkService.GetOrderRemark(orderRemark.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reorderRemark": reorderRemark}, c)
	}
}

// GetOrderRemarkList 分页获取OrderRemark列表
// @Tags OrderRemark
// @Summary 分页获取OrderRemark列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderRemarkSearch true "分页获取OrderRemark列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderRemark/getOrderRemarkList [get]
func (orderRemarkApi *OrderRemarkApi) GetOrderRemarkList(c *gin.Context) {
	var pageInfo ordersReq.OrderRemarkSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := orderRemarkService.GetOrderRemarkInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
