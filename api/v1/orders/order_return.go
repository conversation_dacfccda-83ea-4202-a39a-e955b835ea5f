package orders

import (
	"errors"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/orders"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderReturnApi struct {
}

var orderReturnService = service.ServiceGroupApp.OrdersServiceGroup.OrderReturnService

// CreateOrderReturn 创建退货单
// @Tags OrderReturn
// @Summary 创建退货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.CreateOrderReturnRequest true "创建退货单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderReturn/createOrderReturn [post]
func (orderReturnApi *OrderReturnApi) CreateOrderReturn(c *gin.Context) {
	var orderReturn ordersReq.CreateOrderReturnRequest
	err := c.ShouldBindJSON(&orderReturn)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(orderReturn, utils.CreateReturnVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if orderReturn.Products == nil {
		response.FailWithMessage(errors.New("product info cannot be empty").Error(), c)
		return
	}

	// 账户锁
	locker := utils.NewDistributedLockEngine(utils.GetFranchiseeAccountKey(orderReturn.FranchiseeId))
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		response.FailWithMessage("退货异常请稍后重试", c)
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()

	if err := orderReturnService.CreateOrderReturn(&orderReturn, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("退货单创建失败!", zap.Error(err))
		response.FailWithMessage("退货单创建失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("退货单创建成功", c)
	}
}

// DeleteOrderReturn 删除OrderReturn
// @Tags OrderReturn
// @Summary 删除OrderReturn
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderReturn true "删除OrderReturn"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderReturn/deleteOrderReturn [delete]
func (orderReturnApi *OrderReturnApi) DeleteOrderReturn(c *gin.Context) {
	var orderReturn orders.OrderReturn
	err := c.ShouldBindJSON(&orderReturn)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderReturnService.DeleteOrderReturn(orderReturn); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteOrderReturnByIds 批量删除OrderReturn
// @Tags OrderReturn
// @Summary 批量删除OrderReturn
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除OrderReturn"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /orderReturn/deleteOrderReturnByIds [delete]
func (orderReturnApi *OrderReturnApi) DeleteOrderReturnByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderReturnService.DeleteOrderReturnByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateOrderReturn 更新OrderReturn
// @Tags OrderReturn
// @Summary 更新OrderReturn
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderReturn true "更新OrderReturn"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderReturn/updateOrderReturn [put]
func (orderReturnApi *OrderReturnApi) UpdateOrderReturn(c *gin.Context) {
	var orderReturn orders.OrderReturn
	err := c.ShouldBindJSON(&orderReturn)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := orderReturnService.UpdateOrderReturn(orderReturn); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOrderReturn 用id查询OrderReturn
// @Tags OrderReturn
// @Summary 用id查询OrderReturn
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderReturn true "用id查询OrderReturn"
// @Success 200  {object}  response.Response{data=response.OrderReturnDetail,msg=string}
// @Router /orderReturn/findOrderReturn [get]
func (orderReturnApi *OrderReturnApi) FindOrderReturn(c *gin.Context) {
	var orderReturn orders.OrderReturn
	err := c.ShouldBindQuery(&orderReturn)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reorderReturn, err := orderReturnService.GetOrderReturn(orderReturn.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reorderReturn, c)
	}
}

// GetOrderReturnList 分页获取OrderReturn列表
// @Tags OrderReturn
// @Summary 分页获取OrderReturn列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query ordersReq.OrderReturnSearch true "分页获取OrderReturn列表"
// @Success 200 {object} response.Response{data=[]response.AppOrderReturnList,msg=string}
// @Router /orderReturn/getOrderReturnList [get]
func (orderReturnApi *OrderReturnApi) GetOrderReturnList(c *gin.Context) {
	var pageInfo ordersReq.OrderReturnSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := orderReturnService.GetOrderReturnInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
