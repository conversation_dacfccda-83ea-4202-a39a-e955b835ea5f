package distribution

import (
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseeReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	franchiseeResp "github.com/OSQianXing/guanpu-server/model/franchisees/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/OSQianXing/guanpu-server/utils/captcha"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var captchaStore = captcha.NewDefaultRedisStore()

type FranchiseeStandbyApi struct {
}

var franchiseeStandbyService = service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeStandbyService

// CreateFranchiseeStandby 创建意向加盟商
// @Tags AppDistribution
// @Summary 创建意向加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchiseeReq.FranchiseeStandbyRegisterReq true "创建FranchiseeStandby"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /app/franchiseeStandby/createFranchiseeStandby [post]
func (franchiseeStandbyApi *FranchiseeStandbyApi) CreateFranchiseeStandby(c *gin.Context) {
	var franchiseeStandbyReq franchiseeReq.FranchiseeStandbyRegisterReq
	err := c.ShouldBindJSON(&franchiseeStandbyReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Tel":       {utils.NotEmpty()},
		"Linkman":   {utils.NotEmpty()},
		"Province":  {utils.NotEmpty()},
		"City":      {utils.NotEmpty()},
		"County":    {utils.NotEmpty()},
		"Remark":    {utils.NotEmpty()},
		"Captcha":   {utils.NotEmpty()},
		"CaptchaId": {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseeStandbyReq, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if captchaStore.Verify(franchiseeStandbyReq.CaptchaId, franchiseeStandbyReq.Captcha, true) {
		global.GVA_LOG.Info("验证码正确")
	} else {
		response.FailWithMessage("验证码错误", c)
		return
	}

	// check franchiseeStandby.InviterTel exist or not
	if len(franchiseeStandbyReq.InviterTel) != 0 {
		fs, err := service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeService.GetFranchiseeByTel(franchiseeStandbyReq.InviterTel)
		if err != nil {
			response.FailWithMessage("邀请人不存在", c)
			return
		} else {
			global.GVA_LOG.Info("register", zap.Any("franchisee_standbyReq", franchiseeStandbyReq), zap.Any("inviter", fs))
		}
	}

	if err := franchiseeStandbyService.CreateFranchiseeStandby(&franchiseeStandbyReq.FranchiseeStandbyBase); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteFranchiseeStandby 删除FranchiseeStandby
// @Tags FranchiseeStandby
// @Summary 删除FranchiseeStandby
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.FranchiseeStandby true "删除FranchiseeStandby"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /franchiseeStandby/deleteFranchiseeStandby [delete]
func (franchiseeStandbyApi *FranchiseeStandbyApi) DeleteFranchiseeStandby(c *gin.Context) {
	var franchiseeStandby franchisees.FranchiseeStandby
	err := c.ShouldBindJSON(&franchiseeStandby)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeStandbyService.DeleteFranchiseeStandby(franchiseeStandby); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteFranchiseeStandbyByIds 批量删除FranchiseeStandby
// @Tags FranchiseeStandby
// @Summary 批量删除FranchiseeStandby
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除FranchiseeStandby"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /franchiseeStandby/deleteFranchiseeStandbyByIds [delete]
func (franchiseeStandbyApi *FranchiseeStandbyApi) DeleteFranchiseeStandbyByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeStandbyService.DeleteFranchiseeStandbyByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateFranchiseeStandby 更新FranchiseeStandby
// @Tags FranchiseeStandby
// @Summary 更新FranchiseeStandby
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.FranchiseeStandby true "更新FranchiseeStandby"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchiseeStandby/updateFranchiseeStandby [put]
func (franchiseeStandbyApi *FranchiseeStandbyApi) UpdateFranchiseeStandby(c *gin.Context) {
	var franchiseeStandby franchisees.FranchiseeStandby
	err := c.ShouldBindJSON(&franchiseeStandby)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Tel":      {utils.NotEmpty()},
		"Linkman":  {utils.NotEmpty()},
		"Province": {utils.NotEmpty()},
		"City":     {utils.NotEmpty()},
		"County":   {utils.NotEmpty()},
		"Remark":   {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseeStandby, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeStandbyService.UpdateFranchiseeStandby(franchiseeStandby); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

//// GetFranchiseeStandby  用id查询FranchiseeStandby
//// @Tags FranchiseeStandby
//// @Summary 用id查询FranchiseeStandby
//// @Security ApiKeyAuth
//// @accept application/json
//// @Produce application/json
//// @Param standbyId path uint  true  "用id查询FranchiseeStandby"
//// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
//// @Router /franchiseeStandby/getFranchiseeStandby/{standbyId} [get]
//func (franchiseeStandbyApi *FranchiseeStandbyApi) GetFranchiseeStandby(c *gin.Context) {
//	idp := c.Param("standbyId")
//	id, _ := strconv.ParseUint(idp, 10, 64)
//	if id == 0 {
//		response.FailWithMessage("参数不合法", c)
//		return
//	}
//	if franchiseeStandby, err := franchiseeStandbyService.GetFranchiseeStandbyID(uint(id)); err != nil {
//		global.GVA_LOG.Error("查询失败!", zap.Error(err))
//		response.FailWithMessage("查询失败", c)
//	} else {
//		response.OkWithDetailed(gin.H{"franchiseeStandby": franchiseeStandby}, "查询成功", c)
//	}
//}

// FindFranchiseeStandbyID 用id查询FranchiseeStandby
// @Tags FranchiseeStandby
// @Summary 用id查询FranchiseeStandby
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseeReq.FranchiseeStandbyFindReq true "用id查询FranchiseeStandby"
// @Success 200 {object} response.Response{data=response.FranchiseeStandbyApproveInfo}
// @Router /franchiseeStandby/findFranchiseeStandby [get]
func (franchiseeStandbyApi *FranchiseeStandbyApi) FindFranchiseeStandbyID(c *gin.Context) {
	var franchiseeStandbyReq franchiseeReq.FranchiseeStandbyFindReq
	err := c.ShouldBindQuery(&franchiseeStandbyReq)
	global.GVA_LOG.Info("查询参数", zap.Any("查询参数", franchiseeStandbyReq))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchiseeStandby, err := franchiseeStandbyService.GetFranchiseeStandbyID(franchiseeStandbyReq.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		resp := &franchiseeResp.FranchiseeStandbyApproveInfo{
			FranchiseeStandby: refranchiseeStandby,
		}
		if refranchiseeStandby.InviterTel != "" {
			inviter, err := service.ServiceGroupApp.FranchiseesServiceGroup.GetFranchiseeByTel(refranchiseeStandby.InviterTel)
			if err != nil {
				resp.InviterID = 0
			}
			resp.InviterID = int(inviter.ID)
			iinfo, err := franchiseeStandbyService.GetInviterInfo(refranchiseeStandby.InviterTel)
			if err != nil {
				resp.FranchiseeStandbyInviter = franchiseeResp.FranchiseeStandbyInviter{}
			}
			resp.FranchiseeStandbyInviter = iinfo
			if iinfo.TopFranchisee != nil { // 获取对应的市场/督导信息
				marketInfo, err := dao.FranchiseeTeam.GetFranchiseeMarketLead(iinfo.TopFranchisee.ID)
				if err != nil {
					// global.GVA_LOG.Error("获取对应的市场/督导信息失败", zap.Error(err))
					resp.MarketLeadID = 0
					resp.MarketLead = nil
				} else {
					resp.MarketLeadID = int(marketInfo.UserId)
					resp.MarketLead = marketInfo.MarketLead
				}
				supervisionInfo, err := dao.FranchiseeTeam.GetFranchiseeSupervisionLead(iinfo.TopFranchisee.ID)
				if err != nil {
					resp.SupervisionLeadID = 0
					resp.SupervisionLead = nil
				} else {
					resp.SupervisionLeadID = int(supervisionInfo.UserId)
					resp.SupervisionLead = supervisionInfo.SupervisionLead
				}

			}
		}
		response.OkWithData(gin.H{"refranchiseeStandby": resp}, c)
	}
}

// GetFranchiseeStandbyList 分页获取FranchiseeStandby列表
// @Tags FranchiseeStandby
// @Summary 分页获取FranchiseeStandby列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseeReq.FranchiseeStandbySearch true "分页获取FranchiseeStandby列表"
// @Success 200 {object} response.PageResult{list=[]franchisees.FranchiseeStandby}
// @Router /franchiseeStandby/getFranchiseeStandbyList [get]
func (franchiseeStandbyApi *FranchiseeStandbyApi) GetFranchiseeStandbyList(c *gin.Context) {
	var pageInfo franchiseeReq.FranchiseeStandbySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := franchiseeStandbyService.GetFranchiseeStandbyInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInviterInfo 获取邀请人信息及邀请人所在团队的一级分销加盟商
// @Tags FranchiseeStandby
// @Summary 获取邀请人信息及邀请人所在团队的一级分销加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseeReq.FranchiseeInviterReq true "加盟商ID"
// @Success 200 {object} response.Response{data=response.FranchiseeStandbyInviter,msg=string}
// @Router /franchiseeStandby/inviterInfo [get]
func (franchiseeStandbyApi *FranchiseeStandbyApi) GetInviterInfo(c *gin.Context) {
	var inviterInfoReq franchiseeReq.FranchiseeInviterReq
	err := c.ShouldBindQuery(&inviterInfoReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if _, err := utils.CheckTel(inviterInfoReq.Tel); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if inviterInfo, err := franchiseeStandbyService.GetInviterInfo(inviterInfoReq.Tel); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(inviterInfo, c)
	}
}
