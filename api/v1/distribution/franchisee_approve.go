package distribution

type FranchiseeApproveLogApi struct {
}

// var franchiseeApproveLogService = service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeApproveService

// // CreateFranchiseeApprove 创建FranchiseeApproveLog
// // @Tags FranchiseeApproveLog
// // @Summary 创建FranchiseeApproveLog
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body distribution.FranchiseeApproveLog true "创建FranchiseeApproveLog"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /franchiseeApproveLog/createFranchiseeApproveLog [post]
// func (franchiseeApproveLogApi *FranchiseeApproveLogApi) CreateFranchiseeApprove(c *gin.Context) {
// 	var franchiseeApproveLog distribution.FranchiseeApproveLog
// 	err := c.ShouldBindJSON(&franchiseeApproveLog)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	franchiseeApproveLog.CreatedBy = utils.GetUserID(c)
// 	if err := franchiseeApproveLogService.CreateFranchiseeApprove(&franchiseeApproveLog); err != nil {
// 		global.GVA_LOG.Error("创建失败!", zap.Error(err))
// 		response.FailWithMessage("创建失败", c)
// 	} else {
// 		response.OkWithMessage("创建成功", c)
// 	}
// }

// // DeleteFranchiseeApprove 删除FranchiseeApproveLog
// // @Tags FranchiseeApproveLog
// // @Summary 删除FranchiseeApproveLog
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body distribution.FranchiseeApproveLog true "删除FranchiseeApproveLog"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// // @Router /franchiseeApproveLog/deleteFranchiseeApproveLog [delete]
// func (franchiseeApproveLogApi *FranchiseeApproveLogApi) DeleteFranchiseeApprove(c *gin.Context) {
// 	var franchiseeApproveLog distribution.FranchiseeApproveLog
// 	err := c.ShouldBindJSON(&franchiseeApproveLog)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	franchiseeApproveLog.DeletedBy = utils.GetUserID(c)
// 	if err := franchiseeApproveLogService.DeleteFranchiseeApprove(franchiseeApproveLog); err != nil {
// 		global.GVA_LOG.Error("删除失败!", zap.Error(err))
// 		response.FailWithMessage("删除失败", c)
// 	} else {
// 		response.OkWithMessage("删除成功", c)
// 	}
// }

// // DeleteFranchiseeApproveByIds 批量删除FranchiseeApproveLog
// // @Tags FranchiseeApproveLog
// // @Summary 批量删除FranchiseeApproveLog
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body request.IdsReq true "批量删除FranchiseeApproveLog"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// // @Router /franchiseeApproveLog/deleteFranchiseeApproveLogByIds [delete]
// func (franchiseeApproveLogApi *FranchiseeApproveLogApi) DeleteFranchiseeApproveByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	deletedBy := utils.GetUserID(c)
// 	if err := franchiseeApproveLogService.DeleteFranchiseeApproveByIds(IDS, deletedBy); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// // UpdateFranchiseeApproveLog 更新FranchiseeApproveLog
// // @Tags FranchiseeApproveLog
// // @Summary 更新FranchiseeApproveLog
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body distribution.FranchiseeApproveLog true "更新FranchiseeApproveLog"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// // @Router /franchiseeApproveLog/updateFranchiseeApproveLog [put]
// func (franchiseeApproveLogApi *FranchiseeApproveLogApi) UpdateFranchiseeApproveLog(c *gin.Context) {
// 	var franchiseeApproveLog distribution.FranchiseeApproveLog
// 	err := c.ShouldBindJSON(&franchiseeApproveLog)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	franchiseeApproveLog.UpdatedBy = utils.GetUserID(c)
// 	if err := franchiseeApproveLogService.UpdateFranchiseeApproveLog(franchiseeApproveLog); err != nil {
// 		global.GVA_LOG.Error("更新失败!", zap.Error(err))
// 		response.FailWithMessage("更新失败", c)
// 	} else {
// 		response.OkWithMessage("更新成功", c)
// 	}
// }

// // FindFranchiseeApproveLog 用id查询FranchiseeApproveLog
// // @Tags FranchiseeApproveLog
// // @Summary 用id查询FranchiseeApproveLog
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data query distribution.FranchiseeApproveLog true "用id查询FranchiseeApproveLog"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// // @Router /franchiseeApproveLog/findFranchiseeApproveLog [get]
// func (franchiseeApproveLogApi *FranchiseeApproveLogApi) FindFranchiseeApproveLog(c *gin.Context) {
// 	var franchiseeApproveLog distribution.FranchiseeApproveLog
// 	err := c.ShouldBindQuery(&franchiseeApproveLog)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if refranchiseeApproveLog, err := franchiseeApproveLogService.GetFranchiseeApprove(franchiseeApproveLog.ID); err != nil {
// 		global.GVA_LOG.Error("查询失败!", zap.Error(err))
// 		response.FailWithMessage("查询失败", c)
// 	} else {
// 		response.OkWithData(gin.H{"refranchiseeApproveLog": refranchiseeApproveLog}, c)
// 	}
// }

// // GetFranchiseeApproveLogList 分页获取FranchiseeApproveLog列表
// // @Tags FranchiseeApproveLog
// // @Summary 分页获取FranchiseeApproveLog列表
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data query distributionReq.FranchiseeApproveLogSearch true "分页获取FranchiseeApproveLog列表"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /franchiseeApproveLog/getFranchiseeApproveLogList [get]
// func (franchiseeApproveLogApi *FranchiseeApproveLogApi) GetFranchiseeApproveLogList(c *gin.Context) {
// 	var pageInfo distributionReq.FranchiseeApproveLogSearch
// 	err := c.ShouldBindQuery(&pageInfo)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if list, total, err := franchiseeApproveLogService.GetFranchiseeApproveLogInfoList(pageInfo); err != nil {
// 		global.GVA_LOG.Error("获取失败!", zap.Error(err))
// 		response.FailWithMessage("获取失败", c)
// 	} else {
// 		response.OkWithDetailed(response.PageResult{
// 			List:     list,
// 			Total:    total,
// 			Page:     pageInfo.Page,
// 			PageSize: pageInfo.PageSize,
// 		}, "获取成功", c)
// 	}
// }
