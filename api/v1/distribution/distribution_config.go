package distribution

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees/distribution"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DistributionConfigApi struct {
}

var distributionConfigService = service.ServiceGroupApp.DistributionServiceGroup.DistributionConfigService

// CreateDistributionConfig 创建DistributionConfig
// @Tags DistributionConfig
// @Summary 创建DistributionConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body distribution.DistributionConfig true "创建DistributionConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /distributionConfig/createDistributionConfig [post]
// func (distributionConfigApi *DistributionConfigApi) CreateDistributionConfig(c *gin.Context) {
// 	var distributionConfig distribution.DistributionConfig
// 	err := c.ShouldBindJSON(&distributionConfig)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	verify := utils.Rules{
// 		"InviteEnable":                   {utils.NotEmpty()},
// 		"InviteFCategoryIds":             {utils.NotEmpty()},
// 		"DistributionPerformanceMallIds": {utils.NotEmpty()},
// 	}
// 	if err := utils.Verify(distributionConfig, verify); err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := distributionConfigService.CreateDistributionConfig(&distributionConfig); err != nil {
// 		global.GVA_LOG.Error("创建失败!", zap.Error(err))
// 		response.FailWithMessage("创建失败", c)
// 	} else {
// 		response.OkWithMessage("创建成功", c)
// 	}
// }

// DeleteDistributionConfig 删除DistributionConfig
// @Tags DistributionConfig
// @Summary 删除DistributionConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body distribution.DistributionConfig true "删除DistributionConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /distributionConfig/deleteDistributionConfig [delete]
// func (distributionConfigApi *DistributionConfigApi) DeleteDistributionConfig(c *gin.Context) {
// 	var distributionConfig distribution.DistributionConfig
// 	err := c.ShouldBindJSON(&distributionConfig)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := distributionConfigService.DeleteDistributionConfig(distributionConfig); err != nil {
// 		global.GVA_LOG.Error("删除失败!", zap.Error(err))
// 		response.FailWithMessage("删除失败", c)
// 	} else {
// 		response.OkWithMessage("删除成功", c)
// 	}
// }

// DeleteDistributionConfigByIds 批量删除DistributionConfig
// @Tags DistributionConfig
// @Summary 批量删除DistributionConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除DistributionConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /distributionConfig/deleteDistributionConfigByIds [delete]
// func (distributionConfigApi *DistributionConfigApi) DeleteDistributionConfigByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := distributionConfigService.DeleteDistributionConfigByIds(IDS); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// UpdateDistributionConfig 更新DistributionConfig
// @Tags DistributionConfig
// @Summary 更新DistributionConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body distribution.DistributionConfig true "更新DistributionConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /distributionConfig/updateDistributionConfig [POST]
func (distributionConfigApi *DistributionConfigApi) UpdateDistributionConfig(c *gin.Context) {
	var distributionConfig distribution.DistributionConfig
	err := c.ShouldBindJSON(&distributionConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"InviteEnable":       {utils.NotEmpty()},
		"InviteFCategoryIds": {utils.NotEmpty()},
		"DisplayPersonalPerformance": {
			utils.NotEmpty(),
		},
		"DisplayMemberPerformance": {
			utils.NotEmpty(),
		},
		"DisplayCustomerPerformance": {
			utils.NotEmpty(),
		},
		"DisplayInvestPerformance":  {utils.NotEmpty()},
		"DisplayCustomerManagement": {utils.NotEmpty()},
	}
	if err := utils.Verify(distributionConfig, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	//check distributionConfig.TopFCategoryId not empty
	//check distributionConfig.DistributionPerformanceMallIds must be []int json not empty

	if distributionConfig.TopFCategoryId == 0 {
		response.FailWithMessage("顶级分类不可为 0", c)
		return
	}

	if err := distributionConfigService.UpdateDistributionConfig(distributionConfig); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindDistributionConfig 查询DistributionConfig
// @Tags DistributionConfig
// @Summary 查询DistributionConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=distribution.DistributionConfig,msg=string} "{"success":true,"redistributionConfig":{},"msg":"查询成功"}"
// @Router /distributionConfig/findDistributionConfig [get]
func (distributionConfigApi *DistributionConfigApi) FindDistributionConfig(c *gin.Context) {
	// var distributionConfig distribution.DistributionConfig
	// err := c.ShouldBindQuery(&distributionConfig)
	// if err != nil {
	// 	response.FailWithMessage(err.Error(), c)
	// 	return
	// }
	if redistributionConfig, err := distributionConfigService.GetDistributionConfig(); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"redistributionConfig": redistributionConfig}, c)
	}
}

// GetDistributionConfigList 分页获取DistributionConfig列表
// @Tags DistributionConfig
// @Summary 分页获取DistributionConfig列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query distributionReq.DistributionConfigSearch true "分页获取DistributionConfig列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /distributionConfig/getDistributionConfigList [get]
// func (distributionConfigApi *DistributionConfigApi) GetDistributionConfigList(c *gin.Context) {
// 	var pageInfo distributionReq.DistributionConfigSearch
// 	err := c.ShouldBindQuery(&pageInfo)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if list, total, err := distributionConfigService.GetDistributionConfigInfoList(pageInfo); err != nil {
// 	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
//         response.FailWithMessage("获取失败", c)
//     } else {
//         response.OkWithDetailed(response.PageResult{
//             List:     list,
//             Total:    total,
//             Page:     pageInfo.Page,
//             PageSize: pageInfo.PageSize,
//         }, "获取成功", c)
//     }
// }
