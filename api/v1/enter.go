package v1

import (
	"github.com/OSQianXing/guanpu-server/api/v1/account"
	"github.com/OSQianXing/guanpu-server/api/v1/app"
	"github.com/OSQianXing/guanpu-server/api/v1/apprelease"
	"github.com/OSQianXing/guanpu-server/api/v1/autoJob"
	"github.com/OSQianXing/guanpu-server/api/v1/bigwarehouse"
	"github.com/OSQianXing/guanpu-server/api/v1/common"
	"github.com/OSQianXing/guanpu-server/api/v1/distribution"
	"github.com/OSQianXing/guanpu-server/api/v1/example"
	"github.com/OSQianXing/guanpu-server/api/v1/finance"
	"github.com/OSQianXing/guanpu-server/api/v1/franchisees"
	"github.com/OSQianXing/guanpu-server/api/v1/orders"
	"github.com/OSQianXing/guanpu-server/api/v1/pay"
	"github.com/OSQianXing/guanpu-server/api/v1/products"
	"github.com/OSQianXing/guanpu-server/api/v1/system"
	"github.com/OSQianXing/guanpu-server/api/v1/warehouse"
)

type ApiGroup struct {
	SystemApiGroup       system.ApiGroup
	ExampleApiGroup      example.ApiGroup
	FranchiseesApiGroup  franchisees.ApiGroup
	ProductsApiGroup     products.ApiGroup
	CommonApiGroup       common.ApiGroup
	OrdersApiGroup       orders.ApiGroup
	AppApiGroup          app.ApiGroup
	AutoJobApiGroup      autoJob.ApiGroup
	AccountApiGroup      account.ApiGroup
	AppreleaseApiGroup   apprelease.ApiGroup
	FinanceApiGroup      finance.ApiGroup
	WarehouseApiGroup    warehouse.ApiGroup
	DistributionApiGroup distribution.ApiGroup
	BigWarehouseApiGroup bigwarehouse.ApiGroup
	BigwarehouseApiGroup bigwarehouse.ApiGroup
	PayApiGroup          pay.ApiGroup

}

var ApiGroupApp = new(ApiGroup)
