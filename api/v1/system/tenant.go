package system

import (
	"strconv"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/system"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TenantApi struct{}

// CreateTenant 创建租户
// @Tags Tenant
// @Summary 创建租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body system.Tenant true "租户信息"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /tenant/createTenant [post]
func (tenantApi *TenantApi) CreateTenant(c *gin.Context) {
	var tenant system.Tenant
	err := c.ShouldBindJSON(&tenant)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.ServiceGroupApp.SystemServiceGroup.TenantService.CreateTenant(tenant); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteTenant 删除租户
// @Tags Tenant
// @Summary 删除租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "租户ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /tenant/deleteTenant [delete]
func (tenantApi *TenantApi) DeleteTenant(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.ServiceGroupApp.SystemServiceGroup.TenantService.DeleteTenant(uint(reqId.ID)); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// UpdateTenant 更新租户
// @Tags Tenant
// @Summary 更新租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body system.Tenant true "租户信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /tenant/updateTenant [put]
func (tenantApi *TenantApi) UpdateTenant(c *gin.Context) {
	var tenant system.Tenant
	err := c.ShouldBindJSON(&tenant)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.ServiceGroupApp.SystemServiceGroup.TenantService.UpdateTenant(tenant); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindTenant 用id查询租户
// @Tags Tenant
// @Summary 用id查询租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "租户ID"
// @Success 200 {object} response.Response{data=system.Tenant,msg=string} "查询成功"
// @Router /tenant/findTenant [get]
func (tenantApi *TenantApi) FindTenant(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if tenant, err := service.ServiceGroupApp.SystemServiceGroup.TenantService.GetTenant(uint(reqId.ID)); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"tenant": tenant}, c)
	}
}

// GetTenantList 分页获取租户列表
// @Tags Tenant
// @Summary 分页获取租户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /tenant/getTenantList [get]
func (tenantApi *TenantApi) GetTenantList(c *gin.Context) {
	var pageInfo request.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if list, total, err := service.ServiceGroupApp.SystemServiceGroup.TenantService.GetTenantList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// AddUserToTenant 添加用户到租户
// @Tags Tenant
// @Summary 添加用户到租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UserTenantRequest true "用户租户关联信息"
// @Success 200 {object} response.Response{msg=string} "添加成功"
// @Router /tenant/addUserToTenant [post]
func (tenantApi *TenantApi) AddUserToTenant(c *gin.Context) {
	var req systemReq.UserTenantRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.ServiceGroupApp.SystemServiceGroup.TenantService.AddUserToTenant(req.UserID, req.TenantID, req.Role); err != nil {
		global.GVA_LOG.Error("添加失败!", zap.Error(err))
		response.FailWithMessage("添加失败", c)
	} else {
		response.OkWithMessage("添加成功", c)
	}
}

// RemoveUserFromTenant 从租户中移除用户
// @Tags Tenant
// @Summary 从租户中移除用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UserTenantRequest true "用户租户关联信息"
// @Success 200 {object} response.Response{msg=string} "移除成功"
// @Router /tenant/removeUserFromTenant [delete]
func (tenantApi *TenantApi) RemoveUserFromTenant(c *gin.Context) {
	var req systemReq.UserTenantRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.ServiceGroupApp.SystemServiceGroup.TenantService.RemoveUserFromTenant(req.UserID, req.TenantID); err != nil {
		global.GVA_LOG.Error("移除失败!", zap.Error(err))
		response.FailWithMessage("移除失败", c)
	} else {
		response.OkWithMessage("移除成功", c)
	}
}

// SetDefaultTenant 设置默认租户
// @Tags Tenant
// @Summary 设置默认租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UserTenantRequest true "用户租户关联信息"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /tenant/setDefaultTenant [put]
func (tenantApi *TenantApi) SetDefaultTenant(c *gin.Context) {
	var req systemReq.UserTenantRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.ServiceGroupApp.SystemServiceGroup.TenantService.SetDefaultTenant(req.UserID, req.TenantID); err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
	} else {
		response.OkWithMessage("设置成功", c)
	}
}

// GetUserTenants 获取用户关联的所有租户
// @Tags Tenant
// @Summary 获取用户关联的所有租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]system.Tenant,msg=string} "获取成功"
// @Router /tenant/getUserTenants [get]
func (tenantApi *TenantApi) GetUserTenants(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	
	if tenants, err := service.ServiceGroupApp.SystemServiceGroup.TenantService.GetUserTenants(userID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"tenants": tenants}, c)
	}
}

// SwitchTenant 切换租户
// @Tags Tenant
// @Summary 切换租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param tenantId path string true "租户ID"
// @Success 200 {object} response.Response{data=gin.H,msg=string} "切换成功"
// @Router /tenant/switchTenant/{tenantId} [post]
func (tenantApi *TenantApi) SwitchTenant(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.FailWithMessage("租户ID格式错误", c)
		return
	}
	
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}
	
	token, err := service.ServiceGroupApp.SystemServiceGroup.TenantService.GenerateTenantToken(userID, uint(tenantID))
	if err != nil {
		global.GVA_LOG.Error("切换租户失败!", zap.Error(err))
		response.FailWithMessage("切换租户失败", c)
		return
	}
	
	response.OkWithDetailed(gin.H{
		"token":    token,
		"tenantId": tenantID,
	}, "切换成功", c)
}