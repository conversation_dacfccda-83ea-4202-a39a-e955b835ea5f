package system

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	systemRes "github.com/OSQianXing/guanpu-server/model/system/response"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UnifiedUserApi struct{}

// GetUserList 统一用户列表接口 - 支持权限分级
// @Tags UnifiedUser
// @Summary 获取用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UnifiedUserListRequest true "用户列表请求参数"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /users/list [post]
func (unifiedUserApi *UnifiedUserApi) GetUserList(c *gin.Context) {
	var pageInfo systemReq.UnifiedUserListRequest
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息
	claims := utils.GetUserInfo(c)
	if claims == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}

	// 根据用户权限设置查询范围
	err = setUserQueryScope(&pageInfo, claims)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 执行查询
	list, total, err := userService.GetUserInfoList(pageInfo.PageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取用户列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	// 根据权限过滤返回数据
	filteredList := filterUserListByPermission(list, claims)

	response.OkWithDetailed(response.PageResult{
		List:     filteredList,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// CreateUser 创建用户 - 统一接口
// @Tags UnifiedUser
// @Summary 创建用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UnifiedUserCreateRequest true "用户创建请求参数"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /users/create [post]
func (unifiedUserApi *UnifiedUserApi) CreateUser(c *gin.Context) {
	var req systemReq.UnifiedUserCreateRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息
	claims := utils.GetUserInfo(c)
	if claims == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}

	// 权限检查
	if !canCreateUser(claims, req.AuthorityId, req.TenantID) {
		response.FailWithMessage("无权限创建该类型用户", c)
		return
	}

	// 设置租户ID（非超级管理员自动使用当前租户）
	if !claims.IsSuperAdmin && claims.UserType != systemReq.UserTypeSuperAdmin {
		req.TenantID = claims.TenantID
	}

	// 转换为系统用户对象
	user := req.ToSysUser()
	// SysUser没有CreatedBy字段，这里可以通过其他方式记录创建者

	_, err = userService.Register(user)
	if err != nil {
		global.GVA_LOG.Error("创建用户失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("创建成功", c)
}

// UpdateUser 更新用户 - 统一接口
// @Tags UnifiedUser
// @Summary 更新用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UnifiedUserUpdateRequest true "用户更新请求参数"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /users/:id [put]
func (unifiedUserApi *UnifiedUserApi) UpdateUser(c *gin.Context) {
	var req systemReq.UnifiedUserUpdateRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户信息
	claims := utils.GetUserInfo(c)
	if claims == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}

	// 获取目标用户ID
	userID := c.Param("id")
	if targetUserID, err := utils.StringToUint(userID); err != nil {
		response.FailWithMessage("用户ID格式错误", c)
		return
	} else {
		req.ID = targetUserID
	}

	// 权限检查
	if !canUpdateUser(claims, req.ID, req.TenantID) {
		response.FailWithMessage("无权限更新该用户", c)
		return
	}

	// 转换为系统用户对象
	user := req.ToSysUser()
	// SysUser没有UpdatedBy字段，这里可以通过其他方式记录更新者

	err = userService.SetUserInfo(user)
	if err != nil {
		global.GVA_LOG.Error("更新用户失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteUser 删除用户 - 统一接口
// @Tags UnifiedUser
// @Summary 删除用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path string true "用户ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /users/:id [delete]
func (unifiedUserApi *UnifiedUserApi) DeleteUser(c *gin.Context) {
	// 获取用户信息
	claims := utils.GetUserInfo(c)
	if claims == nil {
		response.FailWithMessage("未授权访问", c)
		return
	}

	// 获取目标用户ID
	userID := c.Param("id")
	targetUserID, err := utils.StringToUint(userID)
	if err != nil {
		response.FailWithMessage("用户ID格式错误", c)
		return
	}

	// 权限检查
	if !canDeleteUser(claims, targetUserID) {
		response.FailWithMessage("无权限删除该用户", c)
		return
	}

	// 不能删除自己
	if targetUserID == claims.BaseClaims.ID {
		response.FailWithMessage("不能删除自己", c)
		return
	}

	err = userService.DeleteUser(int(targetUserID))
	if err != nil {
		global.GVA_LOG.Error("删除用户失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// setUserQueryScope 根据用户权限设置查询范围
func setUserQueryScope(pageInfo *systemReq.UnifiedUserListRequest, claims *systemReq.CustomClaims) error {
	switch {
	case claims.IsSuperAdmin || claims.UserType == systemReq.UserTypeSuperAdmin:
		// 超级管理员可以查看所有用户
		// 不设置租户限制
		break
	case claims.UserType == systemReq.UserTypeTenantAdmin:
		// 租户管理员只能查看当前租户的用户
		pageInfo.TenantID = claims.TenantID
		break
	default:
		// 普通用户只能查看有限的用户信息
		pageInfo.TenantID = claims.TenantID
		pageInfo.LimitedView = true
		break
	}

	return nil
}

// filterUserListByPermission 根据权限过滤用户列表
func filterUserListByPermission(list interface{}, claims *systemReq.CustomClaims) interface{} {
	// 如果是超级管理员，返回完整信息
	if claims.IsSuperAdmin || claims.UserType == systemReq.UserTypeSuperAdmin {
		return list
	}

	// 其他用户过滤敏感信息
	if userList, ok := list.([]systemRes.SysUserResponse); ok {
		for i := range userList {
			// 隐藏敏感信息 - 通过User字段访问
			userList[i].User.Phone = "***"
			userList[i].User.Email = "***"
		}
		return userList
	}

	return list
}

// canCreateUser 检查是否有权限创建用户
func canCreateUser(claims *systemReq.CustomClaims, targetAuthorityId, targetTenantID uint) bool {
	// 超级管理员可以创建任何用户
	if claims.IsSuperAdmin || claims.UserType == systemReq.UserTypeSuperAdmin {
		return true
	}

	// 租户管理员只能在自己的租户内创建用户
	if claims.UserType == systemReq.UserTypeTenantAdmin {
		return targetTenantID == claims.TenantID
	}

	// 普通用户不能创建用户
	return false
}

// canUpdateUser 检查是否有权限更新用户
func canUpdateUser(claims *systemReq.CustomClaims, targetUserID, targetTenantID uint) bool {
	// 超级管理员可以更新任何用户
	if claims.IsSuperAdmin || claims.UserType == systemReq.UserTypeSuperAdmin {
		return true
	}

	// 用户可以更新自己
	if targetUserID == claims.BaseClaims.ID {
		return true
	}

	// 租户管理员可以更新同租户的用户
	if claims.UserType == systemReq.UserTypeTenantAdmin {
		return targetTenantID == claims.TenantID
	}

	return false
}

// canDeleteUser 检查是否有权限删除用户
func canDeleteUser(claims *systemReq.CustomClaims, targetUserID uint) bool {
	// 超级管理员可以删除任何用户（除了自己）
	if claims.IsSuperAdmin || claims.UserType == systemReq.UserTypeSuperAdmin {
		return targetUserID != claims.BaseClaims.ID
	}

	// 租户管理员可以删除同租户的用户（除了自己）
	if claims.UserType == systemReq.UserTypeTenantAdmin {
		// 这里需要查询目标用户的租户ID进行验证
		// 为简化示例，暂时返回false，实际应该查询数据库
		return false
	}

	return false
}