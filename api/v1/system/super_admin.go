package system

import (
	"strconv"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	ordersModel "github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/system"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"
	serviceSystem "github.com/OSQianXing/guanpu-server/service/system"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SuperAdminApi struct{}

// GetAllTenants 获取所有租户（超级管理员）
// @Tags SuperAdmin
// @Summary 获取所有租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /super/tenants [get]
func (superAdminApi *SuperAdminApi) GetAllTenants(c *gin.Context) {
	var pageInfo request.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用超级管理员权限查询所有租户
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	var tenants []system.Tenant
	var total int64

	limit := pageInfo.PageSize
	offset := pageInfo.PageSize * (pageInfo.Page - 1)

	err = db.Model(&system.Tenant{}).Count(&total).Error
	if err != nil {
		global.GVA_LOG.Error("获取租户总数失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	err = db.Limit(limit).Offset(offset).Preload("AppConfig").Find(&tenants).Error
	if err != nil {
		global.GVA_LOG.Error("获取租户列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     tenants,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAllUsers 获取所有用户（超级管理员）
// @Tags SuperAdmin
// @Summary 获取所有用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /super/users [get]
func (superAdminApi *SuperAdminApi) GetAllUsers(c *gin.Context) {
	var pageInfo request.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用超级管理员权限查询所有用户
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	var users []system.SysUser
	var total int64

	limit := pageInfo.PageSize
	offset := pageInfo.PageSize * (pageInfo.Page - 1)

	err = db.Model(&system.SysUser{}).Count(&total).Error
	if err != nil {
		global.GVA_LOG.Error("获取用户总数失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	err = db.Limit(limit).Offset(offset).Find(&users).Error
	if err != nil {
		global.GVA_LOG.Error("获取用户列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     users,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// DisableTenant 禁用租户
// @Tags SuperAdmin
// @Summary 禁用租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "租户ID"
// @Success 200 {object} response.Response{msg=string} "禁用成功"
// @Router /super/tenants/disable [post]
func (superAdminApi *SuperAdminApi) DisableTenant(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用超级管理员权限更新租户状态
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	status := false
	err = db.Model(&system.Tenant{}).Where("id = ?", reqId.ID).Update("status", status).Error
	if err != nil {
		global.GVA_LOG.Error("禁用租户失败!", zap.Error(err))
		response.FailWithMessage("禁用失败", c)
		return
	}

	response.OkWithMessage("禁用成功", c)
}

// EnableTenant 启用租户
// @Tags SuperAdmin
// @Summary 启用租户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "租户ID"
// @Success 200 {object} response.Response{msg=string} "启用成功"
// @Router /super/tenants/enable [post]
func (superAdminApi *SuperAdminApi) EnableTenant(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用超级管理员权限更新租户状态
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	status := true
	err = db.Model(&system.Tenant{}).Where("id = ?", reqId.ID).Update("status", status).Error
	if err != nil {
		global.GVA_LOG.Error("启用租户失败!", zap.Error(err))
		response.FailWithMessage("启用失败", c)
		return
	}

	response.OkWithMessage("启用成功", c)
}

// PromoteUser 提升用户为管理员
// @Tags SuperAdmin
// @Summary 提升用户为管理员
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body systemReq.UserPromoteRequest true "用户提升请求"
// @Success 200 {object} response.Response{msg=string} "提升成功"
// @Router /super/users/promote [post]
func (superAdminApi *SuperAdminApi) PromoteUser(c *gin.Context) {
	var req systemReq.UserPromoteRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用超级管理员权限更新用户类型
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	updates := map[string]interface{}{
		"user_type": req.UserType,
	}

	if req.UserType == systemReq.UserTypeSuperAdmin {
		updates["is_super_admin"] = true
	}

	err = db.Model(&system.SysUser{}).Where("id = ?", req.UserID).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("提升用户失败!", zap.Error(err))
		response.FailWithMessage("提升失败", c)
		return
	}

	response.OkWithMessage("提升成功", c)
}

// DemoteUser 降级用户为普通用户
// @Tags SuperAdmin
// @Summary 降级用户为普通用户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "用户ID"
// @Success 200 {object} response.Response{msg=string} "降级成功"
// @Router /super/users/demote [post]
func (superAdminApi *SuperAdminApi) DemoteUser(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 使用超级管理员权限更新用户类型
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	updates := map[string]interface{}{
		"user_type":      systemReq.UserTypeNormal,
		"is_super_admin": false,
	}

	err = db.Model(&system.SysUser{}).Where("id = ?", reqId.ID).Updates(updates).Error
	if err != nil {
		global.GVA_LOG.Error("降级用户失败!", zap.Error(err))
		response.FailWithMessage("降级失败", c)
		return
	}

	response.OkWithMessage("降级成功", c)
}

// GetSystemStats 获取系统统计信息
// @Tags SuperAdmin
// @Summary 获取系统统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=gin.H,msg=string} "获取成功"
// @Router /super/system/stats [get]
func (superAdminApi *SuperAdminApi) GetSystemStats(c *gin.Context) {
	db := global.GVA_DB.Scopes(tenant.SuperAdmin)

	stats := gin.H{}

	// 租户统计
	var tenantCount int64
	db.Model(&system.Tenant{}).Count(&tenantCount)
	stats["tenantCount"] = tenantCount

	var activeTenantCount int64
	db.Model(&system.Tenant{}).Where("status = ?", true).Count(&activeTenantCount)
	stats["activeTenantCount"] = activeTenantCount

	// 用户统计
	var userCount int64
	db.Model(&system.SysUser{}).Count(&userCount)
	stats["userCount"] = userCount

	var superAdminCount int64
	db.Model(&system.SysUser{}).Where("is_super_admin = ?", true).Count(&superAdminCount)
	stats["superAdminCount"] = superAdminCount

	// 订单统计（所有租户）
	var orderCount int64
	db.Table("orders").Count(&orderCount)
	stats["orderCount"] = orderCount

	// 商品统计（所有租户）
	var productCount int64
	db.Table("products").Count(&productCount)
	stats["productCount"] = productCount

	response.OkWithData(stats, c)
}

// GetOperationLogs 获取超级管理员操作日志
// @Tags SuperAdmin
// @Summary 获取超级管理员操作日志
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param page query int false "页码"
// @Param pageSize query int false "每页大小"
// @Param operationType query string false "操作类型"
// @Param targetType query string false "目标类型"
// @Param userId query int false "用户ID"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /super/logs/operations [get]
func (superAdminApi *SuperAdminApi) GetOperationLogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "10"))

	filters := make(map[string]interface{})

	if operationType := c.Query("operationType"); operationType != "" {
		filters["operation_type"] = operationType
	}

	if targetType := c.Query("targetType"); targetType != "" {
		filters["target_type"] = targetType
	}

	if userIDStr := c.Query("userId"); userIDStr != "" {
		if userID, err := strconv.Atoi(userIDStr); err == nil {
			filters["user_id"] = userID
		}
	}

	superAdminLogService := &serviceSystem.SuperAdminLogService{}
	logs, total, err := superAdminLogService.GetSuperAdminLogs(page, pageSize, filters)
	if err != nil {
		global.GVA_LOG.Error("获取操作日志失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     logs,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, "获取成功", c)
}

// GetOperationLogStats 获取操作日志统计
// @Tags SuperAdmin
// @Summary 获取操作日志统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param days query int false "统计天数"
// @Success 200 {object} response.Response{data=system.SuperAdminLogStats,msg=string} "获取成功"
// @Router /super/logs/stats [get]
func (superAdminApi *SuperAdminApi) GetOperationLogStats(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))

	superAdminLogService := &serviceSystem.SuperAdminLogService{}
	stats, err := superAdminLogService.GetSuperAdminLogStats(days)
	if err != nil {
		global.GVA_LOG.Error("获取操作统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithData(stats, c)
}

// GetCrossTenantsData 获取跨租户数据
// @Tags SuperAdmin
// @Summary 获取跨租户数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param dataType path string true "数据类型"
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /super/data/{dataType} [get]
func (superAdminApi *SuperAdminApi) GetCrossTenantsData(c *gin.Context) {
	dataType := c.Param("dataType")

	var pageInfo request.PageInfo
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	db := global.GVA_DB.Scopes(tenant.SuperAdmin)
	limit := pageInfo.PageSize
	offset := pageInfo.PageSize * (pageInfo.Page - 1)

	var total int64
	var data interface{}

	switch dataType {
	case "orders":
		var orders []interface{}
		tableName := ordersModel.Order{}.TableName()
		err = db.Table(tableName).Count(&total).Error
		if err == nil {
			err = db.Table(tableName).Limit(limit).Offset(offset).Find(&orders).Error
		}
		data = orders
	case "products":
		var products []interface{}
		err = db.Table("products").Count(&total).Error
		if err == nil {
			err = db.Table("products").Limit(limit).Offset(offset).Find(&products).Error
		}
		data = products
	case "franchisees":
		var franchisees []interface{}
		err = db.Table("franchisees").Count(&total).Error
		if err == nil {
			err = db.Table("franchisees").Limit(limit).Offset(offset).Find(&franchisees).Error
		}
		data = franchisees
	default:
		response.FailWithMessage("不支持的数据类型", c)
		return
	}

	if err != nil {
		global.GVA_LOG.Error("获取跨租户数据失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     data,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
