package system

import (
	"strconv"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type TenantHealthApi struct{}

// GetTenantHealth 获取租户系统健康状态
// @Tags TenantHealth
// @Summary 获取租户系统健康状态
// @Description 获取多租户系统的健康状态和监控指标
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /tenant/health [get]
func (t *TenantHealthApi) GetTenantHealth(c *gin.Context) {
	monitor := global.GetTenantMonitor()

	healthStatus := monitor.GetHealthStatus()
	metrics := monitor.GetMetrics()

	// 获取缓存统计
	cacheStats := global.GetTenantCacheStats()

	// 获取错误处理器统计
	errorHandler := global.GetTenantErrorHandler()
	errorStats := errorHandler.GetErrorStats()

	// 获取自动检测的租户表
	autoDetectedTables := global.GetAutoDetectedTenantTables()

	result := map[string]interface{}{
		"health_status":        healthStatus,
		"monitoring_metrics":   metrics,
		"cache_statistics":     cacheStats,
		"error_statistics":     errorStats,
		"auto_detected_tables": autoDetectedTables,
		"system_info": map[string]interface{}{
			"distributed_mode":  global.IsDistributedMode(),
			"distributed_cache": global.IsDistributedCacheEnabled(),
			"fallback_mode":     !global.IsTenantSystemHealthy(),
		},
	}

	if healthStatus["healthy"].(bool) {
		response.OkWithData(result, c)
	} else {
		global.GVA_LOG.Warn("租户系统健康检查发现问题",
			zap.Any("issues", healthStatus["issues"]))
		response.FailWithDetailed(result, "租户系统存在健康问题", c)
	}
}

// GetTenantMetrics 获取租户监控指标
// @Tags TenantHealth
// @Summary 获取租户监控指标
// @Description 获取详细的租户系统监控指标
// @Produce json
// @Success 200 {object} response.Response{data=global.TenantMonitorMetrics} "成功"
// @Router /tenant/metrics [get]
func (t *TenantHealthApi) GetTenantMetrics(c *gin.Context) {
	monitor := global.GetTenantMonitor()
	metrics := monitor.GetMetrics()

	response.OkWithData(metrics, c)
}

// GetCacheStats 获取缓存统计信息
// @Tags TenantHealth
// @Summary 获取租户缓存统计
// @Description 获取租户缓存的详细统计信息
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /tenant/cache/stats [get]
func (t *TenantHealthApi) GetCacheStats(c *gin.Context) {
	cacheStats := global.GetTenantCacheStats()
	response.OkWithData(cacheStats, c)
}

// GetErrorStats 获取错误统计信息
// @Tags TenantHealth
// @Summary 获取租户错误统计
// @Description 获取租户系统的错误统计信息
// @Produce json
// @Success 200 {object} response.Response{data=map[string]interface{}} "成功"
// @Router /tenant/errors/stats [get]
func (t *TenantHealthApi) GetErrorStats(c *gin.Context) {
	errorHandler := global.GetTenantErrorHandler()
	errorStats := errorHandler.GetErrorStats()
	response.OkWithData(errorStats, c)
}

// GetSecurityEvents 获取安全事件
// @Tags TenantHealth
// @Summary 获取租户安全事件
// @Description 获取最近的租户安全事件列表
// @Produce json
// @Param limit query int false "限制数量" default(50)
// @Success 200 {object} response.Response{data=[]global.SecurityEvent} "成功"
// @Router /tenant/security/events [get]
func (t *TenantHealthApi) GetSecurityEvents(c *gin.Context) {
	limit := 50
	if l := c.Query("limit"); l != "" {
		if parsedLimit, err := strconv.Atoi(l); err == nil && parsedLimit > 0 && parsedLimit <= 1000 {
			limit = parsedLimit
		}
	}

	auditor := global.GetSecurityAuditor()
	events := auditor.GetRecentEvents(limit)

	response.OkWithData(events, c)
}

// ResetMetrics 重置监控指标
// @Tags TenantHealth
// @Summary 重置租户监控指标
// @Description 重置租户系统的监控指标（需要管理员权限）
// @Produce json
// @Success 200 {object} response.Response "成功"
// @Router /tenant/metrics/reset [post]
func (t *TenantHealthApi) ResetMetrics(c *gin.Context) {
	// TODO: 添加管理员权限检查

	monitor := global.GetTenantMonitor()
	monitor.ResetMetrics()

	global.GVA_LOG.Info("租户监控指标已重置",
		zap.String("operator", "admin"), // TODO: 从context获取实际用户
		zap.String("ip", c.ClientIP()))

	response.OkWithMessage("监控指标已重置", c)
}

// ClearCache 清空租户缓存
// @Tags TenantHealth
// @Summary 清空租户缓存
// @Description 清空所有租户缓存（需要管理员权限）
// @Produce json
// @Success 200 {object} response.Response "成功"
// @Router /tenant/cache/clear [post]
func (t *TenantHealthApi) ClearCache(c *gin.Context) {
	// TODO: 添加管理员权限检查

	global.ClearTenantCache()

	global.GVA_LOG.Info("租户缓存已清空",
		zap.String("operator", "admin"), // TODO: 从context获取实际用户
		zap.String("ip", c.ClientIP()))

	response.OkWithMessage("租户缓存已清空", c)
}

// ClearErrors 清空错误记录
// @Tags TenantHealth
// @Summary 清空租户错误记录
// @Description 清空租户系统的错误记录（需要管理员权限）
// @Produce json
// @Success 200 {object} response.Response "成功"
// @Router /tenant/errors/clear [post]
func (t *TenantHealthApi) ClearErrors(c *gin.Context) {
	// TODO: 添加管理员权限检查

	errorHandler := global.GetTenantErrorHandler()
	errorHandler.ClearErrors()

	global.GVA_LOG.Info("租户错误记录已清空",
		zap.String("operator", "admin"), // TODO: 从context获取实际用户
		zap.String("ip", c.ClientIP()))

	response.OkWithMessage("错误记录已清空", c)
}

// UpdateAlertThresholds 更新告警阈值
// @Tags TenantHealth
// @Summary 更新租户告警阈值
// @Description 更新租户监控的告警阈值配置（需要管理员权限）
// @Accept json
// @Produce json
// @Param thresholds body global.AlertThresholds true "告警阈值配置"
// @Success 200 {object} response.Response "成功"
// @Router /tenant/alerts/thresholds [put]
func (t *TenantHealthApi) UpdateAlertThresholds(c *gin.Context) {
	// TODO: 添加管理员权限检查

	var thresholds global.AlertThresholds
	if err := c.ShouldBindJSON(&thresholds); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	monitor := global.GetTenantMonitor()
	monitor.UpdateThresholds(thresholds)

	global.GVA_LOG.Info("租户告警阈值已更新",
		zap.String("operator", "admin"), // TODO: 从context获取实际用户
		zap.String("ip", c.ClientIP()),
		zap.Any("thresholds", thresholds))

	response.OkWithMessage("告警阈值已更新", c)
}

// AnalyzeIndexUsage 分析索引使用情况
// @Tags TenantHealth
// @Summary 分析数据库索引使用情况
// @Description 分析租户相关表的数据库索引使用情况（需要管理员权限）
// @Produce json
// @Success 200 {object} response.Response "成功"
// @Router /tenant/database/analyze-indexes [post]
func (t *TenantHealthApi) AnalyzeIndexUsage(c *gin.Context) {
	// TODO: 添加管理员权限检查

	// 这是一个异步操作，不阻塞请求
	go func() {
		global.GVA_LOG.Info("开始分析数据库索引使用情况")
		// 这里需要导入initialize包
		// initialize.AnalyzeIndexUsage()
		global.GVA_LOG.Info("数据库索引分析完成")
	}()

	response.OkWithMessage("索引分析已启动，请查看日志获取结果", c)
}
