package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/app"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseeAddressApi struct {
}

var franchiseeAddressService = service.ServiceGroupApp.AppServiceGroup.FranchiseeAddressService

// CreateFranchiseeAddress 创建FranchiseeAddress
// @Tags FranchiseeAddress
// @Summary 创建FranchiseeAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body app.FranchiseeAddress true "创建FranchiseeAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /franchiseeAddress/createFranchiseeAddress [post]
func (franchiseeAddressApi *FranchiseeAddressApi) CreateFranchiseeAddress(c *gin.Context) {
	var franchiseeAddress app.FranchiseeAddress
	err := c.ShouldBindJSON(&franchiseeAddress)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if franchiseeAddress.FranchiseeId == 0 {
		response.FailWithMessage("加盟商ID不能为空", c)
		return
	}

	err = utils.Verify(franchiseeAddress, utils.FranchiseeAddressVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := franchiseeAddressService.CreateFranchiseeAddress(&franchiseeAddress); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteFranchiseeAddress 删除FranchiseeAddress
// @Tags FranchiseeAddress
// @Summary 删除FranchiseeAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body app.FranchiseeAddress true "删除FranchiseeAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /franchiseeAddress/deleteFranchiseeAddress [delete]
func (franchiseeAddressApi *FranchiseeAddressApi) DeleteFranchiseeAddress(c *gin.Context) {
	var franchiseeAddress app.FranchiseeAddress
	err := c.ShouldBindJSON(&franchiseeAddress)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeAddressService.DeleteFranchiseeAddress(franchiseeAddress); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteFranchiseeAddressByIds 批量删除FranchiseeAddress
// @Tags FranchiseeAddress
// @Summary 批量删除FranchiseeAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除FranchiseeAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /franchiseeAddress/deleteFranchiseeAddressByIds [delete]
func (franchiseeAddressApi *FranchiseeAddressApi) DeleteFranchiseeAddressByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeAddressService.DeleteFranchiseeAddressByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateFranchiseeAddress 更新FranchiseeAddress
// @Tags FranchiseeAddress
// @Summary 更新FranchiseeAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body app.FranchiseeAddress true "更新FranchiseeAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchiseeAddress/updateFranchiseeAddress [put]
func (franchiseeAddressApi *FranchiseeAddressApi) UpdateFranchiseeAddress(c *gin.Context) {
	var franchiseeAddress app.FranchiseeAddress
	err := c.ShouldBindJSON(&franchiseeAddress)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeAddressService.UpdateFranchiseeAddress(franchiseeAddress); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindFranchiseeAddress 用id查询FranchiseeAddress
// @Tags FranchiseeAddress
// @Summary 用id查询FranchiseeAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query app.FranchiseeAddress true "用id查询FranchiseeAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /franchiseeAddress/findFranchiseeAddress [get]
func (franchiseeAddressApi *FranchiseeAddressApi) FindFranchiseeAddress(c *gin.Context) {
	var franchiseeAddress app.FranchiseeAddress
	err := c.ShouldBindQuery(&franchiseeAddress)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchiseeAddress, err := franchiseeAddressService.GetFranchiseeAddress(franchiseeAddress.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchiseeAddress": refranchiseeAddress}, c)
	}
}

// GetFranchiseeAddressList 分页获取FranchiseeAddress列表
// @Tags FranchiseeAddress
// @Summary 分页获取FranchiseeAddress列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.FranchiseeAddressSearch true "分页获取FranchiseeAddress列表"
// @Success 200 {object} []app.FranchiseeAddress "data.list"
// @Router /franchiseeAddress/getFranchiseeAddressList [get]
func (franchiseeAddressApi *FranchiseeAddressApi) GetFranchiseeAddressList(c *gin.Context) {
	var pageInfo appReq.FranchiseeAddressSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := franchiseeAddressService.GetFranchiseeAddressInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
