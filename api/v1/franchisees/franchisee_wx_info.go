package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/franchisees/request"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseeWxInfoApi struct {
}

var franchiseeWxInfoService = service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeWxInfoService

// // CreateFranchiseeWxInfo 创建FranchiseeWxInfo
// // @Tags FranchiseeWxInfo
// // @Summary 创建FranchiseeWxInfo
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body franchisees.FranchiseeWxInfo true "创建FranchiseeWxInfo"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /franchiseeWxInfo/createFranchiseeWxInfo [post]
// func (franchiseeWxInfoApi *FranchiseeWxInfoApi) CreateFranchiseeWxInfo(c *gin.Context) {
// 	var franchiseeWxInfo franchisees.FranchiseeWxInfo
// 	err := c.ShouldBindJSON(&franchiseeWxInfo)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	verify := utils.Rules{
// 		"FranchiseeId": {utils.NotEmpty()},
// 	}
// 	if err := utils.Verify(franchiseeWxInfo, verify); err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := franchiseeWxInfoService.CreateFranchiseeWxInfo(&franchiseeWxInfo); err != nil {
// 		global.GVA_LOG.Error("创建失败!", zap.Error(err))
// 		response.FailWithMessage("创建失败", c)
// 	} else {
// 		response.OkWithMessage("创建成功", c)
// 	}
// }

// // DeleteFranchiseeWxInfo 删除FranchiseeWxInfo
// // @Tags FranchiseeWxInfo
// // @Summary 删除FranchiseeWxInfo
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body franchisees.FranchiseeWxInfo true "删除FranchiseeWxInfo"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// // @Router /franchiseeWxInfo/deleteFranchiseeWxInfo [delete]
// func (franchiseeWxInfoApi *FranchiseeWxInfoApi) DeleteFranchiseeWxInfo(c *gin.Context) {
// 	var franchiseeWxInfo franchisees.FranchiseeWxInfo
// 	err := c.ShouldBindJSON(&franchiseeWxInfo)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := franchiseeWxInfoService.DeleteFranchiseeWxInfo(franchiseeWxInfo); err != nil {
// 		global.GVA_LOG.Error("删除失败!", zap.Error(err))
// 		response.FailWithMessage("删除失败", c)
// 	} else {
// 		response.OkWithMessage("删除成功", c)
// 	}
// }

// // DeleteFranchiseeWxInfoByIds 批量删除FranchiseeWxInfo
// // @Tags FranchiseeWxInfo
// // @Summary 批量删除FranchiseeWxInfo
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body request.IdsReq true "批量删除FranchiseeWxInfo"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// // @Router /franchiseeWxInfo/deleteFranchiseeWxInfoByIds [delete]
// func (franchiseeWxInfoApi *FranchiseeWxInfoApi) DeleteFranchiseeWxInfoByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := franchiseeWxInfoService.DeleteFranchiseeWxInfoByIds(IDS); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// UpdateFranchiseeWxInfo 更新FranchiseeWxInfo
// @Tags FranchiseeWxInfo
// @Summary 更新FranchiseeWxInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.FranchiseeWxInfo true "更新FranchiseeWxInfo"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchiseeWxInfo/updateFranchiseeWxInfo [put]
func (franchiseeWxInfoApi *FranchiseeWxInfoApi) UpdateFranchiseeWxInfo(c *gin.Context) {
	var franchiseeWxInfo franchisees.FranchiseeWxInfo
	err := c.ShouldBindJSON(&franchiseeWxInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"FranchiseeId": {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseeWxInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeWxInfoService.UpdateFranchiseeWxInfo(franchiseeWxInfo); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindFranchiseeWxInfo 用id查询FranchiseeWxInfo
// @Tags FranchiseeWxInfo
// @Summary 用id查询FranchiseeWxInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.FranchiseeWxInfoCreate true "用id查询FranchiseeWxInfo"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /franchiseeWxInfo/findFranchiseeWxInfo [get]
func (franchiseeWxInfoApi *FranchiseeWxInfoApi) FindFranchiseeWxInfo(c *gin.Context) {
	var franchiseeWxInfo franchiseesReq.FranchiseeWxInfoCreate
	err := c.ShouldBindQuery(&franchiseeWxInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"FranchiseeId": {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseeWxInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if refranchiseeWxInfo, err := franchiseeWxInfoService.GetFranchiseeWxInfo(franchiseeWxInfo.FranchiseeId); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchiseeWxInfo": refranchiseeWxInfo}, c)
	}
}

// FranchiseeWxMiniProgramBind 微信小程序登录
// @Tags FranchiseeWxInfo
// @Summary FranchiseeWxMiniProgramBand 微信小程序登录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.FranchiseeWxMiniProgramBindRequest true "FranchiseeWxLogin"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"登录成功"}"
// @Router /franchiseeWxInfo/franchiseeWxMiniProgramBind [get]
func (franchiseeWxInfoApi *FranchiseeWxInfoApi) FranchiseeWxMiniProgramBind(c *gin.Context) {
	var franchiseeWxInfo request.FranchiseeWxMiniProgramBindRequest
	err := c.ShouldBindQuery(&franchiseeWxInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Code": {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseeWxInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeWxInfoService.FranchiseeWxMiniProgramBind(franchiseeWxInfo.Code, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("登录失败!", zap.Error(err))
		response.FailWithMessage("登录失败"+err.Error(), c)
	} else {
		response.OkWithMessage("登录成功", c)
	}
}

// // GetFranchiseeWxInfoList 分页获取FranchiseeWxInfo列表
// // @Tags FranchiseeWxInfo
// // @Summary 分页获取FranchiseeWxInfo列表
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data query franchiseesReq.FranchiseeWxInfoSearch true "分页获取FranchiseeWxInfo列表"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /franchiseeWxInfo/getFranchiseeWxInfoList [get]
// func (franchiseeWxInfoApi *FranchiseeWxInfoApi) GetFranchiseeWxInfoList(c *gin.Context) {
// 	var pageInfo franchiseesReq.FranchiseeWxInfoSearch
// 	err := c.ShouldBindQuery(&pageInfo)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if list, total, err := franchiseeWxInfoService.GetFranchiseeWxInfoInfoList(pageInfo); err != nil {
// 		global.GVA_LOG.Error("获取失败!", zap.Error(err))
// 		response.FailWithMessage("获取失败", c)
// 	} else {
// 		response.OkWithDetailed(response.PageResult{
// 			List:     list,
// 			Total:    total,
// 			Page:     pageInfo.Page,
// 			PageSize: pageInfo.PageSize,
// 		}, "获取成功", c)
// 	}
// }
