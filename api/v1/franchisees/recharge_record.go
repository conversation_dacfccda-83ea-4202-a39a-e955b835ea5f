package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

type RechargeRecordApi struct {
}

var rechargeRecordService = service.ServiceGroupApp.FranchiseesServiceGroup.RechargeRecordService

/*
// CreateRechargeRecord 创建RechargeRecord
// @Tags RechargeRecord
// @Summary 创建RechargeRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.RechargeRecord true "创建RechargeRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /rechargeRecord/createRechargeRecord [post]
*/
func (rechargeRecordApi *RechargeRecordApi) CreateRechargeRecord(c *gin.Context) {
	var rechargeRecord franchisees.RechargeRecord
	err := c.ShouldBindJSON(&rechargeRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := rechargeRecordService.CreateRechargeRecord(&rechargeRecord); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

/*
// DeleteRechargeRecord 删除RechargeRecord
// @Tags RechargeRecord
// @Summary 删除RechargeRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.RechargeRecord true "删除RechargeRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /rechargeRecord/deleteRechargeRecord [delete]
*/
func (rechargeRecordApi *RechargeRecordApi) DeleteRechargeRecord(c *gin.Context) {
	var rechargeRecord franchisees.RechargeRecord
	err := c.ShouldBindJSON(&rechargeRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := rechargeRecordService.DeleteRechargeRecord(rechargeRecord); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

/*
// DeleteRechargeRecordByIds 批量删除RechargeRecord
// @Tags RechargeRecord
// @Summary 批量删除RechargeRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除RechargeRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /rechargeRecord/deleteRechargeRecordByIds [delete]
*/
func (rechargeRecordApi *RechargeRecordApi) DeleteRechargeRecordByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := rechargeRecordService.DeleteRechargeRecordByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

/*
// UpdateRechargeRecord 更新RechargeRecord
// @Tags RechargeRecord
// @Summary 更新RechargeRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.RechargeRecord true "更新RechargeRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /rechargeRecord/updateRechargeRecord [put]
*/
func (rechargeRecordApi *RechargeRecordApi) UpdateRechargeRecord(c *gin.Context) {
	var rechargeRecord franchisees.RechargeRecord
	err := c.ShouldBindJSON(&rechargeRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := rechargeRecordService.UpdateRechargeRecord(rechargeRecord); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindRechargeRecord 用id查询RechargeRecord
// @Tags RechargeRecord
// @Summary 用id查询RechargeRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchisees.RechargeRecord true "用id查询RechargeRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /rechargeRecord/findRechargeRecord [get]
func (rechargeRecordApi *RechargeRecordApi) FindRechargeRecord(c *gin.Context) {
	var rechargeRecord franchisees.RechargeRecord
	err := c.ShouldBindQuery(&rechargeRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rerechargeRecord, err := rechargeRecordService.GetRechargeRecord(rechargeRecord.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rerechargeRecord": rerechargeRecord}, c)
	}
}

// GetRechargeRecordList 分页获取RechargeRecord列表
// @Tags RechargeRecord
// @Summary 分页获取RechargeRecord列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.RechargeRecordSearch true "分页获取RechargeRecord列表"
// @Success 200 {object} response.PageResult{list=[]response.RechargeRecordItem} "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /rechargeRecord/getRechargeRecordList [get]
func (rechargeRecordApi *RechargeRecordApi) GetRechargeRecordList(c *gin.Context) {
	var pageInfo franchiseesReq.RechargeRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, summary, err := rechargeRecordService.GetRechargeRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Extra:    summary,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// Recharge 充值金额/积分
// @Tags RechargeRecord
// @Summary 充值金额/积分
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchiseesReq.Recharge true "充值金额/积分"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"充值成功"}"
// @Router /rechargeRecord/recharge [post]
func (rechargeRecordApi *RechargeRecordApi) Recharge(c *gin.Context) {
	var recharge franchiseesReq.Recharge
	err := c.ShouldBindJSON(&recharge)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(recharge, utils.RechargeVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 账户锁
	locker := utils.NewDistributedLockEngine(utils.GetFranchiseeAccountKey(recharge.FranchiseeId))
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		response.FailWithMessage("充值异常请稍后重试", c)
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()

	if err := rechargeRecordService.Recharge(&recharge, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("充值失败!", zap.Error(err))
		response.FailWithMessage("充值失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("充值成功", c)
	}
}

// Deduct 扣减金额/积分
// @Tags RechargeRecord
// @Summary 扣减金额/积分
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchiseesReq.Deduct true "扣减金额/积分"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"扣减成功"}"
// @Router /rechargeRecord/deduct [post]
func (rechargeRecordApi *RechargeRecordApi) Deduct(c *gin.Context) {
	var deduct franchiseesReq.Deduct
	err := c.ShouldBindJSON(&deduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(deduct, utils.RechargeVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 账户锁
	locker := utils.NewDistributedLockEngine(utils.GetFranchiseeAccountKey(deduct.FranchiseeId))
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		response.FailWithMessage("扣减异常请稍后重试", c)
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()

	if err := rechargeRecordService.Deduct(&deduct, utils.GetUserID(c)); err != nil {
		global.GVA_LOG.Error("扣减失败!", zap.Error(err))
		response.FailWithMessage("扣减失败", c)
	} else {
		response.OkWithMessage("扣减成功", c)
	}
}

// ExportRechargeRecord 导出交易流水记录(默认导出前 3 个月的数据)
// @Tags RechargeRecord
// @Summary 导出交易流水记录 (默认导出前 3 个月的数据)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.RechargeRecordSearch true "导出交易流水记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"导出成功"}"
// @Router /rechargeRecord/exportRechargeRecord [get]
func (rechargeRecordApi *RechargeRecordApi) ExportRechargeRecord(c *gin.Context) {
	var info franchiseesReq.RechargeRecordSearch
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	f, err := rechargeRecordService.ExportRechargeRecords(info)
	if err != nil {
		global.GVA_LOG.Error("导出失败!", zap.Error(err))
		response.FailWithMessage("导出失败", c)
		return
	}
	// 设置响应头
	c.Header("Content-Disposition", "attachment; filename=交易流水.xlsx")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}
