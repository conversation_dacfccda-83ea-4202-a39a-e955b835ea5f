package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetFranchiseeAccountSummary 获取加盟商账户摘要
// @Tags Franchisee
// @Summary 获取加盟商账户摘要
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.FranchiseeAccountSummary,msg=string} "获取加盟商账户摘要,返回包括总数，总余额，总积分"
// @Router /franchisee/franchiseeAccountSummary [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeAccountSummary(c *gin.Context) {
	summary, err := franchiseeService.GetFranchiseeAccountSummary()
	if err != nil {
		global.GVA_LOG.Error("获取加盟商账户摘要失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商总数失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(summary, "获取加盟商总数成功", c)
	}
}
