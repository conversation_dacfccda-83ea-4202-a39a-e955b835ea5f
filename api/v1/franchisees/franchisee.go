package franchisees

import (
	"net/http"
	"strconv"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseeApi struct {
}

var franchiseeService = service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeService

// CreateFranchisee 创建Franchisee
// @Tags Franchisee
// @Summary 创建Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.Franchisee true "创建Franchisee"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /franchisee/createFranchisee [post]
func (franchiseeApi *FranchiseeApi) CreateFranchisee(c *gin.Context) {
	var franchisee franchisees.Franchisee
	err := c.ShouldBindJSON(&franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	//if franchisee.MarketLeadID == 0 || franchisee.SupervisionLeadID == 0 {
	//	response.FailWithMessage("市场经理和督导经理必填", c)
	//	return
	//}

	err = utils.Verify(franchisee, utils.FranchiseeVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if _, err := franchiseeService.CreateFranchisee(&franchisee); err != nil {
		global.GVA_LOG.Error("加盟商添加失败!", zap.Error(err))
		response.FailWithMessage("加盟商添加失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("加盟商添加成功", c)
	}
}

// DeleteFranchisee 删除Franchisee
// @Tags Franchisee
// @Summary 删除Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.Franchisee true "删除Franchisee"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /franchisee/deleteFranchisee [delete]
func (franchiseeApi *FranchiseeApi) DeleteFranchisee(c *gin.Context) {
	var franchisee franchisees.Franchisee
	err := c.ShouldBindJSON(&franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeService.DeleteFranchisee(franchisee); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteFranchiseeByIds 批量删除Franchisee
// @Tags Franchisee
// @Summary 批量删除Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Franchisee"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /franchisee/deleteFranchiseeByIds [delete]
func (franchiseeApi *FranchiseeApi) DeleteFranchiseeByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeService.DeleteFranchiseeByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateFranchisee 更新Franchisee
// @Tags Franchisee
// @Summary 更新Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.Franchisee true "更新Franchisee"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchisee/updateFranchisee [put]
func (franchiseeApi *FranchiseeApi) UpdateFranchisee(c *gin.Context) {
	var franchisee franchisees.Franchisee
	err := c.ShouldBindJSON(&franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeService.UpdateFranchisee(franchisee); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindFranchisee 用id查询Franchisee
// @Tags Franchisee
// @Summary 用id查询Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseesReq.FranchiseeIdReq true "用id查询Franchisee"
// @Success 200 {object} response.Response{refranchisee=response.FranchiseeBase}
// @Router /franchisee/findFranchisee [get]
func (franchiseeApi *FranchiseeApi) FindFranchisee(c *gin.Context) {
	var franchiseeIdReq franchiseesReq.FranchiseeIdReq
	err := c.ShouldBindQuery(&franchiseeIdReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchisee, err := franchiseeService.GetFranchiseeBase(franchiseeIdReq.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"refranchisee": refranchisee}, c)
	}
}

// GetFranchiseeList 分页获取Franchisee列表
// @Tags Franchisee
// @Summary 分页获取Franchisee列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseesReq.FranchiseeSearch true "分页获取Franchisee列表"
// @Success 200 {object} response.PageResult{List=[]response.FranchiseeList} "data.list"
// @Router /franchisee/getFranchiseeList [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeList(c *gin.Context) {
	var pageInfo franchiseesReq.FranchiseeSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := franchiseeService.GetFranchiseeInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetFranchiseeBaseList 获取Franchisee 基础信息列表，支持手机号和姓名模糊搜索
// @Tags Franchisee
// @Summary 获取Franchisee 基础信息列表支持手机号和姓名模糊搜索
// @Security ApiKeyAuth
// @accept application/json
// @Param data query request.FranchiseeBaseListReq true "获取Franchisee 基础信息列表"
// @Success 200 {object} response.PageResult{List=[]response.FranchiseeWithCategory} "data.list"
// @Router /franchisee/getFranchiseeBaseList [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeBaseList(c *gin.Context) {
	//var pageInfo request.PageInfo
	var baseListReq franchiseesReq.FranchiseeBaseListReq
	err := c.ShouldBindQuery(&baseListReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	authID := utils.GetUserAuthorityId(c)
	if authID == 222 && baseListReq.MarketLeadID == nil {
		userID := utils.GetUserID(c)
		userIDint := int(userID)
		baseListReq.MarketLeadID = &userIDint
	}
	if authID == 333 && baseListReq.SupervisionLeadID == nil {
		userID := utils.GetUserID(c)
		userIDint := int(userID)
		baseListReq.SupervisionLeadID = &userIDint
	}

	if list, total, err := franchiseeService.GetFranchiseeBaseList(baseListReq); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     baseListReq.Page,
			PageSize: baseListReq.PageSize,
		}, "获取成功", c)
	}
}

// ExportFranchisee 导出加盟商
// @Tags Franchisee
// @Summary 导出加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseesReq.FranchiseeSearch true "导出加盟商"
// @Success 200
// @Router /franchisee/exportFranchisee [get]
func (franchiseeApi *FranchiseeApi) ExportFranchisee(c *gin.Context) {
	var pageInfo franchiseesReq.FranchiseeSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	f, err := franchiseeService.ExportFranchiseeInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("导出失败!", zap.Error(err))
		response.FailWithMessage("导出失败", c)
		return
	}
	// 设置响应头
	c.Header("Content-Disposition", "attachment; filename=加盟商列表.xlsx")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}

// GetFranchiseeInfo 加盟商简介
// @Tags Franchisee
// @Summary 加盟商简介
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param franchiseeId path int  true "加盟商id"
// @Success 200 {object} response.Response{data=response.FranchiseeBrief,msg=string}
// @Router /franchisee/franchiseeInfo/{franchiseeId} [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeInfo(c *gin.Context) {
	param := c.Param("franchiseeId")
	// 参数验证
	if param == "" {
		response.FailWithMessage("参数错误", c)
		return
	}
	franchiseeID, err := strconv.Atoi(param)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	if franchiseeBrief, err := franchiseeService.GetFranchiseeInfo(franchiseeID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(franchiseeBrief, c)
	}
}

// Baned 禁用Franchisee
// @Tags Franchisee
// @Summary 禁用Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "禁用Franchisee"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"禁用成功"}"
// @Router /franchisee/baned [post]
func (franchiseeApi *FranchiseeApi) Baned(c *gin.Context) {
	var ids request.IdsReq
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(ids.Ids) == 0 {
		response.FailWithMessage("参数错误", c)
		return
	}

	if err := franchiseeService.BanedFranchisee(ids); err != nil {
		global.GVA_LOG.Error("禁用失败!", zap.Error(err))
		response.FailWithMessage("禁用失败"+err.Error(), c)
	} else {
		response.OkWithMessage("禁用成功", c)
	}
}

// Unbaned 解禁Franchisee
// @Tags Franchisee
// @Summary 解禁Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "解禁Franchisee"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"解禁成功"}"
// @Router /franchisee/unbaned [post]
func (franchiseeApi *FranchiseeApi) Unbaned(c *gin.Context) {
	var ids request.IdsReq
	err := c.ShouldBindJSON(&ids)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if len(ids.Ids) == 0 {
		response.FailWithMessage("参数错误", c)
		return
	}
	if err := franchiseeService.UnbanedFranchisee(ids); err != nil {
		global.GVA_LOG.Error("解禁失败!", zap.Error(err))
		response.FailWithMessage("解禁失败", c)
	} else {
		response.OkWithMessage("解禁成功", c)
	}
}

// ApproveFranchisee 审核意向加盟商
// @Tags Franchisee
// @Summary 审核意向加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchiseesReq.FranchiseeStandbyApproveReq true "审核意向加盟商"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"审批成功"}"
// @Router /franchisee/approveFranchisee [post]
func (franchiseeApi *FranchiseeApi) ApproveFranchisee(c *gin.Context) {
	var approveReq franchiseesReq.FranchiseeStandbyApproveReq
	if err := c.ShouldBindJSON(&approveReq); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	switch approveReq.Result {
	case false:
		// Handle false case if needed

	case true:
		if err := utils.Verify(approveReq.FR, utils.FranchiseeVerify); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if err := utils.Verify(approveReq.ST, utils.FranchiseeStandbyRegVerify); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}

	default:
		response.FailWithMessage("参数错误", c)
	}
	userID := utils.GetUserID(c)
	if err := franchiseeService.ApproveFranchisee(userID, approveReq); err != nil {
		global.GVA_LOG.Error("审批失败!", zap.Error(err))
		response.FailWithMessage("审批失败", c)
		return
	}

	response.OkWithMessage("审批成功", c)
}

// GetTopFranchisee 获取一级分销加盟商(不包括下级信息，用来处理简单请求)
// @Tags Franchisee
// @Summary 获取一级分销加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]franchisees.Franchisee,msg=string}
// @Router /franchisee/topFranchisee [get]
func (franchiseeApi *FranchiseeApi) GetTopFranchisee(c *gin.Context) {
	if topFranchisee, err := franchiseeService.GetTopFranchisee(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(topFranchisee, c)
	}
}

// GetTopFranchiseeWithCustomer 获取一级分销加盟商及树形结构
// @Tags Franchisee
// @Summary 获取一级分销加盟商及树形结构
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.FranchiseeCustomerCount,msg=string}
// @Router /franchisee/topFranchiseeWithCustomer [get]
func (franchiseeApi *FranchiseeApi) GetTopFranchiseeWithCustomer(c *gin.Context) {
	if topFranchisee, err := franchiseeService.GetTopFranchiseeWithCustomerCount(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(topFranchisee, c)
	}
}

// GetFranchiseeStatistics 获取加盟商统计
// @Tags Franchisee
// @Summary 获取加盟商统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.FranchiseeStatistics,msg=string}
// @Router /franchisee/franchiseeStatistics [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeStatistics(c *gin.Context) {
	if franchiseeStatistics, err := franchiseeService.GetFranchiseeStatistics(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(franchiseeStatistics, c)
	}
}

// GetDirectCustomers 获取直接邀请的加盟商及直接下级结构[包括一级分销加盟商,ID=0]
// @Tags Franchisee
// @Summary 获取直接邀请的加盟商,如果ID=0,则返回一级分销加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseesReq.FranchiseeIdReq true "加盟商ID"
// @Success 200 {object} response.Response{data=[]response.FranchiseeCustomer,msg=string}
// @Router /franchisee/directCustomers [get]
func (franchiseeApi *FranchiseeApi) GetDirectCustomers(c *gin.Context) {
	var franchiseeIdReq franchiseesReq.FranchiseeIdReq
	err := c.ShouldBindQuery(&franchiseeIdReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if directCustomers, err := franchiseeService.GetDirectCustomers(franchiseeIdReq.ID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(directCustomers, c)
	}
}

// GetUpTopFranchisee 获取上级链路中的一级分销加盟商
// @Tags Franchisee
// @Summary 获取上级链路中的一级分销加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.FranchiseeUpTopIDReq true "加盟商ID"
// @Success 200 {object} response.Response{data=franchisees.Franchisee,msg=string}
// @Router /franchisee/upTopFranchisee [get]
func (franchiseeApi *FranchiseeApi) GetUpTopFranchisee(c *gin.Context) {
	var franchiseeUpTopIDReq franchiseesReq.FranchiseeUpTopIDReq
	err := c.ShouldBindQuery(&franchiseeUpTopIDReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	global.GVA_LOG.Debug("franchiseeIdReq", zap.Any("franchiseeIdReq", franchiseeUpTopIDReq))
	if franchiseeUpTopIDReq.ID != 0 {
		if upTopFranchisee, err := franchiseeService.GetUpTopFranchisee(franchiseeUpTopIDReq.ID); err != nil {
			global.GVA_LOG.Error("获取失败!", zap.Error(err))
			// response.FailWithMessage("获取失败", c)
			response.OkWithData(nil, c)
			return
		} else {
			response.OkWithData(upTopFranchisee, c)
			return
		}
	}

	if franchiseeUpTopIDReq.ID == 0 && franchiseeUpTopIDReq.FranchiseeBaseID != 0 {
		disSrv := service.ServiceGroupApp.DistributionServiceGroup.DistributionConfigService
		disConf, _ := disSrv.GetDistributionConfig()
		f, err := franchiseeService.GetFranchisee(franchiseeUpTopIDReq.FranchiseeBaseID)
		if err != nil {
			global.GVA_LOG.Error("获取失败!", zap.Error(err))
			response.FailWithMessage("获取失败", c)
			return
		}
		if (f.FCategoryId != nil) && (*f.FCategoryId == disConf.TopFCategoryId) {
			response.OkWithData(f, c)
			return
		}
	} else {
		response.OkWithData(nil, c)
	}
}

// ResetFranchiseePassword 重置加盟商密码
// @Tags Franchisee
// @Summary 重置加盟商密码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.FranchiseeIdReq true "加盟商ID"
// @Success 200 {object} response.Response{msg=string}
// @Router /franchisee/resetFranchiseePassword [post]
func (franchiseeApi *FranchiseeApi) ResetFranchiseePassword(c *gin.Context) {
	var franchiseeIdReq franchiseesReq.FranchiseeIdReq
	err := c.ShouldBindJSON(&franchiseeIdReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeService.ResetFranchiseePassword(franchiseeIdReq.ID); err != nil {
		global.GVA_LOG.Error("重置加盟商密码失败!", zap.Error(err))
		response.FailWithMessage("重置加盟商密码失败", c)
	} else {
		response.OkWithMessage("重置加盟商密码成功", c)
	}
}
