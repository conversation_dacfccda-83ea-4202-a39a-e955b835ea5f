package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseeCategoryApi struct {
}

var franchiseeCategoryService = service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeCategoryService

// CreateFranchiseeCategory 创建FranchiseeCategory
// @Tags FranchiseeCategory
// @Summary 创建FranchiseeCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.FranchiseeCategory true "创建FranchiseeCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /franchiseeCategory/createFranchiseeCategory [post]
func (franchiseeCategoryApi *FranchiseeCategoryApi) CreateFranchiseeCategory(c *gin.Context) {
	var franchiseeCategory franchisees.FranchiseeCategory
	err := c.ShouldBindJSON(&franchiseeCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeCategoryService.CreateFranchiseeCategory(&franchiseeCategory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteFranchiseeCategory 删除FranchiseeCategory
// @Tags FranchiseeCategory
// @Summary 删除FranchiseeCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.FranchiseeCategory true "删除FranchiseeCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /franchiseeCategory/deleteFranchiseeCategory [delete]
func (franchiseeCategoryApi *FranchiseeCategoryApi) DeleteFranchiseeCategory(c *gin.Context) {
	var franchiseeCategory franchisees.FranchiseeCategory
	err := c.ShouldBindJSON(&franchiseeCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeCategoryService.DeleteFranchiseeCategory(franchiseeCategory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteFranchiseeCategoryByIds 批量删除FranchiseeCategory
// @Tags FranchiseeCategory
// @Summary 批量删除FranchiseeCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除FranchiseeCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /franchiseeCategory/deleteFranchiseeCategoryByIds [delete]
func (franchiseeCategoryApi *FranchiseeCategoryApi) DeleteFranchiseeCategoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeCategoryService.DeleteFranchiseeCategoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateFranchiseeCategory 更新FranchiseeCategory
// @Tags FranchiseeCategory
// @Summary 更新FranchiseeCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body franchisees.FranchiseeCategory true "更新FranchiseeCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchiseeCategory/updateFranchiseeCategory [put]
func (franchiseeCategoryApi *FranchiseeCategoryApi) UpdateFranchiseeCategory(c *gin.Context) {
	var franchiseeCategory franchisees.FranchiseeCategory
	err := c.ShouldBindJSON(&franchiseeCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseeCategoryService.UpdateFranchiseeCategory(franchiseeCategory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindFranchiseeCategory 用id查询FranchiseeCategory
// @Tags FranchiseeCategory
// @Summary 用id查询FranchiseeCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchisees.FranchiseeCategory true "用id查询FranchiseeCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /franchiseeCategory/findFranchiseeCategory [get]
func (franchiseeCategoryApi *FranchiseeCategoryApi) FindFranchiseeCategory(c *gin.Context) {
	var franchiseeCategory franchisees.FranchiseeCategory
	err := c.ShouldBindQuery(&franchiseeCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchiseeCategory, err := franchiseeCategoryService.GetFranchiseeCategory(franchiseeCategory.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchiseeCategory": refranchiseeCategory}, c)
	}
}

// GetFranchiseeCategoryList 分页获取FranchiseeCategory列表
// @Tags FranchiseeCategory
// @Summary 分页获取FranchiseeCategory列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query franchiseesReq.FranchiseeCategorySearch true "分页获取FranchiseeCategory列表"
// @Success 200 {object} []franchisees.FranchiseeCategory "data.list"
// @Router /franchiseeCategory/getFranchiseeCategoryList [get]
func (franchiseeCategoryApi *FranchiseeCategoryApi) GetFranchiseeCategoryList(c *gin.Context) {
	var pageInfo franchiseesReq.FranchiseeCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := franchiseeCategoryService.GetFranchiseeCategoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetSuperiorFranchiseeCategoryList 获取上级分类
// @Tags FranchiseeCategory
// @Summary 获取上级分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} []response.SuperiorFranchiseeCategory "data.list"
// @Router /franchiseeCategory/getSuperiorFranchiseeCategory [get]
func (franchiseeCategoryApi *FranchiseeCategoryApi) GetSuperiorFranchiseeCategoryList(c *gin.Context) {
	if list, err := franchiseeCategoryService.GetSuperiorFranchiseeCategoryList(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}
