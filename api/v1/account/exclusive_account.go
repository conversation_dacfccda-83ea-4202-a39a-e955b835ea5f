package account

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/account"
	accountReq "github.com/OSQianXing/guanpu-server/model/account/request"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ExclusiveAccountApi struct {
}

var exclusiveAccountService = service.ServiceGroupApp.AccountServiceGroup.ExclusiveAccountService

// CreateExclusiveAccount 创建ExclusiveAccount
// @Tags ExclusiveAccount
// @Summary 创建ExclusiveAccount
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body account.ExclusiveAccount true "创建ExclusiveAccount"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /exclusiveAccount/createExclusiveAccount [post]
func (exclusiveAccountApi *ExclusiveAccountApi) CreateExclusiveAccount(c *gin.Context) {
	var exclusiveAccount account.ExclusiveAccount
	err := c.ShouldBindJSON(&exclusiveAccount)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if len(*exclusiveAccount.FCategoryIds) == 0 {
		response.FailWithMessage("所属的加盟商分类不能为空", c)
		return
	}

	if err := exclusiveAccountService.CreateExclusiveAccount(&exclusiveAccount); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

/*
// DeleteExclusiveAccount 删除ExclusiveAccount
// @Tags ExclusiveAccount
// @Summary 删除ExclusiveAccount
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body account.ExclusiveAccount true "删除ExclusiveAccount"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /exclusiveAccount/deleteExclusiveAccount [delete]
*/
func (exclusiveAccountApi *ExclusiveAccountApi) DeleteExclusiveAccount(c *gin.Context) {
	var exclusiveAccount account.ExclusiveAccount
	err := c.ShouldBindJSON(&exclusiveAccount)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := exclusiveAccountService.DeleteExclusiveAccount(exclusiveAccount); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

/*
// DeleteExclusiveAccountByIds 批量删除ExclusiveAccount
// @Tags ExclusiveAccount
// @Summary 批量删除ExclusiveAccount
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ExclusiveAccount"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /exclusiveAccount/deleteExclusiveAccountByIds [delete]
*/
func (exclusiveAccountApi *ExclusiveAccountApi) DeleteExclusiveAccountByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := exclusiveAccountService.DeleteExclusiveAccountByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateExclusiveAccount 更新ExclusiveAccount
// @Tags ExclusiveAccount
// @Summary 更新ExclusiveAccount
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body account.ExclusiveAccount true "更新ExclusiveAccount"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /exclusiveAccount/updateExclusiveAccount [put]
func (exclusiveAccountApi *ExclusiveAccountApi) UpdateExclusiveAccount(c *gin.Context) {
	var exclusiveAccount account.ExclusiveAccount
	err := c.ShouldBindJSON(&exclusiveAccount)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := exclusiveAccountService.UpdateExclusiveAccount(exclusiveAccount); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		if err.Error() == response.AccountBalanceNotZero {
			response.FailWithMessageAndCode(response.ERRORAccountBalanceNotZero, err.Error(), c)
		} else {
			response.FailWithMessage("更新失败,"+err.Error(), c)
		}
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindExclusiveAccount 用id查询ExclusiveAccount
// @Tags ExclusiveAccount
// @Summary 用id查询ExclusiveAccount
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query account.ExclusiveAccount true "用id查询ExclusiveAccount"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /exclusiveAccount/findExclusiveAccount [get]
func (exclusiveAccountApi *ExclusiveAccountApi) FindExclusiveAccount(c *gin.Context) {
	var exclusiveAccount account.ExclusiveAccount
	err := c.ShouldBindQuery(&exclusiveAccount)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reexclusiveAccount, err := exclusiveAccountService.GetExclusiveAccount(exclusiveAccount.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reexclusiveAccount": reexclusiveAccount}, c)
	}
}

// GetExclusiveAccountList 分页获取ExclusiveAccount列表
// @Tags ExclusiveAccount
// @Summary 分页获取ExclusiveAccount列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query accountReq.ExclusiveAccountSearch true "分页获取ExclusiveAccount列表"
// @Success 200 {object} []response.ExclusiveAccount "data.list"
// @Router /exclusiveAccount/getExclusiveAccountList [get]
func (exclusiveAccountApi *ExclusiveAccountApi) GetExclusiveAccountList(c *gin.Context) {
	var pageInfo accountReq.ExclusiveAccountSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := exclusiveAccountService.GetExclusiveAccountInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// Banned
// @Tags ExclusiveAccount
// @Summary 专属账户启用禁用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body accountReq.ExclusiveAccountBannedRequest true "专属账户启用停用"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /exclusiveAccount/banned [post]
func (exclusiveAccountApi *ExclusiveAccountApi) Banned(c *gin.Context) {
	var req accountReq.ExclusiveAccountBannedRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := exclusiveAccountService.UpdateBanned(req); err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		if err.Error() == response.AccountBalanceNotZero {
			response.FailWithMessageAndCode(response.ERRORAccountBalanceNotZero, err.Error(), c)
		} else {
			response.FailWithMessage("操作失败,"+err.Error(), c)
		}
	} else {
		response.OkWithMessage("操作成功", c)
	}
}
