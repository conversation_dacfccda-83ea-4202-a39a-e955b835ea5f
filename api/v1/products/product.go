package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/products"
	productsReq "github.com/OSQianXing/guanpu-server/model/products/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

type ProductApi struct {
}

var productService = service.ServiceGroupApp.ProductsServiceGroup.ProductService

// CreateProduct 创建Product
// @Tags Product
// @Summary 创建Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.Product true "创建Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/createProduct [post]
func (productApi *ProductApi) CreateProduct(c *gin.Context) {
	var product products.Product
	err := c.ShouldBindJSON(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productService.CreateProduct(&product); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteProduct 删除Product
// @Tags Product
// @Summary 删除Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.Product true "删除Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /product/deleteProduct [delete]
func (productApi *ProductApi) DeleteProduct(c *gin.Context) {
	var product products.Product
	err := c.ShouldBindJSON(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if product.ID == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}
	if err := productService.SoftDeleteProduct(product); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteProductByIds 批量删除Product
// @Tags Product
// @Summary 批量删除Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /product/deleteProductByIds [delete]
func (productApi *ProductApi) DeleteProductByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if len(IDS.Ids) == 0 {
		response.FailWithMessage("IDs 不能为空", c)
		return
	}
	if err := productService.SoftDeleteProductByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateProduct 更新Product
// @Tags Product
// @Summary 更新Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.Product true "更新Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /product/updateProduct [put]
func (productApi *ProductApi) UpdateProduct(c *gin.Context) {
	var product products.Product
	err := c.ShouldBindJSON(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if product.ID == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}
	if err := productService.UpdateProduct(product); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// UpdateProductInventory 更新Product库存
// @Tags Product
// @Summary 更新Product库存
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.Product true "更新Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /product/updateProductInventory [put]
func (productApi *ProductApi) UpdateProductInventory(c *gin.Context) {
	var product products.Product
	err := c.ShouldBindJSON(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if product.ID == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}

	if *product.Inventory == 0 {
		response.FailWithMessage("库存数量不能为空", c)
		return
	}

	if err := productService.UpdateProductInventory(product); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// OnSale 上下架商品
// @Tags Product
// @Summary 上下架商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.OnSaleRequset true "上下架商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /product/onSale [post]
func (productApi *ProductApi) OnSale(c *gin.Context) {
	var req productsReq.OnSaleRequset
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := productService.UpdateOnSale(req); err != nil {
		global.GVA_LOG.Error("操作上下架失败!", zap.Error(err))
		response.FailWithMessage("操作上下架失败", c)
	} else {
		response.OkWithMessage("操作上下架成功", c)
	}
}

// FindProduct 用id查询Product
// @Tags Product
// @Summary 用id查询Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query products.Product true "用id查询Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /product/findProduct [get]
func (productApi *ProductApi) FindProduct(c *gin.Context) {
	var product products.Product
	err := c.ShouldBindQuery(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproduct, err := productService.GetProduct(product.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproduct": reproduct}, c)
	}
}

// GetProductList 分页获取Product列表
// @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.ProductSearch true "分页获取Product列表"
// @Success 200 {object} []response.ProductList "data.list"
// @Router /product/getProductList [get]
func (productApi *ProductApi) GetProductList(c *gin.Context) {
	var pageInfo productsReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productService.GetProductInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportProductList 导出商品列表
// @Tags Product
// @Summary 导出商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.ProductSearch true "导出商品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"导出成功"}"
// @Router /product/exportProductList [get]
func (productApi *ProductApi) ExportProductList(c *gin.Context) {
	var pageInfo productsReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	f, err := productService.ExportProductList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("导出失败!", zap.Error(err))
		response.FailWithMessage("导出失败", c)
		return
	}
	// 设置响应头
	c.Header("Content-Disposition", "attachment; filename=商品列表.xlsx")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Transfer-Encoding", "binary")

	// 写入响应
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}

// CheckProductCouldBeModified 检查商品是否可以修改(是否已经被购买)
// @Tags Product
// @Summary 检查商品是否可以修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ProductIDRequest true "检查商品是否可以修改"
// @Success 200 {object} response.Response{response.ProductCouldModify} "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /product/checkProductCouldBeModified [get]
func (productApi *ProductApi) CheckProductCouldBeModified(c *gin.Context) {
	var id productsReq.ProductIDRequest
	err := c.ShouldBindQuery(&id)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if couldBeModified, err := productService.HasBeenOrdered(id.ProductID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(
			gin.H{
				"couldBeModified": couldBeModified,
			},
			c,
		)
	}
}
