package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/products"
	productsReq "github.com/OSQianXing/guanpu-server/model/products/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductBrandApi struct {
}

var productBrandService = service.ServiceGroupApp.ProductsServiceGroup.ProductBrandService

// CreateProductBrand 创建ProductBrand
// @Tags ProductBrand
// @Summary 创建ProductBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductBrand true "创建ProductBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productBrand/createProductBrand [post]
func (productBrandApi *ProductBrandApi) CreateProductBrand(c *gin.Context) {
	var productBrand products.ProductBrand
	err := c.ShouldBindJSON(&productBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productBrandService.CreateProductBrand(&productBrand); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteProductBrand 删除ProductBrand
// @Tags ProductBrand
// @Summary 删除ProductBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductBrand true "删除ProductBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productBrand/deleteProductBrand [delete]
func (productBrandApi *ProductBrandApi) DeleteProductBrand(c *gin.Context) {
	var productBrand products.ProductBrand
	err := c.ShouldBindJSON(&productBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if productBrand.ID == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}
	if err := productBrandService.SoftDeleteProductBrand(productBrand); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteProductBrandByIds 批量删除ProductBrand
// @Tags ProductBrand
// @Summary 批量删除ProductBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ProductBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /productBrand/deleteProductBrandByIds [delete]
func (productBrandApi *ProductBrandApi) DeleteProductBrandByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if len(IDS.Ids) == 0 {
		response.FailWithMessage("IDS不能为空", c)
		return
	}
	if err := productBrandService.DeleteProductBrandByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateProductBrand 更新ProductBrand
// @Tags ProductBrand
// @Summary 更新ProductBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductBrand true "更新ProductBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /productBrand/updateProductBrand [put]
func (productBrandApi *ProductBrandApi) UpdateProductBrand(c *gin.Context) {
	var productBrand products.ProductBrand
	err := c.ShouldBindJSON(&productBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if productBrand.ID == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}
	if err := productBrandService.UpdateProductBrand(productBrand); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindProductBrand 用id查询ProductBrand
// @Tags ProductBrand
// @Summary 用id查询ProductBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query products.ProductBrand true "用id查询ProductBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /productBrand/findProductBrand [get]
func (productBrandApi *ProductBrandApi) FindProductBrand(c *gin.Context) {
	var productBrand products.ProductBrand
	err := c.ShouldBindQuery(&productBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproductBrand, err := productBrandService.GetProductBrand(productBrand.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproductBrand": reproductBrand}, c)
	}
}

// GetProductBrandList 分页获取ProductBrand列表
// @Tags ProductBrand
// @Summary 分页获取ProductBrand列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.ProductBrandSearch true "分页获取ProductBrand列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productBrand/getProductBrandList [get]
func (productBrandApi *ProductBrandApi) GetProductBrandList(c *gin.Context) {
	var pageInfo productsReq.ProductBrandSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productBrandService.GetProductBrandInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
