package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	commonReq "github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	productsReq "github.com/OSQianXing/guanpu-server/model/products/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SpecialMallApi struct {
}

var specialMallService = service.ServiceGroupApp.ProductsServiceGroup.SpecialMallService

// CreateSpecialMall 创建专区
// @Tags SpecialMall
// @Summary 创建专区
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.CreateSpecialMallRequest true "创建专区"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /specialMall/createSpecialMall [post]
func (specialMallApi *SpecialMallApi) CreateSpecialMall(c *gin.Context) {
	var specialMall productsReq.CreateSpecialMallRequest
	err := c.ShouldBindJSON(&specialMall)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.CreateSpecialMall(&specialMall); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteSpecialMall 删除专区
// @Tags SpecialMall
// @Summary 删除专区
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body commonReq.GetById true "删除专区"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /specialMall/deleteSpecialMall [delete]
func (specialMallApi *SpecialMallApi) DeleteSpecialMall(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.DeleteSpecialMall(req.ID); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// UpdateSpecialMall 更新专区
// @Tags SpecialMall
// @Summary 更新专区
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.UpdateSpecialMallRequest true "更新专区"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /specialMall/updateSpecialMall [put]
func (specialMallApi *SpecialMallApi) UpdateSpecialMall(c *gin.Context) {
	var specialMall productsReq.UpdateSpecialMallRequest
	err := c.ShouldBindJSON(&specialMall)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.UpdateSpecialMall(specialMall); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// BatchUpdateSpecialMallBatch 批量更新专区
// @Tags SpecialMall
// @Summary 批量更新专区
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []productsReq.BatchUpdateSpecialMallRequest true "批量更新专区"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /specialMall/batchUpdateSpecialMall [put]
func (specialMallApi *SpecialMallApi) BatchUpdateSpecialMallBatch(c *gin.Context) {
	var batchUpDate []productsReq.BatchUpdateSpecialMallRequest
	err := c.ShouldBindJSON(&batchUpDate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.BatchUpdateSpecialMall(batchUpDate); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// BatchSetSpecialPerformance 批量更新专区业绩计算设置
// @Tags SpecialMall
// @Summary 批量更新专区业绩计算设置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []productsReq.BatchSetSpecialPerformanceRequest true "批量更新专区"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /specialMall/batchSetSpecialPerformance [put]
func (specialMallApi *SpecialMallApi) BatchSetSpecialPerformance(c *gin.Context) {
	var batchUpDate []productsReq.BatchSetSpecialPerformanceRequest
	err := c.ShouldBindJSON(&batchUpDate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.BatchSetSpecialPerformance(batchUpDate); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// AddProductToSpecialMall 向专区添加商品
// @Tags SpecialMall
// @Summary 向专区添加商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.AddProductToSpecialMallRequest true "向专区添加商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /specialMall/addProduct [post]
func (specialMallApi *SpecialMallApi) AddProductToSpecialMall(c *gin.Context) {
	var specialMall productsReq.AddProductToSpecialMallRequest
	err := c.ShouldBindJSON(&specialMall)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.AddProductToSpacialMall(specialMall); err != nil {
		global.GVA_LOG.Error("添加失败!", zap.Error(err))
		response.FailWithMessage("添加失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("添加成功", c)
	}
}

// DeleteProductFromSpecialMall 删除专区商品
// @Tags SpecialMall
// @Summary 删除专区商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.DeleteProductFromSpecialMallRequest true "删除专区商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /specialMall/deleteProduct [delete]
func (specialMallApi *SpecialMallApi) DeleteProductFromSpecialMall(c *gin.Context) {
	var specialMall productsReq.DeleteProductFromSpecialMallRequest
	err := c.ShouldBindJSON(&specialMall)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := specialMallService.DeleteProductFromSpecialMall(specialMall); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// FindSpecialMall 用id查询专区
// @Tags SpecialMall
// @Summary 用id查询专区
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query commonReq.GetById true "用id查询专区"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /specialMall/findSpecialMall [get]
func (specialMallApi *SpecialMallApi) FindSpecialMall(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if respecialMall, err := specialMallService.GetSpecialMall(req.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"respecialMall": respecialMall}, c)
	}
}

// GetSpecialMallList 分页获取专区列表
// @Tags SpecialMall
// @Summary 分页获取专区列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.SpecialMallSearch true "分页获取专区列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /specialMall/getSpecialMallList [get]
func (specialMallApi *SpecialMallApi) GetSpecialMallList(c *gin.Context) {
	var pageInfo productsReq.SpecialMallSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := specialMallService.GetSpecialMallInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
