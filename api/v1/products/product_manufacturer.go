package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/products"
	productsReq "github.com/OSQianXing/guanpu-server/model/products/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductManufacturerApi struct {
}

var productManufacturerService = service.ServiceGroupApp.ProductsServiceGroup.ProductManufacturerService

// CreateProductManufacturer 创建ProductManufacturer
// @Tags ProductManufacturer
// @Summary 创建ProductManufacturer
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductManufacturer true "创建ProductManufacturer"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productManufacturer/createProductManufacturer [post]
func (productManufacturerApi *ProductManufacturerApi) CreateProductManufacturer(c *gin.Context) {
	var productManufacturer products.ProductManufacturer
	err := c.ShouldBindJSON(&productManufacturer)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Name":    {utils.NotEmpty()},
		"Address": {utils.NotEmpty()},
	}
	if err := utils.Verify(productManufacturer, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productManufacturerService.CreateProductManufacturer(&productManufacturer); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteProductManufacturer 删除ProductManufacturer
// @Tags ProductManufacturer
// @Summary 删除ProductManufacturer
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductManufacturer true "删除ProductManufacturer"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productManufacturer/deleteProductManufacturer [delete]
func (productManufacturerApi *ProductManufacturerApi) DeleteProductManufacturer(c *gin.Context) {
	var productManufacturer products.ProductManufacturer
	err := c.ShouldBindJSON(&productManufacturer)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productManufacturerService.DeleteProductManufacturer(productManufacturer); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteProductManufacturerByIds 批量删除ProductManufacturer
// @Tags ProductManufacturer
// @Summary 批量删除ProductManufacturer
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ProductManufacturer"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /productManufacturer/deleteProductManufacturerByIds [delete]
func (productManufacturerApi *ProductManufacturerApi) DeleteProductManufacturerByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productManufacturerService.DeleteProductManufacturerByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateProductManufacturer 更新ProductManufacturer
// @Tags ProductManufacturer
// @Summary 更新ProductManufacturer
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductManufacturer true "更新ProductManufacturer"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /productManufacturer/updateProductManufacturer [put]
func (productManufacturerApi *ProductManufacturerApi) UpdateProductManufacturer(c *gin.Context) {
	var productManufacturer products.ProductManufacturer
	err := c.ShouldBindJSON(&productManufacturer)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Name":    {utils.NotEmpty()},
		"Address": {utils.NotEmpty()},
	}
	if err := utils.Verify(productManufacturer, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productManufacturerService.UpdateProductManufacturer(productManufacturer); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindProductManufacturer 用id查询ProductManufacturer
// @Tags ProductManufacturer
// @Summary 用id查询ProductManufacturer
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query products.ProductManufacturer true "用id查询ProductManufacturer"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /productManufacturer/findProductManufacturer [get]
func (productManufacturerApi *ProductManufacturerApi) FindProductManufacturer(c *gin.Context) {
	var productManufacturer products.ProductManufacturer
	err := c.ShouldBindQuery(&productManufacturer)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproductManufacturer, err := productManufacturerService.GetProductManufacturer(productManufacturer.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproductManufacturer": reproductManufacturer}, c)
	}
}

// GetProductManufacturerList 分页获取ProductManufacturer列表
// @Tags ProductManufacturer
// @Summary 分页获取ProductManufacturer列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.ProductManufacturerSearch true "分页获取ProductManufacturer列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productManufacturer/getProductManufacturerList [get]
func (productManufacturerApi *ProductManufacturerApi) GetProductManufacturerList(c *gin.Context) {
	var pageInfo productsReq.ProductManufacturerSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productManufacturerService.GetProductManufacturerInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
