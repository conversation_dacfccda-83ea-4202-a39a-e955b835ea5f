package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/products"
	productsReq "github.com/OSQianXing/guanpu-server/model/products/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductCategoryApi struct {
}

var productCategoryService = service.ServiceGroupApp.ProductsServiceGroup.ProductCategoryService

// CreateProductCategory 创建ProductCategory
// @Tags ProductCategory
// @Summary 创建ProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductCategory true "创建ProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productCategory/createProductCategory [post]
func (productCategoryApi *ProductCategoryApi) CreateProductCategory(c *gin.Context) {
	var productCategory products.ProductCategory
	err := c.ShouldBindJSON(&productCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := productCategoryService.CreateProductCategory(&productCategory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteProductCategory 删除ProductCategory
// @Tags ProductCategory
// @Summary 删除ProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductCategory true "删除ProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productCategory/deleteProductCategory [delete]
func (productCategoryApi *ProductCategoryApi) DeleteProductCategory(c *gin.Context) {
	var productCategory products.ProductCategory
	err := c.ShouldBindJSON(&productCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if productCategory.ID == 0 {
		response.FailWithMessage("缺少参数ID", c)
		return
	}
	if err := productCategoryService.SoftDeleteProductCategory(productCategory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteProductCategoryByIds 批量删除ProductCategory
// @Tags ProductCategory
// @Summary 批量删除ProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /productCategory/deleteProductCategoryByIds [delete]
func (productCategoryApi *ProductCategoryApi) DeleteProductCategoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if len(IDS.Ids) == 0 {
		response.FailWithMessage("缺少参数IDS", c)
		return
	}
	if err := productCategoryService.SoftDeleteProductCategoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateProductCategory 更新ProductCategory
// @Tags ProductCategory
// @Summary 更新ProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body products.ProductCategory true "更新ProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /productCategory/updateProductCategory [put]
func (productCategoryApi *ProductCategoryApi) UpdateProductCategory(c *gin.Context) {
	var productCategory products.ProductCategory
	err := c.ShouldBindJSON(&productCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if productCategory.ID == 0 {
		response.FailWithMessage("缺少参数ID", c)
		return
	}
	if err := productCategoryService.UpdateProductCategory(productCategory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindProductCategory 用id查询ProductCategory
// @Tags ProductCategory
// @Summary 用id查询ProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query products.ProductCategory true "用id查询ProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /productCategory/findProductCategory [get]
func (productCategoryApi *ProductCategoryApi) FindProductCategory(c *gin.Context) {
	var productCategory products.ProductCategory
	err := c.ShouldBindQuery(&productCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproductCategory, err := productCategoryService.GetProductCategory(productCategory.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproductCategory": reproductCategory}, c)
	}
}

// GetProductCategoryList 分页获取ProductCategory列表
// @Tags ProductCategory
// @Summary 分页获取ProductCategory列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.ProductCategorySearch true "分页获取ProductCategory列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productCategory/getProductCategoryList [get]
func (productCategoryApi *ProductCategoryApi) GetProductCategoryList(c *gin.Context) {
	var pageInfo productsReq.ProductCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productCategoryService.GetProductCategoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetSuperiorProductCategoryList 获取上级分类
// @Tags ProductCategory
// @Summary 获取上级分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} []response.SuperiorProductCategory "data.list"
// @Router /productCategory/getSuperiorProductCategory [get]
func (productCategoryApi *ProductCategoryApi) GetSuperiorProductCategoryList(c *gin.Context) {
	if list, err := productCategoryService.GetSuperiorProductCategoryList(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}
