package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	productsReq "github.com/OSQianXing/guanpu-server/model/products/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type GiftRuleApi struct {
}

var giftRuleService = service.ServiceGroupApp.ProductsServiceGroup.GiftRuleService

// CreateGiftRule 创建赠品规则
// @Tags GiftRule
// @Summary 创建赠品规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.GiftRuleCreate true "创建赠品规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /giftRule/createGiftRule [post]
func (giftRuleApi *GiftRuleApi) CreateGiftRule(c *gin.Context) {
	var req productsReq.GiftRuleCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := giftRuleService.CreateGiftRule(&req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败,"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteGiftRule 删除赠品规则
// @Tags GiftRule
// @Summary 删除赠品规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.GiftRuleDelete true "删除赠品规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /giftRule/deleteGiftRule [delete]
func (giftRuleApi *GiftRuleApi) DeleteGiftRule(c *gin.Context) {
	var req productsReq.GiftRuleDelete
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := giftRuleService.DeleteGiftRule(req); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteGiftRuleByIds 批量删除GiftRule
// @ignore
// @Tags
// @Summary 批量删除GiftRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除GiftRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /giftRule/deleteGiftRuleByIds [delete]
func (giftRuleApi *GiftRuleApi) DeleteGiftRuleByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := giftRuleService.DeleteGiftRuleByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateGiftRule 更新赠品规则
// @Tags GiftRule
// @Summary 更新赠品规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body productsReq.GiftRuleUpdate true "更新赠品规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /giftRule/updateGiftRule [put]
func (giftRuleApi *GiftRuleApi) UpdateGiftRule(c *gin.Context) {
	var req productsReq.GiftRuleUpdate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := giftRuleService.UpdateGiftRule(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败, "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindGiftRule 用id查询GiftRule
// @ignore
// @Tags GiftRule
// @Summary 用id查询GiftRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.GiftRuleFind true "用id查询GiftRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /giftRule/findGiftRule [get]
func (giftRuleApi *GiftRuleApi) FindGiftRule(c *gin.Context) {
	var req productsReq.GiftRuleFind
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if regiftRule, err := giftRuleService.GetGiftRule(req.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"regiftRule": regiftRule}, c)
	}
}

// GetGiftRuleList 分页获取GiftRule列表
// @Tags GiftRule
// @Summary 分页获取GiftRule列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query productsReq.GiftRuleSearch true "分页获取GiftRule列表"
// @Success 200 {object} response.Response{data=[]products.GiftRule}
// @Router /giftRule/getGiftRuleList [get]
func (giftRuleApi *GiftRuleApi) GetGiftRuleList(c *gin.Context) {
	var pageInfo productsReq.GiftRuleSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := giftRuleService.GetGiftRuleInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
