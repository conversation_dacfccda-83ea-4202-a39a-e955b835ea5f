package pay

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreatOnlinePayFranchiseeConfig
// @Tags Pay
// @Summary 创建在线支付渠道用户配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body payReq.OnlinePayFranchiseeConfigCreate true "创建在线支付渠道用户配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /pay/createOnlinePayFranchiseeConfig [post]
func (p *PayApi) CreatePayFranchiseeConfig(c *gin.Context) {
	var payFranchiseeConfig payReq.OnlinePayFranchiseeConfigCreate
	err := c.ShouldBindJSON(&payFranchiseeConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verfiy := utils.Rules{
		"FranchiseeId":     {utils.NotEmpty()},
		"OnlinePayChannel": {utils.NotEmpty()},
		"OnlinePayType":    {utils.NotEmpty()},
	}
	if err := utils.Verify(payFranchiseeConfig, verfiy); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := PayService.CreateOnlinePayFranchiseeConfig(&payFranchiseeConfig); err != nil {
		response.FailWithMessage("创建失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// UpdateOnlinePayFranchiseeConfig
// @Tags Pay
// @Summary 更新在线支付渠道用户配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body payReq.OnlinePayFranchiseeConfigUpdate true "更新在线支付渠道用户配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /pay/updateOnlinePayFranchiseeConfig [post]
func (p *PayApi) UpdatePayFranchiseeConfig(c *gin.Context) {
	var payFranchiseeConfig payReq.OnlinePayFranchiseeConfigUpdate
	err := c.ShouldBindJSON(&payFranchiseeConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verfiy := utils.Rules{
		"FranchiseeId":     {utils.NotEmpty()},
		"OnlinePayChannel": {utils.NotEmpty()},
		"OnlinePayType":    {utils.NotEmpty()},
	}
	if err := utils.Verify(payFranchiseeConfig, verfiy); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := PayService.UpdateOnlinePayFranchiseeConfig(&payFranchiseeConfig); err != nil {
		response.FailWithMessage("更新失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// DeleteOnlinePayFranchiseeConfig
// @Tags Pay
// @Summary 删除在线支付渠道用户配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body payReq.OnlinePayFranchiseeConfigDelete true "删除在线支付渠道用户配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /pay/deleteOnlinePayFranchiseeConfig [delete]
func (p *PayApi) DeletePayFranchiseeConfig(c *gin.Context) {
	var payFranchiseeConfig payReq.OnlinePayFranchiseeConfigDelete
	err := c.ShouldBindJSON(&payFranchiseeConfig)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := PayService.DeleteOnlinePayFranchiseeConfig(&payFranchiseeConfig); err != nil {
		response.FailWithMessage("删除失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetOnlinePayFranchiseeConfigInfoList
// @Tags Pay
// @Summary 分页获取在线支付渠道用户配置列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.OnlinePayFranchiseeConfigSearch true "分页获取在线支付渠道用户配置列表"
// @Success 200 {object} response.PageResult "{"code": 200, "list": []response.OnlinePayFranchiseeConfigRes, "total": int64, "msg": "获取成功"}"
// @Router /pay/getOnlinePayFranchiseeConfigInfoList [get]
func (p *PayApi) GetPayFranchiseeConfigInfoList(c *gin.Context) {
	var pageInfo payReq.OnlinePayFranchiseeConfigSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := PayService.GetOnlinePayFranchiseeConfigInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
