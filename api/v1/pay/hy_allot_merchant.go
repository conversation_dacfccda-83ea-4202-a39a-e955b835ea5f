package pay

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/pay"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HyAllotMerchantApi struct {
}

var hyAllotMerchantService = service.ServiceGroupApp.OnlinePayServiceGroup.HyAllotMerchantService

// CreateHyAllotMerchant 创建HyAllotMerchant
// @Tags HyAllotMerchant
// @Summary 创建HyAllotMerchant
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotMerchant true "创建HyAllotMerchant"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotMerchant/createHyAllotMerchant [post]
func (hyAllotMerchantApi *HyAllotMerchantApi) CreateHyAllotMerchant(c *gin.Context) {
	var hyAllotMerchant pay.HyAllotMerchant
	err := c.ShouldBindJSON(&hyAllotMerchant)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotMerchantService.CreateHyAllotMerchant(&hyAllotMerchant); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteHyAllotMerchant 删除HyAllotMerchant
// @Tags HyAllotMerchant
// @Summary 删除HyAllotMerchant
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotMerchant true "删除HyAllotMerchant"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /hyAllotMerchant/deleteHyAllotMerchant [delete]
func (hyAllotMerchantApi *HyAllotMerchantApi) DeleteHyAllotMerchant(c *gin.Context) {
	var hyAllotMerchant pay.HyAllotMerchant
	err := c.ShouldBindJSON(&hyAllotMerchant)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotMerchantService.DeleteHyAllotMerchant(hyAllotMerchant); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteHyAllotMerchantByIds 批量删除HyAllotMerchant
// @Tags HyAllotMerchant
// @Summary 批量删除HyAllotMerchant
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除HyAllotMerchant"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /hyAllotMerchant/deleteHyAllotMerchantByIds [delete]
func (hyAllotMerchantApi *HyAllotMerchantApi) DeleteHyAllotMerchantByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotMerchantService.DeleteHyAllotMerchantByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateHyAllotMerchant 更新HyAllotMerchant
// @Tags HyAllotMerchant
// @Summary 更新HyAllotMerchant
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotMerchant true "更新HyAllotMerchant"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /hyAllotMerchant/updateHyAllotMerchant [put]
func (hyAllotMerchantApi *HyAllotMerchantApi) UpdateHyAllotMerchant(c *gin.Context) {
	var hyAllotMerchant pay.HyAllotMerchant
	err := c.ShouldBindJSON(&hyAllotMerchant)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotMerchantService.UpdateHyAllotMerchant(hyAllotMerchant); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindHyAllotMerchant 用id查询HyAllotMerchant
// @Tags HyAllotMerchant
// @Summary 用id查询HyAllotMerchant
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query pay.HyAllotMerchant true "用id查询HyAllotMerchant"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /hyAllotMerchant/findHyAllotMerchant [get]
func (hyAllotMerchantApi *HyAllotMerchantApi) FindHyAllotMerchant(c *gin.Context) {
	var hyAllotMerchant pay.HyAllotMerchant
	err := c.ShouldBindQuery(&hyAllotMerchant)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rehyAllotMerchant, err := hyAllotMerchantService.GetHyAllotMerchant(hyAllotMerchant.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rehyAllotMerchant": rehyAllotMerchant}, c)
	}
}

// GetHyAllotMerchantList 分页获取HyAllotMerchant列表
// @Tags HyAllotMerchant
// @Summary 分页获取HyAllotMerchant列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotMerchantSearch true "分页获取HyAllotMerchant列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotMerchant/getHyAllotMerchantList [get]
func (hyAllotMerchantApi *HyAllotMerchantApi) GetHyAllotMerchantList(c *gin.Context) {
	var pageInfo payReq.HyAllotMerchantSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := hyAllotMerchantService.GetHyAllotMerchantInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
