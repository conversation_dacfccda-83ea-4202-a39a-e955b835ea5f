package pay

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/pay"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HyAllotOrderRecordApi struct {
}

var hyAllotOrderRecordService = service.ServiceGroupApp.OnlinePayServiceGroup.HyAllotOrderRecordService

// // CreateHyAllotOrderRecord 创建HyAllotOrderRecord
// // @Tags HyAllotOrderRecord
// // @Summary 创建HyAllotOrderRecord
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.HyAllotOrderRecord true "创建HyAllotOrderRecord"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /hyAllotOrderRecord/createHyAllotOrderRecord [post]
// func (hyAllotOrderRecordApi *HyAllotOrderRecordApi) CreateHyAllotOrderRecord(c *gin.Context) {
// 	var hyAllotOrderRecord pay.HyAllotOrderRecord
// 	err := c.ShouldBindJSON(&hyAllotOrderRecord)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderRecordService.CreateHyAllotOrderRecord(&hyAllotOrderRecord); err != nil {
// 		global.GVA_LOG.Error("创建失败!", zap.Error(err))
// 		response.FailWithMessage("创建失败", c)
// 	} else {
// 		response.OkWithMessage("创建成功", c)
// 	}
// }

// // DeleteHyAllotOrderRecord 删除HyAllotOrderRecord
// // @Tags HyAllotOrderRecord
// // @Summary 删除HyAllotOrderRecord
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.HyAllotOrderRecord true "删除HyAllotOrderRecord"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// // @Router /hyAllotOrderRecord/deleteHyAllotOrderRecord [delete]
// func (hyAllotOrderRecordApi *HyAllotOrderRecordApi) DeleteHyAllotOrderRecord(c *gin.Context) {
// 	var hyAllotOrderRecord pay.HyAllotOrderRecord
// 	err := c.ShouldBindJSON(&hyAllotOrderRecord)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderRecordService.DeleteHyAllotOrderRecord(hyAllotOrderRecord); err != nil {
// 		global.GVA_LOG.Error("删除失败!", zap.Error(err))
// 		response.FailWithMessage("删除失败", c)
// 	} else {
// 		response.OkWithMessage("删除成功", c)
// 	}
// }

// // DeleteHyAllotOrderRecordByIds 批量删除HyAllotOrderRecord
// // @Tags HyAllotOrderRecord
// // @Summary 批量删除HyAllotOrderRecord
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body request.IdsReq true "批量删除HyAllotOrderRecord"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// // @Router /hyAllotOrderRecord/deleteHyAllotOrderRecordByIds [delete]
// func (hyAllotOrderRecordApi *HyAllotOrderRecordApi) DeleteHyAllotOrderRecordByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderRecordService.DeleteHyAllotOrderRecordByIds(IDS); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// // UpdateHyAllotOrderRecord 更新HyAllotOrderRecord
// // @Tags HyAllotOrderRecord
// // @Summary 更新HyAllotOrderRecord
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.HyAllotOrderRecord true "更新HyAllotOrderRecord"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// // @Router /hyAllotOrderRecord/updateHyAllotOrderRecord [put]
// func (hyAllotOrderRecordApi *HyAllotOrderRecordApi) UpdateHyAllotOrderRecord(c *gin.Context) {
// 	var hyAllotOrderRecord pay.HyAllotOrderRecord
// 	err := c.ShouldBindJSON(&hyAllotOrderRecord)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderRecordService.UpdateHyAllotOrderRecord(hyAllotOrderRecord); err != nil {
// 		global.GVA_LOG.Error("更新失败!", zap.Error(err))
// 		response.FailWithMessage("更新失败", c)
// 	} else {
// 		response.OkWithMessage("更新成功", c)
// 	}
// }

// FindHyAllotOrderRecord 用id查询HyAllotOrderRecord
// @Tags HyAllotOrderRecord
// @Summary 用id查询HyAllotOrderRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query pay.HyAllotOrderRecord true "用id查询HyAllotOrderRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /hyAllotOrderRecord/findHyAllotOrderRecord [get]
func (hyAllotOrderRecordApi *HyAllotOrderRecordApi) FindHyAllotOrderRecord(c *gin.Context) {
	var hyAllotOrderRecord pay.HyAllotOrderRecord
	err := c.ShouldBindQuery(&hyAllotOrderRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rehyAllotOrderRecord, err := hyAllotOrderRecordService.GetHyAllotOrderRecord(hyAllotOrderRecord.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rehyAllotOrderRecord": rehyAllotOrderRecord}, c)
	}
}

// GetHyAllotOrderRecordList 分页获取HyAllotOrderRecord列表
// @Tags HyAllotOrderRecord
// @Summary 分页获取HyAllotOrderRecord列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotOrderRecordSearch true "分页获取HyAllotOrderRecord列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotOrderRecord/getHyAllotOrderRecordList [get]
func (hyAllotOrderRecordApi *HyAllotOrderRecordApi) GetHyAllotOrderRecordList(c *gin.Context) {
	var pageInfo payReq.HyAllotOrderRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := hyAllotOrderRecordService.GetHyAllotOrderRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
