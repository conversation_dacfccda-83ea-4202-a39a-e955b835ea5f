package pay

import (
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	payRequest "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	utilHeepay "github.com/OSQianXing/guanpu-server/utils/payment/heepay"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PayApi struct{}

var PayService = service.ServiceGroupApp.OnlinePayServiceGroup.OnlinePayService

// TestCreateOnlinePayOrder 测试
// @Tags Pay
// @Summary 测试
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":0,"retoken":{},"msg":"获取成功"}"
// @Router /app/pay/testCreateOnlinePayOrder [get]
func (p *PayApi) TestCreateOnlinePayOrder(c *gin.Context) {

	userIp := c.ClientIP()
	token, err := PayService.TestCreateOnlinePayOrder(userIp, utils.GetUserID(c))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"retoken": token}, c)
}

// GetOnlinePayChannel 获取支付渠道
// @Tags Pay
// @Summary 获取支付渠道
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":0,"reonlinepaychannel":{},"msg":"获取成功"}
// @Router /app/pay/getOnlinePayChannel [get]
func (p *PayApi) GetOnlinePayChannel(c *gin.Context) {
	franchiseeId, err := dao.Franchisee.GetFranchiseeByUserID(utils.GetUserID(c))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if franchiseeId.ID == 0 {
		response.FailWithMessage("用户不存在", c)
		return
	}

	// 获取用户支付渠道
	payChinnels, err := PayService.GetFranchiseeOnlinePayChannels(franchiseeId.ID)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"reonlinepaychannel": payChinnels}, c)
}

// GetFranchiseePayChannel 获取支付渠道
// @Tags Pay
// @Summary 获取支付渠道
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payRequest.PayChannelReq true "获取支付渠道"
// @Success 200 {object} string "{"code":0,"reuserPayChannel":{pay.OnlinePayFranchiseeConfig},"msg":"获取成功"}"
// @Router /app/pay/getFranchiseePayChannel [get]
func (p *PayApi) GetFranchiseePayChannel(c *gin.Context) {
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(utils.GetUserID(c))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if franchisee.ID == 0 {
		response.FailWithMessage("用户不存在", c)
		return
	}
	var payChannelReq = payRequest.PayChannelReq{}
	err = c.ShouldBindQuery(&payChannelReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if payChannelReq.Channel == 0 {
		response.FailWithMessage("payChannel不能为空", c)
		return
	}

	userPayChannel, err := PayService.GetFranchiseePayChannel(franchisee.ID, payChannelReq.Channel)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"reuserPayChannel": userPayChannel}, c)
}

// // CreateOnlinePayOrder 创建在线支付订单
// // @Tags Pay
// // @Summary 创建在线支付订单
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body payRequest.OnlinePayOrderCreate true "创建在线支付订单"
// // @Success 200 {string} string "{"success":true,"reOnlinePayTransNo":{},"msg":"获取成功"}"
// // @Router /app/pay/createOnlinePayOrder [get]
// func (p *PayApi) CreateOnlinePayOrder(c *gin.Context) {

// 	response.FailWithMessage("该功能暂未开放", c)
// }

// OnlinePay 在线支付
// @Tags Pay
// @Summary 在线支付
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body payRequest.OnlinePayOrderCreate true "在线支付"
// @Success 200 {string} string "{"code":0,"reOnlinePayTransNo":{},"msg":"获取成功"}"
// @Router /app/pay/onlinePay [post]
func (p *PayApi) OnlinePay(c *gin.Context) {
	var req payRequest.OnlinePayOrderCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	transNo, err := PayService.OnlinePay(req.OrderNo, req.Amount, req.PayChannel, req.PayType, c.ClientIP(), utils.GetUserID(c))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if transNo == "" {
		response.FailWithMessage("got reOnlinePayTransNo empty", c)
		return
	}
	response.OkWithData(gin.H{"reOnlinePayTransNo": transNo}, c)
}

// RePayOnlinePay 重新支付
// @Tags Pay
// @Summary 重新支付
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body payRequest.OnlinePayOrderCreate true "重新支付"
// @Success 200 {string} string "{"success":true,"reOnlinePayTransNo":{},"msg":"获取成功"}"
// @Router /app/pay/rePayOnlinePay [post]
func (p *PayApi) RePayOnlinePay(c *gin.Context) {
	var req payRequest.OnlinePayOrderCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	transNo, err := PayService.RePayOnlinePay(req.OrderNo, req.Amount, req.PayChannel, req.PayType, c.ClientIP(), utils.GetUserID(c))
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if transNo == "" {
		response.FailWithMessage("got reOnlinePayTransNo empty", c)
		return
	}
	response.OkWithData(gin.H{"reOnlinePayTransNo": transNo}, c)

}

// OnlinePayCallbackHeepay 在线支付回调
// @Tags Pay
// @Summary 在线支付回调
// @accept application/json
// @Produce application/json
// @Param data query utilHeepay.HeepayCallbackRequest true "在线支付回调"
// @Success 200 {string} string "{"success":true,"reOnlinePayTransNo":{},"msg":"获取成功"}"
// @Router /pay/onlinePayCallbackHeepay [GET]
func (p *PayApi) OnlinePayCallbackHeepay(c *gin.Context) {
	var req utilHeepay.HeepayCallbackRequest

	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = PayService.OnlinePayCallbackHeepay(req, c.ClientIP())
	if err != nil {
		global.GVA_LOG.Error("在线支付回调失败", zap.Any("req", req), zap.Uint("userID", utils.GetUserID(c)), zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	c.String(200, "OK")
}

// HeepayallotTradeCallBack heepay分账回调
// @Tags Pay
// @Summary heepay分账回调
// @accept application/json
// @Produce application/json
// @Param data body utilHeepay.HeepayAllotTradeCallbakReq true "heepay分账回调"
// @Success 200 {string} string "{"success":true,"reOnlinePayTransNo":{},"msg":"获取成功"}"
// @Router /pay/heepayallotTradeCallBack [POST]
func (p *PayApi) HeepayallotTradeCallBack(c *gin.Context) {
	// global.GVA_LOG.Debug("heepay分账回调", zap.Any("req", c.Request), zap.Any("body", c.Request.Body))
	global.GVA_LOG.Debug("heepay分账回调", zap.Any("url", c.Request.URL.RawQuery), zap.Any("body", c.Request.Body))
	var req utilHeepay.HeepayAllotTradeCallbakReq
	err := c.ShouldBind(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	global.GVA_LOG.Debug("heepay分账回调", zap.Any("req", req))

	// body, err := io.ReadAll(c.Request.Body)
	// if err != nil {
	// 	response.FailWithMessage(err.Error(), c)
	// 	return
	// }
	// global.GVA_LOG.Debug("heepay分账回调", zap.Any("body", string(body)))

	err = PayService.HeepayAllotTradeCallBack(req, c.ClientIP())
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	c.String(200, "OK")
}

// HeepayRefundOnlinePayCallback heepay退款回调
// @Tags Pay
// @Summary heepay退款回调
// @accept application/json
// @Produce application/json
// @Param data query utilHeepay.HeepayRefundCallbackRequest true "在线支付回调"
// @Success 200 {string} string "{"success":true,"reOnlinePayTransNo":{},"msg":"获取成功"}"
// @Router /pay/heepayRefundOnlinePayCallback [GET]
func (p *PayApi) HeepayRefundOnlinePayCallback(c *gin.Context) {
	var req utilHeepay.HeepayRefundCallbackRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = PayService.RefundOrderOnlinePayHeepayCallBack(&req, c.ClientIP())
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	c.String(200, "OK")
}

// GetOrderOnlinePay 获取订单在线支付记录
// @Tags Pay
// @Summary 获取订单在线支付记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payRequest.GetOrderOnlinePayReq true "获取订单在线支付记录"
// @Success 200 {string} string "{"code":0,"reOnlinePay":{},"msg":"获取成功"}"
// @Router /pay/getOrderOnlinePay [get]
func (p *PayApi) GetOrderOnlinePay(c *gin.Context) {
	var req payRequest.GetOrderOnlinePayReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	record, err := PayService.GetOrderOnlinePay(req.OrderNo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"reOnlinePay": record}, c)
}
