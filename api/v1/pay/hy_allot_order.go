package pay

import (
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/pay"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type HyAllotOrderApi struct {
}

var hyAllotOrderService = service.ServiceGroupApp.OnlinePayServiceGroup.HyAllotOrderService

// CreateHyAllotOrder 创建HyAllotOrder
// @Tags HyAllotOrder
// @Summary 创建HyAllotOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body []pay.HyAllotOrder true "创建HyAllotOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotOrder/createHyAllotOrders [post]
func (hyAllotOrderApi *HyAllotOrderApi) CreateHyAllotOrders(c *gin.Context) {
	var hyAllotOrders []pay.HyAllotOrder
	err := c.ShouldBindJSON(&hyAllotOrders)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if len(hyAllotOrders) > 10 {
		response.FailWithMessage("最多10个订单", c)
		return
	}
	verify := utils.Rules{
		"HyBillNo":  {utils.NotEmpty()},
		"HySubId":   {utils.NotEmpty()},
		"HySubMail": {utils.NotEmpty()},
		"AllotAmt":  {utils.NotEmpty()},
	}
	for i := 0; i < len(hyAllotOrders); i++ {
		if err := utils.Verify(hyAllotOrders[i], verify); err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
		if !hyAllotOrders[i].AllotAmt.GreaterThan(decimal.NewFromFloat(0)) {
			response.FailWithMessage("分账金额不能小于0", c)
		}
		hyAllotOrders[i].CreatedAt = time.Now().Local()
		hyAllotOrders[i].UpdatedAt = time.Now().Local()
		hyAllotOrders[i].DeletedAt = gorm.DeletedAt{Time: time.Now().Local(), Valid: false}
		hyAllotOrders[i].Status = types.AllotOrderStatusPending.ToPtr()
	}

	if err := hyAllotOrderService.CreateHyAllotOrders(hyAllotOrders, c.ClientIP()); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteHyAllotOrder 删除HyAllotOrder
// @Tags HyAllotOrder
// @Summary 删除HyAllotOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotOrder true "删除HyAllotOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /hyAllotOrder/deleteHyAllotOrder [delete]
func (hyAllotOrderApi *HyAllotOrderApi) DeleteHyAllotOrder(c *gin.Context) {
	var hyAllotOrder pay.HyAllotOrder
	err := c.ShouldBindJSON(&hyAllotOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotOrderService.DeleteHyAllotOrder(hyAllotOrder); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// // DeleteHyAllotOrderByIds 批量删除HyAllotOrder
// // @Tags HyAllotOrder
// // @Summary 批量删除HyAllotOrder
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body request.IdsReq true "批量删除HyAllotOrder"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// // @Router /hyAllotOrder/deleteHyAllotOrderByIds [delete]
// func (hyAllotOrderApi *HyAllotOrderApi) DeleteHyAllotOrderByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderService.DeleteHyAllotOrderByIds(IDS); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// UpdateHyAllotOrder 更新HyAllotOrder
// @Tags HyAllotOrder
// @Summary 更新HyAllotOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotOrder true "更新HyAllotOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /hyAllotOrder/updateHyAllotOrder [put]
func (hyAllotOrderApi *HyAllotOrderApi) UpdateHyAllotOrder(c *gin.Context) {
	var hyAllotOrder pay.HyAllotOrder
	err := c.ShouldBindJSON(&hyAllotOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"HyBillNo":  {utils.NotEmpty()},
		"HySubId":   {utils.NotEmpty()},
		"HySubMail": {utils.NotEmpty()},
		"AllotAmt":  {utils.NotEmpty()},
	}
	if err := utils.Verify(hyAllotOrder, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotOrderService.UpdateHyAllotOrder(hyAllotOrder); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindHyAllotOrder 用id查询HyAllotOrder
// @Tags HyAllotOrder
// @Summary 用id查询HyAllotOrder
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotOrderIdQuery true "用id查询HyAllotOrder"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /hyAllotOrder/findHyAllotOrder [get]
func (hyAllotOrderApi *HyAllotOrderApi) FindHyAllotOrder(c *gin.Context) {
	var hyAllotOrder payReq.HyAllotOrderIdQuery
	err := c.ShouldBindQuery(&hyAllotOrder)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rehyAllotOrder, err := hyAllotOrderService.GetHyAllotOrder(hyAllotOrder.Id); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rehyAllotOrder": rehyAllotOrder}, c)
	}
}

// GetHyAllotOrderList 分页获取HyAllotOrder列表
// @Tags HyAllotOrder
// @Summary 分页获取HyAllotOrder列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotOrderSearch true "分页获取HyAllotOrder列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotOrder/getHyAllotOrderList [get]
func (hyAllotOrderApi *HyAllotOrderApi) GetHyAllotOrderList(c *gin.Context) {
	var pageInfo payReq.HyAllotOrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := hyAllotOrderService.GetHyAllotOrderInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
