package pay

// import (
// 	"github.com/OSQianXing/guanpu-server/global"
// 	"github.com/OSQianXing/guanpu-server/model/common/response"
// 	"github.com/gin-gonic/gin"
// 	"go.uber.org/zap"
// )

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	payResp "github.com/OSQianXing/guanpu-server/model/pay/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HyAllotOrderCallbackApi struct {
}

var hyAllotOrderCallbackService = service.ServiceGroupApp.OnlinePayServiceGroup.HyAllotOrderCallbackService

// // CreateHyAllotOrderCallback 创建HyAllotOrderCallback
// // @Tags HyAllotOrderCallback
// // @Summary 创建HyAllotOrderCallback
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.HyAllotOrderCallback true "创建HyAllotOrderCallback"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /hyAllotOrderCallback/createHyAllotOrderCallback [post]
// func (hyAllotOrderCallbackApi *HyAllotOrderCallbackApi) CreateHyAllotOrderCallback(c *gin.Context) {
// 	var hyAllotOrderCallback pay.HyAllotOrderCallback
// 	err := c.ShouldBindJSON(&hyAllotOrderCallback)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderCallbackService.CreateHyAllotOrderCallback(&hyAllotOrderCallback); err != nil {
// 		global.GVA_LOG.Error("创建失败!", zap.Error(err))
// 		response.FailWithMessage("创建失败", c)
// 	} else {
// 		response.OkWithMessage("创建成功", c)
// 	}
// }

// // DeleteHyAllotOrderCallback 删除HyAllotOrderCallback
// // @Tags HyAllotOrderCallback
// // @Summary 删除HyAllotOrderCallback
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.HyAllotOrderCallback true "删除HyAllotOrderCallback"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// // @Router /hyAllotOrderCallback/deleteHyAllotOrderCallback [delete]
// func (hyAllotOrderCallbackApi *HyAllotOrderCallbackApi) DeleteHyAllotOrderCallback(c *gin.Context) {
// 	var hyAllotOrderCallback pay.HyAllotOrderCallback
// 	err := c.ShouldBindJSON(&hyAllotOrderCallback)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderCallbackService.DeleteHyAllotOrderCallback(hyAllotOrderCallback); err != nil {
// 		global.GVA_LOG.Error("删除失败!", zap.Error(err))
// 		response.FailWithMessage("删除失败", c)
// 	} else {
// 		response.OkWithMessage("删除成功", c)
// 	}
// }

// // DeleteHyAllotOrderCallbackByIds 批量删除HyAllotOrderCallback
// // @Tags HyAllotOrderCallback
// // @Summary 批量删除HyAllotOrderCallback
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body request.IdsReq true "批量删除HyAllotOrderCallback"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// // @Router /hyAllotOrderCallback/deleteHyAllotOrderCallbackByIds [delete]
// func (hyAllotOrderCallbackApi *HyAllotOrderCallbackApi) DeleteHyAllotOrderCallbackByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderCallbackService.DeleteHyAllotOrderCallbackByIds(IDS); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// // UpdateHyAllotOrderCallback 更新HyAllotOrderCallback
// // @Tags HyAllotOrderCallback
// // @Summary 更新HyAllotOrderCallback
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.HyAllotOrderCallback true "更新HyAllotOrderCallback"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// // @Router /hyAllotOrderCallback/updateHyAllotOrderCallback [put]
// func (hyAllotOrderCallbackApi *HyAllotOrderCallbackApi) UpdateHyAllotOrderCallback(c *gin.Context) {
// 	var hyAllotOrderCallback pay.HyAllotOrderCallback
// 	err := c.ShouldBindJSON(&hyAllotOrderCallback)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := hyAllotOrderCallbackService.UpdateHyAllotOrderCallback(hyAllotOrderCallback); err != nil {
// 		global.GVA_LOG.Error("更新失败!", zap.Error(err))
// 		response.FailWithMessage("更新失败", c)
// 	} else {
// 		response.OkWithMessage("更新成功", c)
// 	}
// }

// FindHyAllotOrderCallback 用id查询HyAllotOrderCallback
// @Tags HyAllotOrderCallback
// @Summary 用id查询HyAllotOrderCallback
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotOrderCallbackId true "用id查询HyAllotOrderCallback"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /hyAllotOrderCallback/findHyAllotOrderCallback [get]
func (hyAllotOrderCallbackApi *HyAllotOrderCallbackApi) FindHyAllotOrderCallback(c *gin.Context) {
	var hyAllotOrderCallback payReq.HyAllotOrderCallbackId
	err := c.ShouldBindQuery(&hyAllotOrderCallback)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rehyAllotOrderCallback, err := hyAllotOrderCallbackService.GetHyAllotOrderCallback(hyAllotOrderCallback.Id); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rehyAllotOrderCallback": rehyAllotOrderCallback}, c)
	}
}

// GetHyAllotOrderCallbackList 分页获取HyAllotOrderCallback列表
// @Tags HyAllotOrderCallback
// @Summary 分页获取HyAllotOrderCallback列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotOrderCallbackSearch true "分页获取HyAllotOrderCallback列表"
// @Success 200 {object} response.PageResult{data=[]response.HyAllotOrderCallback,total=int,page=int,pageSize=int,Extra=response.TmpAllotAmount} "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotOrderCallback/getHyAllotOrderCallbackList [get]
func (hyAllotOrderCallbackApi *HyAllotOrderCallbackApi) GetHyAllotOrderCallbackList(c *gin.Context) {
	var pageInfo payReq.HyAllotOrderCallbackSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, allotAmount, total, err := hyAllotOrderCallbackService.GetHyAllotOrderCallbackInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List: list,
			Extra: payResp.TmpAllotAmount{
				AllotAmt: allotAmount,
			},
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
