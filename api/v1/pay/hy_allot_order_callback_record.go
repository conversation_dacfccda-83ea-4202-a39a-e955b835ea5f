package pay

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/pay"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HyAllotOrderCallbackRecordApi struct {
}

var hyAllotOrderCallbackRecordService = service.ServiceGroupApp.OnlinePayServiceGroup.HyAllotOrderCallbackRecordService

// CreateHyAllotOrderCallbackRecord 创建HyAllotOrderCallbackRecord
// @Tags HyAllotOrderCallbackRecord
// @Summary 创建HyAllotOrderCallbackRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotOrderCallbackRecord true "创建HyAllotOrderCallbackRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotOrderCallbackRecord/createHyAllotOrderCallbackRecord [post]
func (hyAllotOrderCallbackRecordApi *HyAllotOrderCallbackRecordApi) CreateHyAllotOrderCallbackRecord(c *gin.Context) {
	var hyAllotOrderCallbackRecord pay.HyAllotOrderCallbackRecord
	err := c.ShouldBindJSON(&hyAllotOrderCallbackRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotOrderCallbackRecordService.CreateHyAllotOrderCallbackRecord(&hyAllotOrderCallbackRecord); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteHyAllotOrderCallbackRecord 删除HyAllotOrderCallbackRecord
// @Tags HyAllotOrderCallbackRecord
// @Summary 删除HyAllotOrderCallbackRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotOrderCallbackRecord true "删除HyAllotOrderCallbackRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /hyAllotOrderCallbackRecord/deleteHyAllotOrderCallbackRecord [delete]
func (hyAllotOrderCallbackRecordApi *HyAllotOrderCallbackRecordApi) DeleteHyAllotOrderCallbackRecord(c *gin.Context) {
	var hyAllotOrderCallbackRecord pay.HyAllotOrderCallbackRecord
	err := c.ShouldBindJSON(&hyAllotOrderCallbackRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotOrderCallbackRecordService.DeleteHyAllotOrderCallbackRecord(hyAllotOrderCallbackRecord); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteHyAllotOrderCallbackRecordByIds 批量删除HyAllotOrderCallbackRecord
// @Tags HyAllotOrderCallbackRecord
// @Summary 批量删除HyAllotOrderCallbackRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除HyAllotOrderCallbackRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /hyAllotOrderCallbackRecord/deleteHyAllotOrderCallbackRecordByIds [delete]
func (hyAllotOrderCallbackRecordApi *HyAllotOrderCallbackRecordApi) DeleteHyAllotOrderCallbackRecordByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotOrderCallbackRecordService.DeleteHyAllotOrderCallbackRecordByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateHyAllotOrderCallbackRecord 更新HyAllotOrderCallbackRecord
// @Tags HyAllotOrderCallbackRecord
// @Summary 更新HyAllotOrderCallbackRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body pay.HyAllotOrderCallbackRecord true "更新HyAllotOrderCallbackRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /hyAllotOrderCallbackRecord/updateHyAllotOrderCallbackRecord [put]
func (hyAllotOrderCallbackRecordApi *HyAllotOrderCallbackRecordApi) UpdateHyAllotOrderCallbackRecord(c *gin.Context) {
	var hyAllotOrderCallbackRecord pay.HyAllotOrderCallbackRecord
	err := c.ShouldBindJSON(&hyAllotOrderCallbackRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := hyAllotOrderCallbackRecordService.UpdateHyAllotOrderCallbackRecord(hyAllotOrderCallbackRecord); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindHyAllotOrderCallbackRecord 用id查询HyAllotOrderCallbackRecord
// @Tags HyAllotOrderCallbackRecord
// @Summary 用id查询HyAllotOrderCallbackRecord
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query pay.HyAllotOrderCallbackRecord true "用id查询HyAllotOrderCallbackRecord"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /hyAllotOrderCallbackRecord/findHyAllotOrderCallbackRecord [get]
func (hyAllotOrderCallbackRecordApi *HyAllotOrderCallbackRecordApi) FindHyAllotOrderCallbackRecord(c *gin.Context) {
	var hyAllotOrderCallbackRecord pay.HyAllotOrderCallbackRecord
	err := c.ShouldBindQuery(&hyAllotOrderCallbackRecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rehyAllotOrderCallbackRecord, err := hyAllotOrderCallbackRecordService.GetHyAllotOrderCallbackRecord(hyAllotOrderCallbackRecord.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rehyAllotOrderCallbackRecord": rehyAllotOrderCallbackRecord}, c)
	}
}

// GetHyAllotOrderCallbackRecordList 分页获取HyAllotOrderCallbackRecord列表
// @Tags HyAllotOrderCallbackRecord
// @Summary 分页获取HyAllotOrderCallbackRecord列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query payReq.HyAllotOrderCallbackRecordSearch true "分页获取HyAllotOrderCallbackRecord列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /hyAllotOrderCallbackRecord/getHyAllotOrderCallbackRecordList [get]
func (hyAllotOrderCallbackRecordApi *HyAllotOrderCallbackRecordApi) GetHyAllotOrderCallbackRecordList(c *gin.Context) {
	var pageInfo payReq.HyAllotOrderCallbackRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := hyAllotOrderCallbackRecordService.GetHyAllotOrderCallbackRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
