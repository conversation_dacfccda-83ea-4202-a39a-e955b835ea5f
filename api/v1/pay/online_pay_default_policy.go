package pay

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/pay"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OnlinePayDefaultPolicyApi struct {
}

var onlinePayDefaultPolicyService = service.ServiceGroupApp.OnlinePayServiceGroup.OnlinePayDefaultPolicyService

// CreateOnlinePayDefaultPolicy 创建OnlinePayDefaultPolicy
// @Tags OnlinePayDefaultPolicy
// @Summary 创建OnlinePayDefaultPolicy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.OnlinePayDefaultPolicyCreate true "创建OnlinePayDefaultPolicy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /onlinePayDefaultPolicy/createOnlinePayDefaultPolicy [post]
func (onlinePayDefaultPolicyApi *OnlinePayDefaultPolicyApi) CreateOnlinePayDefaultPolicy(c *gin.Context) {
	var onlinePayDefaultPolicy payReq.OnlinePayDefaultPolicyCreate
	err := c.ShouldBindJSON(&onlinePayDefaultPolicy)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"DefaultOnlinePayEnabled": {utils.NotEmpty()},
		"DefaultOnlinePayChannel": {utils.NotEmpty()},
		"DefaultOnlinePayType":    {utils.NotEmpty()},
	}
	if err := utils.Verify(onlinePayDefaultPolicy, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := onlinePayDefaultPolicyService.CreateOnlinePayDefaultPolicy(&onlinePayDefaultPolicy); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// // DeleteOnlinePayDefaultPolicy 删除OnlinePayDefaultPolicy
// // @Tags OnlinePayDefaultPolicy
// // @Summary 删除OnlinePayDefaultPolicy
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body pay.OnlinePayDefaultPolicy true "删除OnlinePayDefaultPolicy"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// // @Router /onlinePayDefaultPolicy/deleteOnlinePayDefaultPolicy [delete]
// func (onlinePayDefaultPolicyApi *OnlinePayDefaultPolicyApi) DeleteOnlinePayDefaultPolicy(c *gin.Context) {
// 	var onlinePayDefaultPolicy pay.OnlinePayDefaultPolicy
// 	err := c.ShouldBindJSON(&onlinePayDefaultPolicy)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := onlinePayDefaultPolicyService.DeleteOnlinePayDefaultPolicy(onlinePayDefaultPolicy); err != nil {
// 		global.GVA_LOG.Error("删除失败!", zap.Error(err))
// 		response.FailWithMessage("删除失败", c)
// 	} else {
// 		response.OkWithMessage("删除成功", c)
// 	}
// }

// // DeleteOnlinePayDefaultPolicyByIds 批量删除OnlinePayDefaultPolicy
// // @Tags OnlinePayDefaultPolicy
// // @Summary 批量删除OnlinePayDefaultPolicy
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data body request.IdsReq true "批量删除OnlinePayDefaultPolicy"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// // @Router /onlinePayDefaultPolicy/deleteOnlinePayDefaultPolicyByIds [delete]
// func (onlinePayDefaultPolicyApi *OnlinePayDefaultPolicyApi) DeleteOnlinePayDefaultPolicyByIds(c *gin.Context) {
// 	var IDS request.IdsReq
// 	err := c.ShouldBindJSON(&IDS)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if err := onlinePayDefaultPolicyService.DeleteOnlinePayDefaultPolicyByIds(IDS); err != nil {
// 		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
// 		response.FailWithMessage("批量删除失败", c)
// 	} else {
// 		response.OkWithMessage("批量删除成功", c)
// 	}
// }

// UpdateOnlinePayDefaultPolicy 更新OnlinePayDefaultPolicy
// @Tags OnlinePayDefaultPolicy
// @Summary 更新OnlinePayDefaultPolicy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.OnlinePayDefaultPolicyCreate true "更新OnlinePayDefaultPolicy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /onlinePayDefaultPolicy/updateOnlinePayDefaultPolicy [put]
func (onlinePayDefaultPolicyApi *OnlinePayDefaultPolicyApi) UpdateOnlinePayDefaultPolicy(c *gin.Context) {
	var onlinePayDefaultPolicy payReq.OnlinePayDefaultPolicyCreate
	err := c.ShouldBindJSON(&onlinePayDefaultPolicy)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"DefaultOnlinePayEnabled": {utils.NotEmpty()},
		"DefaultOnlinePayChannel": {utils.NotEmpty()},
		"DefaultOnlinePayType":    {utils.NotEmpty()},
	}
	if err := utils.Verify(onlinePayDefaultPolicy, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := onlinePayDefaultPolicyService.UpdateOnlinePayDefaultPolicy(pay.OnlinePayDefaultPolicy{
		GVA_MODEL: global.GVA_MODEL{
			ID: 1,
		},
		OnlinePayDefaultPolicyCreate: onlinePayDefaultPolicy,
	}); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindOnlinePayDefaultPolicy 用id查询OnlinePayDefaultPolicy
// @Tags OnlinePayDefaultPolicy
// @Summary 用id查询OnlinePayDefaultPolicy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /onlinePayDefaultPolicy/findOnlinePayDefaultPolicy [get]
func (onlinePayDefaultPolicyApi *OnlinePayDefaultPolicyApi) FindOnlinePayDefaultPolicy(c *gin.Context) {
	// var onlinePayDefaultPolicy pay.OnlinePayDefaultPolicy
	// err := c.ShouldBindQuery(&onlinePayDefaultPolicy)
	// if err != nil {
	// 	response.FailWithMessage(err.Error(), c)
	// 	return
	// }
	if reonlinePayDefaultPolicy, err := onlinePayDefaultPolicyService.GetOnlinePayDefaultPolicy(); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reonlinePayDefaultPolicy": reonlinePayDefaultPolicy}, c)
	}
}

// // GetOnlinePayDefaultPolicyList 分页获取OnlinePayDefaultPolicy列表
// // @Tags OnlinePayDefaultPolicy
// // @Summary 分页获取OnlinePayDefaultPolicy列表
// // @Security ApiKeyAuth
// // @accept application/json
// // @Produce application/json
// // @Param data query payReq.OnlinePayDefaultPolicySearch true "分页获取OnlinePayDefaultPolicy列表"
// // @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// // @Router /onlinePayDefaultPolicy/getOnlinePayDefaultPolicyList [get]
// func (onlinePayDefaultPolicyApi *OnlinePayDefaultPolicyApi) GetOnlinePayDefaultPolicyList(c *gin.Context) {
// 	var pageInfo payReq.OnlinePayDefaultPolicySearch
// 	err := c.ShouldBindQuery(&pageInfo)
// 	if err != nil {
// 		response.FailWithMessage(err.Error(), c)
// 		return
// 	}
// 	if list, total, err := onlinePayDefaultPolicyService.GetOnlinePayDefaultPolicyInfoList(pageInfo); err != nil {
// 		global.GVA_LOG.Error("获取失败!", zap.Error(err))
// 		response.FailWithMessage("获取失败", c)
// 	} else {
// 		response.OkWithDetailed(response.PageResult{
// 			List:     list,
// 			Total:    total,
// 			Page:     pageInfo.Page,
// 			PageSize: pageInfo.PageSize,
// 		}, "获取成功", c)
// 	}
// }
