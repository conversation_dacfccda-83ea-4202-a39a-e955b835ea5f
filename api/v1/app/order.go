package app

import (
	"errors"

	"github.com/OSQianXing/guanpu-server/global"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/orders"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"

	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderApi struct {
}

var orderService = service.ServiceGroupApp.OrdersServiceGroup.OrderService
var orderAppService = service.ServiceGroupApp.AppServiceGroup.OrderService

// CreateOrder 客户端下单
// @Tags AppOrder
// @Summary 创建Order
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.CreateOrderRequest true "创建Order"
// @Success 200 {string} string "{"success":true,"reorderNo":{},"msg":"获取成功"}"
// @Router /app/order/createOrder [post]
func (orderApi *OrderApi) CreateOrder(c *gin.Context) {
	var order ordersReq.CreateOrderRequest
	err := c.ShouldBindJSON(&order)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if order.AddressId == nil {
		response.FailWithMessage("参数有误", c)
		return
	}

	if len(order.Products) == 0 {
		response.FailWithMessage(errors.New("product info cannot be empty").Error(), c)
		return
	}

	var userID uint
	userID = utils.GetUserID(c)
	if order.OrderType == types.Franchisee {
		franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
		if err != nil {
			global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
			response.FailWithMessage("下单异常请稍后重试", c)
			return
		}
		order.FranchiseeId = franchisee.ID
	}

	// 账户锁
	locker := utils.NewDistributedLockEngine(utils.GetFranchiseeAccountKey(order.FranchiseeId))
	locker.Timeout(3600)
	lockSuccess, err := locker.Lock()
	if err != nil || !lockSuccess {
		global.GVA_LOG.Error("failed to acquire lock", zap.Error(err))
		response.FailWithMessage("下单异常请稍后重试", c)
		return
	}
	defer func() {
		_ = locker.UnLock()
	}()
	if order.PaymentType == types.OfflineReceipt {
		response.FailWithMessage("不支持的支付方式", c)
	}
	if orderNo, err := orderService.CreateOrder(&order, userID, utils.GetUserIP(c)); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败, "+err.Error(), c)
	} else {
		// response.OkWithMessage("创建成功", c)
		response.OkWithData(gin.H{"reorderNo": orderNo}, c)
	}
}

// FindOrder 订单详情
// @Tags AppOrder
// @Summary 订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param ID query int true "订单ID"
// @Success 200 {object} response.Response{data=response.AppOrderDetail}
// @Router /app/order/findOrder [get]
func (orderApi *OrderApi) FindOrder(c *gin.Context) {
	var order orders.Order
	err := c.ShouldBindQuery(&order)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reorder, err := orderAppService.GetOrder(order.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reorder": reorder}, c)
	}
}

// GetOrderList 分页获取Order列表
// @Tags AppOrder
// @Summary 分页获取Order列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.OrderSearch true "分页获取Order列表"
// @Success 200 {object} response.Response{data=[]response.OrderList,msg=string}
// @Router /app/order/getOrderList [get]
func (orderApi *OrderApi) GetOrderList(c *gin.Context) {
	var pageInfo appReq.OrderSearch

	//values := c.Request.URL.Query()
	//if statusValues, exists := values["status"]; exists && len(statusValues) == 0 {
	//	values.Del("status")
	//	c.Request.URL.RawQuery = values.Encode()
	//}

	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("查询失败,请稍后重试", c)
		return
	}

	if list, total, err := orderAppService.GetOrderInfoList(pageInfo, franchisee.ID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// FindOrderReturn 用id查询OrderReturn
// @Tags AppOrder
// @Summary 用id查询OrderReturn
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orders.OrderReturn true "用id查询OrderReturn"
// @Success 200  {object}  response.Response{data=response.OrderReturnDetail,msg=string}
// @Router /app/order/findOrderReturn [get]
func (orderApi *OrderApi) FindOrderReturn(c *gin.Context) {
	var orderReturn orders.OrderReturn
	err := c.ShouldBindQuery(&orderReturn)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reorderReturn, err := orderAppService.GetOrderReturn(orderReturn.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reorderReturn, c)
	}
}

// GetOrderReturnList 分页获取OrderReturn列表
// @Tags AppOrder
// @Summary 分页获取售后单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ordersReq.OrderReturnSearch true "分页获取售后单列表"
// @Success 200 {object} response.Response{data=[]response.AppOrderReturnList,msg=string}
// @Router /app/order/getOrderReturnList [get]
func (orderApi *OrderApi) GetOrderReturnList(c *gin.Context) {
	var pageInfo ordersReq.OrderReturnSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("下单异常请稍后重试", c)
		return
	}
	pageInfo.FranchiseeId = int(franchisee.ID)

	if list, total, err := orderAppService.GetOrderReturnInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// UpdateOrderAddress 更新订单收货地址
// @Tags AppOrder
// @Summary 更新订单收货地址
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.OrderAddressUpdate true "更新订单收货地址"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /app/order/updateOrderAddress [post]
func (orderApi *OrderApi) UpdateOrderAddress(c *gin.Context) {

	var orderAddressUpdate ordersReq.OrderAddressUpdate
	err := c.ShouldBindJSON(&orderAddressUpdate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	err = orderService.UpdateOrderAddress(franchisee.ID, &orderAddressUpdate)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}
