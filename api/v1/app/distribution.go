package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DistributionApi struct {
}

//var distributionService = service.ServiceGroupApp.DistributionServiceGroup.DistributionConfigService

// GetDistributionConfigForUser
// @Summary 获取分销配置
// @Description 获取分销配置
// @Tags AppDistribution
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.DistributionConfigForUser}
// @Router /app/distribution/getDistributionConfigForUser [get]
func (distributionApi *DistributionApi) GetDistributionConfigForUser(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户不存在", c)
		return
	}
	franchisee, err := service.ServiceGroupApp.AppServiceGroup.FranchiseeService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("获取加盟商异常请稍后重试", c)
		return
	}

	reDistributionConf, err := service.ServiceGroupApp.FranchiseesServiceGroup.GetDistributionConfigForUser(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("failed to get distribution config", zap.Error(err))
		response.FailWithMessage("获取分销配置异常请稍后重试", c)
		return
	}
	response.OkWithData(gin.H{"redistributionconf": reDistributionConf}, c)
}

// func (distributionApi *DistributionApi) RegisterDistributer(c *gin.Context) {

// }
