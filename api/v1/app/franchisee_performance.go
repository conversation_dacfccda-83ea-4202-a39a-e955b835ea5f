package app

import (
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/app"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	appRes "github.com/OSQianXing/guanpu-server/model/app/response"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	response "github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseePerformanceApi struct {
}

var franchiseePerformanceService = service.ServiceGroupApp.AppServiceGroup.FranchiseePerformanceService

// FindFranchiseePerformance 用id查询FranchiseePerformance
// @Tags AppFranchiseePerformance
// @Summary 用id查询FranchiseePerformance
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query app.FranchiseePerformance true "用id查询FranchiseePerformance"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /app/franchiseePerformance/findFranchiseePerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) FindFranchiseePerformance(c *gin.Context) {
	var franchiseePerformance app.FranchiseePerformance
	err := c.ShouldBindQuery(&franchiseePerformance)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchiseePerformance, err := franchiseePerformanceService.GetFranchiseePerformance(franchiseePerformance.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchiseePerformance": refranchiseePerformance}, c)
	}
}

// GetFranchiseePerformanceList 加盟商业绩详情.每笔的业绩
// @Tags AppFranchiseePerformance
// @Summary 加盟商业绩详情.每笔的业绩
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.FranchiseePerformanceSearch true "加盟商业绩详情.每笔的业绩"
// @Success 200 {object} response.PageResult{list=appRes.AppFranchiseePerformance}
// @Router /app/franchiseePerformance/getFranchiseePerformanceList [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetFranchiseePerformanceList(c *gin.Context) {
	var pageInfo appReq.FranchiseePerformanceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := franchiseePerformanceService.GetFranchiseePerformanceInfoList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// SummaryPerformance 加盟商业绩汇总
// @Tags AppFranchiseePerformance
// @Summary 加盟商业绩汇总
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=appRes.FranchiseePerformanceSummary}
// @Router /app/franchiseePerformance/summaryPerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) SummaryPerformance(c *gin.Context) {
	var _ appRes.FranchiseePerformanceSummary
	if list, err := franchiseePerformanceService.SummaryPerformance(c); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"summary": list}, c)
	}
}

// GetFranchiseeMemberPerformance 加盟商团队业绩汇总
// @Tags AppFranchiseePerformance
// @Summary 加盟商团队业绩
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{memberPerformance=response.FranchiseeMembersPerformance}
// @Router /app/franchiseePerformance/getFranchiseeMemberPerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetFranchiseeMemberPerformance(c *gin.Context) {

	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	memberPerformance, err := franchiseePerformanceService.GetFranchiseeMemberPerformance(franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"memberPerformance": memberPerformance}, c)
	return
}

// GetFranchiseeCustomerPerformance 加盟商客户业绩汇总
// @Tags AppFranchiseePerformance
// @Summary 加盟商客户业绩汇总
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{customerPerformance=response.FranchiseeCustomersPerformance}
// @Router /app/franchiseePerformance/getFranchiseeCustomerPerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetFranchiseeCustomerPerformance(c *gin.Context) {
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	customerPerformance, err := franchiseePerformanceService.GetFranchiseeCustomerPerformance(franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"customerPerformance": customerPerformance}, c)
}

// CheckAbilityForCustomerPerformance
// @Tags AppFranchiseePerformance
// @Summary 检测是否能够查看自己的客户业绩数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=bool}
// @Router /app/franchiseePerformance/checkAbilityForCustomerPerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) CheckAbilityForCustomerPerformance(c *gin.Context) {
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	checkAbilityForCustomerPerformance, err := franchiseePerformanceService.CheckAbilityForCustomerPerformance(franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"checkAbilityForCustomerPerformance": checkAbilityForCustomerPerformance}, c)
	return
}

// GetMyCustomerPerformanceMonthTop 我的当月客户业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 我的月份客户业绩Top 列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码,每页大小"
// @Success 200 {object} response.PageResult{List=[]response.FranchiseeCustomerPerformanceUnitNew,Total=int64,msg=string}
// @Router /app/franchiseePerformance/getMyCustomerPerformanceMonthTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyCustomerPerformanceMonthTop(c *gin.Context) {
	var info request.PageInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myCustomerPerformanceMonthTop, total, err := franchiseePerformanceService.GetMyCustomersPerformanceMonthTop(franchisee, info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// response.OkWithData(gin.H{"myCustomerPerformanceMonthTop": myCustomerPerformanceMonthTop}, c)
	response.OkWithDetailed(response.PageResult{
		List:     myCustomerPerformanceMonthTop,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMyCustomerPerformanceTodayTop 我的当日客户业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 我的当日客户业绩 Top 列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码,每页大小"
// @Success 200 {object} response.PageResult{data=[]response.FranchiseeCustomerPerformanceUnitNew,msg=string}
// @Router /app/franchiseePerformance/getMyCustomerPerformanceTodayTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyCustomerPerformanceTodayTop(c *gin.Context) {
	var info request.PageInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myCustomerPerformanceTodayTop, total, err := franchiseePerformanceService.GetMyCustomersPerformanceTodayTop(franchisee, info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// response.OkWithData(gin.H{"myCustomerPerformanceTodayTop": myCustomerPerformanceTodayTop}, c)
	response.OkWithDetailed(response.PageResult{
		List:     myCustomerPerformanceTodayTop,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMyCustomersPerformanceTopSearch 我的客户业绩 Top 搜索
// @Tags AppFranchiseePerformance
// @Summary 我的客户业绩 Top 搜索
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.CustomerPerformanceSearch true "页码,每页大小"
// @Success 200 {object} response.PageResult{data=[]response.FranchiseeCustomerPerformanceUnitNew,extra=response.SearchPerformanceExtra,msg=string}
// @Router /app/franchiseePerformance/getMyCustomersPerformanceTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyCustomersPerformanceTopSearch(c *gin.Context) {
	var info appReq.CustomerPerformanceSearch
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info.StartCreatedAt == nil || info.EndCreatedAt == nil {
		response.FailWithMessage("请选择日期范围", c)
		return
	}
	myCustomerPerformanceTop, total, err := franchiseePerformanceService.GetMyCustomersPerformanceTopSearch(franchisee, info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myMemberPerformance, err := franchiseePerformanceService.GetMemberPerformance(franchisee.ID, info.StartCreatedAt, info.EndCreatedAt)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myCustomerPerformance, err := franchiseePerformanceService.GetCustomerPerformance(franchisee.ID, info.StartCreatedAt, info.EndCreatedAt)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// response.OkWithData(gin.H{"myCustomerPerformanceTop": myCustomerPerformanceTop, "myMemberPerformance": myMemberPerformance}, c)
	response.OkWithDetailed(response.PageResult{
		List: myCustomerPerformanceTop,
		Extra: appRes.SearchPerformanceExtra{
			CustomerPerformance:   myCustomerPerformance,
			MemberPerformance:     myMemberPerformance,
			InvestmentPerformance: 0,
		},
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMyInvestmentPerformance 获取我的招商业绩汇总
// @Tags AppFranchiseePerformance
// @Summary 我的招商业绩汇总
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.FranchiseeInvestmentPerformanceSummary,msg=string}
// @Router /app/franchiseePerformance/getMyInvestmentPerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyInvestmentPerformance(c *gin.Context) {
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myInvestmentPerformance, err := franchiseePerformanceService.GetMyInvestmentPerformanceSummary(franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"myInvestmentPerformance": myInvestmentPerformance}, c)
}

// GetMyInvestmentPerformanceTodayTop 获取今日招商业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 今日招商业绩 Top 列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.Response{data=[]response.FranchiseeCustomerPerformanceUnitNew,msg=string}
// @Router /app/franchiseePerformance/getMyInvestmentPerformanceTodayTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyInvestmentPerformanceTodayTop(c *gin.Context) {
	var info request.PageInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myInvestmentPerformanceTodayTop, total, err := franchiseePerformanceService.GetMyInvestmentPerformanceTodayTop(franchisee, info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// response.OkWithData(gin.H{"myInvestmentPerformanceTodayTop": myInvestmentPerformanceTodayTop}, c)
	response.OkWithDetailed(response.PageResult{
		List:     myInvestmentPerformanceTodayTop,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMyInvestmentPerformanceMonthTop 获取当月招商业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 当月招商业绩 Top 列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeCustomerPerformanceUnitNew,msg=string}
// @Router /app/franchiseePerformance/getMyInvestmentPerformanceMonthTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyInvestmentPerformanceMonthTop(c *gin.Context) {
	var info request.PageInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myInvestmentPerformanceMonthTop, total, err := franchiseePerformanceService.GetMyInvestmentPerformanceMonthTop(franchisee, info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	// response.OkWithData(gin.H{"myInvestmentPerformanceMonthTop": myInvestmentPerformanceMonthTop}, c)
	response.OkWithDetailed(response.PageResult{
		List:     myInvestmentPerformanceMonthTop,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMyInvestmentPerformanceTopSearch 获取招商业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 招商业绩 Top 列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.InvestmentPerformanceSearch true "页码, 每页大小"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeInvestmentPerformanceUnit,extra=response.SearchPerformanceExtra,msg=string}
// @Router /app/franchiseePerformance/getMyInvestmentPerformanceTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMyInvestmentPerformanceTopSearch(c *gin.Context) {
	var info appReq.InvestmentPerformanceSearch
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info.StartCreatedAt == nil || info.EndCreatedAt == nil {
		response.FailWithMessage("请选择日期范围", c)
		return
	}
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myInvestmentPerformanceTop, total, err := franchiseePerformanceService.GetMyInvestmentPerformanceTopSearch(franchisee, info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	myInvestmentPerformance, err := franchiseePerformanceService.GetMyInvestmentPerformance(franchisee, info.StartCreatedAt, info.EndCreatedAt)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// response.OkWithData(gin.H{"myInvestmentPerformanceTop": myInvestmentPerformanceTop}, c)
	response.OkWithDetailed(response.PageResult{
		List: myInvestmentPerformanceTop,
		Extra: appRes.SearchPerformanceExtra{
			CustomerPerformance:   0,
			MemberPerformance:     0,
			InvestmentPerformance: myInvestmentPerformance,
		},
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)

}

// GetPerformanceDisplayConfigForUser 获取当前用户的业绩显示配置
// @Tags AppFranchiseePerformance
// @Summary 获取当前用户的业绩显示配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.FranchiseePerformanceConfig,msg=string}
// @Router /app/franchiseePerformance/getPerformanceDisplayConfigForUser [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetPerformanceDisplayConfigForUser(c *gin.Context) {
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	performanceConfig, err := franchiseePerformanceService.GetPerformanceDisplayConfigForFranchisee(franchisee)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(gin.H{"performanceConfig": performanceConfig}, c)
}

// GetMySecondaryInvestmentPerformanceSummary 获取我的二级招商业绩汇总
// @Tags AppFranchiseePerformance
// @Summary 我的二级招商业绩汇总
// @Description 获取我的二级招商业绩汇总信息，包含今日、本月、上月业绩
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.FranchiseeInvestmentPerformanceSummary,msg=string} "获取成功"
// @Router /app/franchiseePerformance/getMySecondaryInvestmentPerformanceSummary [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMySecondaryInvestmentPerformanceSummary(c *gin.Context) {
	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商信息失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商信息失败", c)
		return
	}

	// Check if franchisee has access to secondary performance
	hasAccess, err := service.ServiceGroupApp.FinanceServiceGroup.GeneratedInvestmentPerformanceFranchiseeConfigService.CheckSecondaryInvestmentPerformanceAccess(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("检查二级招商业绩权限失败!", zap.Error(err))
		response.FailWithMessage("检查二级招商业绩权限失败", c)
		return
	}
	if !hasAccess {
		response.FailWithMessage("没有查看二级招商业绩的权限", c)
		return
	}

	summary, err := franchiseePerformanceService.GetMySecondaryInvestmentPerformanceSummary(franchisee)
	if err != nil {
		global.GVA_LOG.Error("获取二级招商业绩汇总失败!", zap.Error(err))
		response.FailWithMessage("获取二级招商业绩汇总失败", c)
		return
	}

	response.OkWithData(summary, c)
}

// GetMySecondaryInvestmentPerformanceList 获取二级招商业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 二级招商业绩 Top 列表
// @Description 获取二级招商业绩 Top 列表, page默认为1, pageSize默认为10且最大为100
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.InvestmentPerformanceSearch true "页码, 每页大小, 开始时间, 结束时间"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeInvestmentPerformanceUnit,extra=response.SearchPerformanceExtra} "获取成功"
// @Router /app/franchiseePerformance/getMySecondaryInvestmentPerformanceList [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMySecondaryInvestmentPerformanceList(c *gin.Context) {
	var info appReq.InvestmentPerformanceSearch
	err := c.ShouldBindQuery(&info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商信息失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商信息失败", c)
		return
	}

	// Check if franchisee has access to secondary performance
	hasAccess, err := service.ServiceGroupApp.FinanceServiceGroup.GeneratedInvestmentPerformanceFranchiseeConfigService.CheckSecondaryInvestmentPerformanceAccess(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("检查二级招商业绩权限失败!", zap.Error(err))
		response.FailWithMessage("检查二级招商业绩权限失败", c)
		return
	}
	if !hasAccess {
		response.FailWithMessage("没有查看二级招商业绩的权限", c)
		return
	}

	list, total, err := franchiseePerformanceService.GetMySecondGenerationInvestmentPerformanceTopSearch(franchisee, info)
	if err != nil {
		global.GVA_LOG.Error("获取二级招商业绩列表失败!", zap.Error(err))
		response.FailWithMessage("获取二级招商业绩列表失败", c)
		return
	}

	// Calculate total amount for the search period
	totalAmount, err := franchiseePerformanceService.GetMySecondGenerationInvestmentPerformance(franchisee, info.StartCreatedAt, info.EndCreatedAt)
	if err != nil {
		global.GVA_LOG.Error("计算二级招商业绩总额失败!", zap.Error(err))
		response.FailWithMessage("计算二级招商业绩总额失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List: list,
		Extra: gin.H{
			"totalAmount": totalAmount,
		},
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMySecondaryInvestmentPerformanceTodayTop 获取今日二级招商业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 今日二级招商业绩 Top 列表
// @Description 获取今日二级招商业绩 Top 列表, page默认为1, pageSize默认为10且最大为100
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeInvestmentPerformanceUnit} "获取成功"
// @Router /app/franchiseePerformance/getMySecondaryInvestmentPerformanceTodayTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMySecondaryInvestmentPerformanceTodayTop(c *gin.Context) {
	var info request.PageInfo
	err := c.ShouldBindQuery(&info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.PageSize > 100 {
		info.PageSize = 100
	}

	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商信息失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商信息失败", c)
		return
	}

	// Check if franchisee has access to secondary performance
	hasAccess, err := service.ServiceGroupApp.FinanceServiceGroup.GeneratedInvestmentPerformanceFranchiseeConfigService.CheckSecondaryInvestmentPerformanceAccess(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("检查二级招商业绩权限失败!", zap.Error(err))
		response.FailWithMessage("检查二级招商业绩权限失败", c)
		return
	}
	if !hasAccess {
		response.FailWithMessage("没有查看二级招商业绩的权限", c)
		return
	}

	list, total, err := franchiseePerformanceService.GetMySecondGenerationInvestmentPerformanceTodayTop(franchisee, info)
	if err != nil {
		global.GVA_LOG.Error("获取今日二级招商业绩列表失败!", zap.Error(err))
		response.FailWithMessage("获取今日二级招商业绩列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}

// GetMySecondaryInvestmentPerformanceMonthTop 获取当月二级招商业绩 Top 列表
// @Tags AppFranchiseePerformance
// @Summary 当月二级招商业绩 Top 列表
// @Description 获取当月二级招商业绩 Top 列表, page默认为1, pageSize默认为10且最大为100
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeInvestmentPerformanceUnit} "获取成功"
// @Router /app/franchiseePerformance/getMySecondaryInvestmentPerformanceMonthTop [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetMySecondaryInvestmentPerformanceMonthTop(c *gin.Context) {
	var info request.PageInfo
	if err := c.ShouldBindQuery(&info); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.PageSize > 100 {
		info.PageSize = 100
	}

	userId := utils.GetUserID(c)
	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商信息失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商信息失败", c)
		return
	}

	// Check if franchisee has access to secondary performance
	hasAccess, err := service.ServiceGroupApp.FinanceServiceGroup.GeneratedInvestmentPerformanceFranchiseeConfigService.CheckSecondaryInvestmentPerformanceAccess(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("检查二级招商业绩权限失败!", zap.Error(err))
		response.FailWithMessage("检查二级招商业绩权限失败", c)
		return
	}
	if !hasAccess {
		response.FailWithMessage("没有查看二级招商业绩的权限", c)
		return
	}

	list, total, err := franchiseePerformanceService.GetMySecondGenerationInvestmentPerformanceMonthTop(franchisee, info)
	if err != nil {
		global.GVA_LOG.Error("获取当月二级招商业绩列表失败!", zap.Error(err))
		response.FailWithMessage("获取当月二级招商业绩列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     info.Page,
		PageSize: info.PageSize,
	}, "获取成功", c)
}
