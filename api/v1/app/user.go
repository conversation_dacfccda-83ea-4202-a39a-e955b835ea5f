package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/system"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserAPi struct{}

var userService = service.ServiceGroupApp.SystemServiceGroup.UserService

// ChangePassword
// @Tags      AppUser
// @Summary   用户修改密码
// @Security  ApiKeyAuth
// @Produce  application/json
// @Param     data  body      systemReq.ChangePasswordReq    true  "用户名, 原密码, 新密码"
// @Success   200   {object}  response.Response{msg=string}  "用户修改密码"
// @Router    /app/user/changePassword [post]
func (b *UserAPi) ChangePassword(c *gin.Context) {
	var req systemReq.ChangePasswordReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.ChangePasswordVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)
	u := &system.SysUser{GVA_MODEL: global.GVA_MODEL{ID: uid}, Password: req.Password}
	_, err = userService.ChangePassword(u, req.NewPassword)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败，原密码与当前账户不符", c)
		return
	}
	response.OkWithMessage("修改成功", c)
}
