package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	warehouseReq "github.com/OSQianXing/guanpu-server/model/warehouse/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CloudWarehouseApi struct {
}

var cloudWarehouseService = service.ServiceGroupApp.AppServiceGroup.CloudWarehouseService
var cloudWarehouseRecordService = service.ServiceGroupApp.WarehouseServiceGroup.CloudWarehouseRecordService

// GetCloudWarehouseProductList 分页获取CloudWarehouse列表
// @Tags AppCloudWarehouse
// @Summary 分页获取CloudWarehouse列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.CloudWarehouseSearch true "分页获取CloudWarehouse列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /app/cloudWarehouse/getCloudWarehouseProductList [get]
func (cloudWarehouseApi *CloudWarehouseApi) GetCloudWarehouseProductList(c *gin.Context) {
	var pageInfo appReq.CloudWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("查询失败,请稍后重试", c)
	}
	if list, total, err := cloudWarehouseService.GetCloudWarehouseInfoList(pageInfo, franchisee.ID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetCloudWarehouseRecord 云仓库流水
// @Tags AppCloudWarehouse
// @Summary 云仓库流水
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query warehouseReq.CloudWarehouseRecordSearch true "云仓库流水"
// @Success 200 {object} response.PageResult{list=[]response.CloudWarehouseRecordPagination}
// @Router /app/cloudWarehouse/getCloudWarehouseRecord [get]
func (cloudWarehouseApi *CloudWarehouseApi) GetCloudWarehouseRecord(c *gin.Context) {
	var pageInfo warehouseReq.CloudWarehouseRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("查询失败,请稍后重试", c)
	} else {
		pageInfo.FranchiseeId = int(franchisee.ID)
	}
	if list, total, err := cloudWarehouseRecordService.GetCloudWarehouseRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetAppCloudWarehouseStatistics 云仓统计
// @Tags AppCloudWarehouse
// @Summary 云仓统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.AppCloudWarehouseStatistics
// @Router /app/cloudWarehouse/getAppCloudWarehouseStatistics [get]
func (cloudWarehouseApi *CloudWarehouseApi) GetAppCloudWarehouseStatistics(c *gin.Context) {
	userID := utils.GetUserID(c)
	franchisee, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("查询失败,请稍后重试", c)
	}
	if list, err := cloudWarehouseService.GetAppCloudWarehouseStatistics(franchisee.ID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}
