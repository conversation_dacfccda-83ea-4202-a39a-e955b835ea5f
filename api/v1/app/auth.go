package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"
	franchiseesService "github.com/OSQianXing/guanpu-server/service/franchisees"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
	"go.uber.org/zap"
)

type AuthApi struct{}

var (
	franchiseeMultiTenantService = franchiseesService.FranchiseeMultiTenantService{}
	store                        = base64Captcha.DefaultMemStore
)

// FranchiseeLoginRequest 加盟商登录请求
type FranchiseeLoginRequest struct {
	Username  string `json:"username" binding:"required"`
	Password  string `json:"password" binding:"required"`
	Captcha   string `json:"captcha" binding:"required"`
	CaptchaId string `json:"captchaId" binding:"required"`
}

// ConfirmTenantLoginRequest 确认租户登录请求
type ConfirmTenantLoginRequest struct {
	TempToken string `json:"tempToken" binding:"required"`
	TenantID  uint   `json:"tenantId" binding:"required"`
}

// SwitchTenantRequest 切换租户请求
type SwitchTenantRequest struct {
	TenantID uint `json:"tenantId" binding:"required"`
}

// FranchiseeLogin 加盟商登录
// @Tags App认证
// @Summary 加盟商登录
// @Description 加盟商登录，支持单租户直接登录和多租户选择登录
// @Accept json
// @Produce json
// @Param data body FranchiseeLoginRequest true "登录信息"
// @Success 200 {object} response.Response{data=interface{}} "登录成功"
// @Router /app/auth/login [post]
func (a *AuthApi) FranchiseeLogin(c *gin.Context) {
	var req FranchiseeLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	// 验证验证码
	if !store.Verify(req.CaptchaId, req.Captcha, true) {
		response.FailWithMessage("验证码错误", c)
		return
	}

	// 验证用户凭据
	user, err := franchiseeMultiTenantService.AuthenticateUser(req.Username, req.Password)
	if err != nil {
		global.GVA_LOG.Error("用户认证失败", zap.Error(err), zap.String("username", req.Username))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户的加盟商租户列表
	tenants, err := franchiseeMultiTenantService.GetUserFranchisees(user.ID)
	if err != nil {
		global.GVA_LOG.Error("获取用户租户列表失败", zap.Error(err), zap.Uint("userID", user.ID))
		response.FailWithMessage("获取租户信息失败", c)
		return
	}

	if len(tenants) == 0 {
		response.FailWithMessage("您还没有加盟商身份，请联系管理员", c)
		return
	}

	// 如果只有一个租户，直接登录
	if len(tenants) == 1 {
		// 获取加盟商信息
		var franchisee franchisees.Franchisee
		err := global.GVA_DB.Scopes(tenant.SkipTenant).
			Where("user_id = ? AND tenant_id = ?", user.ID, tenants[0].TenantID).
			First(&franchisee).Error
		if err != nil {
			global.GVA_LOG.Error("获取加盟商信息失败", zap.Error(err))
			response.FailWithMessage("获取加盟商信息失败", c)
			return
		}

		token, err := franchiseeMultiTenantService.HandleSingleTenantLogin(user, &franchisee)
		if err != nil {
			global.GVA_LOG.Error("单租户登录失败", zap.Error(err))
			response.FailWithMessage("登录失败", c)
			return
		}

		response.OkWithDetailed(gin.H{
			"token":      token,
			"user":       user,
			"tenant":     tenants[0],
			"franchisee": franchisee,
		}, "登录成功", c)
		return
	}

	// 多租户情况，返回租户选择列表
	multiTenantResponse, err := franchiseeMultiTenantService.HandleMultiTenantLogin(user)
	if err != nil {
		global.GVA_LOG.Error("多租户登录处理失败", zap.Error(err))
		response.FailWithMessage("登录处理失败", c)
		return
	}

	response.OkWithDetailed(multiTenantResponse, "请选择要登录的租户", c)
}

// ConfirmTenantLogin 确认租户登录
// @Tags App认证
// @Summary 确认租户登录
// @Description 用户选择租户后确认登录
// @Accept json
// @Produce json
// @Param data body ConfirmTenantLoginRequest true "确认登录信息"
// @Success 200 {object} response.Response{data=interface{}} "登录成功"
// @Router /app/auth/confirm-tenant [post]
func (a *AuthApi) ConfirmTenantLogin(c *gin.Context) {
	var req ConfirmTenantLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	token, err := franchiseeMultiTenantService.ConfirmTenantLogin(req.TempToken, req.TenantID)
	if err != nil {
		global.GVA_LOG.Error("确认租户登录失败", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取用户、租户和加盟商信息用于响应
	j := utils.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		response.FailWithMessage("令牌解析失败", c)
		return
	}

	response.OkWithDetailed(gin.H{
		"token":      token,
		"user":       gin.H{"id": claims.UserID, "username": claims.Username, "nickName": claims.NickName},
		"tenant":     gin.H{"id": claims.TenantID, "code": claims.TenantCode},
		"franchisee": gin.H{"id": claims.FranchiseeID},
	}, "登录成功", c)
}

// SwitchTenant 切换租户
// @Tags App认证
// @Summary 切换租户
// @Description 切换到指定租户
// @Accept json
// @Produce json
// @Param data body SwitchTenantRequest true "切换租户信息"
// @Success 200 {object} response.Response{data=interface{}} "切换成功"
// @Router /app/auth/switch-tenant [post]
func (a *AuthApi) SwitchTenant(c *gin.Context) {
	var req SwitchTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户信息获取失败", c)
		return
	}

	token, err := franchiseeMultiTenantService.SwitchTenant(userID, req.TenantID)
	if err != nil {
		global.GVA_LOG.Error("切换租户失败", zap.Error(err), zap.Uint("userID", userID), zap.Uint("tenantID", req.TenantID))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取新的令牌信息用于响应
	j := utils.NewJWT()
	claims, err := j.ParseToken(token)
	if err != nil {
		response.FailWithMessage("令牌解析失败", c)
		return
	}

	response.OkWithDetailed(gin.H{
		"token":      token,
		"tenant":     gin.H{"id": claims.TenantID, "code": claims.TenantCode},
		"franchisee": gin.H{"id": claims.FranchiseeID},
	}, "切换成功", c)
}

// GetMyTenants 获取我的租户列表
// @Tags App认证
// @Summary 获取我的租户列表
// @Description 获取当前用户的所有租户列表
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]franchiseesService.FranchiseeTenant} "获取成功"
// @Router /app/auth/my-tenants [get]
func (a *AuthApi) GetMyTenants(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("用户信息获取失败", c)
		return
	}

	currentTenantID := utils.GetTenantID(c)

	tenants, err := franchiseeMultiTenantService.GetUserFranchisees(userID)
	if err != nil {
		global.GVA_LOG.Error("获取用户租户列表失败", zap.Error(err), zap.Uint("userID", userID))
		response.FailWithMessage("获取租户列表失败", c)
		return
	}

	// 标记当前租户
	for i := range tenants {
		tenants[i].IsCurrent = tenants[i].TenantID == currentTenantID
	}

	response.OkWithDetailed(tenants, "获取成功", c)
}
