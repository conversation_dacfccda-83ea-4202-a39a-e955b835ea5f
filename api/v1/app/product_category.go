package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductCategoryApi struct {
}

var productCategoryService = service.ServiceGroupApp.AppServiceGroup.ProductCategoryService

// QueryTopCategory  查询商品所有顶级分类
// @Tags AppProductCategory
// @Summary 查询商品所有顶级分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]app.ProductCategory}
// @Router /app/productCategory/queryTopCategory [get]
func (productCategoryApi *ProductCategoryApi) QueryTopCategory(c *gin.Context) {
	if reproductCategory, err := productCategoryService.GetProductTopCategory(); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproductCategory": reproductCategory}, c)
	}
}

// QuerySubCategory  查询子分类
// @Tags AppProductCategory
// @Summary 查询子分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.IDRequest true "查询子分类"
// @Success 200 {object} response.Response{data=[]app.ProductCategory}
// @Router /app/productCategory/querySubCategory [get]
func (productCategoryApi *ProductCategoryApi) QuerySubCategory(c *gin.Context) {
	var req appReq.IDRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproductCategory, err := productCategoryService.GetProductSubCategory(req.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproductCategory": reproductCategory}, c)
	}
}
