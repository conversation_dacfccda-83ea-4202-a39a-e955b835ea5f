package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	appRes "github.com/OSQianXing/guanpu-server/model/app/response"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductApi struct {
}

var productService = service.ServiceGroupApp.AppServiceGroup.ProductService

// FindProduct 用id查询Product
// @Tags AppProduct
// @Summary 用id查询Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.IDRequest true "用id查询Product"
// @Success 200 {object} response.Response{data=app.Product}
// @Router /app/product/findProduct [get]
func (productApi *ProductApi) FindProduct(c *gin.Context) {
	var req appReq.IDRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reproduct, err := productService.GetProduct(req.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reproduct": reproduct}, c)
	}
}

// GetProductList 分页获取Product列表
// @Tags AppProduct
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.ProductSearch true "分页获取Product列表"
// @Success 200 {object} response.Response{data=[]response.AppProductPagination}
// @Router /app/product/getProductList [get]
func (productApi *ProductApi) GetProductList(c *gin.Context) {
	var pageInfo appReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := productService.GetProductInfoList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"list": list}, c)
	}
}

// GetProductListB 分页获取Product列表 B方案
// @Tags AppProduct
// @Summary 分页获取Product列表 B方案
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.ProductSearch true "分页获取Product列表 B方案"
// @Success 200 {object} response.Response{data=[]response.AppProductPaginationB}
// @Router /app/product/getProductListB [get]
func (productApi *ProductApi) GetProductListB(c *gin.Context) {
	var pageInfo appReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := productService.GetProductInfoListB(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"list": list}, c)
	}
}

// GetProductListC 分页获取Product列表 方案 C
// @Tags AppProduct
// @Summary 分页获取Product列表方案 C
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.ProductSearch true "分页获取Product列表方案 C"
// @Success 200 {object} response.Response{data=[]response.AppProduct}
// @Router /app/product/getProductListC [get]
func (productApi *ProductApi) GetProductListC(c *gin.Context) {
	var pageInfo appReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productService.GetProductInfoListC(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetProductListD 分页获取Product列表 方案 D
// @Tags AppProduct
// @Summary 分页获取Product列表方案 D
// @accept application/json
// @Produce application/json
// @Param data query appReq.ProductSearch true "分页获取Product列表方案 D"
// @Success 200 {object} response.Response{data=[]response.AppProduct}
// @Router /app/product/getProductListD [get]
func (productApi *ProductApi) GetProductListD(c *gin.Context) {
	var pageInfo appReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := productService.GetProductInfoListD(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)

	}
}

// GetSpecialMallList 获取当前加盟商业绩的专区列表
// @Tags AppProduct
// @Summary 获取当前加盟商的专区列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]appRes.SpecialMall}
// @Router /app/product/getSpecialMallList [get]
func (productApi *ProductApi) GetSpecialMallList(c *gin.Context) {
	var _ appRes.SpecialMall
	if list, err := productService.GetSpecialMallListForPerformance(c); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}
