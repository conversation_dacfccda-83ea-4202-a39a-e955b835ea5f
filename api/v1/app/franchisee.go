package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	franchiseeReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	franchiseeResp "github.com/OSQianXing/guanpu-server/model/franchisees/response"
	"github.com/OSQianXing/guanpu-server/service"
	distributionSrv "github.com/OSQianXing/guanpu-server/service/franchisees/distribution"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseeApi struct {
}

var franchiseeAppService = service.ServiceGroupApp.AppServiceGroup.FranchiseeService
var franchiseeService = service.ServiceGroupApp.FranchiseesServiceGroup.FranchiseeService
var rechargeRecordService = service.ServiceGroupApp.FranchiseesServiceGroup.RechargeRecordService

// FindFranchisee 用加盟商id查询 加盟商信息
// @Tags AppFranchisee
// @Summary 用id查询Franchisee
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.AppFranchiseeBrief}
// @Router /app/franchisee/findFranchisee [get]
func (franchiseeApi *FranchiseeApi) FindFranchisee(c *gin.Context) {
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}

	if refranchisee, err := franchiseeAppService.GetFranchisee(f.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchisee": refranchisee}, c)
	}
}

// GetFranchiseeCustomerOverview 获取加盟商客户概览
// @Tags AppFranchisee
// @Summary 获取加盟商客户概览
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]response.FranchiseeCustomerOverview}
// @Router /app/franchisee/getFranchiseeCustomerOverview [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeCustomerOverview(c *gin.Context) {
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	overview, err := franchiseeAppService.GetFranchiseeCustomerOverview(f)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商客户概览失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商客户概览失败", c)
		return
	} else {
		response.OkWithData(overview, c)
		return
	}
}

// GetFranchiseeSearchCustomer 搜索我的客户
// @Tags AppFranchisee
// @Summary 搜索我的客户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @param data body franchiseeReq.FranchiseeCustomerSearchReq true "搜索我的客户关键字"
// @Success 200 {object} response.Response{data=[]response.FranchiseeCustomer}
// @Router /app/franchisee/getFranchiseeSearchCustomer [post]
func (franchiseeApi *FranchiseeApi) GetFranchiseeSearchCustomer(c *gin.Context) {
	var searchCustomer franchiseeReq.FranchiseeCustomerSearchReq
	if err := c.ShouldBindJSON(&searchCustomer); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	res, err := franchiseeAppService.GetFranchiseeSearchCustomer(f, searchCustomer.Keyword, 20)
	if err != nil {
		//global.GVA_LOG.Error("搜索我的客户失败!", zap.Error(err))
		response.FailWithMessage("未搜到相关客户", c)
		return
	}
	global.GVA_LOG.Debug("service.app.franchisee.getFranchiseeSearchCustomer res", zap.Any("res", res))

	response.OkWithData(res, c)
	return
}

// IsTopCategory 判断当前用户是否是一级分销加盟商
// @Tags AppFranchisee
// @Summary 判断当前用户是否是一级分销加盟商
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=response.IsTopCategory}
// @Router /app/franchisee/isTopCategory [get]
func (franchiseeApi *FranchiseeApi) IsTopCategory(c *gin.Context) {
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	disSvr := &distributionSrv.DistributionConfigService{}
	disConf, err := disSvr.GetDistributionConfig()
	if err != nil {
		global.GVA_LOG.Error("获取分销配置失败!", zap.Error(err))
		response.FailWithMessage("获取分销配置失败", c)
		return
	}
	global.GVA_LOG.Debug("disConf", zap.Any("disConf", disConf), zap.Any("f", f), zap.Any("disConf.TopFCategoryId", disConf.TopFCategoryId), zap.Any("f.FCategoryId", f.FCategoryId))
	re := franchiseeResp.IsTopCategory{IsTopCategory: disConf.TopFCategoryId == *f.FCategoryId}
	response.OkWithData(re, c)
	return
}

// GetFranchiseeAllCustomer 获取所有客户兼容版本
// @Tags AppFranchisee
// @Summary 获取所有客户兼容分页版本
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @param data body request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeCustomerUnit}
// @Router /app/franchisee/getFranchiseeAllCustomer [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeAllCustomer(c *gin.Context) {
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	var pageInfo request.PageInfo
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		pageInfo.Page = 1
		pageInfo.PageSize = 1999
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}
	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 1999
	}
	res, total, err := franchiseeAppService.GetFranchiseeAllCustomer(f, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取所有客户失败!", zap.Error(err))
		response.FailWithMessage("获取所有客户失败", c)
		return
	}
	global.GVA_LOG.Debug("service.app.franchisee.getFranchiseeAllCustomer res",
		zap.Any("f", f),
		zap.Any("res", res),
		zap.Any("total", total))
	response.OkWithData(gin.H{"list": res, "total": total, "page": pageInfo.Page, "pageSize": pageInfo.PageSize}, c)
}

// GetFranchiseeAllCustomerV2 获取所有客户分页版本
// @Tags AppFranchisee
// @Summary 获取所有客户分页版本
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.PageInfo true "页码, 每页大小"
// @Success 200 {object} response.PageResult{list=[]response.FranchiseeCustomerUnit,total=int64,page=int,pageSize=int}
// @Router /app/franchisee/getFranchiseeAllCustomerV2 [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeAllCustomerV2(c *gin.Context) {
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	var pageInfo request.PageInfo
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		pageInfo.Page = 1
		pageInfo.PageSize = 1999
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}
	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 1999
	}
	res, total, err := franchiseeAppService.GetFranchiseeAllCustomer(f, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取所有客户失败!", zap.Error(err))
		response.FailWithMessage("获取所有客户失败", c)
		return
	}
	global.GVA_LOG.Debug("service.app.franchisee.getFranchiseeAllCustomer res",
		zap.Any("f", f),
		zap.Any("res", res),
		zap.Any("total", total))
	response.OkWithData(gin.H{"list": res, "total": total, "page": pageInfo.Page, "pageSize": pageInfo.PageSize}, c)
}

// GetCustomerInviterInfo 获取客户的邀请人信息
// @Tags AppFranchisee
// @Summary 获取客户的邀请人信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @param data query request.FranchiseeIdReq true "客户ID"
// @Success 200 {object} response.Response{data=response.FranchiseeCustomerUnit}
// @Router /app/franchisee/getCustomerInviterInfo [get]
func (franchiseeApi *FranchiseeApi) GetCustomerInviterInfo(c *gin.Context) {
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	var req franchiseeReq.FranchiseeIdReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	res, err := franchiseeService.GetCustomerInviterInfo(f, req)
	if err != nil {
		global.GVA_LOG.Error("获取客户邀请人信息失败!", zap.Error(err))
		response.FailWithMessage("获取客户邀请人信息失败"+err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

// GetFranchiseeProfile 获取加盟商信息
// @Tags AppFranchisee
// @Summary 获取加盟商信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @param data query request.FranchiseeIdReq true "客户ID"
// @Success 200 {object} response.Response{data=response.FranchiseeProfileRes}
// @Router /app/franchisee/getFranchiseeProfile [get]
func (franchiseeApi *FranchiseeApi) GetFranchiseeProfile(c *gin.Context) {
	var req franchiseeReq.FranchiseeIdReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}

	if req.ID == 0 {
		f.ID = req.ID
	} else if req.ID != 0 && req.ID != f.ID {
		dconf, err := service.ServiceGroupApp.DistributionServiceGroup.GetDistributionConfig()
		if err != nil {
			global.GVA_LOG.Error("获取加盟商配置失败!", zap.Error(err))
			response.FailWithMessage("获取加盟商配置失败"+err.Error(), c)
			return
		}
		if dconf.TopFCategoryId == *f.FCategoryId {
			customersIDs, err := franchiseeService.GetCustomerIDsViaFranchiseeLink(f.ID)
			if err != nil {
				global.GVA_LOG.Error("获取客户列表失败!", zap.Error(err))
				response.FailWithMessage("获取客户列表失败"+err.Error(), c)
				return
			}
			if !utils.Contains(req.ID, customersIDs) {
				response.FailWithMessage("该客户不是您的客户", c)
				return
			} else {
				f.ID = req.ID
			}
		} else {
			customersIDs, err := franchiseeService.GetCustomersIDsForNonTopViaFranchiseeLink(f.ID)
			if err != nil {
				global.GVA_LOG.Error("获取客户列表失败!", zap.Error(err))
				response.FailWithMessage("获取客户列表失败"+err.Error(), c)
				return
			}
			if !utils.Contains(req.ID, customersIDs) {
				response.FailWithMessage("该客户不是您的客户", c)
				return
			} else {
				f.ID = req.ID
			}
		}
	}

	res, err := franchiseeService.GetFranchiseeProfile(f)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商信息失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商信息失败"+err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

// GetDirectCustomers  获取直招客户
// @Tags AppFranchisee
// @Summary 获取直招客户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.FranchiseeIdReq true "加盟商ID"
// @Success 200 {object} response.Response{data=[]response.FranchiseeCustomer}
// @Router /app/franchisee/getDirectCustomers [get]
func (franchiseeApi *FranchiseeApi) GetDirectCustomers(c *gin.Context) {
	var req franchiseeReq.FranchiseeIdReq
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	if req.ID == 0 {
		req.ID = f.ID
	} else if req.ID != 0 && req.ID != f.ID {
		dconf, err := service.ServiceGroupApp.DistributionServiceGroup.GetDistributionConfig()
		if err != nil {
			global.GVA_LOG.Error("获取加盟商配置失败!", zap.Error(err))
			response.FailWithMessage("获取加盟商配置失败"+err.Error(), c)
			return
		}
		if dconf.TopFCategoryId == *f.FCategoryId {
			customersIDs, err := franchiseeService.GetCustomerIDsViaFranchiseeLink(f.ID)
			if err != nil {
				global.GVA_LOG.Error("获取客户列表失败!", zap.Error(err))
				response.FailWithMessage("获取客户列表失败"+err.Error(), c)
				return
			}
			if !utils.Contains(req.ID, customersIDs) {
				response.FailWithMessage("该客户不是您的客户", c)
				return
			}
		} else {
			customersIDs, err := franchiseeService.GetCustomersIDsForNonTopViaFranchiseeLink(f.ID)
			if err != nil {
				global.GVA_LOG.Error("获取客户列表失败!", zap.Error(err))
				response.FailWithMessage("获取客户列表失败"+err.Error(), c)
				return
			}
			if !utils.Contains(req.ID, customersIDs) {
				response.FailWithMessage("该客户不是您的客户", c)
				return
			}
		}
	}
	global.GVA_LOG.Debug("获取直招客户", zap.Any("req", req))
	directCustomer, err := franchiseeService.GetDirectCustomers(req.ID)
	if err != nil {
		global.GVA_LOG.Error("获取直招客户失败!", zap.Error(err))
		response.FailWithMessage("获取直招客户失败"+err.Error(), c)
		return
	}
	response.OkWithData(directCustomer, c)
	return
}

// GetRechargeRecordList 分页获取RechargeRecord列表
// @Tags AppFranchisee
// @Summary 分页获取RechargeRecord列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.RechargeRecordSearch true "分页获取RechargeRecord列表"
// @Success 200 {object} response.PageResult{list=[]response.RechargeRecordItem} "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /app/franchisee/getRechargeRecordList [get]
func (franchiseeApi *FranchiseeApi) GetRechargeRecordList(c *gin.Context) {
	var pageInfo franchiseeReq.RechargeRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	userID := utils.GetUserID(c)
	f, err := franchiseeAppService.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("failed to get franchisee", zap.Error(err))
		response.FailWithMessage("当前用户不是加盟商", c)
		return
	}
	pageInfo.FranchiseeId = f.ID
	if list, total, summary, err := rechargeRecordService.GetRechargeRecordInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Extra:    summary,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
	return
}
