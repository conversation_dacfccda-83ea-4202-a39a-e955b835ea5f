package app

import (
	"github.com/OSQianXing/guanpu-server/global"
	appReq "github.com/OSQianXing/guanpu-server/model/app/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CartApi struct {
}

var cartService = service.ServiceGroupApp.AppServiceGroup.CartService

// CreateCart 创建Cart
// @Tags AppCart
// @Summary 创建Cart
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body appReq.CartCreate true "创建Cart"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /app/cart/createCart [post]
func (cartApi *CartApi) CreateCart(c *gin.Context) {
	var req appReq.CartCreate
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cartService.CreateCart(c, &req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateCart 清空购物车及更新Cart
// @Tags AppCart
// @Summary 清空购物车及更新Cart
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body appReq.CartUpdate true "清空购物车及更新Cart"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /app/cart/updateCart [put]
func (cartApi *CartApi) UpdateCart(c *gin.Context) {
	var cart appReq.CartUpdate
	err := c.ShouldBindJSON(&cart)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := cartService.UpdateCart(c, cart); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// GetCartList 分页获取Cart列表
// @Tags AppCart
// @Summary 分页获取Cart列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appReq.CartSearch true "分页获取Cart列表"
// @Success 200 {object} response.Response{data=[]app.Cart}
// @Router /app/cart/getCartList [get]
func (cartApi *CartApi) GetCartList(c *gin.Context) {
	var pageInfo appReq.CartSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := cartService.GetCartInfoList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
