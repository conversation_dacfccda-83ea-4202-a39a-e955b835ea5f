package apprelease

import (
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/apprelease"
	appreleaseReq "github.com/OSQianXing/guanpu-server/model/apprelease/request"
	appreleaseResp "github.com/OSQianXing/guanpu-server/model/apprelease/response"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AppReleaseApi struct {
}

var arService = service.ServiceGroupApp.AppreleaseServiceGroup.AppReleaseService

// CreateAppRelease 创建AppRelease
// @Tags AppRelease
// @Summary 创建AppRelease
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body appreleaseReq.AppReleaseCreate true "创建AppRelease"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ar/createAppRelease [post]
func (arApi *AppReleaseApi) CreateAppRelease(c *gin.Context) {
	var arq appreleaseReq.AppReleaseCreate
	err := c.ShouldBindJSON(&arq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	arq.CreatedBy = utils.GetUserID(c)
	verify := utils.Rules{
		"OsType":     {utils.NotEmpty()},
		"VersionVal": {utils.NotEmpty()},
		"UpdateUrl":  {utils.NotEmpty()},
		"Status":     {utils.NotEmpty()},
		"MustUpdate": {utils.NotEmpty()},
	}
	if err := utils.Verify(arq, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	now := time.Now()
	ar := apprelease.AppRelease{
		GVA_MODEL: global.GVA_MODEL{
			CreatedAt: now,
			UpdatedAt: now,
		},
		OsType:     arq.OsType,
		VersionVal: arq.VersionVal,
		UpdateUrl:  arq.UpdateUrl,
		Desc:       string(arq.Desc),
		MustUpdate: arq.MustUpdate,
		CreatedBy:  arq.CreatedBy,
	}
	if arq.Status == "" {
		ar.Status = "offline"
	} else {
		ar.Status = arq.Status
	}

	if err := arService.CreateAppRelease(&ar); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteAppRelease 删除AppRelease
// @Tags AppRelease
// @Summary 删除AppRelease
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body appreleaseReq.AppReleaseDelete true "删除AppRelease"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /ar/deleteAppRelease [delete]
func (arApi *AppReleaseApi) DeleteAppRelease(c *gin.Context) {
	var ar appreleaseReq.AppReleaseDelete
	err := c.ShouldBindJSON(&ar)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	ar.DeletedBy = utils.GetUserID(c)
	if err := arService.DeleteAppRelease(ar); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteAppReleaseByIds 批量删除AppRelease
// @Tags AppRelease
// @Summary 批量删除AppRelease
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AppRelease"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /ar/deleteAppReleaseByIds [delete]
func (arApi *AppReleaseApi) DeleteAppReleaseByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	deletedBy := utils.GetUserID(c)
	if err := arService.DeleteAppReleaseByIds(IDS, deletedBy); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateAppRelease 更新AppRelease
// @Tags AppRelease
// @Summary 更新AppRelease
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body appreleaseReq.AppReleaseUpdate true "更新AppRelease"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /ar/updateAppRelease [put]
func (arApi *AppReleaseApi) UpdateAppRelease(c *gin.Context) {
	var arq apprelease.AppRelease
	err := c.ShouldBindJSON(&arq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	arq.UpdatedBy = utils.GetUserID(c)
	verify := utils.Rules{
		"OsType":     {utils.NotEmpty()},
		"VersionVal": {utils.NotEmpty()},
		"UpdateUrl":  {utils.NotEmpty()},
		"Status":     {utils.NotEmpty()},
	}
	if err := utils.Verify(arq, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if arq.Status == "" {
		arq.Status = "offline"
	}

	if err := arService.UpdateAppRelease(arq); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindAppRelease 用id查询AppRelease
// @Tags AppRelease
// @Summary 用id查询AppRelease
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query apprelease.AppRelease true "用id查询AppRelease"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /ar/findAppRelease [get]
func (arApi *AppReleaseApi) FindAppRelease(c *gin.Context) {
	var ar apprelease.AppRelease
	err := c.ShouldBindQuery(&ar)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rear, err := arService.GetAppRelease(ar.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rear": rear}, c)
	}
}

// GetAppReleaseList 分页获取AppRelease列表
// @Tags AppRelease
// @Summary 分页获取AppRelease列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appreleaseReq.AppReleaseSearch true "分页获取AppRelease列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ar/getAppReleaseList [get]
func (arApi *AppReleaseApi) GetAppReleaseList(c *gin.Context) {
	var pageInfo appreleaseReq.AppReleaseSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := arService.GetAppReleaseInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetAppLatestRelease app 获取最新发版
// @Tags AppRelease
// @Summary 获取最新发版
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appreleaseReq.AppLarstRelease true "获取最新发版"
// @Success 200 {object} appreleaseResp.AppReleaseLastest "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /app/ar/getAppLatestRelease [get]
func (arApi *AppReleaseApi) GetAppLatestRelease(c *gin.Context) {
	var req appreleaseReq.AppLarstRelease
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"OsType":     {utils.NotEmpty()},
		"VersionVal": {utils.NotEmpty()},
	}

	if err := utils.Verify(req, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var list appreleaseResp.AppReleaseLastest
	if list, err = arService.GetAppLatestRelease(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}

// GetAppLatestReleaseSimple app 获取最新发版简洁接口
// @Tags AppRelease
// @Summary 获取最新发版简洁接口
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query appreleaseReq.AppLarstRelease true "获取最新发版"
// @Success 200 {object} appreleaseResp.AppReleaseLastest "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /app/ar/getAppLatestReleaseSimple [get]
func (arApi *AppReleaseApi) GetAppLatestReleaseSimple(c *gin.Context) {
	var req appreleaseReq.AppLarstRelease
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"OsType": {utils.NotEmpty()},
	}
	if err := utils.Verify(req, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var list appreleaseResp.AppReleaseLastest
	if list, err = arService.GetAppLatestReleaseSimple(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}

// UpdateAppReleaseStatus 更新AppRelease状态
// @Tags AppRelease
// @Summary 更新AppRelease状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body appreleaseReq.AppReleaseUpdateStatusReq true "更新AppRelease状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /ar/updateAppReleaseStatus [put]
func (arApi *AppReleaseApi) UpdateAppReleaseStatus(c *gin.Context) {
	var arq appreleaseReq.AppReleaseUpdateStatusReq
	err := c.ShouldBindJSON(&arq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"ID":     {utils.NotEmpty()},
		"Status": {utils.NotEmpty()},
	}

	if err := utils.Verify(arq, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	arq.UpdatedBy = utils.GetUserID(c)

	if err := arService.UpdateAppReleaseStatus(arq.ID, arq.Status, arq.UpdatedBy); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	}
	response.OkWithMessage("更新成功", c)
}
