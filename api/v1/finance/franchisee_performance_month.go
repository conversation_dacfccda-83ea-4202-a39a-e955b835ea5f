package finance

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/finance"
	financeReq "github.com/OSQianXing/guanpu-server/model/finance/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseePerformanceMonthApi struct {
}

var franchiseePerformanceMonthService = service.ServiceGroupApp.FinanceServiceGroup.FranchiseePerformanceMonthService

// CreateFranchiseePerformanceMonth 创建FranchiseePerformanceMonth
// @Tags FranchiseePerformanceMonth
// @Summary 创建FranchiseePerformanceMonth
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.FranchiseePerformanceMonth true "创建FranchiseePerformanceMonth"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /franchiseePerformanceMonth/createFranchiseePerformanceMonth [post]
func (franchiseePerformanceMonthApi *FranchiseePerformanceMonthApi) CreateFranchiseePerformanceMonth(c *gin.Context) {
	var franchiseePerformanceMonth finance.FranchiseePerformanceMonth
	err := c.ShouldBindJSON(&franchiseePerformanceMonth)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"FranchiseeId":  {utils.NotEmpty()},
		"Performance":   {utils.NotEmpty()},
		"SpecialMallId": {utils.NotEmpty()},
		"YearMonth":     {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseePerformanceMonth, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceMonthService.CreateFranchiseePerformanceMonth(&franchiseePerformanceMonth); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteFranchiseePerformanceMonth 删除FranchiseePerformanceMonth
// @Tags FranchiseePerformanceMonth
// @Summary 删除FranchiseePerformanceMonth
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.FranchiseePerformanceMonth true "删除FranchiseePerformanceMonth"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /franchiseePerformanceMonth/deleteFranchiseePerformanceMonth [delete]
func (franchiseePerformanceMonthApi *FranchiseePerformanceMonthApi) DeleteFranchiseePerformanceMonth(c *gin.Context) {
	var franchiseePerformanceMonth finance.FranchiseePerformanceMonth
	err := c.ShouldBindJSON(&franchiseePerformanceMonth)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceMonthService.DeleteFranchiseePerformanceMonth(franchiseePerformanceMonth); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteFranchiseePerformanceMonthByIds 批量删除FranchiseePerformanceMonth
// @Tags FranchiseePerformanceMonth
// @Summary 批量删除FranchiseePerformanceMonth
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除FranchiseePerformanceMonth"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /franchiseePerformanceMonth/deleteFranchiseePerformanceMonthByIds [delete]
func (franchiseePerformanceMonthApi *FranchiseePerformanceMonthApi) DeleteFranchiseePerformanceMonthByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceMonthService.DeleteFranchiseePerformanceMonthByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateFranchiseePerformanceMonth 更新FranchiseePerformanceMonth
// @Tags FranchiseePerformanceMonth
// @Summary 更新FranchiseePerformanceMonth
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.FranchiseePerformanceMonth true "更新FranchiseePerformanceMonth"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchiseePerformanceMonth/updateFranchiseePerformanceMonth [put]
func (franchiseePerformanceMonthApi *FranchiseePerformanceMonthApi) UpdateFranchiseePerformanceMonth(c *gin.Context) {
	var franchiseePerformanceMonth finance.FranchiseePerformanceMonth
	err := c.ShouldBindJSON(&franchiseePerformanceMonth)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"FranchiseeId":  {utils.NotEmpty()},
		"Performance":   {utils.NotEmpty()},
		"SpecialMallId": {utils.NotEmpty()},
		"YearMonth":     {utils.NotEmpty()},
	}
	if err := utils.Verify(franchiseePerformanceMonth, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceMonthService.UpdateFranchiseePerformanceMonth(franchiseePerformanceMonth); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindFranchiseePerformanceMonth 用id查询FranchiseePerformanceMonth
// @Tags FranchiseePerformanceMonth
// @Summary 用id查询FranchiseePerformanceMonth
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query finance.FranchiseePerformanceMonth true "用id查询FranchiseePerformanceMonth"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /franchiseePerformanceMonth/findFranchiseePerformanceMonth [get]
func (franchiseePerformanceMonthApi *FranchiseePerformanceMonthApi) FindFranchiseePerformanceMonth(c *gin.Context) {
	var franchiseePerformanceMonth finance.FranchiseePerformanceMonth
	err := c.ShouldBindQuery(&franchiseePerformanceMonth)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchiseePerformanceMonth, err := franchiseePerformanceMonthService.GetFranchiseePerformanceMonth(franchiseePerformanceMonth.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchiseePerformanceMonth": refranchiseePerformanceMonth}, c)
	}
}

// GetFranchiseePerformanceMonthList 分页获取FranchiseePerformanceMonth列表
// @Tags FranchiseePerformanceMonth
// @Summary 分页获取FranchiseePerformanceMonth列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query financeReq.FranchiseePerformanceMonthSearch true "分页获取FranchiseePerformanceMonth列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /franchiseePerformanceMonth/getFranchiseePerformanceMonthList [get]
func (franchiseePerformanceMonthApi *FranchiseePerformanceMonthApi) GetFranchiseePerformanceMonthList(c *gin.Context) {
	var pageInfo financeReq.FranchiseePerformanceMonthSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := franchiseePerformanceMonthService.GetFranchiseePerformanceMonthInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
