package finance

import (
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/finance"
	financeReq "github.com/OSQianXing/guanpu-server/model/finance/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type GeneratedInvestmentPerformanceFranchiseeConfigApi struct {
}

var generatedInvestmentPerformanceFranchiseeConfigService = service.ServiceGroupApp.FinanceServiceGroup.GeneratedInvestmentPerformanceFranchiseeConfigService

// CreateGeneratedInvestmentPerformanceFranchiseeConfig 创建GeneratedInvestmentPerformanceFranchiseeConfig
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 创建GeneratedInvestmentPerformanceFranchiseeConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GeneratedInvestmentPerformanceFranchiseeConfigCreate true "创建GeneratedInvestmentPerformanceFranchiseeConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /investPerformConfig/createGeneratedInvestmentPerformanceFranchiseeConfig [post]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) CreateGeneratedInvestmentPerformanceFranchiseeConfig(c *gin.Context) {
	var createInfo financeReq.GeneratedInvestmentPerformanceFranchiseeConfigCreate
	err := c.ShouldBindJSON(&createInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"FranchiseeId": {utils.NotEmpty()},
	}
	if err := utils.Verify(createInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	config := &finance.GeneratedInvestmentPerformanceFranchiseeConfig{
		FranchiseeId: createInfo.FranchiseeId,
	}
	if err := generatedInvestmentPerformanceFranchiseeConfigService.CreateGeneratedInvestmentPerformanceFranchiseeConfig(config); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteGeneratedInvestmentPerformanceFranchiseeConfig 删除GeneratedInvestmentPerformanceFranchiseeConfig
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 删除GeneratedInvestmentPerformanceFranchiseeConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body financeReq.GeneratedInvestmentPerformanceFranchiseeConfigDelete true "删除GeneratedInvestmentPerformanceFranchiseeConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /investPerformConfig/deleteGeneratedInvestmentPerformanceFranchiseeConfig [delete]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) DeleteGeneratedInvestmentPerformanceFranchiseeConfig(c *gin.Context) {
	var req financeReq.GeneratedInvestmentPerformanceFranchiseeConfigDelete
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}
	if err := generatedInvestmentPerformanceFranchiseeConfigService.DeleteGeneratedInvestmentPerformanceFranchiseeConfig(req); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteGeneratedInvestmentPerformanceFranchiseeConfigByIds 批量删除GeneratedInvestmentPerformanceFranchiseeConfig
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 批量删除GeneratedInvestmentPerformanceFranchiseeConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除GeneratedInvestmentPerformanceFranchiseeConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /investPerformConfig/deleteGeneratedInvestmentPerformanceFranchiseeConfigByIds [delete]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) DeleteGeneratedInvestmentPerformanceFranchiseeConfigByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage("批量删除失败: "+err.Error(), c)
		return
	}
	if err := generatedInvestmentPerformanceFranchiseeConfigService.DeleteGeneratedInvestmentPerformanceFranchiseeConfigByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateGeneratedInvestmentPerformanceFranchiseeConfig 更新GeneratedInvestmentPerformanceFranchiseeConfig
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 更新GeneratedInvestmentPerformanceFranchiseeConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.GeneratedInvestmentPerformanceFranchiseeConfig true "更新GeneratedInvestmentPerformanceFranchiseeConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /investPerformConfig/updateGeneratedInvestmentPerformanceFranchiseeConfig [put]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) UpdateGeneratedInvestmentPerformanceFranchiseeConfig(c *gin.Context) {
	var generatedInvestmentPerformanceFranchiseeConfig finance.GeneratedInvestmentPerformanceFranchiseeConfig
	err := c.ShouldBindJSON(&generatedInvestmentPerformanceFranchiseeConfig)
	if err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}
	verify := utils.Rules{
		"ID": {utils.NotEmpty()},
	}
	if err := utils.Verify(generatedInvestmentPerformanceFranchiseeConfig, verify); err != nil {
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}
	if err := generatedInvestmentPerformanceFranchiseeConfigService.UpdateGeneratedInvestmentPerformanceFranchiseeConfig(generatedInvestmentPerformanceFranchiseeConfig); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindGeneratedInvestmentPerformanceFranchiseeConfig 用id查询GeneratedInvestmentPerformanceFranchiseeConfig
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 用id查询GeneratedInvestmentPerformanceFranchiseeConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query finance.GeneratedInvestmentPerformanceFranchiseeConfig true "用id查询GeneratedInvestmentPerformanceFranchiseeConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /investPerformConfig/findGeneratedInvestmentPerformanceFranchiseeConfig [get]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) FindGeneratedInvestmentPerformanceFranchiseeConfig(c *gin.Context) {
	var generatedInvestmentPerformanceFranchiseeConfig finance.GeneratedInvestmentPerformanceFranchiseeConfig
	err := c.ShouldBindQuery(&generatedInvestmentPerformanceFranchiseeConfig)
	if err != nil {
		response.FailWithMessage("查询失败: "+err.Error(), c)
		return
	}
	if regeneratedInvestmentPerformanceFranchiseeConfig, err := generatedInvestmentPerformanceFranchiseeConfigService.GetGeneratedInvestmentPerformanceFranchiseeConfig(generatedInvestmentPerformanceFranchiseeConfig.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败: "+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"regeneratedInvestmentPerformanceFranchiseeConfig": regeneratedInvestmentPerformanceFranchiseeConfig}, c)
	}
}

// GetGeneratedInvestmentPerformanceFranchiseeConfigList 分页获取GeneratedInvestmentPerformanceFranchiseeConfig列表
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 分页获取GeneratedInvestmentPerformanceFranchiseeConfig列表
// @Description 分页获取列表, page默认为1, pageSize默认为10且最大为100
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query financeReq.GeneratedInvestmentPerformanceFranchiseeConfigSearch true "分页获取GeneratedInvestmentPerformanceFranchiseeConfig列表"
// @Success 200 {object} response.Response{data=response.PageResult{list=[]finance.GeneratedInvestmentPerformanceFranchiseeConfig}} "获取成功"
// @Router /investPerformConfig/getGeneratedInvestmentPerformanceFranchiseeConfigList [get]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) GetGeneratedInvestmentPerformanceFranchiseeConfigList(c *gin.Context) {
	var pageInfo financeReq.GeneratedInvestmentPerformanceFranchiseeConfigSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}
	if list, total, err := generatedInvestmentPerformanceFranchiseeConfigService.GetGeneratedInvestmentPerformanceFranchiseeConfigInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CheckSecondaryInvestmentPerformanceAccess 检查加盟商是否具有二级投资业绩查看权限
// @Tags GeneratedInvestmentPerformanceFranchiseeConfig
// @Summary 检查加盟商是否具有二级投资业绩查看权限
// @Description 检查当前登录的加盟商是否具有查看二级投资业绩的权限
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=bool,msg=string} "获取成功"
// @Router /app/investPerformConfig/checkSecondaryInvestmentPerformanceAccess [get]
func (generatedInvestmentPerformanceFranchiseeConfigApi *GeneratedInvestmentPerformanceFranchiseeConfigApi) CheckSecondaryInvestmentPerformanceAccess(c *gin.Context) {
	userID := utils.GetUserID(c)
	if userID == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	franchisee, err := dao.Franchisee.GetFranchiseeByUserID(userID)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商信息失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商信息失败", c)
		return
	}

	hasAccess, err := generatedInvestmentPerformanceFranchiseeConfigService.CheckSecondaryInvestmentPerformanceAccess(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("获取加盟商二级招商业绩查看权限失败!", zap.Error(err))
		response.FailWithMessage("获取加盟商二级招商业绩查看权限失败", c)
		return
	}

	response.OkWithData(hasAccess, c)
}
