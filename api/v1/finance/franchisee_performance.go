package finance

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/finance"
	financeReq "github.com/OSQianXing/guanpu-server/model/finance/request"
	financeRes "github.com/OSQianXing/guanpu-server/model/finance/response"
	"github.com/OSQianXing/guanpu-server/service"
	financeService "github.com/OSQianXing/guanpu-server/service/finance"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FranchiseePerformanceApi struct {
}

var franchiseePerformanceService = service.ServiceGroupApp.FinanceServiceGroup.FranchiseePerformanceService

// CreateFranchiseePerformance 创建FranchiseePerformance
// @Tags FranchiseePerformance
// @Summary 创建FranchiseePerformance
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.FranchiseePerformance true "创建FranchiseePerformance"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /franchiseePerformance/createFranchiseePerformance [post]
func (franchiseePerformanceApi *FranchiseePerformanceApi) CreateFranchiseePerformance(c *gin.Context) {
	var franchiseePerformance finance.FranchiseePerformance
	err := c.ShouldBindJSON(&franchiseePerformance)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceService.CreateFranchiseePerformance(&franchiseePerformance); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteFranchiseePerformance 删除FranchiseePerformance
// @Tags FranchiseePerformance
// @Summary 删除FranchiseePerformance
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.FranchiseePerformance true "删除FranchiseePerformance"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /franchiseePerformance/deleteFranchiseePerformance [delete]
func (franchiseePerformanceApi *FranchiseePerformanceApi) DeleteFranchiseePerformance(c *gin.Context) {
	var franchiseePerformance finance.FranchiseePerformance
	err := c.ShouldBindJSON(&franchiseePerformance)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceService.DeleteFranchiseePerformance(franchiseePerformance); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteFranchiseePerformanceByIds 批量删除FranchiseePerformance
// @Tags FranchiseePerformance
// @Summary 批量删除FranchiseePerformance
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除FranchiseePerformance"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /franchiseePerformance/deleteFranchiseePerformanceByIds [delete]
func (franchiseePerformanceApi *FranchiseePerformanceApi) DeleteFranchiseePerformanceByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceService.DeleteFranchiseePerformanceByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateFranchiseePerformance 更新FranchiseePerformance
// @Tags FranchiseePerformance
// @Summary 更新FranchiseePerformance
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body finance.FranchiseePerformance true "更新FranchiseePerformance"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /franchiseePerformance/updateFranchiseePerformance [put]
func (franchiseePerformanceApi *FranchiseePerformanceApi) UpdateFranchiseePerformance(c *gin.Context) {
	var franchiseePerformance finance.FranchiseePerformance
	err := c.ShouldBindJSON(&franchiseePerformance)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := franchiseePerformanceService.UpdateFranchiseePerformance(franchiseePerformance); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindFranchiseePerformance 用id查询FranchiseePerformance
// @Tags FranchiseePerformance
// @Summary 用id查询FranchiseePerformance
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query finance.FranchiseePerformance true "用id查询FranchiseePerformance"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /franchiseePerformance/findFranchiseePerformance [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) FindFranchiseePerformance(c *gin.Context) {
	var franchiseePerformance finance.FranchiseePerformance
	err := c.ShouldBindQuery(&franchiseePerformance)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if refranchiseePerformance, err := franchiseePerformanceService.GetFranchiseePerformance(franchiseePerformance.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"refranchiseePerformance": refranchiseePerformance}, c)
	}
}

// GetFranchiseePerformanceList 分页获取FranchiseePerformance列表
// @Tags FranchiseePerformance
// @Summary 分页获取FranchiseePerformance列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body financeReq.FranchiseePerformanceSearch true "分页获取FranchiseePerformance列表"
// @Success 200 {object} []response.FranchiseePerformanceList "data.list"
// @Router /franchiseePerformance/getFranchiseePerformanceList [post]
func (franchiseePerformanceApi *FranchiseePerformanceApi) GetFranchiseePerformanceList(c *gin.Context) {
	var pageInfo financeReq.FranchiseePerformanceSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	authID := utils.GetUserAuthorityId(c)
	if (authID == 222 || authID == 333) && pageInfo.UserId == nil {
		pageInfo.UserId = &userID
		if pageInfo.Type != nil {
			pageInfo.Type = nil
		}
	}

	if list, total, err := franchiseePerformanceService.GetFranchiseePerformanceInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportFranchiseePerformance 导出FranchiseePerformance
// @Tags FranchiseePerformance
// @Summary 分页获取FranchiseePerformance列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body financeReq.FranchiseePerformanceSearch true "导出FranchiseePerformance"
// @Success 200
// @Router /franchiseePerformance/exportFranchiseePerformance [post]
func (franchiseePerformanceApi *FranchiseePerformanceApi) ExportFranchiseePerformance(c *gin.Context) {
	var pageInfo financeReq.FranchiseePerformanceSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if f, err := franchiseePerformanceService.ExportFranchiseePerformance(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		// 设置响应头
		c.Header("Content-Disposition", "attachment; filename=加盟商业绩详情.xlsx")
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		c.Header("Content-Transfer-Encoding", "binary")

		// 写入响应
		if err := f.Write(c.Writer); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}
}

// SummaryPerformanceForFranchiseeTeam 市场经理,督导查看加盟商业绩汇总
// @Tags FranchiseePerformance
// @Summary 市场经理,督导查看加盟商业绩汇总
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body financeReq.FranchiseePerformanceSearch true "加盟商业绩汇总"
// @Success 200 {object} response.Response{data=financeRes.SpecialMallPerformance,msg=string}
// @Router /franchiseePerformance/summaryPerformanceForFranchiseeTeam [post]
func (franchiseePerformanceApi *FranchiseePerformanceApi) SummaryPerformanceForFranchiseeTeam(c *gin.Context) {
	var req financeReq.FranchiseePerformanceSearch
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var _ financeRes.SpecialMallPerformance
	f, err := franchiseePerformanceService.SummaryPerformanceForFranchiseeTeam(c, req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"summaryPerformance": f}, c)
	}
}

// ChartSummaryPerformanceForFranchisee 加盟商每天的折线图业绩 [所有加盟商每天汇总图,加盟商按分类汇总图]
// @Tags FranchiseePerformance
// @Summary 每个加盟商每天的折线图业绩
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body financeReq.FranchiseePerformanceSearch true "每个加盟商每天的折线图业绩"
// @Success 200 {object} response.Response{data=financeRes.ChartSummaryPerformance,msg=string}
// @Router /franchiseePerformance/chartSummaryPerformanceForFranchisee [post]
func (franchiseePerformanceApi *FranchiseePerformanceApi) ChartSummaryPerformanceForFranchisee(c *gin.Context) {
	var req financeReq.FranchiseePerformanceSearch
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	f, err := franchiseePerformanceService.ChartSummaryPerformanceForFranchisee(c, req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"summaryPerformance": f}, c)
	}
}

// PerformanceJob 加盟商业绩任务
// @Tags FranchiseePerformance
// @Summary 加盟商业绩任务
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param date query string true "加盟商业绩任务格式 yyyy-mm-dd 如 2023-10-30 就会刷出 2023-10-01 至 2023-10-30 的业绩"
// @Success 200
// @Router /franchiseePerformance/performanceJob [get]
func (franchiseePerformanceApi *FranchiseePerformanceApi) PerformanceJob(c *gin.Context) {
	t := c.Query("date")
	targetTime, err := time.Parse("2006-01-02", t)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	begin, end := utils.GetMonthlyStartAndEndDateByTargetTime(targetTime)
	if end.After(targetTime) {
		end = targetTime
	}
	utils.Go(c, func(c context.Context) {
		for !begin.After(end) {
			financeService.PerformanceJob.FranchiseePerformance(begin)
			begin = begin.AddDate(0, 0, 1)
		}
	})
	response.OkWithMessage(fmt.Sprintf("任务已经开始执行 执行业绩周期 %s 至 %s", begin.Format(`2006-01-02`), end.Format(`2006-01-02`)), c)
}
