// 自动生成模板Franchisee
package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/system"
)

const (
	DefaultInviterID = 0 // 自主注册，无邀请
)

// Franchisee 结构体
type Franchisee struct {
	global.GVA_MODEL
	TenantID    uint   `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"` // 租户ID
	UserId      uint   `json:"userId" form:"userId" gorm:"column:user_id;comment:userID;size:10;"`                    // 用户ID
	Name        string `json:"name" form:"name" gorm:"column:name;comment:加盟商名称;size:56;"`                            // 加盟商名称
	Code        string `json:"code" form:"code" gorm:"column:code;comment:加盟商编码;size:56;"`                            // 加盟商编码
	Province    string `json:"province" form:"province" gorm:"column:province;comment:省份;size:16;"`                   // 省份
	City        string `json:"city" form:"city" gorm:"column:city;comment:市;size:16;"`                                // 市
	County      string `json:"county" form:"county" gorm:"column:county;comment:区/县;size:16;"`                        // 区/县
	Area        string `json:"area" form:"area" gorm:"column:area;comment:片区;size:128;"`                              // 片区
	Address     string `json:"address" form:"address" gorm:"column:address;comment:详细地址;size:128;"`                   // 详细地址
	FCategoryId *int   `json:"fCategoryId" form:"fCategoryId" gorm:"column:f_category_id;comment:加盟商分类ID;size:10;"`   // 加盟商分类ID
	Tel         string `json:"tel" form:"tel" gorm:"column:tel;comment:加盟商联系电话;size:11;"`                             // 加盟商联系电话
	Linkman     string `json:"linkman" form:"linkman" gorm:"column:linkman;comment:加盟商联系人;size:16;"`                  // 加盟商联系人
	Remark      string `json:"remark" form:"remark" gorm:"column:remark;comment:备注;"`                                 // 备注
	Balance     int    `json:"balance" form:"balance" gorm:"column:balance;comment:余额;size:10;"`                      // 余额
	Points      int    `json:"points" form:"points" gorm:"column:points;comment:积分;size:10;"`
	InviterID   int    `json:"inviterID" form:"inviterID" gorm:"column:inviter_id;comment:邀请人ID;size:10;"` // 邀请人ID（加盟商ID）

	MarketLeadID      int             `json:"marketLeadID" form:"marketLeadID" gorm:"-"`           // 市场经理ID
	SupervisionLeadID int             `json:"supervisionLeadID" form:"supervisionLeadID" gorm:"-"` // 督导经理ID
	MarketLead        *system.SysUser `json:"marketLead" form:"marketLead" gorm:"-"`               // 市场经理
	SupervisionLead   *system.SysUser `json:"supervisionLead" form:"supervisionLead" gorm:"-"`     // 督导经理
	//FCategory         *FranchiseeCategory `json:"fCategory" form:"fCategory" gorm:"ForeignKey:FCategoryId"` // 加盟商分类
	IsOldFranchisee bool `json:"isOldFranchisee" form:"isOldFranchisee" gorm:"column:is_old_franchisee"` // 是否老加盟商
}

type FranchiseeWithSysUser struct {
	Franchisee
	SysUser system.SysUser `json:"sysUser" form:"sysUser" gorm:"foreignKey:UserId"`
}

// TableName Franchisee 表名
func (Franchisee) TableName() string {
	return "franchisee"
}
