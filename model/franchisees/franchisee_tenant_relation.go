package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
)

// FranchiseeTenantRelation 加盟商租户关联表
// 管理加盟商与租户的多对多关系
type FranchiseeTenantRelation struct {
	global.GVA_MODEL
	FranchiseeID uint   `json:"franchiseeId" form:"franchiseeId" gorm:"column:franchisee_id;not null;index;comment:加盟商ID;size:10;"` // 加盟商ID
	TenantID     uint   `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"`             // 租户ID
	Status       string `json:"status" form:"status" gorm:"column:status;default:active;comment:状态：active/inactive;size:20;"`     // 状态：active/inactive
	Role         string `json:"role" form:"role" gorm:"column:role;comment:在该租户中的角色;size:50;"`                                    // 在该租户中的角色
	IsDefault    bool   `json:"isDefault" form:"isDefault" gorm:"column:is_default;default:false;comment:是否为默认租户;"`                // 是否为默认租户
	Remark       string `json:"remark" form:"remark" gorm:"column:remark;comment:备注;size:255;"`                                   // 备注
}

// TableName FranchiseeTenantRelation 表名
func (FranchiseeTenantRelation) TableName() string {
	return "franchisee_tenant_relation"
}

// 常量定义
const (
	// 状态常量
	StatusActive   = "active"   // 激活状态
	StatusInactive = "inactive" // 非激活状态
	
	// 角色常量
	RoleFranchisee = "franchisee" // 普通加盟商
	RoleManager    = "manager"    // 管理员加盟商
	RoleOwner      = "owner"      // 所有者
)
