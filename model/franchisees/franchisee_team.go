package franchisees

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/types"
)

// FranchiseeTeam 结构体
type FranchiseeTeam struct {
	global.GVA_MODEL
	TenantID     uint           `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"` // 租户ID
	FranchiseeId uint           `json:"franchiseeId" form:"franchiseeId" gorm:"column:franchisee_id;comment:加盟商ID;size:10;"`   // 加盟商ID
	UserId       uint           `json:"userId" form:"userId" gorm:"column:user_id;comment:用户ID;size:10;"`                      // 经理用户ID
	Type         types.TeamType `json:"type" form:"type" gorm:"column:type;comment:经理类型（0-督导 1-市场）;"`                          // 经理类型（0-督导 1-市场）
}

// TableName FranchiseeAccount 表名
func (FranchiseeTeam) TableName() string {
	return "franchisee_team"
}
