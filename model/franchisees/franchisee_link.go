package franchisees

import "github.com/OSQianXing/guanpu-server/global"

/**
 design for FranchiseeLink:
1. f, i , T => Franchisee, Inviter, TopFranchisee/or Franchisee with no inviter within the same chain
  T 可以不是一级分销加盟商，但是 T 是链条中最顶层的加盟商。与业务权限无关。
2. if i is 0 , T is f
如果 inviter is 0，那么 T 就是自己
3 if f is Top Franchisee, T is f , i can be anyone
如果 f 是一级分销加盟商，那么 T 就是 f，i 可以是任何人

 updater:
1. for f,  if update i , update T at same time , T is T of i; and update all T of f[s] with inviter f but except when f is Top Franchisee
	如果更新 f 的 inviter ，那么同时更新TopFranchisee(更新为 inviter 的 TopFranchisee),循环更新 inviter 为 f 的下级，除非遇到 TopFranchisee
2. for f, if update T , update all T of f with inviter f
	如果更新 f 的 TopFranchisee那么同时更新所有 inviter 为 f 的 TopFranchisee

*/

type FranchiseeLink struct {
	global.GVA_MODEL
	TenantID        uint `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"`      // 租户ID
	TopFranchiseeID uint `json:"topFranchiseeID" form:"topFranchiseeID" gorm:"column:top_franchisee_id; comment:一级分销加盟商ID;"` // 一级分销加盟商ID，分销中的具有客户管理权限的加盟商
	InviterID       uint `json:"inviterID" form:"inviterID" gorm:"column:inviter_id;comment:邀请人ID;size:10"`
	FranchiseeID    uint `json:"franchiseeID" form:"franchiseeID" gorm:"column:franchisee_id;comment:加盟商ID"` // 一级分销加盟商下的加盟商
}

func (franchiseeLink FranchiseeLink) TableName() string {
	return "franchisee_link"
}
