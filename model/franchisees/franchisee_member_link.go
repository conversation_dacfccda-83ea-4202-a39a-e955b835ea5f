package franchisees

import "github.com/OSQianXing/guanpu-server/global"

type FranchiseeMemberLink struct {
	global.GVA_MODEL
	TenantID           uint `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"` // 租户ID
	FranchiseeId       uint `json:"franchiseeId" form:"franchiseeId" gorm:"column:franchisee_id;comment:加盟商ID;size:10;"`
	MemberFranchiseeId uint `json:"memberFranchiseeId" form:"memberFranchiseeId" gorm:"column:member_franchisee_id;comment:加盟商ID;size:10;"`
	MemberInviterId    uint `json:"memberInviterId" form:"memberInviterId" gorm:"column:member_inviter_id;comment:邀请人ID;size:10;"`
}

// TableName FranchiseeAccount 表名
func (FranchiseeMemberLink) TableName() string {
	return "franchisee_member_link"
}
