// 自动生成模板FranchiseePerformance
package app

import (
	"time"

	"github.com/OSQianXing/guanpu-server/global"
)

// FranchiseePerformance 结构体
type FranchiseePerformance struct {
	global.GVA_MODEL
	TenantID           uint       `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"` // 租户ID
	FranchiseeId       *int       `json:"franchiseeId" form:"franchiseeId" gorm:"column:franchisee_id;comment:;size:10;"`
	Date               *time.Time `json:"date" form:"date" gorm:"column:date;comment:;"`
	SpecialMallId      *int       `json:"specialMallId" form:"specialMallId" gorm:"column:special_mall_id;comment:;size:10;"`
	Performance        *int       `json:"performance" form:"performance" gorm:"column:performance;comment:;size:10;"`
	HistoryPerformance *int       `json:"historyPerformance" form:"historyPerformance" gorm:"column:history_performance;comment:;size:10;"`
}

// TableName FranchiseePerformance 表名
func (FranchiseePerformance) TableName() string {
	return "franchisee_performance"
}
