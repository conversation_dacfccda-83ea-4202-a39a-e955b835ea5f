// 自动生成模板Franchisee
package app

import (
	"github.com/OSQianXing/guanpu-server/global"
)

// Franchisee 结构体
type Franchisee struct {
	global.GVA_MODEL
	TenantID        uint   `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"` // 租户ID
	UserId          *int   `json:"userId" form:"userId" gorm:"column:user_id;comment:userID;size:10;"`
	Name            string `json:"name" form:"name" gorm:"column:name;comment:加盟商名称;size:56;"`
	Code            string `json:"code" form:"code" gorm:"column:code;comment:加盟商编码;size:56;"`
	Province        string `json:"province" form:"province" gorm:"column:province;comment:省份;size:16;"`
	City            string `json:"city" form:"city" gorm:"column:city;comment:市;size:16;"`
	County          string `json:"county" form:"county" gorm:"column:county;comment:区/县;size:16;"`
	Area            string `json:"area" form:"area" gorm:"column:area;comment:片区;size:128;"`
	Address         string `json:"address" form:"address" gorm:"column:address;comment:详细地址;size:128;"`
	FCategoryId     *int   `json:"fCategoryId" form:"fCategoryId" gorm:"column:f_category_id;comment:加盟商分类ID;size:10;"`
	Tel             string `json:"tel" form:"tel" gorm:"column:tel;comment:加盟商联系电话;size:11;"`
	Linkman         string `json:"linkman" form:"linkman" gorm:"column:linkman;comment:加盟商联系人;size:16;"`
	Remark          string `json:"remark" form:"remark" gorm:"column:remark;comment:备注;size:191;"`
	Balance         *int   `json:"balance" form:"balance" gorm:"column:balance;comment:余额;size:10;"`
	Points          *int   `json:"points" form:"points" gorm:"column:points;comment:积分;size:10;"`
	IsOldFranchisee bool   `json:"isOldFranchisee" form:"isOldFranchisee" gorm:"is_old_franchisee"`
}

// TableName Franchisee 表名
func (Franchisee) TableName() string {
	return "franchisee"
}
