// 自动生成模板FranchiseeAddress
package app

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/OSQianXing/guanpu-server/global"
)

// FranchiseeAddress 结构体
type FranchiseeAddress struct {
	global.GVA_MODEL
	TenantID     uint   `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;not null;index;comment:租户ID;size:10;"` // 租户ID
	FranchiseeId int    `json:"franchiseeId" form:"franchiseeId" gorm:"column:franchisee_id;comment:加盟商ID;size:10;"`
	Consignee    string `json:"consignee" form:"consignee" gorm:"column:consignee;comment:收货人姓名;size:56;"`
	Tel          string `json:"tel" form:"tel" gorm:"column:tel;comment:;size:56;"`
	Province     string `json:"province" form:"province" gorm:"column:province;comment:省份;size:16;"`
	City         string `json:"city" form:"city" gorm:"column:city;comment:市;size:16;"`
	County       string `json:"county" form:"county" gorm:"column:county;comment:区/县;size:16;"` // 此处可能是错误的，目前表示区县，对应 china division 库的 area
	Area         string `json:"area" form:"area" gorm:"column:area;comment:片区;size:128;"`
	Address      string `json:"address" form:"address" gorm:"column:address;comment:详细地址;size:128;"`
	IsDefault    *bool  `json:"isDefault" form:"isDefault" gorm:"column:is_default;comment:是否默认地址;"` // 是否默认地址
}

// TableName FranchiseeAddress 表名
func (f *FranchiseeAddress) TableName() string {
	return "franchisee_address"
}
func (f *FranchiseeAddress) Scan(src interface{}) error {
	return json.Unmarshal(src.([]byte), f)
}
func (f *FranchiseeAddress) Value() (driver.Value, error) {
	return json.Marshal(f)
}
