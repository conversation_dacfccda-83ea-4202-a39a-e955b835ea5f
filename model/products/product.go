// 自动生成模板Product
package products

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/types"
)

// Product 结构体
type Product struct {
	global.GVA_MODEL
	TenantID      uint              `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;size:10;"` // 租户ID
	Name          string            `json:"name" form:"name" gorm:"column:name;comment:商品名称;size:255;"` // 商品名称
	Code          string            `json:"code" form:"code" gorm:"column:code;comment:商品编码;size:50;"` // 商品编码
	SpecialMallID int               `json:"specialMallId" form:"specialMallId" gorm:"column:special_mall_id;comment:专区ID;size:10;"` // 专区ID
	OnSale        bool              `json:"onSale" form:"onSale" gorm:"column:on_sale;comment:是否上架;"` // 是否上架 (0-否 1-是)
	Spec          string            `json:"spec" form:"spec" gorm:"column:spec;comment:商品规格;size:255;"` // 商品规格
	PCategoryId   *int              `json:"pCategoryId" form:"pCategoryId" gorm:"column:p_category_id;comment:商品分类ID;size:10;"` // 商品分类ID
	Unit          string            `json:"unit" form:"unit" gorm:"column:unit;comment:商品单位;size:50;"` // 商品单位
	BrandId       *int              `json:"brandId" form:"brandId" gorm:"column:brand_id;comment:商品品牌ID;size:10;"` // 商品品牌ID
	Rebate        *int              `json:"rebate" form:"rebate" gorm:"column:rebate;comment:返利积分;size:10;"` // 返利积分
	ExchangePrice *int              `json:"exchangePrice" form:"exchangePrice" gorm:"column:exchange_price;comment:积分兑换价;size:10;"` // 积分兑换价
	Cost          *int              `json:"cost" form:"cost" gorm:"column:cost;comment:成本价;size:10;"` // 成本价 (进货价)
	VipPrice      *int              `json:"vipPrice" form:"vipPrice" gorm:"column:vip_price;comment:会员价;size:10;"` // 会员价 (经销价)
	RetailPrice   *int              `json:"retailPrice" form:"retailPrice" gorm:"column:retail_price;comment:零售价;size:10;"` // 零售价
	IsRecommend   *bool             `json:"isRecommend" form:"isRecommend" gorm:"column:is_recommend;comment:是否推荐;"` // 是否推荐
	IsGift        *bool             `json:"isGift" form:"isGift" gorm:"column:is_gift;comment:是否赠品;"` // 是否赠品
	IsCombination *bool             `json:"isCombination" form:"isCombination" gorm:"column:is_combination;comment:是否为组合商品 0：否  1：是;"` // 是否组合商品
	Sort          *int              `json:"sort" form:"sort" gorm:"column:sort;comment:排序;size:10;"` // 排序
	Inventory     *int              `json:"inventory" form:"inventory" gorm:"column:inventory;comment:库存;size:10;"` // 库存
	ImageAddr     string            `json:"imageAddr" form:"imageAddr" gorm:"column:image_addr;comment:商品图片;size:512;"` // 商品图片地址
	Remark        string            `json:"remark" form:"remark" gorm:"column:remark;comment:备注;"` // 备注
	Description   string            `json:"description" form:"description" gorm:"column:description;comment:商品详情描述;"` // 商品详情描述
	FCategoryIds  *types.XIntergers `json:"fCategoryIds" form:"fCategoryIds" gorm:"column:f_category_ids;comment:加盟商分类ID;size:10;"` // 允许购买的加盟商分类IDList

	CombinationDetail   []*CombinationDetail `json:"combinationDetail" form:"combinationDetail" gorm:"-"`                                // 组合商品详情
	SpecialMall         *SpecialMall         `json:"specialMall" form:"specialMall" gorm:"-"`                                            // 专区信息
	OldFranchiseeCanBuy *bool                `json:"oldFranchiseeCanBuy" form:"oldFranchiseeCanBuy" gorm:"column:old_franchisee_canbuy"` // 是否允许老加盟商购买
}

// TableName Product 表名
func (p *Product) TableName() string {
	return "product"
}
func (p *Product) IsContainsFranchiseeCategory(id int) bool {
	// 如果为空，说明次商品允许所有加盟商购买
	if p.FCategoryIds == nil || len(*p.FCategoryIds) == 0 {
		return true
	}

	for _, v := range *p.FCategoryIds {
		if v == id {
			return true
		}
	}
	return false
}

func (p *Product) IsPointProduct() bool {
	return p.ExchangePrice != nil && *p.ExchangePrice > 0
}

type CombinationDetail struct {
	ProductId   uint     `json:"productId" form:"productId" gorm:"column:product_id"`                              // 子商品 ID
	ProductName string   `json:"productName" form:"productName" gorm:"column:product_name;comment:商品名称;size:255;"` // 子商品名称
	Quantity    int      `json:"quantity" form:"quantity" gorm:"column:quantity;comment:数量;size:10;"`              // 子商品数量
	ImageAddr   string   `json:"imageAddr" form:"imageAddr" gorm:"column:image_addr;comment:商品图片;size:512;"`       // 商品图片地址
	Spec        string   `json:"spec" form:"spec" gorm:"column:spec;comment:商品规格;size:255;"`                       // 商品规格
	Product     *Product `json:"productInfo"  gorm:"foreignKey:ProductId;references:ID"`                           // 子商品信息
}
