package response

import (
	"github.com/OSQianXing/guanpu-server/model/account"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/products"
)

type ExclusiveAccount struct {
	account.ExclusiveAccount
	FranchiseesCategories []franchisees.FranchiseeCategory `json:"franchiseesCategories"` // 加盟商分类
	SpecialMalls          []products.SpecialMall           `json:"specialMalls"`          // 已绑定专区
}
