package request

import (
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"time"
)

type ExclusiveAccountSearch struct {
	Name           *string    `json:"name" form:"name" `                  // 专属账户名称
	IsBanned       *bool      `json:"isBanned" form:"isBanned"`           // 是否停用
	SpecialMallID  *int       `json:"specialMallID" form:"specialMallID"` // 专区商城ID
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
}

type ExclusiveAccountBannedRequest struct {
	ExclusiveAccountIDs []int `json:"exclusiveAccountIDs" binding:"required"` // 专属账户 IDs
	Banned              *bool `json:"banned" binding:"required"`              // 是否停用 true: 停用 false: 启用
}
