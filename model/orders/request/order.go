package request

import (
	"fmt"
	"strings"
	"time"

	"github.com/OSQianXing/guanpu-server/model/app"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/orders/response"
	"github.com/OSQianXing/guanpu-server/model/pay"
	"github.com/OSQianXing/guanpu-server/model/products"

	bigwarehouseResp "github.com/OSQianXing/guanpu-server/model/bigwarehouse/response"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/types"
)

type OrderSearch struct {
	TenantID        uint                `json:"tenantId" form:"tenantId"`               // 租户ID
	OrderNo         string              `json:"orderNo" form:"orderNo"`                 // 订单号
	PaymentType     *types.PaymentType  `json:"paymentType" form:"paymentType"`         // 支付方式（0-线下收款  1-账户余额 2-积分余额 3-云仓提货 4-零元订单 5-加盟赠送 6-在线支付 -1 -未支付）
	Status          *types.OrderStatus  `json:"status" form:"status"`                   // 订单状态 （0-待发货 1-已发货 2-已退货 3-已退款 4-部分发货 5-已完成 7-云仓已退货 8-支付失败 9-支付成功 -1-待支付）
	OrderType       *types.OrderType    `json:"orderType" form:"orderType"`             // 订单类型（0-加盟商  1-待客下单）
	OrderSubType    *types.OrderSubType `json:"orderSubType" form:"orderSubType"`       // 订单子类型（0-普通订单 1-压货订单 2-提货订单）
	ProductCode     string              `json:"productCode" form:"productCode"`         // 商品编码
	ProductName     string              `json:"productName" form:"productName"`         // 商品名称
	FranchiseName   string              `json:"franchiseName" form:"franchiseName"`     // 加盟商名称
	FranchiseTel    string              `json:"franchiseTel" form:"franchiseTel"`       // 加盟商电话
	StartCreatedAt  *time.Time          `json:"startCreatedAt" form:"startCreatedAt"`   // 订单时间 开始
	EndCreatedAt    *time.Time          `json:"endCreatedAt" form:"endCreatedAt"`       // 订单时间 结束
	StartDeliveryAt *time.Time          `json:"startDeliveryAt" form:"startDeliveryAt"` // 发货时间 开始
	EndDeliveryAt   *time.Time          `json:"endDeliveryAt" form:"endDeliveryAt"`     // 发货时间 结束
	MinAmount       int                 `json:"minAmount" form:"minAmount"`             // 最小订单金额
	MaxAmount       int                 `json:"maxAmount" form:"maxAmount"`             // 最大订单金额
	NeedDelivery    *bool               `json:"needDelivery" form:"needDelivery"`       // 需要发货
	ShipmentType    *types.ShipmentType `json:"shipmentType" form:"shipmentType"`       // 发货方式 // 1.快递 2.自提
	ConsigneeName   *string             `json:"consigneeName" form:"consigneeName"`     // 收货人
	ConsigneeTel    *string             `json:"consigneeTel" form:"consigneeTel"`       // 收货人手机号
	BigWarehouseId  *uint               `json:"bigWarehouseId" form:"bigWarehouseId"`   // 仓库ID
	request.PageInfo
	// 地区搜索
	// {
	//    "area":[
	//        "北京市|市辖区|海淀区",
	//        "北京市|市辖区|朝阳区",
	//        "河北省",
	//        "广东省|深圳市"
	//    ]
	// }
	Area []string `json:"area" form:"area"` // 地区搜索
}

type CreateOrderRequest struct {
	TenantID      uint                   `json:"tenantId" form:"tenantId"`         // 租户ID
	FranchiseeId   uint                   `json:"franchiseeId" form:"franchiseeId"` // 加盟商ID
	PaymentType    types.PaymentType      `json:"paymentType" form:"paymentType"`   // 支付方式（0-线下收款  1-账户余额 2-积分余额 3-云仓提货 4-零元订单 5-加盟赠送 6-在线支付 -1 -未支付）
	OrderType      types.OrderType        `json:"orderType" form:"orderType"`       // 订单类型（0-加盟商  1-代客下单 2-零元订单 3-系统导入 ）
	OrderSubType   types.OrderSubType     `json:"orderSubType" form:"orderSubType"` // 订单子类型（0-普通订单 1-压货订单 2-提货订单）
	Remark         string                 `json:"remark" form:"remark"`             // 备注
	AddressId      *uint                  `json:"addressId" form:"addressId"`       // 地址ID
	Products       []Product              `json:"products" form:"products"`
	Gifts          []Product              `json:"gifts" form:"gifts"`                   // 赠品商品
	ReturnOrderNos *orders.ReturnOrderNos `json:"returnOrderNos" form:"returnOrderNos"` // 关联退货单号
	ShipmentType   types.ShipmentType     `json:"shipmentType" form:"shipmentType"`     // 发货方式ID
}

type Product struct {
	ProductId         uint    `json:"productId" form:"productId"`                 // 商品ID
	UnitPrice         int     `json:"unitPrice" form:"unitPrice"`                 // 商品单价(目前是成本价cost)(赠品无需赋值)
	Quantity          int     `json:"quantity" form:"quantity"`                   // 商品数量（赠品数量赋值此字段）
	PointsQuantity    int     `json:"pointsQuantity" form:"pointsQuantity"`       // 积分兑换数量(赠品无需赋值)
	PointsPrice       int     `json:"pointsPrice" form:"pointsPrice"`             // 积分兑换价(赠品无需赋值)
	CloudBatchOrderNo *string `json:"cloudBatchOrderNo" form:"cloudBatchOrderNo"` // 云仓批次号
}

type UpdateOrder struct {
	TenantID     uint               `json:"tenantId" form:"tenantId"`         // 租户ID
	FranchiseeId uint               `json:"franchiseeId" form:"franchiseeId"` // 加盟商ID
	OrderNo      string             `json:"orderNo" form:"orderNo"`           // 订单号
	OrderOperate types.OrderOperate `json:"orderOperate" form:"orderOperate"` // 订单操作（0-发货 1-退货 2-退款）
}

type OrderDetailRequest struct {
	OrderID uint `json:"orderId" form:"orderId" binding:"required"` // 订单ID
}

type OrderExport struct {
	OrderID              uint                                             `json:"orderId" gorm:"column:id" `                                                               // 订单ID
	OrderGoodsID         int                                              `json:"orderGoodsID" gorm:"column:order_goods_id" `                                              // ordergoods.id
	OrderNo              string                                           `json:"orderNo" gorm:"column:order_no" `                                                         // 订单号
	OrderType            *types.OrderType                                 `json:"orderType" gorm:"column:order_type" `                                                     // 订单类型（0-加盟商  1-待客下单）
	OrderSubType         *types.OrderSubType                              `json:"orderSubType" form:"orderSubType"`                                                        // 订单子类型（0-普通订单 1-压货订单 2-提货订单）
	PaymentType          *types.PaymentType                               `json:"paymentType"`                                                                             // 支付方式（0-线下收款  1-账户余额 2-积分余额 3-云仓提货 4-零元订单）
	FranchiseeName       string                                           `json:"franchiseeName" gorm:"column:franchisee_name" `                                           // 加盟商名称
	FranchiseeAccount    string                                           `json:"franchiseeAccount" gorm:"column:franchisee_account" `                                     // 加盟商账号
	ProductName          string                                           `json:"productName" gorm:"column:product_name" `                                                 // 商品名称
	SpecialMallName      string                                           `json:"special_mall_name" gorm:"column:special_mall_name"`                                       // 专区名称
	ProductID            int                                              `json:"product_id" gorm:"column:product_id"`                                                     // 商品ID
	ProductUnitPrice     int                                              `json:"productUnitPrice" gorm:"column:product_unit_price" `                                      // 商品单价
	ProductQuantity      int                                              `json:"productQuantity" gorm:"column:product_quantity" `                                         // 购买数量
	WaitingDeliveryCount int                                              `json:"waitingDeliveryCount" gorm:"column:waiting_delivery_count" `                              // 待发货数量
	Amount               int                                              `json:"amount" gorm:"column:amount" `                                                            // 订单金额
	Points               int                                              `json:"points" gorm:"column:points" `                                                            // 订单积分
	Status               *types.OrderStatus                               `json:"status" gorm:"column:status" `                                                            // 订单状态 （0-待发货 1-已发货 2-已退货 3-已退款 4-部分发货 5-已完成）
	AddressSnapshot      *app.FranchiseeAddress                           `json:"addressSnapshot" form:"addressSnapshot" gorm:"column:address_snapshot;comment:下单地址快照"`    // 下单地址快照
	Remark               string                                           `json:"remark" gorm:"column:remark" `                                                            // 订单备注
	CreatedAt            time.Time                                        `json:"createdAt" gorm:"column:created_at" `                                                     // 下单时间
	OrderRecords         []*orders.OrderRecord                            `json:"orderRecords" gorm:"foreignKey:OrderID;references:OrderID"`                               // 订单记录
	OrderRemarks         []*orders.OrderRemark                            `json:"orderRemarks" gorm:"foreignKey:OrderID;references:OrderID"`                               // 订单备注
	ProductUnitPoints    int                                              `json:"productUnitPoints" gorm:"column:product_unit_points"`                                     // 商品积分单价
	CombinationDetail    []*products.CombinationDetail                    `json:"combinationDetail" gorm:"-"`                                                              // 组合商品详情
	IsCombination        bool                                             `json:"isCombination" gorm:"column:is_combination;comment:是否为组合商品 0：否  1：是;"`                    // 是否为组合商品 0：否  1：是
	WalletPay            []*response.WalletPay                            `json:"walletPay" gorm:"-"`                                                                      // 钱包支付信息
	BigWarehouseOrder    *bigwarehouseResp.BigWarehouseOrderWithWarehouse `json:"bigWarehouseOrder" form:"bigWareHouseOrder" gorm:"foreignKey:OrderId;references:OrderID"` // 大仓订单
	OrderOnlinePay       *pay.OrderOnlinePay                              `json:"orderOnlinePay" gorm:"foreignKey:OrderNo;references:OrderNo"`                             // 在线支付
}

func (o *OrderExport) CombinationDetailString() string {
	var out string
	for _, v := range o.CombinationDetail {
		out += fmt.Sprintf("%s(%d)\n", v.ProductName, v.Quantity)
		out = strings.Trim(out, "\n")
	}
	return out
}

type WalletPayInfo struct {
	WalletPayMap map[string]*response.WalletPay
	WalletPay    []*response.WalletPay
}

type OrderAddressUpdate struct {
	OrderNo   string `json:"orderNo" form:"orderNo"`
	AddressId uint   `json:"addressId" form:"addressId"`
}

type OrderWarehouseUpdate struct {
	OrderNo        string `json:"orderNo" form:"orderNo"`               // 订单号
	OrderId        uint   `json:"orderId" form:"orderId"`               // 订单ID
	BigWarehouseId uint   `json:"bigWarehouseId" form:"bigWarehouseId"` // 仓库ID
}
