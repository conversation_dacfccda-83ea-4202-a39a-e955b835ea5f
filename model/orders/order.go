// 自动生成模板Order
package orders

import (
	"database/sql/driver"
	"encoding/json"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/app"
	bigwarehouseResp "github.com/OSQianXing/guanpu-server/model/bigwarehouse/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/pay"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/types"
)

// Order 结构体
type Order struct {
	global.GVA_MODEL
	TenantID          uint                                             `json:"tenantId" form:"tenantId" gorm:"column:tenant_id;comment:租户ID;size:10;"`                                   // 租户ID
	OrderNo           string                                           `json:"orderNo" form:"orderNo" gorm:"column:order_no;comment:订单号;size:36;"`                                       // 订单号
	FranchiseeId      int                                              `json:"franchiseeId" form:"franchiseeId" gorm:"column:franchisee_id;comment:加盟商ID;size:10;"`                      // 加盟商ID
	AddressSnapshot   *app.FranchiseeAddress                           `json:"addressSnapshot" form:"addressSnapshot" gorm:"column:address_snapshot;comment:下单地址快照;foreignKey:UserName"` // 下单地址快照
	PaymentType       types.PaymentType                                `json:"paymentType" form:"paymentType" gorm:"column:payment_type;comment:支付方式（0-线下收款  1-账户余额 2-积分余额）;"`           // 支付方式（0-线下收款  1-账户余额 2-积分余额 3-云仓提货 4-零元订单 5-加盟赠送 6-在线支付 -1-无）
	OrderType         types.OrderType                                  `json:"orderType" form:"orderType" gorm:"column:order_type;comment:订单类型（0-加盟商  1-待客下单）;"`                         // 订单类型（0-加盟商  1-待客下单）
	OrderSubType      types.OrderSubType                               `json:"orderSubType" form:"orderSubType" gorm:"column:order_sub_type;comment:订单子类型（0-普通订单 2-压货订单 3-提货订单）;"`       // 订单子类型（0-普通订单 2-压货订单 3-提货订单）
	Remark            string                                           `json:"remark" form:"remark" gorm:"column:remark;comment:备注;"`                                                    // 备注
	Amount            int                                              `json:"amount" form:"amount" gorm:"column:amount;comment:金额;size:10;"`                                            // 金额
	Points            int                                              `json:"points" form:"points" gorm:"column:points;comment:积分;size:10;"`                                            // 积分
	Status            types.OrderStatus                                `json:"status" form:"status" gorm:"column:status;comment:订单状态 （ 0-待发货 1-已发货 2-已退货 3-已退款 4-部分发货 5-部分退货）;"`         // 订单状态 （ 0-待发货 1-已发货 2-已退货 3-已退款 4-部分发货 5-已完成）
	ReturnOrderNos    *ReturnOrderNos                                  `json:"returnOrderNos" form:"returnOrderNos" gorm:"column:return_order_nos;comment:关联退货订单号;size:255;"`            // 关联退货订单号
	ShipmentType      types.ShipmentType                               `json:"shipmentType" form:"shipmentType" gorm:"column:shipment_type;comment:配送方式ID;size:10;"`                     // 配送方式ID 0 未知 1 快递 2 自提
	Franchisee        *franchisees.Franchisee                          `json:"franchisee" form:"franchisee" gorm:"foreignKey:FranchiseeId;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"`
	GoodsDetail       []*ProductDetail                                 `json:"goodsDetail" form:"goodsDetail" gorm:"-"`                                                                       // 商品详情
	OrderRemarks      []*OrderRemark                                   `json:"remarks" form:"remarks" gorm:"foreignKey:OrderID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:SET NULL;"` // 订单备注
	OrderRecords      []*OrderRecord                                   `json:"orderRecords" form:"orderRecords" gorm:"foreignKey:OrderID;references:ID;"`
	OrderDelivery     []*OrderDelivery                                 `json:"orderDelivery" form:"orderDelivery" gorm:"foreignKey:OrderNo;references:OrderNo;"`
	BigWarehouseOrder *bigwarehouseResp.BigWarehouseOrderWithWarehouse `json:"bigWarehouseOrder" form:"bigWareHouseOrder" gorm:"foreignKey:OrderId;references:ID;"`
	OrderOnlinePay    *pay.OrderOnlinePay                              `json:"orderOnlinePay,omitempty" form:"orderOnlinePay" gorm:"foreignKey:OrderNo;references:OrderNo;"`
}

type ProductDetail struct {
	Name                     string                        `json:"name" `                                               // 商品名称
	CombinationDetail        []*products.CombinationDetail `json:"combinationDetail" form:"combinationDetail" gorm:"-"` // 组合商品详情
	AwaitingDeliveryQuantity int                           `json:"awaitingDeliveryQuantity" `                           // 待发货数量
	TotalQuantity            int                           `json:"totalQuantity"`                                       // 购买的商品总量
}

// TableName Order 表名
func (Order) TableName() string {
	return "order"
}

type ReturnOrderNos []string

func (r *ReturnOrderNos) Scan(src interface{}) error {
	return json.Unmarshal(src.([]byte), r)
}

func (r *ReturnOrderNos) Value() (driver.Value, error) {
	return json.Marshal(r)
}
