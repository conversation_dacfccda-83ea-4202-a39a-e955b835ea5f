package response

import (
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/system"
)

type SysUserResponse struct {
	User system.SysUser `json:"user"`
}

type LoginResponse struct {
	User      system.SysUser `json:"user"`
	Token     string         `json:"token"`
	ExpiresAt int64          `json:"expiresAt"`
	// 新增字段用于多租户支持
	NeedTenantSelection bool                                   `json:"needTenantSelection,omitempty"` // 是否需要租户选择
	AvailableTenants    []franchisees.FranchiseeTenantRelation `json:"availableTenants,omitempty"`    // 可用租户列表
	TenantID            uint                                   `json:"tenantId,omitempty"`            // 当前租户ID
}
