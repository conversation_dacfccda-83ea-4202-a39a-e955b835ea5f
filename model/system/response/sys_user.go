package response

import (
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/system"
)

type SysUserResponse struct {
	User system.SysUser `json:"user"`
}

type LoginResponse struct {
	User      system.SysUser `json:"user"`
	Token     string         `json:"token"`
	ExpiresAt int64          `json:"expiresAt"`
}

// FranchiseeLoginResponse 加盟商登录响应
type FranchiseeLoginResponse struct {
	NeedTenantSelection bool                                   `json:"needTenantSelection"`        // 是否需要租户选择
	AvailableTenants    []franchisees.FranchiseeTenantRelation `json:"availableTenants,omitempty"` // 可用租户列表
	User                *system.SysUser                        `json:"user,omitempty"`             // 用户信息
	Token               string                                 `json:"token,omitempty"`            // JWT token
	ExpiresAt           int64                                  `json:"expiresAt,omitempty"`        // 过期时间
	TenantID            uint                                   `json:"tenantId,omitempty"`         // 当前租户ID
}
