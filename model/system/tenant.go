package system

import (
	"time"

	"github.com/OSQianXing/guanpu-server/global"
)

// Tenant 租户模型
type Tenant struct {
	global.GVA_MODEL
	Name          string `json:"name" gorm:"column:name;comment:租户名称;size:56;"` // 租户名称
	Code          string `json:"code" gorm:"column:code;comment:租户编码;size:56;unique;"` // 租户编码
	Logo          string `json:"logo" gorm:"column:logo;comment:租户logo;size:255;"` // 租户logo
	PrimaryColor  string `json:"primaryColor" gorm:"column:primary_color;comment:主色调;size:20;"` // 主色调
	SecondaryColor string `json:"secondaryColor" gorm:"column:secondary_color;comment:次色调;size:20;"` // 次色调
	AppConfig     *TenantAppConfig `json:"appConfig" gorm:"foreignKey:TenantID;references:ID;"` // 应用配置
	Status        *bool  `json:"status" gorm:"column:status;comment:状态(启用/禁用);"` // 状态
	ContactName   string `json:"contactName" gorm:"column:contact_name;comment:联系人;size:50;"` // 联系人
	ContactPhone  string `json:"contactPhone" gorm:"column:contact_phone;comment:联系电话;size:20;"` // 联系电话
	ExpireDate    *time.Time `json:"expireDate" gorm:"column:expire_date;comment:租约到期日期;"` // 租约到期
}

// TableName 表名
func (Tenant) TableName() string {
	return "tenant"
}

// TenantAppConfig 租户应用配置
type TenantAppConfig struct {
	global.GVA_MODEL
	TenantID      uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;"` // 租户ID
	AppName       string `json:"appName" gorm:"column:app_name;comment:应用名称;size:100;"` // 应用名称
	AppLogo       string `json:"appLogo" gorm:"column:app_logo;comment:应用logo;size:255;"` // 应用logo
	LoginBgImage  string `json:"loginBgImage" gorm:"column:login_bg_image;comment:登录背景图;size:255;"` // 登录背景
	HomePageConfig string `json:"homePageConfig" gorm:"column:home_page_config;comment:首页配置;type:json;"` // 首页配置
}

// TableName 表名
func (TenantAppConfig) TableName() string {
	return "tenant_app_config"
}

// UserTenantRelation 用户租户关联
type UserTenantRelation struct {
	global.GVA_MODEL
	UserID       uint   `json:"userId" gorm:"column:user_id;comment:用户ID;index:idx_user_tenant,unique;"` // 用户ID
	TenantID     uint   `json:"tenantId" gorm:"column:tenant_id;comment:租户ID;index:idx_user_tenant,unique;"` // 租户ID
	IsDefault    bool   `json:"isDefault" gorm:"column:is_default;comment:是否默认租户;"` // 默认租户
	Role         string `json:"role" gorm:"column:role;comment:用户角色;size:50;"` // 角色
	Status       *bool  `json:"status" gorm:"column:status;comment:状态;"` // 状态
	JoinTime     *time.Time `json:"joinTime" gorm:"column:join_time;comment:加入时间;"` // 加入时间
}

// TableName 表名
func (UserTenantRelation) TableName() string {
	return "user_tenant_relation"
}

// ValidateTenant 验证租户有效性（使用缓存优化）
func ValidateTenant(tenantID uint) bool {
	// 使用缓存验证，提升性能
	return global.ValidateTenantCached(tenantID)
}