package request

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/system"
)

// Register User register structure
type Register struct {
	Username     string `json:"userName" example:"用户名"`
	Password     string `json:"passWord" example:"密码"`
	NickName     string `json:"nickName" example:"昵称"`
	HeaderImg    string `json:"headerImg" example:"头像链接"`
	AuthorityId  uint   `json:"authorityId" swaggertype:"string" example:"int 角色id"`
	Enable       int    `json:"enable" swaggertype:"string" example:"int 是否启用"`
	AuthorityIds []uint `json:"authorityIds" swaggertype:"string" example:"[]uint 角色id"`
	Phone        string `json:"phone" example:"电话号码"`
	Email        string `json:"email" example:"电子邮箱"`
}

// User login structure
type Login struct {
	Username  string `json:"username"`  // 用户名
	Password  string `json:"password"`  // 密码
	Captcha   string `json:"captcha"`   // 验证码
	CaptchaId string `json:"captchaId"` // 验证码ID
}

// FranchiseeLogin 加盟商登录请求
type FranchiseeLogin struct {
	Tel        string `json:"tel" binding:"required"`      // 手机号（作为用户名）
	Password   string `json:"password" binding:"required"` // 密码
	TenantCode string `json:"tenantCode"`                  // 可选：租户代码
	Captcha    string `json:"captcha"`                     // 验证码
	CaptchaId  string `json:"captchaId"`                   // 验证码ID
}

// Modify password structure
type ChangePasswordReq struct {
	ID          uint   `json:"-"`           // 从 JWT 中提取 user id，避免越权
	Password    string `json:"password"`    // 密码
	NewPassword string `json:"newPassword"` // 新密码
}

// Modify  user's auth structure
type SetUserAuth struct {
	AuthorityId uint `json:"authorityId"` // 角色ID
}

// Modify  user's auth structure
type SetUserAuthorities struct {
	ID           uint
	AuthorityIds []uint `json:"authorityIds"` // 角色ID
}

type ChangeUserInfo struct {
	ID           uint                  `gorm:"primarykey"`                                // 主键ID
	NickName     string                `json:"nickName" gorm:"default:系统用户;comment:用户昵称"` // 用户昵称
	Phone        string                `json:"phone"  gorm:"comment:用户手机号"`               // 用户手机号
	AuthorityIds []uint                `json:"authorityIds" gorm:"-"`                     // 角色ID
	Email        string                `json:"email"  gorm:"comment:用户邮箱"`                // 用户邮箱
	HeaderImg    string                `json:"headerImg" gorm:"default:'';comment:用户头像"`  // 用户头像
	SideMode     string                `json:"sideMode"  gorm:"comment:用户侧边主题"`           // 用户侧边主题
	Enable       int                   `json:"enable" gorm:"comment:冻结用户"`                //冻结用户
	Authorities  []system.SysAuthority `json:"-" gorm:"many2many:sys_user_authority;"`
}

type UserSearch struct {
	IsMarketLead      bool    `json:"isMarketLead" form:"isMarketLead"`           // 是否只返回市场经理账户
	IsSupervisionLead bool    `json:"isSupervisionLead" form:"isSupervisionLead"` // 是否只返回督导经理账户
	IsManager         bool    `json:"isManager" form:"isManager"`                 // 是否只返回管理员
	UserName          *string `json:"userName" form:"userName"`                   // 用户名
	TenantID          uint    `json:"tenantId" form:"tenantId"`                   // 租户ID
	request.PageInfo
}

// UnifiedUserListRequest 统一用户列表请求
type UnifiedUserListRequest struct {
	request.PageInfo
	UserName     *string `json:"userName" form:"userName"`         // 用户名
	NickName     *string `json:"nickName" form:"nickName"`         // 昵称
	Phone        *string `json:"phone" form:"phone"`               // 手机号
	Email        *string `json:"email" form:"email"`               // 邮箱
	Enable       *int    `json:"enable" form:"enable"`             // 启用状态
	AuthorityId  uint    `json:"authorityId" form:"authorityId"`   // 角色ID
	TenantID     uint    `json:"tenantId" form:"tenantId"`         // 租户ID
	BusinessType *string `json:"businessType" form:"businessType"` // 业务类型
	LimitedView  bool    `json:"-"`                                // 是否限制视图（内部使用）
}

// ToUserSearch 转换为UserSearch
func (req *UnifiedUserListRequest) ToUserSearch() UserSearch {
	return UserSearch{
		UserName: req.UserName,
		TenantID: req.TenantID,
		PageInfo: req.PageInfo,
	}
}

// UnifiedUserCreateRequest 统一用户创建请求
type UnifiedUserCreateRequest struct {
	Username     string `json:"userName" binding:"required" example:"用户名"`
	Password     string `json:"passWord" binding:"required" example:"密码"`
	NickName     string `json:"nickName" example:"昵称"`
	HeaderImg    string `json:"headerImg" example:"头像链接"`
	AuthorityId  uint   `json:"authorityId" binding:"required" example:"角色id"`
	Enable       int    `json:"enable" example:"是否启用"`
	AuthorityIds []uint `json:"authorityIds" example:"角色id列表"`
	Phone        string `json:"phone" example:"电话号码"`
	Email        string `json:"email" example:"电子邮箱"`
	TenantID     uint   `json:"tenantId" example:"租户ID"`
}

// ToSysUser 转换为系统用户
func (req *UnifiedUserCreateRequest) ToSysUser() system.SysUser {
	return system.SysUser{
		Username:    req.Username,
		Password:    req.Password,
		NickName:    req.NickName,
		HeaderImg:   req.HeaderImg,
		Phone:       req.Phone,
		Email:       req.Email,
		Enable:      req.Enable,
		AuthorityId: req.AuthorityId,
	}
}

// UnifiedUserUpdateRequest 统一用户更新请求
type UnifiedUserUpdateRequest struct {
	ID           uint   `json:"id"`
	NickName     string `json:"nickName" example:"昵称"`
	HeaderImg    string `json:"headerImg" example:"头像链接"`
	Phone        string `json:"phone" example:"电话号码"`
	Email        string `json:"email" example:"电子邮箱"`
	Enable       int    `json:"enable" example:"是否启用"`
	AuthorityId  uint   `json:"authorityId" example:"角色id"`
	AuthorityIds []uint `json:"authorityIds" example:"角色id列表"`
	TenantID     uint   `json:"tenantId" example:"租户ID"`
}

// ToSysUser 转换为系统用户
func (req *UnifiedUserUpdateRequest) ToSysUser() system.SysUser {
	return system.SysUser{
		GVA_MODEL:   global.GVA_MODEL{ID: req.ID},
		NickName:    req.NickName,
		HeaderImg:   req.HeaderImg,
		Phone:       req.Phone,
		Email:       req.Email,
		Enable:      req.Enable,
		AuthorityId: req.AuthorityId,
	}
}
