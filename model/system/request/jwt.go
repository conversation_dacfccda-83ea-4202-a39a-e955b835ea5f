package request

import (
	"github.com/gofrs/uuid"
	jwt "github.com/golang-jwt/jwt/v4"
)

// Custom claims structure
type CustomClaims struct {
	BaseClaims
	BufferTime int64
	jwt.RegisteredClaims
	UserID       uint   `json:"userId"`       // 用户ID
	TenantID     uint   `json:"tenantId"`     // 当前租户ID
	FranchiseeID uint   `json:"franchiseeId"` // 当前加盟商ID
	TenantCode   string `json:"tenantCode"`   // 租户编码
}

type BaseClaims struct {
	UUID           uuid.UUID
	ID             uint
	Username       string
	NickName       string
	AuthorityId    uint
	UserType       UserType `json:"userType"`                 // 用户类型
	IsSuperAdmin   bool     `json:"isSuperAdmin"`             // 是否为超级管理员
	ManagedTenants []uint   `json:"managedTenants,omitempty"` // 管理的租户列表
}

// UserType 用户类型
type UserType int

const (
	UserTypeNormal      UserType = 0 // 普通用户
	UserTypeTenantAdmin UserType = 1 // 租户管理员
	UserTypeSystemAdmin UserType = 2 // 系统管理员
	UserTypeSuperAdmin  UserType = 3 // 超级管理员
)
