package request

// UserTenantRequest 用户租户关联请求
type UserTenantRequest struct {
	UserID   uint   `json:"userId" form:"userId" binding:"required"`   // 用户ID
	TenantID uint   `json:"tenantId" form:"tenantId" binding:"required"` // 租户ID
	Role     string `json:"role" form:"role"`                          // 角色
}

// TenantSwitchRequest 租户切换请求
type TenantSwitchRequest struct {
	TenantID uint `json:"tenantId" form:"tenantId" binding:"required"` // 租户ID
}

// UserPromoteRequest 用户提升请求
type UserPromoteRequest struct {
	UserID   uint     `json:"userId" form:"userId" binding:"required"`     // 用户ID
	UserType UserType `json:"userType" form:"userType" binding:"required"` // 用户类型
}