package system

import (
	"encoding/json"
	"time"
	
	"gorm.io/gorm"
)

// 操作类型常量
const (
	OpTypeTenantCreate = "tenant_create"
	OpTypeTenantUpdate = "tenant_update"
	OpTypeTenantDelete = "tenant_delete"
	OpTypeUserCreate   = "user_create"
	OpTypeUserUpdate   = "user_update"
	OpTypeUserDelete   = "user_delete"
	OpTypeDataQuery    = "data_query"
)

// 目标类型常量
const (
	TargetTypeTenant     = "tenant"
	TargetTypeUser       = "user"
	TargetTypeOrder      = "order"
	TargetTypeProduct    = "product"
	TargetTypeFranchisee = "franchisee"
	TargetTypeSystem     = "system"
)

// SuperAdminOperationLog 超级管理员操作日志
type SuperAdminOperationLog struct {
	gorm.Model
	OperatorID    uint            `json:"operatorID" gorm:"index;comment:操作员ID"`
	OperationType string          `json:"operationType" gorm:"comment:操作类型"`
	TargetType    string          `json:"targetType" gorm:"comment:目标类型"`
	TargetID      *uint           `json:"targetID" gorm:"comment:目标ID"`
	TenantID      *uint           `json:"tenantID" gorm:"comment:租户ID"`
	OperationDesc string          `json:"operationDesc" gorm:"comment:操作描述"`
	RequestData   json.RawMessage `json:"requestData" gorm:"type:json;comment:请求数据"`
	ResponseData  json.RawMessage `json:"responseData" gorm:"type:json;comment:响应数据"`
	IPAddress     string          `json:"ipAddress" gorm:"comment:IP地址"`
	UserAgent     string          `json:"userAgent" gorm:"comment:用户代理"`
	Status        string          `json:"status" gorm:"comment:操作状态"`
	ErrorMessage  string          `json:"errorMessage" gorm:"comment:错误信息"`
	Duration      int64           `json:"duration" gorm:"comment:操作耗时(毫秒)"`
}

// SuperAdminLog 保持向后兼容
type SuperAdminLog struct {
	gorm.Model
	UserID       uint      `json:"userID" gorm:"index"`
	OperationType string    `json:"operationType"` 
	TargetType   string    `json:"targetType"`    
	TargetID     uint      `json:"targetID"`      
	Description  string    `json:"description"`   
	IPAddress    string    `json:"ipAddress"`     
	CreatedAt    time.Time `json:"createdAt"`     
}

type SuperAdminLogStats struct {
	OperationTypeStats []struct {
		OperationType string `json:"operationType"`
		Total         int    `json:"total"`
	} `json:"operationTypeStats"`
	TopUsers []struct {
		UserID uint `json:"userID"`
		Total  int  `json:"total"`
	} `json:"topUsers"`
}