package main

import (
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseeResp "github.com/OSQianXing/guanpu-server/model/franchisees/response"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/samber/lo"

	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()

	}

	var sg = service.ServiceGroupApp
	disconf, err := sg.DistributionServiceGroup.GetDistributionConfig()
	topFlist, err := sg.FranchiseesServiceGroup.GetTopFranchiseeV2(disconf, true)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetTopFranchisee", zap.Error(err))
	}
	global.GVA_LOG.Info("topFlist:", zap.Any("topFlist", topFlist))
	lo.ForEach(topFlist, func(item franchiseeResp.FranchiseeCustomerUnit, index int) {

		global.GVA_LOG.Info("topFlist:", zap.Any("item", item))
		var link []franchiseeModel.FranchiseeLink

		ti := 0
		err := global.GVA_DB.Model(franchiseeModel.Franchisee{}).
			Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
			Where("sys_users.enable = ?", 1).
			Where("franchisee.id = ?", item.ID).Select("franchisee.inviter_id").Find(&ti).Error
		if err != nil {
			global.GVA_LOG.Error("Error in service.GetCustomers", zap.Error(err))
			return
		}
		link = append(link, franchiseeModel.FranchiseeLink{TopFranchiseeID: item.ID, InviterID: uint(ti), FranchiseeID: item.ID})

		customers, err := sg.FranchiseesServiceGroup.GetCustomers(franchiseeModel.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: item.ID}}, false)
		if err != nil {
			global.GVA_LOG.Error("Error in service.GetCustomers", zap.Error(err))
			return
		}
		global.GVA_LOG.Info("customers:", zap.Any("customers", customers))

		if len(customers.CustomersIDList) > 0 {
			for _, v := range customers.CustomersIDList {
				var i uint
				err := global.GVA_DB.Model(franchiseeModel.Franchisee{}).
					Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
					Where("sys_users.enable = ?", 1).
					Where("franchisee.id = ?", v).Select("franchisee.inviter_id").Find(&i).Error
				if err != nil {
					i = 0 // 无邀请人
				}
				link = append(link, franchiseeModel.FranchiseeLink{TopFranchiseeID: item.ID, InviterID: i, FranchiseeID: v})
			}
		}
		if len(link) > 0 {
			global.GVA_LOG.Info("link:", zap.Any("link", link))
			err := global.GVA_DB.Create(&link).Error
			if err != nil {
				global.GVA_LOG.Error("Error in service.CreateFranchiseeLink", zap.Error(err))
			}
		}
		global.GVA_LOG.Info("next item")
	})
}
