package main

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	AppModel "github.com/OSQianXing/guanpu-server/model/app"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	FranchiseeReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	sysModel "github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

type Coin struct {
	Name          string
	Tel           string
	FCategoryName string
	Balance       int
	Points        int
}

type Address struct {
	Tel   string
	Name  string
	Tel2  string
	Addr1 string
	Addr2 string
}

var DryRun bool = true
var confirm string

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()

	franchiseeFile, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/franchisee-2024.1.2-4733.xlsx")
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	defer func() {
		if err := franchiseeFile.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()

	coinFile, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/cloudwarehouse-2024.1.2.xlsx")
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	defer func() {
		if err := coinFile.Close(); err != nil {
			fmt.Println("err:", err)

		}
	}()
	AddrFile, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/customer-address-12.21.xlsx")
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	defer func() {
		if err := AddrFile.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()

	// 加盟商地址整理
	addrRows, err := AddrFile.GetRows("sheet1")
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Error(err))
		return
	}
	var AddrTelSlice []string
	var AddrMap map[string][]*AppModel.FranchiseeAddress = make(map[string][]*AppModel.FranchiseeAddress)
	lo.ForEach(addrRows, func(item []string, index int) {
		if index == 0 {
			// 忽略第一行
			return
		}
		v, err := ParseFranchiseeAddressRow(item)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			return
		}
		AddrMap[strings.TrimSpace(item[1])] = append(AddrMap[strings.TrimSpace(item[1])], v)

		AddrTelSlice = append(AddrTelSlice, strings.TrimSpace(item[1]))
	})
	AddrTelSlice = lo.Uniq(lo.Compact(AddrTelSlice))
	global.GVA_LOG.Debug("AddrTelSlice:", zap.Any("AddrTelSlice", AddrTelSlice), zap.Any("len ", len(AddrTelSlice)))
	for {
		global.GVA_LOG.Warn("parse addr finish, press Y to continue,N to exit...")
		_, err = fmt.Scanln(&confirm)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			return
		}
		if confirm == "Y" {
			break
		}
		if confirm == "N" {
			global.GVA_LOG.Fatal("exit")
			return
		}
	}

	// 账户余额整理
	var CoinMap map[string]*Coin = make(map[string]*Coin)
	var CoinSlice = make([]string, 0)
	coinRows, err := coinFile.GetRows("账户余额")
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Error(err))
		return
	}
	lo.ForEach(coinRows, func(item []string, index int) {
		if index == 0 {
			// 忽略第一行
			return
		}
		global.GVA_LOG.Debug("coin item:", zap.Any("item", item))
		b, err := strconv.ParseFloat(strings.TrimSpace(item[3]), 64)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			return
		}
		p, err := strconv.ParseFloat(strings.TrimSpace(item[4]), 64)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			return
		}

		CoinMap[strings.TrimSpace(item[1])] = &Coin{
			Name:          strings.TrimSpace(item[0]),
			Tel:           strings.TrimSpace(item[1]),
			FCategoryName: strings.TrimSpace(item[2]),
			Balance:       int(b * 100),
			Points:        int(math.Ceil(p)),
		}
		CoinSlice = append(CoinSlice, strings.TrimSpace(item[1]))
	})
	global.GVA_LOG.Warn("CoinSlice:", zap.Any("CoinSlice", CoinSlice), zap.Any("len ", len(CoinSlice)))

	for {
		global.GVA_LOG.Warn("parse coin finish, press Y to continue,N to exit...")
		_, err = fmt.Scanln(&confirm)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			return
		}
		if confirm == "Y" {
			break
		}
		if confirm == "N" {
			global.GVA_LOG.Fatal("exit")
			return
		}
	}

	//加盟商
	var FranchiseeMList []*franchiseeModel.FranchiseeM
	//手机号 -- 加盟商对应关系
	var TelMap map[string]*franchiseeModel.Franchisee = make(map[string]*franchiseeModel.Franchisee)
	var FranchiseeTelSlice = make([]string, 0)
	var InviterTelSlice = make([]string, 0)

	var banIDS []int
	Frows, err := franchiseeFile.GetRows("Sheet1")
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Error(err))
		return
	}
	global.GVA_LOG.Debug("Frows:", zap.Any("Frows", Frows[0]))

	//加盟商分类
	categoryM := GetFCategoryMap()
	global.GVA_LOG.Debug("categoryM:", zap.Any("categoryM", categoryM))

	// 处理加盟商每行数据
	lo.ForEach(Frows, func(item []string, index int) {
		if index == 0 {
			// 忽略第一行
			return
		}
		franM, err := ParseFranchiseeRow(item, index, franchiseeFile, categoryM)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Any("err", err))
			return
		}
		global.GVA_LOG.Debug("franM:", zap.Any("franM", franM))
		FranchiseeMList = append(FranchiseeMList, franM)
		FranchiseeTelSlice = append(FranchiseeTelSlice, franM.Franchisee.Tel)
		InviterTelSlice = append(InviterTelSlice, franM.Inviter.Tel)
		TelMap[franM.Franchisee.Tel] = &franM.Franchisee
	})

	//TODO: 找出系统中已经存在的数据(Franchisee + SysUser)，franchisee表中的 tel 与 sysuser表中的 name 是否匹配，是否有 sysuser的 name 存在（存在于将要导入的 FranchiseeMList中）但是 franchisee 的 tel 不存在的情况
	// 1. 找出系统中已经存在的数据,对比 franchisee.tel 与 sysuser.name 是否匹配
	// 2. 更新FranchiseeMList中对应的 franchiseeID 和 sysUserID / 更新 TelMap (留给邀请关系使用)
	// Find existing data in the system
	existFranchiseeTelList := make([]string, 0)
	err = global.GVA_DB.Model(&franchiseeModel.Franchisee{}).Unscoped().Where("tel in ?", FranchiseeTelSlice).Select("tel").Find(&existFranchiseeTelList).Error
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return
	}
	global.GVA_LOG.Debug("existFranchiseeList:", zap.Any("existFranchiseeList", existFranchiseeTelList))
	existSysUserNameList := make([]string, 0)
	err = global.GVA_DB.Model(&sysModel.SysUser{}).Unscoped().Where("username in ?", FranchiseeTelSlice).Select("username").Find(&existSysUserNameList).Error
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return
	}
	global.GVA_LOG.Debug("existSysUserList:", zap.Any("existSysUserList", existSysUserNameList))

	// check existFranchiseeList and existSysUserList are corresponding
	lo.ForEach(existFranchiseeTelList, func(item string, index int) {
		if !lo.Contains(existSysUserNameList, item) {
			global.GVA_LOG.Fatal("existFranchiseeList err:", zap.Any("err", err))
			return
		}
	})
	lo.ForEach(existSysUserNameList, func(item string, index int) {
		if !lo.Contains(existFranchiseeTelList, item) {
			global.GVA_LOG.Fatal("existSysUserList err:", zap.Any("err", err), zap.Any("item", item))
			return
		}
	})

	global.GVA_LOG.Warn("existFranchiseeList:", zap.Any("existFranchiseeList", existFranchiseeTelList))
	global.GVA_LOG.Warn("existSysUserList:", zap.Any("existSysUserList", existSysUserNameList))

	InviterTelSlice = lo.Compact(lo.Uniq(InviterTelSlice))
	lo.ForEach(InviterTelSlice, func(item string, index int) {
		if !lo.Contains(existFranchiseeTelList, item) && !lo.Contains(FranchiseeTelSlice, item) {
			if item == "0" || item == "" {
				return
			}
			f := &franchiseeModel.Franchisee{}
			err := global.GVA_DB.Model(&franchiseeModel.Franchisee{}).Unscoped().Where("tel = ?", item).First(&f).Error
			if err != nil {
				global.GVA_LOG.Fatal("InviterTelSlice not in DB System and FranchiseeTelSlice and existFranchiseeList err:", zap.Any("err", err), zap.Any("item", item))
				return
			}
		}
	})

	for {
		global.GVA_LOG.Warn("Please check and confirm the data in the system. Press Y to continue, N to exit...")
		_, err = fmt.Scanln(&confirm)
		if err != nil {
			global.GVA_LOG.Fatal("confirm err:", zap.Any("err", err))
			return
		}
		if confirm == "Y" {
			break
		}
		if confirm == "N" {
			global.GVA_LOG.Fatal("exit:", zap.Any("happy ", confirm))
			return
		}
	}

	// 更新已有的加盟商的部分信息为导入信息

	lo.ForEach(existFranchiseeTelList, func(item string, index int) {
		var deleted = false
		var f *franchiseeModel.Franchisee
		err = global.GVA_DB.Model(&franchiseeModel.Franchisee{}).Unscoped().Where("tel = ?", item).First(&f).Error
		if err != nil {
			global.GVA_LOG.Error("get franchisee err:", zap.Any("err", err), zap.Any("item", item))
			return
		}
		global.GVA_LOG.Debug("origin f:", zap.Any("f", f))
		if f.DeletedAt.Valid {
			deleted = true
			fmt.Println("franchisee has been deleted", f)
			for {
				global.GVA_LOG.Warn("Please check and confirm the data in the system. Press Y to continue, N to exit...")
				_, err = fmt.Scanln(&confirm)
				if err != nil {
					global.GVA_LOG.Fatal("confirm err:", zap.Any("err", err))
					return
				}
				if confirm == "Y" {
					break
				}
				if confirm == "N" {
					global.GVA_LOG.Fatal("exit:", zap.Any("happy ", confirm))
					return
				}
			}
		}
		// 更新已有的加盟商的部分信息为导入信息
		//f.CreatedAt = TelMap[item].CreatedAt
		//f.Remark = TelMap[item].Remark
		//f.InviterID = TelMap[item].InviterID
		//f.DeletedAt = gorm.DeletedAt{}
		TelMap[item].ID = f.ID

		if deleted {
			fmt.Println("franchisee deleted has been updated:", TelMap[item])
			for {
				global.GVA_LOG.Warn("Please check and confirm the data in the system. Press Y to continue, N to exit...")
				_, err = fmt.Scanln(&confirm)
				if err != nil {
					global.GVA_LOG.Fatal("confirm err:", zap.Any("err", err))
					return
				}
				if confirm == "Y" {
					break
				}
				if confirm == "N" {
					global.GVA_LOG.Fatal("exit:", zap.Any("happy ", confirm))
					return
				}
			}
		}
		global.GVA_LOG.Debug("after f:", zap.Any("f", f), zap.Any("item", TelMap[item]))
	})

	for {
		global.GVA_LOG.Info("the data in the system has been updated,if you want to continue update DB, please press Y, if not please press N, exit...")
		_, err = fmt.Scanln(&confirm)
		if err != nil {
			global.GVA_LOG.Fatal("confirm err:", zap.Any("err", err))
			return
		}
		if confirm == "Y" {
			break
		}
		if confirm == "N" {
			global.GVA_LOG.Fatal("exit:", zap.Any("happy ", confirm))
			return
		}
	}

	if DryRun {
		global.GVA_LOG.Info("dry run, exit")
		return
	}

	var alwaysConfirm string
	// 添加要添加的加盟商,更新添加后的 franchiseeID字段，留给邀请关系使用
	var FranchiseeInsertedList []string
	var FranchiseeUpdatedList []string
	var FranchiseeBalanceUpdatedList []string
	var FranchiseePointsUpdatedList []string
	var FranchiseeCoinTotalUpdatedList []string
	var FranchiseeAddrTelInsertedList []string
	var FranchiseeAddrInsertedList []string
	lo.ForEach(FranchiseeMList, func(item *franchiseeModel.FranchiseeM, index int) {
		global.GVA_LOG.Debug("item:", zap.Any("item", item))
		//创建加盟商,余额和流水
		if !DryRun {
			if item.Franchisee.ID != 0 {
				global.GVA_LOG.Info("franchisee has been exist,need update:", zap.Any("item", item), zap.Any("index", index))
				if alwaysConfirm != "Y" {
					for {
						global.GVA_LOG.Info("franchisee exist,press Y to continue, N to exit:")
						_, err = fmt.Scanln(&confirm)
						if err != nil {
							global.GVA_LOG.Fatal("confirm err:", zap.Any("err", err))
							return
						}
						if confirm == "Y" {
							break
						}
						if confirm == "N" {
							global.GVA_LOG.Fatal("exit:", zap.Any("happy ", confirm))
							return
						}
					}

					global.GVA_LOG.Info("does always confirm? Press Y to continue")
					_, err = fmt.Scanln(&alwaysConfirm)
					if err != nil {
						global.GVA_LOG.Fatal("confirm err:", zap.Any("err", err))
						return
					}
				}

				// 该加盟商已经存在
				err := service.ServiceGroupApp.FranchiseesServiceGroup.SimpleUpdateFranchisee(item.Franchisee, []string{"Name", "Code", "FCategoryId", "IsOldFranchisee", "Province", "City", "County", "Area", "Address", "CreatedAt", "Remark", "InviterID", "DeletedAt"})
				if err != nil {
					global.GVA_LOG.Fatal("SimpleUpdateFranchisee err:", zap.Any("err", err), zap.Any("item.Franchisee", item.Franchisee))
					return
				}
				var f franchiseeModel.Franchisee
				err = global.GVA_DB.Model(f).Where("tel = ?", item.Franchisee.Tel).First(&f).Error
				if err != nil {
					global.GVA_LOG.Fatal("get updated Franchisee err:", zap.Any("err", err), zap.Any("tel", item.Franchisee.Tel))
					return
				}
				global.GVA_LOG.Info("franchisee exist update db success:", zap.Any("item.Franchisee", item.Franchisee), zap.Any("f", f))
				FranchiseeUpdatedList = append(FranchiseeUpdatedList, f.Tel)
				//return
			} else {
				global.GVA_LOG.Info("franchisee has not been created,need create:", zap.Any("item", item), zap.Any("index", index))
				fid, err := service.ServiceGroupApp.FranchiseesServiceGroup.CreateFranchisee(&item.Franchisee)
				if err != nil {
					global.GVA_LOG.Fatal("createFranchisee err:", zap.Any("err", err), zap.Any("item.Franchisee", item.Franchisee))
					return
				}
				ff := &franchiseeModel.Franchisee{}
				err = global.GVA_DB.Model(ff).Where("tel = ?", item.Franchisee.Tel).First(&ff).Error
				if err != nil {
					global.GVA_LOG.Fatal("get created Franchisee err:", zap.Any("err", err), zap.Any("tel", item.Franchisee.Tel))
					return
				} else {
					FranchiseeInsertedList = append(FranchiseeInsertedList, ff.Tel)
					global.GVA_LOG.Debug("ff:", zap.Any("ff", ff))
				}
				if item.SysUser.Enable == 2 {
					banIDS = append(banIDS, int(fid))
				}
				item.Franchisee.ID = fid
			}
			// 更新余额
			if c, ok := CoinMap[item.Franchisee.Tel]; ok {
				global.GVA_LOG.Info("CoinMap need update:", zap.Any("item.Franchisee.Tel", item.Franchisee.Tel), zap.Any("c", c))
				//franM.Franchisee.Balance = CoinMap[franM.Franchisee.Tel].Balance
				//franM.Franchisee.Points = CoinMap[franM.Franchisee.Tel].Points
				if c.Balance > 0 {
					err = service.ServiceGroupApp.FranchiseesServiceGroup.Recharge(
						&FranchiseeReq.Recharge{
							FranchiseeId: item.Franchisee.ID,
							Amount:       CoinMap[item.Franchisee.Tel].Balance,
							Type:         types.RechargeAmount,
							AccountType:  types.GeneralAccount,
							Remark:       time.Now().Local().Format(time.DateTime) + " 导入充值 ",
						})
					if err != nil {
						global.GVA_LOG.Fatal("err:", zap.Any("err", err))
						return
					}
					FranchiseeBalanceUpdatedList = append(FranchiseeBalanceUpdatedList, item.Franchisee.Tel)
				}
				if c.Points != 0 {
					err = service.ServiceGroupApp.FranchiseesServiceGroup.Recharge(
						&FranchiseeReq.Recharge{
							FranchiseeId: item.Franchisee.ID,
							Amount:       CoinMap[item.Franchisee.Tel].Points,
							Type:         types.RechargePoints,
							AccountType:  types.GeneralAccount,
							Remark:       time.Now().Local().Format(time.DateTime) + " 导入充值 ",
						})
					if err != nil {
						global.GVA_LOG.Fatal("err:", zap.Any("err", err))
						return
					}
					FranchiseePointsUpdatedList = append(FranchiseePointsUpdatedList, item.Franchisee.Tel)
				}
				var rrs []franchiseeModel.RechargeRecord
				err = global.GVA_DB.Model(&franchiseeModel.RechargeRecord{}).Where("franchisee_id = ?", item.Franchisee.ID).Find(&rrs).Error
				if err != nil {
					global.GVA_LOG.Fatal("err:", zap.Any("err", err), zap.Any("fid", item.Franchisee.ID))
					return
				} else {
					global.GVA_LOG.Debug("rrs: CoinMap[item.Franchisee.Tel	]", zap.Any("tel", item.Franchisee.Tel), zap.Any("rrs", rrs))
					FranchiseeCoinTotalUpdatedList = append(FranchiseeCoinTotalUpdatedList, item.Franchisee.Tel)
				}
				global.GVA_LOG.Debug("CoinMap updated:", zap.Any("item.Franchisee.Tel", item.Franchisee.Tel), zap.Any("c", c))
			}

			TelMap[item.Franchisee.Tel] = &item.Franchisee
		}
		time.Sleep(time.Millisecond * 10)
	})

	if !DryRun {
		global.GVA_LOG.Info("will update some franchisee to be disabled ", zap.Any("banIDs", banIDS))
		if len(banIDS) == 0 {
			global.GVA_LOG.Info("length of banIDs is zero ")
		} else {
			for {
				global.GVA_LOG.Info("length of banIDs is not zero ,please press Y to continue,N to exit", zap.Any("banIDS", banIDS))
				_, err = fmt.Scanln(&confirm)
				if err != nil {
					global.GVA_LOG.Error("err:", zap.Any("err", err))
					return
				}
				if confirm == "Y" {
					break
				}
				if confirm == "N" {
					return
				}

			}

			// 禁用加盟商状态
			err = service.ServiceGroupApp.FranchiseesServiceGroup.BanedFranchisee(request.IdsReq{Ids: banIDS})
			if err != nil {
				global.GVA_LOG.Error("err:", zap.Any("err", err))
				return
			}
		}
	}

	global.GVA_LOG.Warn("will update some franchisee to be inviter, length of FranchiseeMList ", zap.Any("TelMap", len(FranchiseeMList)))
	for {
		global.GVA_LOG.Warn("Press Y to continue, N to exit")
		_, err = fmt.Scanln(&confirm)
		if err != nil {
			global.GVA_LOG.Error("err:", zap.Any("err", err))
			return
		}
		if confirm == "Y" {
			break
		}
		if confirm == "N" {
			global.GVA_LOG.Error("happy day", zap.Any("confirm", confirm))
			return
		}
	}

	// 更新邀请关系
	TelMap["0"] = &franchiseeModel.Franchisee{
		GVA_MODEL: global.GVA_MODEL{
			ID: 0,
		},
	}
	lo.ForEach(FranchiseeMList, func(item *franchiseeModel.FranchiseeM, index int) {
		global.GVA_LOG.Debug("loop update inviter id item:", zap.Any("item", item))

		if item.Inviter.Tel == "" {
			global.GVA_LOG.Error("item.Inviter.Tel == \"\":", zap.Any("item", item))
			return
		}
		if item.Inviter.Tel == "0" {
			return
		}

		if _, ok := TelMap[item.Inviter.Tel]; !ok {
			var f *franchiseeModel.Franchisee
			err = global.GVA_DB.Unscoped().Where("tel = ?", item.Inviter.Tel).First(&f).Error
			if err != nil {
				global.GVA_LOG.Fatal("find inviter from db err:", zap.Any("err", err), zap.Any("item", item))
				return
			}
			if f != nil {
				TelMap[item.Inviter.Tel] = f
			}
		}
		item.Inviter = TelMap[item.Inviter.Tel]
		item.Franchisee.InviterID = int(item.Inviter.ID)
		if !DryRun {
			err = global.GVA_DB.Model(&franchiseeModel.Franchisee{}).Where("id = ?", item.Franchisee.ID).Update("inviter_id", item.Franchisee.InviterID).Error
			if err != nil {
				global.GVA_LOG.Fatal("err:", zap.Any("err", err))
				return
			}
		}

		time.Sleep(time.Millisecond * 10)
	})

	global.GVA_LOG.Warn("will update some addr, length of AddrMap ", zap.Any("AddrMap", len(AddrTelSlice)))
	for {
		global.GVA_LOG.Warn("Press Y to continue, N to exit")
		_, err = fmt.Scanln(&confirm)
		if err != nil {
			global.GVA_LOG.Error("err:", zap.Any("err", err))
			return
		}
		if confirm == "Y" {
			break
		}
		if confirm == "N" {
			global.GVA_LOG.Error("happy day", zap.Any("confirm", confirm))
			return
		}
	}

	//更新加盟商地址
	global.GVA_LOG.Debug("AddrTelSlice:", zap.Any("AddrTelSlice", AddrTelSlice))
	if !DryRun {
		lo.ForEach(AddrTelSlice, func(item string, index int) {
			if item == "" {
				return
			}
			global.GVA_LOG.Debug("loop update addr item:", zap.Any("item", item))
			if v, ok := TelMap[item]; !ok || v.ID == 0 {
				var f *franchiseeModel.Franchisee
				err = global.GVA_DB.Unscoped().Where("tel = ?", item).First(&f).Error
				if err != nil {
					global.GVA_LOG.Error("find franchisee from db err:", zap.Any("err", err), zap.Any("item", item))
					return
				}
				if f != nil {
					TelMap[item] = f
				}
			}
			addrs := AddrMap[item]
			for _, addr := range addrs {
				addr.FranchiseeId = int(TelMap[item].ID)
				err = service.ServiceGroupApp.AppServiceGroup.CreateFranchiseeAddress(addr)
				if err != nil {
					global.GVA_LOG.Fatal("err:", zap.Any("err", err))
					return
				}
			}
			var addrq []AppModel.FranchiseeAddress
			err = global.GVA_DB.Table("franchisee_address").Where("franchisee_id = ?", TelMap[item].ID).Find(&addrq).Error
			if err != nil {
				global.GVA_LOG.Fatal("err:", zap.Any("err", err))
				return
			} else {
				global.GVA_LOG.Debug("addrq:", zap.Any("addrq", addrq))
			}
		})
	}

	global.GVA_LOG.Warn("update addr end")
	global.GVA_LOG.Warn("Total update franchisee :", zap.Any("existFranchiseeTelList", len(existFranchiseeTelList)), zap.Any("real deal", len(FranchiseeUpdatedList)))
	global.GVA_LOG.Warn("Total insert franchisee :", zap.Any("len(FranchiseeMList)-len(existFranchiseeTelList)", len(FranchiseeMList)-len(existFranchiseeTelList)), zap.Any("real deal", len(FranchiseeInsertedList)))
	global.GVA_LOG.Warn("Total update balance :", zap.Any("CoinSlice", len(CoinSlice)), zap.Any("real deal total", len(FranchiseeCoinTotalUpdatedList)), zap.Any("real deal balance", len(FranchiseeBalanceUpdatedList)), zap.Any("real deal points ", len(FranchiseePointsUpdatedList)))
	global.GVA_LOG.Warn("Total ban franchisee :", zap.Any("banIDs", len(banIDS)))
	global.GVA_LOG.Warn("Total update addr :", zap.Any("AddrTelSlice", len(AddrTelSlice)), zap.Any("real deal addr", len(FranchiseeAddrTelInsertedList)), zap.Any("real deal addr", len(FranchiseeAddrInsertedList)))
}

func ParseCreateTime(str string) (time.Time, error) {
	if str == "" {
		return time.Time{}, nil
	}
	str = strings.Replace(str, "/", "-", -1)
	return time.Parse("2006-01-02 15:04:05", str)
}
func GetFCategoryMap() map[string]franchiseeModel.FranchiseeCategory {
	sg := service.ServiceGroupApp
	categorys, _, err := sg.FranchiseesServiceGroup.GetFranchiseeCategoryInfoList(FranchiseeReq.FranchiseeCategorySearch{
		PageInfo: request.PageInfo{
			PageSize: 1000,
			Page:     1,
		},
	})
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return nil
	}

	var categoryMap = make(map[string]franchiseeModel.FranchiseeCategory)
	lo.ForEach[franchiseeModel.FranchiseeCategory](categorys, func(item franchiseeModel.FranchiseeCategory, index int) {
		categoryMap[item.Name] = item
	})
	return categoryMap
}

func ParseFranchiseeRow(row []string, rowID int, f *excelize.File, categoryMap map[string]franchiseeModel.FranchiseeCategory) (*franchiseeModel.FranchiseeM, error) {
	global.GVA_LOG.Debug("row:", zap.Any("row", row), zap.Any("rowID", rowID))
	valStyle, err := f.NewStyle(&excelize.Style{
		NumFmt: 0,
	})
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Error(err))
		return nil, err
	}

	CellPos := fmt.Sprintf("P%d", rowID+1)
	err = f.SetCellStyle("Sheet1", CellPos, CellPos, valStyle)
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Error(err), zap.Any("CellPos", CellPos))
		return nil, err
	}
	x, err := f.GetCellValue("Sheet1", CellPos)
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Error(err), zap.Any("CellPos", CellPos))
		return nil, err
	}
	xfloat, err := strconv.ParseFloat(x, 64)
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Error(err), zap.Any("CellPos", CellPos), zap.Any("x", x))
		return nil, err
	}
	y, err := excelize.ExcelDateToTime(xfloat, false)
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Error(err), zap.Any("CellPos", CellPos), zap.Any("xfloat", xfloat))
		return nil, err
	}

	if y.IsZero() {
		global.GVA_LOG.Error("err:", zap.Any("y.IsZero()", errors.New("y.IsZero()")))
		return nil, errors.New("y.IsZero()")
	}

	if row[0] == "" {
		global.GVA_LOG.Error("err:", zap.Any("row[0]", errors.New("row[0] is empty")))
		return nil, errors.New("row[0] is empty")
	}
	var isOld = false
	if (row[0] == "老酒道馆" || row[0] == "秘书长=老酒道馆") && strings.TrimSpace(row[1]) == "是" {
		row[0] = "酒道馆"
		isOld = true
	} else if (row[0] == "新酒道馆") && strings.TrimSpace(row[1]) == "否" {
		row[0] = "酒道馆"
		isOld = false
	} else if row[0] == "代理商" && strings.TrimSpace(row[1]) == "否" {
		isOld = false
	} else {
		global.GVA_LOG.Error("err:", zap.Any("row[0] is not in [老酒道馆, 新酒道馆, 代理商, 秘书长=老酒道馆]", errors.New("row[0] is not in [老酒道馆, 新酒道馆, 代理商, 秘书长=老酒道馆]")))
		return nil, errors.New("row[0] is not in [老酒道馆, 新酒道馆, 代理商, 秘书长=老酒道馆]")
	}
	cate, ok := categoryMap[row[0]]
	if !ok {
		return nil, errors.New("row[0] is not in categoryMap")
	}
	if cate.ID == 0 {
		global.GVA_LOG.Error("err:", zap.Any("cate.ID == 0", errors.New("cate.ID == 0")))
		return nil, errors.New("cate.ID == 0")
	}
	cateid := int(cate.ID)

	if strings.TrimSpace(row[6]) == "观朴酒业" || strings.TrimSpace(row[6]) == "" {
		row[6] = "0"
	}

	inviterTel := strings.TrimSpace(row[7])
	//if inviterTel == "" {
	//	inviterTel = "0"
	//}
	//
	code := strings.TrimSpace(row[2])
	if code == "" {
		code = strings.TrimSpace(row[4])
	}

	var enable = 2
	if strings.TrimSpace(row[14]) == "正常" {
		enable = 1
	}
	if len(row) < 17 {
		row = append(row, "")
	}
	return &franchiseeModel.FranchiseeM{
		Franchisee: franchiseeModel.Franchisee{
			GVA_MODEL: global.GVA_MODEL{
				CreatedAt: y.Local().Add(-8 * time.Hour),
			},
			Name:            strings.TrimSpace(row[3]),
			Tel:             strings.TrimSpace(row[4]),
			FCategoryId:     &cateid,
			Code:            code,
			Province:        strings.TrimSpace(row[11]),
			City:            strings.TrimSpace(row[12]),
			County:          strings.TrimSpace(row[13]),
			Address:         "",
			Linkman:         strings.TrimSpace(row[3]),
			IsOldFranchisee: isOld,
			Remark:          strings.TrimSpace(row[16]),
		},
		SysUser: &sysModel.SysUser{
			Enable: enable,
		},
		Inviter: &franchiseeModel.Franchisee{
			Tel: inviterTel,
		},
	}, nil
}
func ParseFranchiseeAddressRow(row []string) (*AppModel.FranchiseeAddress, error) {
	global.GVA_LOG.Debug("ParseFranchiseeAddressRow row:", zap.Any("row", row))
	pcc := strings.Split(strings.TrimSpace(row[4]), " ")
	if len(pcc) != 3 {
		return nil, errors.New("province city county error")
	}
	return &AppModel.FranchiseeAddress{
		FranchiseeId: 0,
		Consignee:    strings.TrimSpace(row[2]),
		Tel:          strings.TrimSpace(row[3]),
		Province:     pcc[0],
		City:         pcc[1],
		County:       pcc[2],
		Address:      strings.TrimSpace(row[5]),
	}, nil
}

/**
Truncate:
delete from sys_users where id > 165;
delete from franchisee where id > 116;
delete from franchisee_account where id in ( select id from franchisee where id > 116 );
delete from franchisee_performance where franchisee_id in ( select id from franchisee where id > 116 );
delete from franchisee_address where franchisee_id in ( select id from franchisee where id > 116 );
delete from recharge_record where franchisee_id in ( select id from franchisee where id > 116 );



*/
