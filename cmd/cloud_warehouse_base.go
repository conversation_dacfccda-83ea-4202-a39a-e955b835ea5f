package main

import (
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/service"
	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	var sg = service.ServiceGroupApp
	record, err := sg.WarehouseServiceGroup.GetCloudWarehouseRecord(4631)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		return
	}
	global.GVA_LOG.Info("获取成功!", zap.Any("record", record))

}
