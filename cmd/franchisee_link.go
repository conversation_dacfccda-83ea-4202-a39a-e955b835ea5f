package main

import (
	"encoding/json"
	"fmt"
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesRes "github.com/OSQianXing/guanpu-server/model/franchisees/response"
	"github.com/OSQianXing/guanpu-server/service"
	"go.uber.org/zap"
	"strings"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	var sg = service.ServiceGroupApp
	//var fReq = fListReq.FranchiseeSearch{}
	//franchiseesList, _, _ := sg.FranchiseesServiceGroup.FranchiseeService.GetFranchiseeInfoList(fReq)
	//fmt.Println("franchiseeList:", franchiseesList)
	//x, _ := sg.FranchiseesServiceGroup.FranchiseeService.GetDistributionConfigForUser(104)
	//fmt.Println("x:", x)
	//y, err := sg.FranchiseesServiceGroup.GetDirectMember(22)
	//if err != nil {
	//	fmt.Println("err:", err)
	//}
	//for _, v := range y {
	//	fmt.Println("y:", v)
	//}

	i := 43
	z, err := sg.FranchiseesServiceGroup.GetCustomers(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 22}, FCategoryId: &i}, true)
	if err != nil {
		fmt.Println("err:", err)
	}
	//for _, v := range z {
	fmt.Println("z:", z.FR, z.CustomerCount, z.DirectCustomers)
	if z != nil {
		for _, v := range z.DirectCustomers {
			fmt.Println("v:", v.FR)
			fmt.Println("v.CustomerCount:", v.CustomerCount)
			fmt.Println("v.DirectCustomers:", v.DirectCustomers)
			fmt.Println("v.CustomerIDList:", v.CustomersIDList)
		}
	}
	jz, err := json.Marshal(z)
	if err != nil {
		fmt.Println("err:", err)
	}
	fmt.Println("jz:", string(jz))

	x, err := sg.FranchiseesServiceGroup.GetFranchiseeSearchCustomer(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 37}, FCategoryId: &i}, "3", 20)
	if err != nil {
		fmt.Println("err:", err)
	}
	fmt.Println("x:", x)
	jx, err := json.Marshal(x)
	if err != nil {
		fmt.Println("err:", err)
	}
	fmt.Println("jx:", string(jx))
}

//zz, err := sg.FranchiseesServiceGroup.FranchiseeService.LoopGetMember(&franchiseesRes.FranchiseeCustomerCount{FR: &franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 37}}})
//if err != nil {
//	fmt.Println("err:", err)
//}
//fmt.Println("zz:", zz.CustomerCount, zz.DirectCustomers, zz.FR)
//loopPrint(zz)
//
//zz, err = sg.FranchiseesServiceGroup.FranchiseeService.GetMembers(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 37}})
//if err != nil {
//	fmt.Println("err:", err)
//}
//fmt.Println("zz:", zz.CustomerCount, zz.DirectCustomers, zz.FR)
//loopPrint(zz)

//zz, err := sg.FranchiseesServiceGroup.FranchiseeService.GetMyTopFranchisee(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 22}})
//if err != nil {
//	fmt.Println("err:", err)
//	fmt.Println("err:", err)
//}
//if len(zz) > 0 {
//	for _, v := range zz {
//		fmt.Println("v:", v)
//	}
//}
// zz, err := sg.FranchiseesServiceGroup.FranchiseeService.GetTopFranchiseeWithCustomerCount()
// jstr, err := json.Marshal(zz)
// if err != nil {
// 	fmt.Println("err:", err)
// }
// for k := range zz {
// 	fmt.Println("zz[k]:", k, zz[k].FR)
// 	loopPrint(&zz[k])
// }
// fmt.Println("jstr:", string(jstr))
//all_franchisee, _, err := sg.FranchiseesServiceGroup.FranchiseeService.GetFranchiseeInfoList(franchiseeReq.FranchiseeSearch{
//	PageInfo: request.PageInfo{
//		Page:     1,
//		PageSize: 1000,
//	},
//})
//if err != nil {
//	fmt.Println("err:", err)
//}
//// fmt.Println("all_franchisee:", all_franchisee, "total:", total)
//for k := range all_franchisee {
//	fmt.Println("all_franchisee[k]:", k, all_franchisee[k])
//	fmt.Println(strings.Repeat("--", 20))
//	zz, err := sg.FranchiseesServiceGroup.FranchiseeService.GetMembers(all_franchisee[k].Franchisee)
//	if err != nil {
//		fmt.Println("err:", err)
//	}
//	fmt.Println("zz:", zz.CustomerCount, zz.DirectCustomers, zz.FR)
//	fmt.Println(strings.Repeat("--", 20))
//	jmember, err := json.Marshal(zz)
//	if err != nil {
//		fmt.Println("err:", err)
//	}
//	fmt.Println("jmember:", string(jmember))
//	loopPrint(zz)
//
//}
// zz, err = sg.FranchiseesServiceGroup.GetMembers(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 22}})
//	dm, err := sg.FranchiseesServiceGroup.FranchiseeService.GetDirectCustomers(37)
//	if err != nil {
//		fmt.Println("err:", err)
//	}
//	for k := range dm {
//		fmt.Println("dm[k]:", k, dm[k])
//		fmt.Println(strings.Repeat("--", 20))
//		jj, e := json.Marshal(dm[k])
//		if e != nil {
//			fmt.Println("e:", e)
//		}
//		fmt.Println("jj:", string(jj))
//	}
//}

func loopPrint(member *franchiseesRes.FranchiseeCustomerCount) {
	for _, v := range member.DirectCustomers {
		fmt.Println(strings.Repeat("--", 20), "v:", v.FR)
		fmt.Println("v.CustomerCount:", v.CustomerCount)
		loopPrint(v)
	}
}
