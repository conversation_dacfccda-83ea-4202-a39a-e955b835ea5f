package main

import (
	"strconv"
	"strings"
	"time"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/finance"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	historyPerformanceFile := "/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/history_performance.2023.1.3.xlsx"

	hpFile, err := excelize.OpenFile(historyPerformanceFile)
	if err != nil {
		global.GVA_LOG.Error("打开文件失败", zap.Error(err))
		return
	}
	hpSheet := hpFile.GetSheetName(0)
	hpRows, err := hpFile.GetRows(hpSheet)
	if err != nil {
		global.GVA_LOG.Error("读取文件失败", zap.Error(err))
		return
	}

	var TelSlice []string
	var TelMap = make(map[string]int64)
	//var HPMap = make(map[int64]finance.FranchiseeHistoryPerformance)

	// 获取业绩 Tel 与 Performance 的映射
	lo.ForEach(hpRows, func(row []string, rowIndex int) {
		if rowIndex == 0 {
			return
		}
		tel := strings.TrimSpace(row[1])
		TelSlice = append(TelSlice, tel)
		pstr := strings.TrimSpace(row[2])
		p, err := strconv.ParseInt(pstr, 10, 64)
		if err != nil {
			global.GVA_LOG.Error("转换类型失败", zap.Error(err))
			return
		}
		TelMap[tel] = p * 100
	})

	type fidTel struct {
		Fid int    `gorm:"column:id"`
		Tel string `gorm:"column:tel"`
	}
	var FidTelSlice []fidTel
	err = global.GVA_DB.Table("franchisee").Where("tel in ?", TelSlice).Select("id, tel").Find(&FidTelSlice).Error
	if err != nil {
		global.GVA_LOG.Error("查询加盟商失败", zap.Error(err))
		return
	}

	lo.ForEach(FidTelSlice, func(row fidTel, rowIndex int) {
		global.GVA_DB.Model(&finance.FranchiseeHistoryPerformance{}).Create(&finance.FranchiseeHistoryPerformance{
			FranchiseeId: lo.ToPtr(row.Fid),
			YearMonth:    lo.ToPtr(time.Date(2023, 12, 31, 0, 0, 0, 0, time.Local)),
			Performance:  lo.ToPtr(TelMap[row.Tel]),
		})
	})

}
