package main

import (
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseeReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	var sg = service.ServiceGroupApp
	rclist, t, err := sg.FranchiseesServiceGroup.RechargeRecordService.GetRechargeRecordInfoList(franchiseeReq.RechargeRecordSearch{
		RechargeRecord: franchisees.RechargeRecord{
			//FranchiseeId: 1,
		},
		PageInfo: request.PageInfo{
			Page:     1,
			PageSize: 10,
		},
		FCategoryId: 43,
		Tel:         "18601152354",
	},
	)
	global.GVA_LOG.Debug("t:", zap.Any("t", t), zap.Any("rclist", rclist), zap.Error(err))
	x, err := sg.FranchiseesServiceGroup.GetRechargeRecordsBySerialNo("20231226000179380001")
	global.GVA_LOG.Debug("x:", zap.Any("x", x), zap.Error(err))
	//x := sg.FranchiseesServiceGroup
	//y, err := x.GetFranchiseeWithCategoryInfo(102)
	//global.GVA_LOG.Debug("y:", zap.Any("y", y), zap.Error(err))
	//
	//z, y, err := x.GetFranchiseeAllCustomerV2(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: 90}}, request.PageInfo{
	//	Page:     2,
	//	PageSize: 999,
	//})
	//global.GVA_LOG.Debug("z:", zap.Any("z", z), zap.Any("y", y), zap.Error(err))
	//for k := range z {
	//	global.GVA_LOG.Debug("v:", zap.Any("v", z[k]))
	//}
	//y, err := x.BatchGetDirectCustomers([]uint{80, 90})
	//global.GVA_LOG.Debug("y:", zap.Any("y", y), zap.Error(err))

}
