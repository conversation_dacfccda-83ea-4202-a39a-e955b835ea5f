package main

import (
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

type CoinV2 struct {
	Name          string
	Tel           string
	FCategoryName string
	Balance       int
	Points        int
}

type AddressV2 struct {
	Tel   string
	Name  string
	Tel2  string
	Addr1 string
	Addr2 string
}

var DryRunV2 bool = false

const (
	FRANCHISEE_FILE = "/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/客户导入模板2023.12.19最终版_副本.xlsx"
	COIN_FILE       = "/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/期初数据导入云仓余额及账户余额2023.12.19.xlsx"
	ADDR_FILE       = "/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/客户收货信息12.21.xlsx"
)

func main() {

	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()

}

func OpenExcelFile(path string) (*excelize.File, error) {
	file, err := excelize.OpenFile(path)
	if err != nil {
		return nil, err
	}
	return file, nil
}
