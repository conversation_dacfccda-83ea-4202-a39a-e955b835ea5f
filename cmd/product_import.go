package main

import (
	"fmt"
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	productsModel "github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

func main() {

	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()

	pFile, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/product.2024.1.2.xlsx")
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	defer func() {
		if err := pFile.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()

	rows, err := pFile.GetRows("Sheet1")
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	global.GVA_LOG.Debug("rows:", zap.Any("rows", rows[5]))

	MallMap, err := GetSpecialMalls()
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	global.GVA_LOG.Debug("MallMap:", zap.Any("MallMap", MallMap))
	PCMap, err := GetProductCategorys()
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	global.GVA_LOG.Debug("PCMap:", zap.Any("PCMap", PCMap))
	PBMap, err := GetProductBrands()
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	global.GVA_LOG.Debug("PBMap:", zap.Any("PBMap", PBMap))
	FCMap, err := GetFranchiseeCategorys()
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	global.GVA_LOG.Debug("FCMap:", zap.Any("FCMap", FCMap))

	var DoInsert []string
	var DoIgnore []string
	var DoUpdate []string

	lo.ForEach(rows, func(row []string, index int) {
		if index < 6 {
			// 忽略第一行
			return
		}

		global.GVA_LOG.Debug("row:", zap.Any("index", index), zap.Any("row", row))
		p, err := DealProductRow(row, MallMap, PCMap, PBMap, FCMap)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			return
		}
		global.GVA_LOG.Debug("p:", zap.Any("p", p))
		if err == nil && p == nil {
			global.GVA_LOG.Warn("product ignore index:", zap.Any("index", index), zap.Any("row", row))
			return
		}
		//check product_name and code if exist
		pCheck, err := GetProductWithName(p.Name)
		if err == nil {
			global.GVA_LOG.Debug("product check with name exist,pCheck:", zap.Any("pCheck", pCheck), zap.Any("p", p))
			DoIgnore = append(DoIgnore, p.Name)
			pCheck.OldFranchiseeCanBuy = p.OldFranchiseeCanBuy
			pCheck.OnSale = p.OnSale
			pCheck.SpecialMallID = p.SpecialMallID
			pCheck.PCategoryId = p.PCategoryId
			pCheck.FCategoryIds = p.FCategoryIds
			pCheck.Spec = p.Spec
			pCheck.Unit = p.Unit
			pCheck.BrandId = p.BrandId
			pCheck.IsRecommend = p.IsRecommend
			pCheck.IsGift = p.IsGift
			pCheck.OnSale = p.OnSale
			pCheck.OldFranchiseeCanBuy = p.OldFranchiseeCanBuy
			pCheck.Code = p.Code
			errre := service.ServiceGroupApp.ProductsServiceGroup.UpdateProduct(pCheck)
			if errre != nil {
				global.GVA_LOG.Fatal("errre:", zap.Error(errre))
				global.GVA_LOG.Fatal("pCheck:", zap.Any("pCheck", pCheck))
			}
			DoUpdate = append(DoUpdate, p.Name)
			return
		} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			global.GVA_LOG.Fatal("p:", zap.Any("p", p))
			return
		}
		//pCheck, err = GetProductWithCode(p.Code)
		//if err == nil {
		//	global.GVA_LOG.Debug("product check with code exist,pCheck:", zap.Any("pCheck", pCheck), zap.Any("p", p))
		//	DoIgnore = append(DoIgnore, p.Name)
		//	return
		//} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//	global.GVA_LOG.Fatal("err:", zap.Error(err))
		//	global.GVA_LOG.Fatal("p:", zap.Any("p", p))
		//}
		//
		// create product
		global.GVA_LOG.Info("do create product:", zap.Any("p", p))
		err = service.ServiceGroupApp.ProductsServiceGroup.CreateProduct(p)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Error(err))
			global.GVA_LOG.Fatal("p:", zap.Any("p", p))
		}
		pp := &productsModel.Product{}
		err = global.GVA_DB.Model(pp).Where("name = ?", p.Name).First(&pp).Error
		global.GVA_LOG.Debug("pp:", zap.Any("pp", pp))
		DoInsert = append(DoInsert, p.Name)
		return
	})

	global.GVA_LOG.Info("DoInsert:", zap.Any("DoInsert", DoInsert), zap.Any("len(DoInsert)", len(DoInsert)))
	global.GVA_LOG.Info("DoIgnore:", zap.Any("DoIgnore", DoIgnore), zap.Any("len(DoIgnore)", len(DoIgnore)))
}

func GetSpecialMalls() (map[string]int, error) {
	var res []productsModel.SpecialMall
	err := global.GVA_DB.Model(&productsModel.SpecialMall{}).Find(&res).Error
	if err != nil {
		return nil, err
	}
	rres := lo.SliceToMap(res, func(item productsModel.SpecialMall) (string, int) {
		return item.Name, item.ID
	})
	global.GVA_LOG.Debug("rres:", zap.Any("rres", rres))
	return rres, nil
}

func GetProductCategorys() (map[string]int, error) {
	var res []productsModel.ProductCategory
	err := global.GVA_DB.Model(&productsModel.ProductCategory{}).Find(&res).Error
	if err != nil {
		return nil, err
	}
	rres := lo.SliceToMap(res, func(item productsModel.ProductCategory) (string, int) {
		return item.Name, int(item.ID)
	})
	global.GVA_LOG.Debug("rres:", zap.Any("rres", rres))
	return rres, nil
}

func GetFranchiseeCategorys() (map[string]int, error) {
	var res []franchiseeModel.FranchiseeCategory
	err := global.GVA_DB.Model(&franchiseeModel.FranchiseeCategory{}).Find(&res).Error
	if err != nil {
		return nil, err
	}
	rres := lo.SliceToMap(res, func(item franchiseeModel.FranchiseeCategory) (string, int) {
		return item.Name, int(item.ID)
	})
	global.GVA_LOG.Debug("rres:", zap.Any("rres", rres))
	return rres, nil
}
func GetProductBrands() (map[string]int, error) {
	var res []productsModel.ProductBrand
	err := global.GVA_DB.Model(&productsModel.ProductBrand{}).Find(&res).Error
	if err != nil {
		return nil, err
	}
	rres := lo.SliceToMap(res, func(item productsModel.ProductBrand) (string, int) {
		return item.Name, int(item.ID)
	})
	global.GVA_LOG.Debug("rres:", zap.Any("rres", rres))
	return rres, nil
}

func DealProductRow(row []string, mallMap map[string]int, productCategoryMap map[string]int, productBrandMap map[string]int, franchiseeCategoryMap map[string]int) (*productsModel.Product, error) {
	boolTrue := true
	boolFalse := false

	intZero := 0
	int20000 := 20000

	if len(row) >= 23 && strings.TrimSpace(row[22]) == "1" {
		return nil, nil
	}

	p := &productsModel.Product{}
	p.Name = strings.TrimSpace(row[1])
	p.Code = strings.TrimSpace(row[18])

	if _, ok := mallMap[strings.TrimSpace(row[2])]; ok {
		p.SpecialMallID = mallMap[strings.TrimSpace(row[2])]
	} else {
		return nil, errors.New("mall not found :" + strings.TrimSpace(row[2]))
	}
	//if strings.TrimSpace(row[2]) == "分销商城" || strings.TrimSpace(row[2]) == "返赠专区" {
	//	p.OldFranchiseeCanBuy = &boolTrue
	//} else {
	//	p.OldFranchiseeCanBuy = &boolFalse
	//}
	if strings.TrimSpace(row[17]) == "是" {
		p.OldFranchiseeCanBuy = &boolTrue
	} else if strings.TrimSpace(row[17]) == "否" {
		p.OldFranchiseeCanBuy = &boolFalse
	} else {
		return nil, errors.New("oldFranchiseeCanBuy not found")
	}

	if strings.TrimSpace(row[3]) == "是" {
		p.IsCombination = &boolTrue
	} else if strings.TrimSpace(row[3]) == "无" {
		p.IsCombination = &boolFalse
	}

	if strings.TrimSpace(row[15]) == "" {
		return nil, errors.New("onSale is empty")
	}
	if strings.TrimSpace(row[15]) == "是" {
		p.OnSale = boolTrue
	} else if strings.TrimSpace(row[15]) == "否" {
		p.OnSale = boolFalse
	} else {
		return nil, errors.New("onSale not found")
	}

	//if strings.TrimSpace(row[6]) == "" {
	//	return nil, errors.New("spec is empty")
	//}
	p.Spec = strings.TrimSpace(row[6])

	if strings.TrimSpace(row[4]) == "" {
		return nil, errors.New("category is empty")
	}
	if v, ok := productCategoryMap[strings.TrimSpace(row[4])]; ok {
		p.PCategoryId = &v
	} else {
		return nil, errors.New("category not found :" + strings.TrimSpace(row[4]))
	}

	if strings.TrimSpace(row[7]) == "" {
		return nil, errors.New("unit is empty")
	}
	p.Unit = strings.TrimSpace(row[7])

	if strings.TrimSpace(row[8]) == "" {
		return nil, errors.New("brand is empty")
	}
	if v, ok := productBrandMap[strings.TrimSpace(row[8])]; ok {
		p.BrandId = &v
	}

	if strings.TrimSpace(row[11]) == "" {
		return nil, errors.New("rebate is empty")
	}
	rebate, err := strconv.ParseInt(strings.TrimSpace(row[11]), 10, 32)
	if err != nil {
		return nil, errors.Wrap(err, "rebate strconv failed")
	}
	rebatex := int(rebate)
	p.Rebate = &rebatex

	if strings.TrimSpace(row[10]) == "" {
		return nil, errors.New("exchangePrice is empty")
	}
	exchangePrice, err := strconv.ParseInt(strings.TrimSpace(row[10]), 10, 32)
	if err != nil {
		return nil, errors.Wrap(err, "exchangePrice strconv failed")
	}
	exchangePricex := int(exchangePrice)
	p.ExchangePrice = &exchangePricex

	//cost
	if strings.TrimSpace(row[9]) == "" {
		return nil, errors.New("cost is empty")
	}
	cost, err := strconv.ParseInt(strings.TrimSpace(row[9]), 10, 32)
	if err != nil {
		return nil, errors.Wrap(err, "cost strconv failed")
	}
	costx := int(cost * 100)
	p.Cost = &costx

	//vipPrice
	if strings.TrimSpace(row[19]) == "" {
		return nil, errors.New("vipPrice is empty")
	}
	vipPrice, err := strconv.ParseInt(strings.TrimSpace(row[19]), 10, 32)
	if err != nil {
		return nil, errors.Wrap(err, "vipPrice strconv failed")
	}
	vipPricex := int(vipPrice * 100)
	p.VipPrice = &vipPricex
	//RetailPrice
	if strings.TrimSpace(row[20]) == "" {
		return nil, errors.New("retailPrice is empty")
	}
	retailPrice, err := strconv.ParseInt(strings.TrimSpace(row[20]), 10, 32)
	if err != nil {
		return nil, errors.Wrap(err, "retailPrice strconv failed")
	}
	retailPricex := int(retailPrice * 100)
	p.RetailPrice = &retailPricex

	p.IsRecommend = &boolFalse

	//isGift
	if strings.TrimSpace(row[14]) == "" {
		return nil, errors.New("isGift is empty")
	}
	if strings.TrimSpace(row[14]) == "是" {
		p.IsGift = &boolTrue
		p.SpecialMallID = 0
	} else if strings.TrimSpace(row[14]) == "否" {
		p.IsGift = &boolFalse
	} else {
		return nil, errors.New("isGift not illegal")
	}
	p.IsCombination = &boolFalse
	p.Sort = &intZero
	p.Inventory = &int20000
	p.Remark = string(time.Now().Local().Format("2006-01-02 15:04:05") + " 初始化导入")

	if strings.TrimSpace(row[5]) == "" {
		return nil, errors.New("Franchisee category is empty")
	}
	fcateStringSlice := strings.Split(strings.TrimSpace(row[5]), "、")
	var fcateIntSlice types.XIntergers
	for _, v := range fcateStringSlice {
		if _, ok := franchiseeCategoryMap[v]; ok {
			fcateIntSlice = append(fcateIntSlice, franchiseeCategoryMap[v])
		} else {
			return nil, errors.New("Franchisee category map not found " + v)
		}
	}
	p.FCategoryIds = &fcateIntSlice
	p.CombinationDetail = nil

	//final check
	if *p.IsGift == false && *p.Cost == 0 && *p.ExchangePrice == 0 && len(*p.FCategoryIds) != 0 && p.OnSale == true {
		return nil, errors.New("进货价和积分兑换价同时为0时，上架状态需要为下架及且所属加盟商类型为 空")
	}
	p.OnSale = boolFalse

	return p, nil
}

func GetProductWithName(name string) (productsModel.Product, error) {
	var product productsModel.Product
	err := global.GVA_DB.Where("name = ?", name).First(&product).Error
	return product, err
}
func GetProductWithCode(code string) (productsModel.Product, error) {
	var product productsModel.Product
	err := global.GVA_DB.Where("code = ?", code).First(&product).Error
	return product, err
}

/**
truncate:
delete from product where id > 210;

*/
