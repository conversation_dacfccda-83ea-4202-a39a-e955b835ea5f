package main

import (
	"errors"
	"fmt"
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	productsModel "github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"time"
)

type CloudWareProduct struct {
	ProductID        uint
	ProductName      string
	ProductInventory int
	ProductPrice     int
	Prod             *productsModel.Product
}

var OrderRemark = time.Now().Local().Format("2006-01-02 15:04:05") + " 历史云仓数据导入"

type CWOrder struct {
	OrderID uint
	CP      []*CloudWareProduct
	Fr      *franchiseeModel.Franchisee
}

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()

	cFile, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/期初数据导入云仓余额及账户余额2023.12.19.xlsx")
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Any("err", err))
		return
	}
	defer func() {
		if err := cFile.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()

	// 获取单元行
	rows, err := cFile.GetRows("云仓余额")

	TelSlice := lo.Map(rows, func(row []string, index int) string {
		if index != 0 {
			return strings.TrimSpace(row[1])
		} else {
			return ""
		}
	})

	TelSlice = lo.Uniq(TelSlice)
	TelSlice = lo.Compact(TelSlice)

	TelMap, err := GetFranchiseeMap(TelSlice)
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Any("err", err))
		return
	}
	//global.GVA_LOG.Debug("TelMap:", zap.Any("TelMap", TelMap))

	PNameSlice := lo.Map(rows, func(row []string, index int) string {
		if index != 0 {
			return strings.TrimSpace(row[3])
		} else {
			return ""
		}
	})
	PNameSlice = lo.Compact(lo.Uniq(PNameSlice))

	PMap, err := GetProductMap(PNameSlice)
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Any("err", err))
	}
	//global.GVA_LOG.Debug("PMap:", zap.Any("PMap", PMap))

	global.GVA_LOG.Debug("PNameSlice:", zap.Any("PNameSlice", PNameSlice))

	var OrderMap map[string]*CWOrder = make(map[string]*CWOrder)
	//构建订单Map
	lo.ForEach(rows, func(row []string, index int) {
		if index == 0 {
			return
		}
		global.GVA_LOG.Debug("row:", zap.Any("row", row))
		cp, err := DealWarehouseRow(row, PMap)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Any("err", err))
			return
		}
		global.GVA_LOG.Debug("cp:", zap.Any("cp", cp))
		// 电话号码
		tel := strings.TrimSpace(row[1])
		if _, ok := OrderMap[tel]; !ok {
			// 第一次

			OrderMap[tel] = &CWOrder{
				OrderID: 0,
				CP:      []*CloudWareProduct{cp},
				Fr:      TelMap[tel],
			}
		} else {
			OrderMap[tel].CP = append(OrderMap[tel].CP, cp)
		}
	})

	global.GVA_LOG.Debug("OrderMap:", zap.Any("OrderMap", OrderMap))
	// 创建订单
	lo.ForEach(TelSlice, func(tel string, index int) {
		if _, ok := OrderMap[tel]; !ok {
			global.GVA_LOG.Fatal("OrderMap 数据异常", zap.Any("tel", tel))
			return
		}
		cwp := OrderMap[tel].CP
		pslice := []ordersReq.Product{}
		lo.ForEach(cwp, func(cp *CloudWareProduct, index int) {

			pslice = append(pslice, ordersReq.Product{
				ProductId: cp.ProductID,
				UnitPrice: cp.ProductPrice,
				Quantity:  cp.ProductInventory,
			})
		})

		// 构造订单请求
		req := ordersReq.CreateOrderRequest{
			FranchiseeId: OrderMap[tel].Fr.ID,
			PaymentType:  types.AccountBalance,
			OrderType:    types.SystemImport,
			OrderSubType: types.Presentation,
			Remark:       OrderRemark,
			AddressId:    nil,
			Products:     pslice,
		}
		err := service.ServiceGroupApp.OrdersServiceGroup.CreateImportOrder(&req, nil)
		if err != nil {
			global.GVA_LOG.Fatal("err:", zap.Any("err", err))
		}

	})
}

func GetFranchiseeMap(tel []string) (map[string]*franchiseeModel.Franchisee, error) {
	var franchiseeMap map[string]*franchiseeModel.Franchisee = make(map[string]*franchiseeModel.Franchisee)
	var franchisees []*franchiseeModel.Franchisee
	err := global.GVA_DB.Model(&franchiseeModel.Franchisee{}).Where("tel in ?", tel).Find(&franchisees).Error
	if err != nil {
		return nil, err
	}
	reTelSlice := lo.Map(franchisees, func(item *franchiseeModel.Franchisee, index int) string {
		return item.Tel
	})
	var NoneTel []string
	lo.ForEach(tel, func(item string, index int) {
		if _, ok := lo.Find(reTelSlice, func(t string) bool { return t == item }); !ok {
			NoneTel = append(NoneTel, item)
			global.GVA_LOG.Error("数据异常", zap.Any("item", item))
			return
		}
	})

	if len(NoneTel) > 0 {
		global.GVA_LOG.Fatal("数据异常", zap.Any("NoneTel", NoneTel))
		return nil, fmt.Errorf("数据异常")
	}

	if len(franchisees) != len(tel) {
		global.GVA_LOG.Fatal("数据异常", zap.Any("franchisees", franchisees), zap.Any("tel", tel), zap.Any("len(franchisees)", len(franchisees)), zap.Any("len(tel)", len(tel)))
		return nil, fmt.Errorf("数据异常")

	}
	franchiseeMap = lo.SliceToMap(franchisees, func(item *franchiseeModel.Franchisee) (string, *franchiseeModel.Franchisee) {
		return item.Tel, item
	})

	return franchiseeMap, nil
}

func GetProductMap(name []string) (map[string]*productsModel.Product, error) {
	var productMap map[string]*productsModel.Product = make(map[string]*productsModel.Product)
	var products []*productsModel.Product
	err := global.GVA_DB.Where("name in ?", name).Find(&products).Error
	if err != nil {
		return nil, err
	}
	var NoneProduct []string
	if len(products) != len(name) {
		lo.ForEach(name, func(item string, index int) {
			if _, ok := lo.Find(products, func(t *productsModel.Product) bool { return t.Name == item }); !ok {
				NoneProduct = append(NoneProduct, item)
				global.GVA_LOG.Error("数据异常", zap.Any("item", item))
				return
			}
		})
		if len(NoneProduct) > 0 {
			global.GVA_LOG.Fatal("数据异常", zap.Any("NoneProduct", NoneProduct))
			return nil, fmt.Errorf("数据异常")
		}
	}

	productMap = lo.SliceToMap(products, func(item *productsModel.Product) (string, *productsModel.Product) {
		return item.Name, item
	})

	return productMap, nil
}

func DealWarehouseRow(row []string, productMap map[string]*productsModel.Product) (*CloudWareProduct, error) {
	cp := &CloudWareProduct{}
	if pid, ok := productMap[strings.TrimSpace(row[3])]; ok {
		cp.ProductID = pid.ID
		cp.ProductName = pid.Name
		cp.Prod = pid
	} else {
		return nil, errors.New("product not found :" + strings.TrimSpace(row[3]))
	}
	if strings.TrimSpace(row[4]) != "" {
		x, err := strconv.ParseInt(strings.TrimSpace(row[4]), 10, 32)
		if err != nil {
			return nil, err
		}
		cp.ProductInventory = int(x)
	} else {
		return nil, errors.New("product inventory not found :" + strings.TrimSpace(row[4]))
	}
	// check price equal
	if strings.TrimSpace(row[5]) != "" {
		x, err := strconv.ParseInt(strings.TrimSpace(row[5]), 10, 32)
		if err != nil {
			return nil, err
		}
		cp.ProductPrice = int(x) * 100
		//if *cp.Prod.Cost == int(x)*100 {
		//	cp.ProductPrice = *cp.Prod.Cost
		//} else {
		//	return nil, errors.New("product price not equal :" + strings.TrimSpace(row[3]))
		//}
	} else {
		return nil, errors.New("product price not found :" + strings.TrimSpace(row[3]))
	}
	return cp, nil
}

/**
truncate :
delete from `order` where id > 792
delete from order_goods where order_id > 792
delete from cloud_warehouse where order_no in (select order_no from `order` where id > 792)
delete from cloud_warehouse_record where trade_no in (select order_no from `order` where id > 792)
delete from sub_cloud_warehouse where cloud_warehouse_id in (select id from cloud_warehouse where order_no in (select order_no from `order` where id > 792))

*/
