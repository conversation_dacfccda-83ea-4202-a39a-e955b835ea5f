package main

import (
	"fmt"
	"strconv"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/xuri/excelize/v2"
)

func main() {
	franchiseeFile, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/客户导入模板2023.12.19最终版.xlsx")
	if err != nil {
		fmt.Println("err:", err)
	}
	defer func() {
		if err := franchiseeFile.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()
	rows, err := franchiseeFile.GetRows("Sheet1")
	if err != nil {
		fmt.Println("err:", err)
	}

	fmt.Println("rows:", rows[0][0], rows[1][0], rows[1][15])
	valStyle, err := franchiseeFile.NewStyle(&excelize.Style{
		NumFmt: 0,
	})
	if err != nil {
		fmt.Println("err:", err)
	}
	franchiseeFile.SetCellStyle("Sheet1", "P2", "P2", valStyle)
	x, err := franchiseeFile.GetCellValue("Sheet1", "P2")
	if err != nil {
		fmt.Println("err:", err)
	}
	xfloat, err := strconv.ParseFloat(x, 64)

	y, err := excelize.ExcelDateToTime(xfloat, false)
	if err == nil {
		fmt.Println("y:", y.Local().Add(-8*time.Hour))
	}

	v := &franchiseeModel.Franchisee{
		GVA_MODEL: global.GVA_MODEL{
			CreatedAt: y,
		},
		Name:     rows[1][3],
		Tel:      rows[1][4],
		Province: rows[1][11],
		City:     rows[1][12],
		County:   rows[1][13],
		Address:  rows[1][11] + rows[1][12] + rows[1][13],
		Linkman:  rows[1][3],
		Remark:   rows[1][16],
	}

	fmt.Println("v:", v)
}
