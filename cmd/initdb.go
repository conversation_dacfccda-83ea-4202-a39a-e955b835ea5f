package main

import (
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/service"
	"go.uber.org/zap"
)

func main() {
	// 定义命令行参数
	dbType := flag.String("type", "mysql", "数据库类型 (mysql/pgsql)")
	username := flag.String("username", "", "数据库用户名")
	password := flag.String("password", "", "数据库密码")
	host := flag.String("host", "127.0.0.1", "数据库主机地址")
	port := flag.String("port", "", "数据库端口")
	dbName := flag.String("dbname", "", "数据库名称")
	// autoCreate := flag.Bool("auto-create", true, "是否自动创建数据库 (默认 true)")
	configPath := flag.String("config", "", "配置文件路径")

	flag.Parse()

	// 验证数据库类型
	*dbType = strings.ToLower(*dbType)
	if *dbType != "mysql" && *dbType != "pgsql" {
		fmt.Println("错误: 数据库类型必须是 mysql 或 pgsql")
		flag.Usage()
		os.Exit(1)
	}

	// 验证必填参数
	if *username == "" || *password == "" || *port == "" || *dbName == "" {
		fmt.Println("错误: 必须提供数据库用户名、密码、端口和数据库名")
		flag.Usage()
		os.Exit(1)
	}

	// 初始化配置
	if *configPath != "" {
		global.GVA_VP = core.Viper(*configPath) // Pass the config path to the existing Viper function
	} else {
		global.GVA_VP = core.Viper()
	}

	if global.GVA_VP == nil {
		fmt.Println("初始化配置失败")
		os.Exit(1)
	}

	// 初始化日志
	global.GVA_LOG = core.Zap()
	if global.GVA_LOG == nil {
		fmt.Println("初始化日志失败")
		os.Exit(1)
	}
	zap.ReplaceGlobals(global.GVA_LOG)

	// 初始化其他组件
	initialize.OtherInit()

	// 构建初始化数据库请求
	initDBReq := request.InitDB{
		DBType:   *dbType,
		Host:     *host,
		Port:     *port,
		UserName: *username,
		Password: *password,
		DBName:   *dbName,
		// AutoCreate: *autoCreate,
	}

	// 执行数据库初始化
	fmt.Printf("开始初始化 %s 数据库: %s\n", *dbType, *dbName)

	var initDBService = service.ServiceGroupApp.SystemServiceGroup.InitDBService
	if err := initDBService.InitDB(initDBReq); err != nil {
		global.GVA_LOG.Error("初始化数据库失败", zap.Error(err))
		fmt.Printf("初始化数据库失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("数据库初始化成功!")
}
