package main

import (
	"fmt"
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"strings"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()

	one_coin_file, err := excelize.OpenFile("/Users/<USER>/Work/osqianxing/guanpu进销存.需求/数据导入/one_coin_balance.xlsx")
	if err != nil {
		global.GVA_LOG.Fatal("err:", zap.Any("err", err))
	}

	defer func() {
		if err := one_coin_file.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()

	rows, err := one_coin_file.GetRows("Sheet1")
	var TelSlice []string
	lo.ForEach(rows, func(row []string, index int) {
		if index != 0 {
			TelSlice = append(TelSlice, strings.TrimSpace(row[1]))
		}
	})
	global.GVA_LOG.Info("TelSlice:", zap.Any("TelSlice", TelSlice))
}
