package main

import (
	"fmt"
	"strconv"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

const (
	IMPORT_FILE = "/Users/<USER>/Work/osqianxing/guanpu/2024年5-10月运费补贴--导入充值积分账户20241104-1.xlsx"
)

var rechargeRecordService = service.ServiceGroupApp.FranchiseesServiceGroup.RechargeRecordService

func main() {
	// 初始化数据库连接
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()
	// 打开 Excel 文件
	f, err := excelize.OpenFile(IMPORT_FILE)
	if err != nil {
		global.GVA_LOG.Error("打开文件失败", zap.Error(err))
		return
	}
	defer f.Close()

	// 获取第一个 sheet 的所有行
	rows, err := f.GetRows(f.GetSheetName(0))
	if err != nil {
		global.GVA_LOG.Error("读取文件失败", zap.Error(err))
		return
	}
	// 预检查所有电话号码
	telList := make([]string, 0, len(rows)-1)
	for i, row := range rows {
		if i == 0 {
			continue // 跳过标题行
		}
		telList = append(telList, row[0])
	}

	if err := validateTelList(telList); err != nil {
		global.GVA_LOG.Error("电话号码验证失败", zap.Error(err))
		return
	}

	// 遍历每一行（跳过标题行）
	for _, row := range rows {
		// if i == 0 {
		// 	continue // 跳过标题行
		// }

		tel := row[0]
		pointsToAdd, err := strconv.ParseInt(row[1], 10, 64)
		if err != nil {
			global.GVA_LOG.Error("解析积分失败", zap.String("tel", tel), zap.Error(err))
			continue
		}

		// 更新加盟商积分
		err = rechargeFranchiseePoints(tel, pointsToAdd)
		if err != nil {
			global.GVA_LOG.Error("充值加盟商积分失败", zap.String("tel", tel), zap.Error(err))
		}
	}

	global.GVA_LOG.Info("积分导入完成")
}

func validateTelList(telList []string) error {
	var count int64
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Where("tel IN ?", telList).
		Count(&count).Error
	if err != nil {
		return fmt.Errorf("查询数据库失败: %v", err)
	}

	if count != int64(len(telList)) {
		return fmt.Errorf("电话号码验证失败: 数据库中找到 %d 个匹配项，但 Excel 中有 %d 个电话号码", count, len(telList))
	}

	// 检查是否有重复的电话号码
	uniqueTels := make(map[string]bool)
	for _, tel := range telList {
		if uniqueTels[tel] {
			return fmt.Errorf("发现重复的电话号码: %s", tel)
		}
		uniqueTels[tel] = true
	}

	return nil
}

func rechargeFranchiseePoints(tel string, pointsToAdd int64) error {
	var franchisee franchisees.Franchisee
	err := global.GVA_DB.Where("tel = ?", tel).First(&franchisee).Error
	if err != nil {
		return fmt.Errorf("未找到该加盟商: %s", tel)
	}

	recharge := &franchiseesReq.Recharge{
		FranchiseeId: franchisee.ID,
		Type:         types.RechargePoints,
		AccountType:  types.GeneralAccount,
		Amount:       int(pointsToAdd),
		Remark:       "2024年5-10月运费补贴",
	}

	// 使用 RechargeRecordService 的 Recharge 方法
	err = rechargeRecordService.Recharge(recharge, 0) // 假设 operatorID 为 0，表示系统操作
	if err != nil {
		return fmt.Errorf("充值失败: %v", err)
	}

	global.GVA_LOG.Info("充值加盟商积分成功",
		zap.String("tel", tel),
		zap.Int64("addedPoints", pointsToAdd))

	return nil
}
