package main

import (
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	//var sg = service.ServiceGroupApp
	//ps, err := sg.FinanceServiceGroup.GetPerformanceForFranchisees([]uint{22, 38, 39, 86, 88, 93, 99, 90, 80}, []int{29, 888, 28, 29}, time.Now().AddDate(0, -2, 0), time.Now())
	//if err != nil {
	//	fmt.Println("err:", err)
	//}
	//jps, err := json.Marshal(ps)
	//fmt.Println("ps:", string(jps))

	//psToday, err := sg.FinanceServiceGroup.GetPerformanceForFranchiseeRealTimeToday(22)
	//jpsToday, err := json.Marshal(psToday)
	//fmt.Println("psToday:", string(jpsToday))
	//x, e := dao.SpecialMall.GetSpecialMallIDsByFCategoryId(34)
	//js, _ := json.Marshal(x)
	//fmt.Println("x:", string(js), e)
	//ps, err := sg.FinanceServiceGroup.GetFranchiseeMemberPerformanceSummary(22)
	//if err != nil {
	//	fmt.Println("err:", err)
	//}
	//jps, err := json.Marshal(ps)
	//fmt.Println("ps:", string(jps))

	//psList, err := sg.FinanceServiceGroup.GetPerformanceForFranchiseesListThisMonth([]uint{22, 37, 38, 39, 86, 88, 93, 99, 90, 80}, []int{29, 888, 28, 29})
	//if err != nil {
	//	fmt.Println("err:", err)
	//}
	//jpsList, err := json.Marshal(psList)
	//fmt.Println("jpsList:", string(jpsList))

	// frCategroyName, err := dao.Franchisee.GetCategoryNameByFranchiseeId(22)
	// fmt.Println("frCategroyName:", frCategroyName, err)
}
