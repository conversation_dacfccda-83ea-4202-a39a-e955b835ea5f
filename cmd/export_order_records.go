package main

import (
	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/app"
	"github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/OSQianXing/guanpu-server/model/orders/response"
	productsModel "github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"go.uber.org/zap"
)

type ExpordOrderRecord struct {
	OrderNo         string                 `gorm:"column:order_no"`
	AddressSnapshot *app.FranchiseeAddress `gorm:"column:address_snapshot"`
	OrderTime       string                 `gorm:"column:created_time"`
	StockingTime    string                 `gorm:"column:stocking_time"`
	DeliveryTime    string                 `gorm:"column:delivery_time"`
	Goods           *orders.OrderGoods     `gorm:"-"`
	Product         *productsModel.Product `gorm:"-"`
}

// select o.*,p.name,og.product_id ,og.unit_price,og.points_price,og.quantity,og.points_quantity from `order` as o left join order_goods as og on og.order_id = o.id left join product as p on p.id = og.product_id where o.id =346;
const LastTime = "2024-01-14 23:59:59"
const path = "/orderDelivery/batchConfirmDelivery"

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	var orderDeliverys []*response.ExcelOrderDelivery
	db := global.GVA_DB.Table("order_delivery as od")
	err := db.Select("od.delivery_no, od.status,o.order_no, o.payment_type, f.name as franchisee_name, u.username as franchisee_account," +
		"od.goods_detail, o.address_snapshot, od.remark, o.created_at as order_time, " +
		"od.created_at as stocking_time," +
		"CASE od.status WHEN 4 THEN od.updated_at ELSE NULL  END as delivery_time," +
		"CASE od.status WHEN 3 THEN od.updated_at ELSE NULL  END as receive_time," +
		" od.logistics_no,od.goods_detail," +
		"lt.express_company,lt.linkman,lt.tel").
		Joins("left join `" + orders.Order{}.TableName() + "` as o on o.order_no = od.order_no").
		Joins("left join franchisee as f on f.id = od.franchisee_id").
		Joins("left join sys_users as u on u.id = f.user_id").
		Joins("left join logistics_template as lt on lt.id = od.logistics_id").
		Find(&orderDeliverys).Error
	if err != nil {
		global.GVA_LOG.Error("err", zap.Any("err", err))
	}
	for i, orderDelivery := range orderDeliverys {
		if orderDelivery.Status != types.DeliveryStatusStocking {
			var sor []system.SysOperationRecord
			err := global.GVA_DB.Model(system.SysOperationRecord{}).Where("path = ?", path).
				Where("body like ?", "%"+orderDelivery.DeliveryNo+"%").Find(&sor).Error
			if err != nil {
				global.GVA_LOG.Error("err", zap.Any("err", err))

			}
			if len(sor) > 0 {
				orderDeliverys[i].DeliveryTime = sor[0].CreatedAt
			} else {
				//orderDeliverys[i].DeliveryTime = LastTime
			}
		}
		ogIDs := orderDelivery.GoodsDetail.GetGoodsIds()
		err := global.GVA_DB.Table("order_goods as og").
			Joins("left join product as p on p.id = og.product_id").
			Where("og.id in ?", ogIDs).
			Select("og.id as goods_id,p.name as product_name,p.special_mall_id ," +
				"p.id as product_id,p.is_combination, if(og.quantity>0,og.quantity,og.points_quantity) as purchase_quantity ").
			Find(&orderDeliverys[i].ExportProductDetail).Error
		if err != nil {
			global.GVA_LOG.Error("err", zap.Any("err", err))
		}

		for _, productDetail := range orderDeliverys[i].ExportProductDetail {
			var specialMallName string
			err = global.GVA_DB.Where("id = ?", productDetail.SpecialMallID).Model(&productsModel.SpecialMall{}).Select("name as specail_mall_name").Scan(&specialMallName).Error
			if err != nil {
				global.GVA_LOG.Error("err", zap.Any("err", err))
			}
			productDetail.SpecailMallName = specialMallName
			if !productDetail.IsCombination {
				continue
			}
			// 组合商品信息
			combination, err := dao.Product.GetCombinationProductDetial(productDetail.ProductID)
			if err != nil {
				global.GVA_LOG.Error("err", zap.Any("err", err))
			}
			productDetail.CombinationDetail = combination
		}
	}
	global.GVA_LOG.Info("orderDeliverys", zap.Any("orderDeliverys", orderDeliverys))
	//return orderDeliveryService.ExportExcel(orderDeliverys)

	f, err := service.ServiceGroupApp.OrdersServiceGroup.OrderDeliveryService.ExportExcel(orderDeliverys)
	f.SaveAs("订单发货记录.xlsx")
}
