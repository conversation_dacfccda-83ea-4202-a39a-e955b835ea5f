package main

import (
	"fmt"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/model/orders"
	productsModel "github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

type ExpordOrderRecord struct {
	OrderNo      string
	Reciver      string
	ReciverTel   string
	OrderTime    string
	StockingTime string
	DeliveryTime string
	Goods        *orders.OrderGoods
	Product      *productsModel.Product
}

const LastTime = "2024-01-14 23:59:59"

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}

	path := "/orderDelivery/batchConfirmDelivery"
	var ods []orders.OrderDelivery
	err := global.GVA_DB.Model(orders.OrderDelivery{}).
		Where("status not in (?, ?,?)", types.DeliveryRefund, types.DeliveryStatusReturned, types.DeliveryStatusPartReturned).
		Where("created_at <? ", LastTime).
		Find(&ods).Error
	if err != nil {
		global.GVA_LOG.Error("find order delivery err:", zap.Any("err", err))
	}
	var ExpordOrderRecords []ExpordOrderRecord
	for _, v := range ods {
		//var orr []orders.OrderRecord
		var sor []system.SysOperationRecord
		err := global.GVA_DB.Model(&system.SysOperationRecord{}).
			Where("path = ?", path).
			Where("body like ?", "%"+v.DeliveryNo+"%").
			Find(&sor).Error
		if err != nil {
			global.GVA_LOG.Error("find operation record err:", zap.Any("err", err))
		}
		if len(sor) == 0 {
			// 未确认发货
			continue
		}
		var o *orders.Order
		err = global.GVA_DB.Model(orders.Order{}).Where("order_no = ?", v.OrderNo).First(&o).Error
		if err != nil {
			global.GVA_LOG.Error("find order err:", zap.Any("err", err))
		}
		orderRecord := ExpordOrderRecord{
			OrderNo:      o.OrderNo,
			Reciver:      o.AddressSnapshot.Consignee,
			ReciverTel:   o.AddressSnapshot.Tel,
			OrderTime:    o.CreatedAt.Format("2006-01-02 15:04:05"),
			StockingTime: v.CreatedAt.Format("2006-01-02 15:04:05"),
			DeliveryTime: sor[0].CreatedAt.Format("2006-01-02 15:04:05"),
		}
		ExpordOrderRecords = append(ExpordOrderRecords, orderRecord)
		//
		//创建两条记录：1，订单记录 2，备货记录 3，确认发货记录 4，确认收货记录
		//orr = append(orr, orders.OrderRecord{
		//	OrderID:     o.ID,
		//	OrderNo:     o.OrderNo,
		//	OrderStatus: types.OrderAwaitingDelivery,
		//	Remark:      "",
		//	GVA_MODEL: global.GVA_MODEL{
		//		CreatedAt: o.CreatedAt, //订单创建时间
		//	},
		//	//
		//})
		//orr = append(orr, orders.OrderRecord{
		//	OrderID:     o.ID,
		//	OrderNo:     o.OrderNo,
		//	OrderStatus: types.OrderStocking,
		//	Remark:      "",
		//	GVA_MODEL: global.GVA_MODEL{
		//		CreatedAt: v.CreatedAt, // 发货单创建时间
		//	},
		//})
		//if len(sor) > 0 {
		//	orr = append(orr, orders.OrderRecord{
		//		OrderID:     o.ID,
		//		OrderNo:     o.OrderNo,
		//		OrderStatus: types.OrderDelivery,
		//		OperatorID:  uint(sor[0].UserID),
		//		Remark:      "",
		//		GVA_MODEL: global.GVA_MODEL{
		//			CreatedAt: sor[0].CreatedAt, // 批量发货请求时间
		//		},
		//	})
		//}
		// 终态,可继续深化，对于退货退款记录，需查找 system_operation_records 找到对应的请求人和请求时间
		//if o.Status == types.OrderCompleted || o.Status == types.OrderReturn || o.Status == types.OrderRefund || o.Status == types.OrderCloudReturn {
		//	orr = append(orr, orders.OrderRecord{
		//		OrderID:     o.ID,
		//		OrderNo:     o.OrderNo,
		//		OrderStatus: o.Status,
		//		Remark:      "",
		//		GVA_MODEL: global.GVA_MODEL{
		//			CreatedAt: o.UpdatedAt, // 订单最后一次更新时间
		//		},
		//	})
		//}
		//
		//if len(orr) > 0 {
		//	if err := global.GVA_DB.Create(&orr).Error; err != nil {
		//		global.GVA_LOG.Error("create order record err:", zap.Any("err", err))
		//	}
		//}
		f := excelize.NewFile()

		//sheet1
		_, err = f.NewSheet("Sheet1")
		if err != nil {
			global.GVA_LOG.Error("new sheet err:", zap.Any("err", err))
		}
		f.SetCellValue("Sheet1", "A1", "订单号")
		f.SetCellValue("Sheet1", "B1", "收货人")
		f.SetCellValue("Sheet1", "C1", "收货人电话")
		f.SetCellValue("Sheet1", "D1", "下单时间")
		f.SetCellValue("Sheet1", "E1", "备货时间")
		f.SetCellValue("Sheet1", "F1", "发货时间")

		for k, v := range ExpordOrderRecords {
			f.SetCellValue("Sheet1", fmt.Sprintf("A%d", k+2), v.OrderNo)
			f.SetCellValue("Sheet1", fmt.Sprintf("B%d", k+2), v.Reciver)
			f.SetCellValue("Sheet1", fmt.Sprintf("C%d", k+2), v.ReciverTel)
			f.SetCellValue("Sheet1", fmt.Sprintf("D%d", k+2), v.OrderTime)
			f.SetCellValue("Sheet1", fmt.Sprintf("E%d", k+2), v.StockingTime)
			f.SetCellValue("Sheet1", fmt.Sprintf("F%d", k+2), v.DeliveryTime)
		}

		if err := f.SaveAs("test.xlsx"); err != nil {
			global.GVA_LOG.Error("save excel err:", zap.Any("err", err))
		}
	}

}
