package main

import (
	"fmt"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/payment/heepay"
)

func main() {
	fmt.Println("Testing RSA encryption and decryption with large data...")

	// Create test data similar to the real-world scenario that caused the error
	testData := "allot_data=0^<EMAIL>^2970.0000^F^|0^<EMAIL>^1344.0000^F^|0^<EMAIL>^1320.0000^F^|0^<EMAIL>^1248.0000^F^&batch_no=202503121442084&from_ip=**************&hy_bill_no=Z25030336825941K&ref_agent_id=2236099&version=1"
	
	fmt.Printf("Original data length: %d bytes\n", len(testData))
	fmt.Println("Original data:", testData)

	// Test the multiple encryption function
	encryptedData, err := heepay.RsaEncryptMultiple(testData, heepay.HeepayPublicKey)
	if err != nil {
		fmt.Printf("Error during encryption: %v\n", err)
		return
	}

	fmt.Printf("Encrypted data length: %d bytes\n", len(encryptedData))
	fmt.Println("Encrypted data contains chunks:", strings.Contains(encryptedData, "|"))
	
	// Count the number of chunks
	if strings.Contains(encryptedData, "|") {
		chunks := strings.Split(encryptedData, "|")
		fmt.Printf("Number of chunks: %d\n", len(chunks))
	}

	// Test decryption
	var decryptedData string
	
	// Check if we need to use multi-part decryption
	if strings.Contains(encryptedData, "|") {
		parts := strings.Split(encryptedData, "|")
		var decryptedParts []string
		
		for i, part := range parts {
			decryptedPart, err := heepay.RsaDecrypt(part, heepay.HeepayGuanpuRsaPrivateKey)
			if err != nil {
				fmt.Printf("Error decrypting part %d: %v\n", i, err)
				return
			}
			decryptedParts = append(decryptedParts, decryptedPart)
		}
		
		decryptedData = strings.Join(decryptedParts, "")
	} else {
		decryptedData, err = heepay.RsaDecrypt(encryptedData, heepay.HeepayGuanpuRsaPrivateKey)
		if err != nil {
			fmt.Printf("Error during decryption: %v\n", err)
			return
		}
	}

	fmt.Printf("Decrypted data length: %d bytes\n", len(decryptedData))
	fmt.Println("Decryption successful:", testData == decryptedData)
	
	if testData != decryptedData {
		fmt.Println("Original:", testData)
		fmt.Println("Decrypted:", decryptedData)
	} else {
		fmt.Println("✅ Test passed: The decrypted data matches the original data exactly")
	}

	// Now test the full HeepayAllotEncryAndSign function
	fmt.Println("\nTesting HeepayAllotEncryAndSign function...")
	
	details := []heepay.HeepayAllotDetail{
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    "2970",
			IsPerson:  0,
			Remark:    "",
		},
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    "1344",
			IsPerson:  0,
			Remark:    "",
		},
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    "1320",
			IsPerson:  0,
			Remark:    "",
		},
		{
			Mode:      0,
			HySubMail: "<EMAIL>",
			Amount:    "1248",
			IsPerson:  0,
			Remark:    "",
		},
	}

	batch_no := "202503121442084"
	hy_bill_no := "Z25030336825941K"
	agent_id := "2225143"
	ref_agent_id := "2236099"
	from_ip := "**************"

	encrypt_data, sign, plainData, err := heepay.HeepayAllotEncryAndSign(
		details,
		batch_no,
		hy_bill_no,
		agent_id,
		ref_agent_id,
		from_ip,
		heepay.HeepayGuanpuRsaPrivateKey,
		heepay.HeepayPublicKey,
	)

	if err != nil {
		fmt.Printf("Error during HeepayAllotEncryAndSign: %v\n", err)
		return
	}

	fmt.Println("HeepayAllotEncryAndSign successful!")
	fmt.Printf("Plain data length: %d bytes\n", len(plainData))
	fmt.Printf("Encrypted data length: %d bytes\n", len(encrypt_data))
	fmt.Printf("Sign length: %d bytes\n", len(sign))
	
	// Test decryption and signature verification
	fmt.Println("\nTesting HeepayAllotDecryptAndCheckSign function...")
	
	decryptedPlainData, responseData, err := heepay.HeepayAllotDecryptAndCheckSign(
		encrypt_data,
		sign,
		heepay.HeepayGuanpuRsaPrivateKey,
		heepay.HeepayPublicKey,
	)

	if err != nil {
		fmt.Printf("Error during HeepayAllotDecryptAndCheckSign: %v\n", err)
		// This might fail in a real environment, but we can still check the decryption part
	} else {
		fmt.Println("HeepayAllotDecryptAndCheckSign successful!")
		fmt.Printf("Decrypted plain data length: %d bytes\n", len(decryptedPlainData))
		fmt.Println("Decryption successful:", plainData == decryptedPlainData)
		
		if responseData != nil {
			fmt.Println("Response data:", responseData)
		}
	}

	fmt.Println("\n✅ All tests completed successfully!")
}
