package main

import (
	"math"
	"time"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/samber/lo"

	"go.uber.org/zap"
)

func main() {
	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()

	}
	var topMemberIDs []uint
	err := global.GVA_DB.Model(franchiseeModel.Franchisee{}).
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id and sys_users.enable = ?", 1).
		Where("franchisee.id != franchisee.inviter_id").
		Where("franchisee.inviter_id = 0").
		Pluck("franchisee.id", &topMemberIDs).Error
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetTopFranchisee", zap.Error(err))
	}
	global.GVA_LOG.Info("topMemberIDs:", zap.Any("topMemberIDs", topMemberIDs))
	// insert into franchisee_member_link topMemberIDs
	var link []franchiseeModel.FranchiseeMemberLink = make([]franchiseeModel.FranchiseeMemberLink, 0, len(topMemberIDs))
	for k := range topMemberIDs {
		link = append(link, franchiseeModel.FranchiseeMemberLink{FranchiseeId: 0, MemberFranchiseeId: topMemberIDs[k], MemberInviterId: 0})
	}
	err = global.GVA_DB.Create(&link).Error
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetTopFranchisee", zap.Error(err))
	}

	time.Sleep(20 * time.Second)

	lo.ForEach(topMemberIDs, func(item uint, index int) {

		global.GVA_LOG.Info("topMemberIDs:", zap.Any("item", item))
		member, err := LoopGetMemberIds(item)
		if err != nil {
			global.GVA_LOG.Error("Error in service.GetTopFranchisee", zap.Error(err))
		}

		global.GVA_LOG.Info("member:", zap.Any("member", member))
		// insert into franchisee_member_link
	})

	err = global.GVA_DB.Raw("update franchisee_member_link as fml ,franchisee as f set fml.member_inviter_id=f.inviter_id where fml.franchisee_id=f.id and fml.member_inviter_id=4294967295;").Error
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetTopFranchisee", zap.Error(err))
	}

}

func LoopGetMemberIds(franchiseeId uint) ([]uint, error) {
	var idList []uint
	directMemberIds, err := service.ServiceGroupApp.FranchiseesServiceGroup.GetDirectMemberIds(franchiseeId)
	if err != nil {
		return nil, err
	}

	if len(directMemberIds) == 0 {
		return idList, nil
	}
	err = BatchAddMemberLink(franchiseeId, directMemberIds, franchiseeId)
	if err != nil {
		global.GVA_LOG.Error("Error in service.FranchiseeService.LoopGetMemberIds", zap.Error(err))
		return nil, err
	}
	idList = append(idList, directMemberIds...)

	for k := range directMemberIds {
		memberIds, err := LoopGetMemberIds(directMemberIds[k])
		if err != nil {
			return nil, err
		}
		global.GVA_LOG.Debug("memberIds:", zap.Any("memberIds", memberIds))
		if len(memberIds) == 0 {
			continue
		}
		err = BatchAddMemberLink(franchiseeId, memberIds, math.MaxUint)
		if err != nil {
			global.GVA_LOG.Error("Error in service.FranchiseeService.LoopGetMemberIds", zap.Error(err))
			return nil, err
		}
		idList = append(idList, memberIds...)
	}
	return idList, nil
}

// BatchAddMemberLink
func BatchAddMemberLink(franchiseeId uint, memberIds []uint, inviterId uint) error {
	var link []franchiseeModel.FranchiseeMemberLink
	for k := range memberIds {
		link = append(link, franchiseeModel.FranchiseeMemberLink{FranchiseeId: franchiseeId, MemberFranchiseeId: memberIds[k], MemberInviterId: inviterId})
	}

	err := global.GVA_DB.Create(&link).Error
	if err != nil {
		global.GVA_LOG.Error("Error in service.FranchiseeService.BatchAddMemberLink", zap.Error(err))
		return err
	}
	return nil
}
