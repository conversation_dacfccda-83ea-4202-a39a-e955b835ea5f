package main

import (
	"fmt"
	"strconv"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	franchiseeReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	"github.com/OSQianXing/guanpu-server/service"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	IMPORTFILE = "/Users/<USER>/Work/osqianxing/guanpu进销存.需求/202405_import_user_primage.xlsx"
)

type UserPrimage struct {
	UserTel string
	Primage int
	Remark  string
}

type UserPrimageList struct {
	UserPrimage  []UserPrimage
	TotalPrimage int
	TotalUser    int
}

func main() {

	global.GVA_VP = core.Viper() // 初始化Viper
	initialize.OtherInit()
	global.GVA_LOG = core.Zap() // 初始化zap日志库
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initialize.Gorm() // gorm连接数据库
	initialize.DBList()
	if global.GVA_DB != nil {
		// initialize.RegisterTables() // 初始化表
		// 程序结束前关闭数据库链接
		db, _ := global.GVA_DB.DB()
		defer db.Close()
	}
	initialize.Redis()
	var sg = service.ServiceGroupApp

	// 读取文件
	upl, err := OpenAndReadExcel(IMPORTFILE)
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return
	}
	global.GVA_LOG.Info("upl.TotalPrimage:", zap.Any("upl.TotalPrimage", upl.TotalPrimage), zap.Any("upl.TotalUser", upl.TotalUser))
	// check user exist
	for _, v := range upl.UserPrimage {
		f, err := sg.FranchiseesServiceGroup.FranchiseeService.GetFranchiseeByTel(v.UserTel)
		if err != nil {
			global.GVA_LOG.Error("err:", zap.Any("err", err))
			return
		}
		if f.ID == 0 {
			global.GVA_LOG.Error("err:", zap.Any("err", err))
			return
		}
	}
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, v := range upl.UserPrimage {
			if v.Primage == 0 {
				continue
			}
			global.GVA_LOG.Info("v.UserTel:", zap.Any("v.UserTel", v.UserTel), zap.Any("v.Primage", v.Primage), zap.Any("v.Remark", v.Remark))
			f, err := sg.FranchiseesServiceGroup.FranchiseeService.GetFranchiseeByTel(v.UserTel)
			if err != nil {
				global.GVA_LOG.Error("err:", zap.Any("err", err))
				return err
			}
			err = sg.FranchiseesServiceGroup.RechargeRecordService.RechargeWithinTx(tx, &franchiseeReq.Recharge{FranchiseeId: f.ID, Type: types.RechargePoints, AccountType: types.GeneralAccount, AccountId: uint(0), Amount: v.Primage, Remark: v.Remark}, 0)
			if err != nil {
				global.GVA_LOG.Error("err:", zap.Any("err", err))
				return err
			}
		}
		return nil
	})
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return
	}

}

func OpenAndReadExcel(f string) (UserPrimageList, error) {

	file, err := excelize.OpenFile(f)
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return UserPrimageList{}, err
	}
	defer func() {
		if err := file.Close(); err != nil {
			fmt.Println("err:", err)
		}
	}()

	rows, err := file.GetRows("Sheet1")
	if err != nil {
		global.GVA_LOG.Error("err:", zap.Any("err", err))
		return UserPrimageList{}, err
	}
	var upl UserPrimageList
	upl.UserPrimage = make([]UserPrimage, 0)

	for index, row := range rows {
		if index == 0 {
			continue
		}
		p, err := strconv.Atoi(row[4])
		if err != nil {
			global.GVA_LOG.Error("err:", zap.Any("err", err))
			return UserPrimageList{}, err
		}

		upl.UserPrimage = append(upl.UserPrimage, UserPrimage{
			UserTel: row[2],
			Primage: p,
			Remark:  row[5],
		})
		upl.TotalUser += 1
		upl.TotalPrimage += p
	}

	return upl, nil
}
