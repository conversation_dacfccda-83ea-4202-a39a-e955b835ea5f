package middleware

import (
	"fmt"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// UnifiedTenantMiddleware 统一租户中间件
// 根据配置自动选择单机或分布式模式
func UnifiedTenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// 记录请求监控指标
		defer func() {
			responseTime := time.Since(startTime)
			global.RecordTenantRequest(responseTime)
		}()

		// 根据配置选择处理模式
		if global.IsDistributedMode() {
			handleDistributedMode(c)
		} else {
			handleStandaloneMode(c)
		}
	}
}

// handleStandaloneMode 处理单机模式
func handleStandaloneMode(c *gin.Context) {
	// 设置清理逻辑
	defer func() {
		global.ClearCurrentTenantID()
	}()

	// 获取和验证Claims
	claims, err := getAndValidateClaims(c)
	if err != nil {
		return // 错误已在函数内处理
	}

	// 验证租户有效性（使用带降级的验证）
	if !global.ValidateTenantWithFallback(claims.TenantID) {
		global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", claims.TenantID))

		// 记录监控指标
		global.RecordTenantValidationFailure()

		// 记录安全事件：无效租户ID访问尝试
		global.GetSecurityAuditor().RecordSecurityEvent(global.SecurityEvent{
			Type:     global.EventInvalidTenantID,
			UserID:   claims.BaseClaims.ID,
			TenantID: claims.TenantID,
			IP:       c.ClientIP(),
			Path:     c.FullPath(),
			Method:   c.Request.Method,
			Message:  "尝试使用无效的租户ID访问系统",
			Severity: global.SeverityHigh,
		})

		response.FailWithMessage("租户无效", c)
		c.Abort()
		return
	}

	// 设置租户上下文
	setupTenantContext(c, claims, "")

	c.Next()
}

// handleDistributedMode 处理分布式模式
func handleDistributedMode(c *gin.Context) {
	// 生成或获取会话ID
	sessionID := c.GetHeader("X-Session-ID")
	if sessionID == "" {
		sessionID = uuid.New().String()
		c.Header("X-Session-ID", sessionID)
	}

	// 设置清理逻辑
	defer func() {
		if global.IsDistributedMode() {
			// 分布式模式：可选择立即清理或让Redis自动过期
			// global.ClearTenantSession(c.Request.Context(), sessionID)
		} else {
			// 降级到单机模式：清理本地状态
			global.ClearCurrentTenantID()
		}
	}()

	// 获取和验证Claims
	claims, err := getAndValidateClaims(c)
	if err != nil {
		return // 错误已在函数内处理
	}

	// 验证租户有效性（分布式模式，使用带降级的验证）
	var isValid bool
	if global.IsDistributedCacheEnabled() {
		isValid = global.ValidateTenantDistributed(c.Request.Context(), claims.TenantID)
	} else {
		isValid = global.ValidateTenantWithFallback(claims.TenantID)
	}

	if !isValid {
		global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", claims.TenantID))

		// 记录安全事件：分布式模式下无效租户ID访问尝试
		global.GetSecurityAuditor().RecordSecurityEvent(global.SecurityEvent{
			Type:     global.EventInvalidTenantID,
			UserID:   claims.BaseClaims.ID,
			TenantID: claims.TenantID,
			IP:       c.ClientIP(),
			Path:     c.FullPath(),
			Method:   c.Request.Method,
			Message:  "分布式模式下尝试使用无效的租户ID访问系统",
			Details: map[string]interface{}{
				"distributed_mode": true,
				"session_id":       sessionID,
			},
			Severity: global.SeverityHigh,
		})

		response.FailWithMessage("租户无效", c)
		c.Abort()
		return
	}

	// 设置分布式租户会话
	if global.IsDistributedMode() {
		err := global.SetTenantSession(c.Request.Context(), sessionID, claims.TenantID, claims.BaseClaims.ID)
		if err != nil {
			global.GVA_LOG.Warn("设置分布式租户会话失败", zap.Error(err))
			// 不中断请求，降级到本地模式
		}
	}

	// 设置租户上下文
	setupTenantContext(c, claims, sessionID)

	c.Next()
}

// getAndValidateClaims 获取和验证用户Claims
func getAndValidateClaims(c *gin.Context) (*systemReq.CustomClaims, error) {
	claims, exists := c.Get("claims")
	if !exists {
		response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
		c.Abort()
		return nil, fmt.Errorf("claims not found")
	}

	customClaims, ok := claims.(*systemReq.CustomClaims)
	if !ok {
		response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
		c.Abort()
		return nil, fmt.Errorf("invalid claims format")
	}

	return customClaims, nil
}

// setupTenantContext 设置租户上下文
func setupTenantContext(c *gin.Context, claims *systemReq.CustomClaims, sessionID string) {
	// 设置基本上下文变量
	c.Set("tenantId", claims.TenantID)
	c.Set("userId", claims.BaseClaims.ID)

	if sessionID != "" {
		c.Set("sessionId", sessionID)
	}

	// 创建带租户信息的上下文
	ctx := global.WithTenantContext(c.Request.Context(), claims.TenantID)
	ctx = global.WithUserContext(ctx, claims.BaseClaims.ID)

	if sessionID != "" {
		ctx = global.WithSessionContext(ctx, sessionID)
	}

	c.Request = c.Request.WithContext(ctx)

	// 设置线程安全的全局租户ID（兼容性支持）
	global.SetCurrentTenantIDSafe(claims.TenantID)

	// 记录租户中间件执行日志
	global.GVA_LOG.Debug("租户中间件执行完成",
		zap.Uint("tenantID", claims.TenantID),
		zap.Uint("userID", claims.BaseClaims.ID),
		zap.String("sessionID", sessionID),
		zap.Bool("distributedMode", global.IsDistributedMode()),
		zap.String("path", c.FullPath()))
}

// 注意：TenantHeaderMiddleware 和 SessionRecoveryMiddleware
// 已在 distributed_tenant.go 中定义，这里不重复定义以避免冲突
