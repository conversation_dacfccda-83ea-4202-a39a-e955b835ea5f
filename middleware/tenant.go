package middleware

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/system"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TenantMiddleware 租户中间件
func TenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先设置defer，确保清理逻辑执行
		defer func() {
			global.ClearCurrentTenantID()
		}()
		
		claims, exists := c.Get("claims")
		if !exists {
			response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
			c.Abort()
			return
		}
		
		customClaims, ok := claims.(*systemReq.CustomClaims)
		if !ok {
			response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
			c.Abort()
			return
		}
		
		// 验证租户有效性
		if !system.ValidateTenant(customClaims.TenantID) {
			global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", customClaims.TenantID))
			response.FailWithMessage("租户无效", c)
			c.Abort()
			return
		}
		
		// 设置租户上下文
		c.Set("tenantId", customClaims.TenantID)
		ctx := global.WithTenantContext(c.Request.Context(), customClaims.TenantID)
		c.Request = c.Request.WithContext(ctx)
		
		// 设置线程安全的全局租户ID
		global.SetCurrentTenantIDSafe(customClaims.TenantID)
		
		c.Next()
	}
}