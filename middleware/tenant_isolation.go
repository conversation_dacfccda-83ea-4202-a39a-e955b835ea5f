package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TenantIsolationMiddleware 租户隔离中间件 - 自动注入租户ID并验证跨租户访问
func TenantIsolationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户Claims
		claims := utils.GetUserInfo(c)
		if claims == nil {
			response.FailWithMessage("未授权访问", c)
			c.Abort()
			return
		}

		// 检查是否为超级管理员，超级管理员跳过租户隔离
		if claims.IsSuperAdmin || claims.UserType == systemReq.UserTypeSuperAdmin {
			c.Set("skip_tenant_isolation", true)
			c.Next()
			return
		}

		// 处理请求参数的租户ID注入和验证
		if err := processTenantIsolation(c, claims); err != nil {
			global.GVA_LOG.Error("租户隔离处理失败", 
				zap.Error(err),
				zap.Uint("userTenantID", claims.TenantID),
				zap.String("path", c.FullPath()))
			response.FailWithMessage(err.Error(), c)
			c.Abort()
			return
		}

		c.Next()
	}
}

// processTenantIsolation 处理租户隔离逻辑
func processTenantIsolation(c *gin.Context, claims *systemReq.CustomClaims) error {
	// 处理查询参数
	if err := processQueryParams(c, claims); err != nil {
		return err
	}

	// 处理请求体参数
	if c.Request.Method != "GET" && c.Request.Method != "DELETE" {
		if err := processRequestBody(c, claims); err != nil {
			return err
		}
	}

	return nil
}

// processQueryParams 处理查询参数中的租户ID
func processQueryParams(c *gin.Context, claims *systemReq.CustomClaims) error {
	query := c.Request.URL.Query()
	modified := false

	// 检查是否存在租户相关参数
	tenantFields := []string{"tenantId", "tenant_id", "TenantID"}
	
	for _, field := range tenantFields {
		if values, exists := query[field]; exists && len(values) > 0 {
			// 验证传入的租户ID
			if tenantID, err := utils.StringToUint(values[0]); err == nil {
				if tenantID != claims.TenantID && tenantID != 0 {
					return fmt.Errorf("无权限访问其他租户数据")
				}
			}
		} else {
			// 自动注入当前用户的租户ID
			query.Set(field, fmt.Sprintf("%d", claims.TenantID))
			modified = true
		}
	}

	// 如果修改了查询参数，更新请求
	if modified {
		c.Request.URL.RawQuery = query.Encode()
	}

	return nil
}

// processRequestBody 处理请求体中的租户ID
func processRequestBody(c *gin.Context, claims *systemReq.CustomClaims) error {
	// 读取原始请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return fmt.Errorf("读取请求体失败: %v", err)
	}

	// 重置请求体供后续使用
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	// 如果请求体为空，直接返回
	if len(body) == 0 {
		return nil
	}

	// 尝试解析JSON
	var data interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		// 如果不是JSON格式，跳过处理
		return nil
	}

	// 处理租户ID注入和验证
	modified, err := injectAndValidateTenantID(data, claims.TenantID)
	if err != nil {
		return err
	}

	// 如果数据被修改，重新序列化并更新请求体
	if modified {
		newBody, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("序列化请求体失败: %v", err)
		}
		c.Request.Body = io.NopCloser(bytes.NewBuffer(newBody))
		c.Request.ContentLength = int64(len(newBody))
	}

	return nil
}

// injectAndValidateTenantID 递归处理数据结构中的租户ID
func injectAndValidateTenantID(data interface{}, userTenantID uint) (bool, error) {
	modified := false

	switch v := data.(type) {
	case map[string]interface{}:
		// 处理对象
		for key, value := range v {
			if isTenantField(key) {
				if value == nil || value == "" || value == 0 {
					// 自动注入租户ID
					v[key] = userTenantID
					modified = true
				} else {
					// 验证租户ID
					if tenantID := extractTenantIDFromValue(value); tenantID != 0 && tenantID != userTenantID {
						return false, fmt.Errorf("无权限访问其他租户数据")
					}
				}
			} else if subModified, err := injectAndValidateTenantID(value, userTenantID); err != nil {
				return false, err
			} else if subModified {
				modified = true
			}
		}
	case []interface{}:
		// 处理数组
		for _, item := range v {
			if subModified, err := injectAndValidateTenantID(item, userTenantID); err != nil {
				return false, err
			} else if subModified {
				modified = true
			}
		}
	}

	return modified, nil
}

// isTenantField 判断字段是否为租户相关字段
func isTenantField(fieldName string) bool {
	tenantFields := []string{
		"tenantId", "tenant_id", "TenantID", "tenantID",
		"tenant", "Tenant", "TENANT_ID",
	}
	
	for _, field := range tenantFields {
		if strings.EqualFold(fieldName, field) {
			return true
		}
	}
	return false
}

// extractTenantIDFromValue 从值中提取租户ID
func extractTenantIDFromValue(value interface{}) uint {
	switch v := value.(type) {
	case float64:
		return uint(v)
	case int:
		return uint(v)
	case int64:
		return uint(v)
	case uint:
		return v
	case uint64:
		return uint(v)
	case string:
		if id, err := utils.StringToUint(v); err == nil {
			return id
		}
	}
	return 0
}

// TenantValidationMiddleware 租户验证中间件 - 验证租户有效性
func TenantValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过超级管理员和公开接口
		if skipTenantValidation(c) {
			c.Next()
			return
		}

		claims := utils.GetUserInfo(c)
		if claims == nil {
			response.FailWithMessage("未授权访问", c)
			c.Abort()
			return
		}

		// 验证租户ID有效性
		if claims.TenantID == 0 {
			response.FailWithMessage("租户ID无效", c)
			c.Abort()
			return
		}

		// 这里可以添加更多的租户验证逻辑
		// 例如检查租户是否激活、是否过期等

		c.Next()
	}
}

// skipTenantValidation 判断是否跳过租户验证
func skipTenantValidation(c *gin.Context) bool {
	// 检查是否设置了跳过标志
	if skip, exists := c.Get("skip_tenant_isolation"); exists && skip.(bool) {
		return true
	}

	// 检查是否为公开接口
	path := c.FullPath()
	publicPaths := []string{
		"/api/v1/public/",
		"/api/v1/base/login",
		"/api/v1/base/register",
		"/health",
	}

	for _, publicPath := range publicPaths {
		if strings.HasPrefix(path, publicPath) {
			return true
		}
	}

	return false
}

// SecurityAuditMiddleware 安全审计中间件 - 记录敏感操作
func SecurityAuditMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录跨租户访问尝试
		if shouldAudit(c) {
			auditSecurityEvent(c)
		}
		c.Next()
	}
}

// shouldAudit 判断是否需要审计
func shouldAudit(c *gin.Context) bool {
	// 审计所有非GET请求
	return c.Request.Method != "GET"
}

// auditSecurityEvent 记录安全事件
func auditSecurityEvent(c *gin.Context) {
	claims := utils.GetUserInfo(c)
	if claims == nil {
		return
	}

	global.GVA_LOG.Info("安全审计",
		zap.Uint("userID", claims.BaseClaims.ID),
		zap.Uint("tenantID", claims.TenantID),
		zap.String("method", c.Request.Method),
		zap.String("path", c.FullPath()),
		zap.String("ip", c.ClientIP()),
		zap.String("userAgent", c.Request.UserAgent()),
	)
}