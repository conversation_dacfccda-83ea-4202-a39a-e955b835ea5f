package middleware

import (
	"fmt"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// DistributedTenantMiddleware 分布式租户中间件
func DistributedTenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成或获取会话ID
		sessionID := c.GetHeader("X-Session-ID")
		if sessionID == "" {
			sessionID = uuid.New().String()
			c.Header("X-Session-ID", sessionID)
		}

		// 设置清理逻辑
		defer func() {
			if global.IsDistributedMode() {
				// 分布式模式：可选择立即清理或让Redis自动过期
				// global.ClearTenantSession(c.Request.Context(), sessionID)
			} else {
				// 单机模式：清理本地状态
				global.ClearCurrentTenantID()
			}
		}()

		claims, exists := c.Get("claims")
		if !exists {
			response.FailWithDetailed(gin.H{"reload": true}, "未登录或非法访问", c)
			c.Abort()
			return
		}

		customClaims, ok := claims.(*systemReq.CustomClaims)
		if !ok {
			response.FailWithDetailed(gin.H{"reload": true}, "令牌格式错误", c)
			c.Abort()
			return
		}

		// 验证租户有效性（自动选择分布式或本地验证）
		var isValid bool
		if global.IsDistributedCacheEnabled() {
			isValid = global.ValidateTenantDistributed(c.Request.Context(), customClaims.TenantID)
		} else {
			isValid = global.ValidateTenantCached(customClaims.TenantID)
		}

		if !isValid {
			global.GVA_LOG.Error("租户ID无效", zap.Uint("tenantID", customClaims.TenantID))
			response.FailWithMessage("租户无效", c)
			c.Abort()
			return
		}

		// 设置租户上下文
		c.Set("sessionId", sessionID)
		c.Set("tenantId", customClaims.TenantID)
		c.Set("userId", customClaims.BaseClaims.ID)

		// 创建带租户信息的上下文
		ctx := global.WithTenantContext(c.Request.Context(), customClaims.TenantID)
		ctx = global.WithSessionContext(ctx, sessionID)
		ctx = global.WithUserContext(ctx, customClaims.BaseClaims.ID)
		c.Request = c.Request.WithContext(ctx)

		// 设置分布式租户会话（如果启用）
		if global.IsDistributedMode() {
			err := global.SetTenantSession(c.Request.Context(), sessionID, customClaims.TenantID, customClaims.BaseClaims.ID)
			if err != nil {
				global.GVA_LOG.Warn("设置分布式租户会话失败", zap.Error(err))
				// 不中断请求，降级到本地模式
			}
		}

		// 同时设置本地租户ID（兼容性和降级支持）
		global.SetCurrentTenantIDSafe(customClaims.TenantID)

		c.Next()
	}
}

// TenantHeaderMiddleware 租户头信息中间件（用于微服务间调用）
func TenantHeaderMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从HTTP头提取租户信息
		tenantIDHeader := c.GetHeader("X-Tenant-ID")
		sessionIDHeader := c.GetHeader("X-Session-ID")
		userIDHeader := c.GetHeader("X-User-ID")

		if tenantIDHeader != "" {
			// 解析租户ID
			var tenantID uint
			if _, err := fmt.Sscanf(tenantIDHeader, "%d", &tenantID); err == nil && tenantID > 0 {
				// 验证租户有效性
				var isValid bool
				if global.IsDistributedCacheEnabled() {
					isValid = global.ValidateTenantDistributed(c.Request.Context(), tenantID)
				} else {
					isValid = global.ValidateTenantCached(tenantID)
				}

				if !isValid {
					global.GVA_LOG.Error("从头信息获取的租户ID无效", zap.Uint("tenantID", tenantID))
					response.FailWithMessage("租户无效", c)
					c.Abort()
					return
				}

				// 设置上下文
				c.Set("tenantId", tenantID)
				ctx := global.WithTenantContext(c.Request.Context(), tenantID)

				if sessionIDHeader != "" {
					c.Set("sessionId", sessionIDHeader)
					ctx = global.WithSessionContext(ctx, sessionIDHeader)
				}

				if userIDHeader != "" {
					var userID uint
					if _, err := fmt.Sscanf(userIDHeader, "%d", &userID); err == nil {
						c.Set("userId", userID)
						ctx = global.WithUserContext(ctx, userID)
					}
				}

				c.Request = c.Request.WithContext(ctx)
				global.SetCurrentTenantIDSafe(tenantID)
			}
		}

		c.Next()
	}
}

// SessionRecoveryMiddleware 会话恢复中间件
func SessionRecoveryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.GetHeader("X-Session-ID")
		if sessionID != "" && global.IsDistributedMode() {
			// 尝试从Redis恢复会话
			session, err := global.GetTenantSession(c.Request.Context(), sessionID)
			if err == nil && session != nil {
				// 验证租户有效性
				isValid := global.ValidateTenantDistributed(c.Request.Context(), session.TenantID)
				if isValid {
					// 恢复上下文
					c.Set("tenantId", session.TenantID)
					c.Set("userId", session.UserID)
					c.Set("sessionId", sessionID)

					ctx := global.WithTenantContext(c.Request.Context(), session.TenantID)
					ctx = global.WithSessionContext(ctx, sessionID)
					ctx = global.WithUserContext(ctx, session.UserID)
					c.Request = c.Request.WithContext(ctx)

					global.SetCurrentTenantIDSafe(session.TenantID)
				}
			}
		}

		c.Next()
	}
}