package middleware

import (
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// TenantAccessMiddleware 租户访问中间件
// 验证用户在当前租户中的加盟商身份，并设置租户和加盟商上下文
func TenantAccessMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置defer，确保清理逻辑执行
		defer func() {
			global.ClearCurrentTenantID()
		}()
		// 获取JWT中的用户信息
		userID := utils.GetUserID(c)
		tenantID := utils.GetTenantID(c)
		franchiseeID := utils.GetFranchiseeID(c)

		if userID == 0 {
			response.FailWithMessage("用户信息获取失败", c)
			c.Abort()
			return
		}

		if tenantID == 0 {
			response.FailWithMessage("租户信息获取失败", c)
			c.Abort()
			return
		}

		if franchiseeID == 0 {
			response.FailWithMessage("加盟商信息获取失败", c)
			c.Abort()
			return
		}

		// 验证用户在当前租户中的加盟商身份
		var franchisee franchisees.Franchisee
		err := global.GVA_DB.Scopes(tenant.SkipTenant).
			Where("id = ? AND user_id = ? AND tenant_id = ?", franchiseeID, userID, tenantID).
			First(&franchisee).Error
		if err != nil {
			global.GVA_LOG.Error("验证加盟商身份失败",
				zap.Error(err),
				zap.Uint("userID", userID),
				zap.Uint("tenantID", tenantID),
				zap.Uint("franchiseeID", franchiseeID))
			response.FailWithMessage("您在该租户中没有有效的加盟商身份", c)
			c.Abort()
			return
		}

		// 设置租户上下文（用于GORM插件）
		c.Set("tenantId", tenantID)
		ctx := global.WithTenantContext(c.Request.Context(), tenantID)
		c.Request = c.Request.WithContext(ctx)

		// 设置线程安全的全局租户ID
		global.SetCurrentTenantIDSafe(tenantID)

		// 设置加盟商信息到上下文中，供后续业务逻辑使用
		c.Set("franchisee", franchisee)
		c.Set("franchiseeID", franchiseeID)
		c.Set("tenantID", tenantID)

		c.Next()
	}
}

// GetFranchiseeFromContext 从上下文中获取加盟商信息
func GetFranchiseeFromContext(c *gin.Context) (*franchisees.Franchisee, bool) {
	if franchisee, exists := c.Get("franchisee"); exists {
		if f, ok := franchisee.(franchisees.Franchisee); ok {
			return &f, true
		}
	}
	return nil, false
}

// GetFranchiseeIDFromContext 从上下文中获取加盟商ID
func GetFranchiseeIDFromContext(c *gin.Context) uint {
	if franchiseeID, exists := c.Get("franchiseeID"); exists {
		if id, ok := franchiseeID.(uint); ok {
			return id
		}
	}
	return 0
}

// GetTenantIDFromContext 从上下文中获取租户ID
func GetTenantIDFromContext(c *gin.Context) uint {
	if tenantID, exists := c.Get("tenantID"); exists {
		if id, ok := tenantID.(uint); ok {
			return id
		}
	}
	return 0
}
