package middleware

import (
	"encoding/json"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	modelSystem "github.com/OSQianXing/guanpu-server/model/system"
	serviceSystem "github.com/OSQianXing/guanpu-server/service/system"
	"github.com/OSQianXing/guanpu-server/model/common/response"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SuperAdminAuth 超级管理员基础认证中间件
func SuperAdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, err := utils.GetSuperAdminClaims(c)
		if err != nil || !claims.IsSuperAdmin {
			response.FailWithMessage("无权限访问", c)
			c.Abort()
			return
		}
		c.Next()
	}
}

// SuperAdminMiddleware 超级管理员权限验证中间件
func SuperAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, _ := utils.GetSuperAdminClaims(c)
		if claims == nil {
			response.FailWithMessage("未授权访问", c)
			c.Abort()
			return
		}

		// 检查是否为超级管理员
		if !claims.IsSuperAdmin && claims.UserType != systemReq.UserTypeSuperAdmin {
			response.FailWithMessage("需要超级管理员权限", c)
			c.Abort()
			return
		}

		// 设置超级管理员标志，跳过租户隔离
		c.Set("is_super_admin", true)
		c.Set("skip_tenant", true)
		
		// 设置全局上下文，超级管理员不设置租户ID
		global.SetCurrentTenantID(0)

		c.Next()
	}
}

// SuperAdminLogMiddleware 超级管理员操作日志中间件
func SuperAdminLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()
		
		// 获取用户信息
		claims := utils.GetUserInfo(c)
		if claims == nil {
			c.Next()
			return
		}

		// 只记录超级管理员的操作
		if !claims.IsSuperAdmin && claims.UserType != systemReq.UserTypeSuperAdmin {
			c.Next()
			return
		}

		// 记录请求数据
		var requestData json.RawMessage
		if c.Request.Method != "GET" {
			if body, err := c.GetRawData(); err == nil {
				requestData = body
				// 重新设置body供后续处理使用
				c.Request.Body = utils.NewReadCloser(body)
			}
		}

		// 创建响应写入器来捕获响应
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          make([]byte, 0),
		}
		c.Writer = writer

		c.Next()

		// 计算操作耗时
		duration := time.Since(startTime).Milliseconds()

		// 确定操作类型和目标类型
		operationType := getOperationType(c.Request.Method, c.FullPath())
		targetType := getTargetType(c.FullPath())

		// 确定操作状态
		status := "success"
		errorMessage := ""
		if c.Writer.Status() >= 400 {
			status = "failed"
			// 尝试从响应中提取错误信息
			if len(writer.body) > 0 {
				var resp map[string]interface{}
				if err := json.Unmarshal(writer.body, &resp); err == nil {
					if msg, ok := resp["msg"].(string); ok {
						errorMessage = msg
					}
				}
			}
		}

		// 创建操作日志
		log := &modelSystem.SuperAdminOperationLog{
			OperatorID:    claims.BaseClaims.ID,
			OperationType: operationType,
			TargetType:    targetType,
			OperationDesc: getOperationDesc(c.Request.Method, c.FullPath()),
			RequestData:   requestData,
			ResponseData:  writer.body,
			IPAddress:     c.ClientIP(),
			UserAgent:     c.Request.UserAgent(),
			Status:        status,
			ErrorMessage:  errorMessage,
			Duration:      duration,
		}

		// 从URL参数中提取目标ID
		if targetID := extractTargetID(c); targetID > 0 {
			log.TargetID = &targetID
		}

		// 从请求中提取租户ID
		if tenantID := extractTenantID(c, requestData); tenantID > 0 {
			log.TenantID = &tenantID
		}

		// 异步记录日志，避免影响响应性能
		go func() {
			if err := serviceSystem.CreateSuperAdminLog(log); err != nil {
				global.GVA_LOG.Error("记录超级管理员操作日志失败", 
					zap.Error(err),
					zap.Uint("userId", claims.BaseClaims.ID),
					zap.String("operation", operationType))
			}
		}()
	}
}

// responseWriter 自定义响应写入器，用于捕获响应内容
type responseWriter struct {
	gin.ResponseWriter
	body []byte
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body = append(w.body, b...)
	return w.ResponseWriter.Write(b)
}

// getOperationType 根据HTTP方法和路径确定操作类型
func getOperationType(method, path string) string {
	switch method {
	case "POST":
		if contains(path, "tenant") {
			return modelSystem.OpTypeTenantCreate
		} else if contains(path, "user") {
			return modelSystem.OpTypeUserCreate
		}
		return "create"
	case "PUT":
		if contains(path, "tenant") {
			return modelSystem.OpTypeTenantUpdate
		} else if contains(path, "user") {
			return modelSystem.OpTypeUserUpdate
		}
		return "update"
	case "DELETE":
		if contains(path, "tenant") {
			return modelSystem.OpTypeTenantDelete
		} else if contains(path, "user") {
			return modelSystem.OpTypeUserDelete
		}
		return "delete"
	case "GET":
		return modelSystem.OpTypeDataQuery
	default:
		return "unknown"
	}
}

// getTargetType 根据路径确定目标类型
func getTargetType(path string) string {
	if contains(path, "tenant") {
		return modelSystem.TargetTypeTenant
	} else if contains(path, "user") {
		return modelSystem.TargetTypeUser
	} else if contains(path, "order") {
		return modelSystem.TargetTypeOrder
	} else if contains(path, "product") {
		return modelSystem.TargetTypeProduct
	} else if contains(path, "franchisee") {
		return modelSystem.TargetTypeFranchisee
	} else if contains(path, "system") {
		return modelSystem.TargetTypeSystem
	}
	return "unknown"
}

// getOperationDesc 生成操作描述
func getOperationDesc(method, path string) string {
	operationType := getOperationType(method, path)
	targetType := getTargetType(path)
	return operationType + "_" + targetType
}

// extractTargetID 从URL参数中提取目标ID
func extractTargetID(c *gin.Context) uint {
	if id := c.Param("id"); id != "" {
		if targetID, err := utils.StringToUint(id); err == nil {
			return targetID
		}
	}
	if id := c.Param("tenantId"); id != "" {
		if targetID, err := utils.StringToUint(id); err == nil {
			return targetID
		}
	}
	if id := c.Param("userId"); id != "" {
		if targetID, err := utils.StringToUint(id); err == nil {
			return targetID
		}
	}
	return 0
}

// extractTenantID 从请求中提取租户ID
func extractTenantID(c *gin.Context, requestData json.RawMessage) uint {
	// 从URL参数提取
	if tenantID := c.Query("tenantId"); tenantID != "" {
		if id, err := utils.StringToUint(tenantID); err == nil {
			return id
		}
	}

	// 从请求体提取
	if len(requestData) > 0 {
		var data map[string]interface{}
		if err := json.Unmarshal(requestData, &data); err == nil {
			if tenantID, ok := data["tenantId"].(float64); ok {
				return uint(tenantID)
			}
		}
	}

	return 0
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
			 s[len(s)-len(substr):] == substr ||
			 containsSubstring(s, substr))))
}

// containsSubstring 检查字符串中是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}