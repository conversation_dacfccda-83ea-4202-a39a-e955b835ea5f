package pay

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/OSQianXing/guanpu-server/config"
	"github.com/OSQianXing/guanpu-server/global"
	franchiseeModel "github.com/OSQianXing/guanpu-server/model/franchisees"
	ordersModel "github.com/OSQianXing/guanpu-server/model/orders"
	"github.com/shopspring/decimal"

	payModel "github.com/OSQianXing/guanpu-server/model/pay"
	payReq "github.com/OSQianXing/guanpu-server/model/pay/request"
	payModelResp "github.com/OSQianXing/guanpu-server/model/pay/response"
	cloudWarehouseModel "github.com/OSQianXing/guanpu-server/model/warehouse"
	"github.com/OSQianXing/guanpu-server/service/bigwarehouse"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/OSQianXing/guanpu-server/utils/payment/heepay"
	utilHeepayClient "github.com/OSQianXing/guanpu-server/utils/payment/heepay"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type OnlinePayService struct {
}

var payChannelServiceMap = map[types.OnlinePayChannel]PayChannelService{
	types.OnlinePayChannelHeepay: &heepayService{},
}

func (ops *OnlinePayService) GetAllOnlinePayChannel() []types.OnlinePayChannel {
	var x types.OnlinePayChannel
	return x.AllOnlinePayChannel()
}

func (ops *OnlinePayService) GetFranchiseeOnlinePayChannels(franchiseeID uint) ([]types.OnlinePayChannel, error) {
	var franchiseeOnlinePayChannels []types.OnlinePayChannel

	if err := global.GVA_DB.
		Model(&payModel.OnlinePayFranchiseeConfig{}).
		Where("franchisee_id = ?", franchiseeID).
		Pluck("online_pay_channel", &franchiseeOnlinePayChannels).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {

		}
		return nil, err
	}

	if len(franchiseeOnlinePayChannels) == 0 {
		var defaultPayPolicy payModel.OnlinePayDefaultPolicy
		if err := global.GVA_DB.Where("id = ?", 1).First(&defaultPayPolicy).Error; err != nil {
			return nil, err
		}
		if defaultPayPolicy.DefaultOnlinePayEnabled == nil || !*defaultPayPolicy.DefaultOnlinePayEnabled {
			return nil, nil
		}

		var defaultOnlinePayChannels []types.OnlinePayChannel
		if err := global.GVA_DB.Model(&payModel.OnlinePayDefaultPolicy{}).Pluck("default_online_pay_channel", &defaultOnlinePayChannels).Error; err != nil {
			return nil, err
		}
		if len(defaultOnlinePayChannels) == 0 {
			return nil, errors.New("online_pay_default_policy.online_pay_channel is empty")
		}
		return defaultOnlinePayChannels, nil
	}
	return franchiseeOnlinePayChannels, nil
}

func (ops *OnlinePayService) GetFranchiseePayChannel(franchiseeId uint, payChannel types.OnlinePayChannel) (payModel.OnlinePayFranchiseeConfig, error) {
	// @TODO: 获取用户支付渠道，如果用户没有设置支付渠道，则使用默认支付渠道，相关表逻辑参考 online_pay_default_policy 及 GetFranchiseeOnlinePayChannels
	if franchiseeId == 0 {
		return payModel.OnlinePayFranchiseeConfig{}, errors.New("user id is 0")
	}
	var payFranchiseeConfig payModel.OnlinePayFranchiseeConfig
	// 获取用户支付渠道
	err := global.GVA_DB.Model(&payModel.OnlinePayFranchiseeConfig{}).Where("franchisee_id = ? AND online_pay_channel = ?", franchiseeId, payChannel).First(&payFranchiseeConfig).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没有设置支付渠道，则使用默认支付渠道
			var defaultPayPolicy payModel.OnlinePayDefaultPolicy
			if err := global.GVA_DB.Where("id = ?", 1).First(&defaultPayPolicy).Error; err != nil {
				return payModel.OnlinePayFranchiseeConfig{}, err
			}
			if defaultPayPolicy.DefaultOnlinePayEnabled == nil || !*defaultPayPolicy.DefaultOnlinePayEnabled {
				return payModel.OnlinePayFranchiseeConfig{}, errors.New("online_pay_default_policy.default_online_pay_enabled is false")
			}
			if defaultPayPolicy.DefaultOnlinePayChannel != payChannel {
				return payModel.OnlinePayFranchiseeConfig{}, errors.New("online_pay_default_policy.default_online_pay_channel is not equal to payChannel")
			}
			// TODO: add default online pay type to OnlinePayFranchiseeConfig
			onlinePayType := []types.OnlinePayType{defaultPayPolicy.DefaultOnlinePayType}
			onlinePayTypeJson, _ := json.Marshal(onlinePayType)

			payFranchiseeConfig = payModel.OnlinePayFranchiseeConfig{
				OnlinePayFranchiseeConfigCreate: payReq.OnlinePayFranchiseeConfigCreate{
					FranchiseeId:     franchiseeId,
					OnlinePayChannel: defaultPayPolicy.DefaultOnlinePayChannel,
					OnlinePayType:    onlinePayTypeJson,
				},
			}
			return payFranchiseeConfig, nil

		}
		return payModel.OnlinePayFranchiseeConfig{}, err
	}
	return payFranchiseeConfig, nil
}

func (ops *OnlinePayService) getOrderPayChannelId(orderNo string) (string, error) {
	return "", nil
}

func (ops *OnlinePayService) getPayChannelInstance(payChannelId types.OnlinePayChannel, payType types.OnlinePayType) (PayChannelService, error) {
	return payChannelServiceMap[payChannelId], nil
}
func (ops *OnlinePayService) TestCreateOnlinePayOrder(userIp string, franchiseeId uint) (string, error) {
	userPayChannel, err := ops.GetFranchiseePayChannel(franchiseeId, types.OnlinePayChannelHeepay)
	if err != nil {
		return "", err
	}
	userPayChaannelService, err := ops.getPayChannelInstance(userPayChannel.OnlinePayChannel, types.OnlinePayTypeWechatIndirect)
	if err != nil {
		return "", err
	}
	re, err := userPayChaannelService.CreateOrderTestPay(nil, userIp)
	if err != nil {
		return "", err
	}
	return re, nil

}

func (ops *OnlinePayService) CreateOrderOnlinePayWithinTx(tx *gorm.DB, orderNo string, payChannel types.OnlinePayChannel, payType types.OnlinePayType, amount uint, rebate uint, operatorId uint, userIp string, extra datatypes.JSON) (string, error) {
	onlinePayModel := payModel.OrderOnlinePay{
		GVA_MODEL:  global.GVA_MODEL{},
		OrderNo:    orderNo,
		Amount:     amount,
		Rebate:     rebate,
		PayChannel: payChannel,
		PayType:    payType,
		Status:     types.OnlinePayStatusPending,
		UserIP:     userIp,
		CreatedBy:  operatorId,
	}

	err := tx.Create(&onlinePayModel).Error
	if err != nil {
		return "", err
	}
	oopr := payModel.OrderOnlinePayRecord{
		GVA_MODEL:        global.GVA_MODEL{},
		OrderOnlinePayId: onlinePayModel.ID,
		OrderNo:          orderNo,
		Amount:           amount,
		PayChannel:       payChannel,
		PayType:          payType,
		TradeNo:          "",
		Status:           types.OnlinePayStatusPending,
		UserIP:           userIp,
		Extra:            extra,
		CreatedBy:        operatorId,
	}

	err = tx.Create(&oopr).Error
	if err != nil {
		return "", err
	}
	return onlinePayModel.OrderNo, nil
}

func (ops *OnlinePayService) CreateOrderOnlinePay(orderNo string, payChannel types.OnlinePayChannel, payType types.OnlinePayType, amount uint, rebate uint, operatorId uint, userIp string) (string, error) {
	return ops.CreateOrderOnlinePayWithinTx(global.GVA_DB, orderNo, payChannel, payType, amount, rebate, operatorId, userIp, []byte("{}"))
}

func (ops *OnlinePayService) RePayOnlinePay(orderNo string, amount uint, userPayChannel types.OnlinePayChannel, userPayType types.OnlinePayType, userIp string, operatorId uint) (string, error) {
	// Check order,orderOnlinePay and orderOnlineRecord ,onlinePayFranchiseeConfig
	// get orderOnlinePay.TradeNo , renturn it
	var orderOnlinePay payModel.OrderOnlinePay
	err := global.GVA_DB.Model(&payModel.OrderOnlinePay{}).Where("order_no =?", orderNo).First(&orderOnlinePay).Error
	if err != nil {
		return "", err
	}
	if orderOnlinePay.Status == types.OnlinePayStatusPending && orderOnlinePay.TradeNo == "" {
		// 无记录，直接支付
		return ops.OnlinePay(orderNo, amount, userPayChannel, userPayType, userIp, operatorId)
	} else if orderOnlinePay.Status == types.OnlinePayStatusPending && orderOnlinePay.TradeNo != "" {
		return orderOnlinePay.TradeNo, nil
	} else if orderOnlinePay.Status == types.OnlinePayStatusFailed {
		//支付失败，可以重新支付
		return orderOnlinePay.TradeNo, nil
	} else if orderOnlinePay.Status == types.OnlinePayStatusSuccess {
		//支付成功，无需支付
		return orderOnlinePay.TradeNo, errors.New("订单已支付，无需再次支付")
	} else if orderOnlinePay.Status == types.OnlinePayStutusRefunded || orderOnlinePay.Status == types.OnlinePayStutusRefunding {
		// 已退款，无需支付
		return orderOnlinePay.TradeNo, errors.New("订单已退款，无需再次支付")
	} else if orderOnlinePay.Status == types.OnlinePayStatusPaying {
		// 支付中，需 检查
		return orderOnlinePay.TradeNo, errors.New("订单支付中，请勿重复支付")
	}

	return "", nil
}

func (ops *OnlinePayService) OnlinePay(orderNo string, amount uint, userPayChannel types.OnlinePayChannel, userPayType types.OnlinePayType, userIp string, operatorId uint) (string, error) {
	var tokenId string
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// get order,orderOnlinePay and orderOnlineRecord and onlinePayFranchiseeConfig
		var o ordersModel.Order
		err := tx.Set("gorm:query_option", "FOR UPDATE").Where("order_no = ?", orderNo).First(&o).Error
		if err != nil {
			return errors.Join(err, errors.New("get and lock order error"))
		}
		if (o.PaymentType != types.PaymentTypeNone && o.PaymentType != types.PaymentTypeOnlinePay) || o.Status != types.OrderStatusAwaitingPayment {
			return errors.New("order is not awaiting online payment or payment")
		}

		if o.Amount != int(amount) {
			return errors.New("order amount not equal")
		}

		var oop payModel.OrderOnlinePay
		err = tx.Set("gorm:query_option", "FOR UPDATE").Where("order_no = ? AND status = ?", orderNo, types.OnlinePayStatusPending).First(&oop).Error
		if err != nil {
			return errors.Join(err, errors.New("get and lock order online pay error"))
		}
		if oop.Status != types.OnlinePayStatusPending {
			return errors.New("order online pay is not pending")
		}

		// update order to paying,此处无需修改order，待支付成功后，直接修改成功?
		// err = tx.Model(&ordersModel.Order{}).Where("order_no = ?", orderNo).Updates(map[string]interface{}{
		// 	"payment_type": types.PaymentTypeOnlinePay,
		// 	"status":       types.OrderStatusPaying,
		// }).Error
		// if err != nil {
		// 	return errors.Join(err, errors.New("update order payment type error"))
		// }
		// Check user online pay channel and type
		sUserPayChannel, err := ops.GetFranchiseePayChannel(uint(o.FranchiseeId), userPayChannel)
		if err != nil {
			return errors.Join(err, errors.New("get user pay channel error"))
		}
		var sUserPayType []types.OnlinePayType
		err = json.Unmarshal(sUserPayChannel.OnlinePayType, &sUserPayType)
		if err != nil {
			return errors.Join(err, errors.New("unmarshal user pay type error"))
		}
		if !lo.Contains(sUserPayType, userPayType) {
			return errors.New("user pay type not in server online pay type")
		}
		// update order online pay
		err = tx.Model(&payModel.OrderOnlinePay{}).Where("order_no = ? AND status = ? AND pay_channel = ? AND pay_type = ?", orderNo, types.OnlinePayStatusPending, types.OnlinePayChannelNone, types.OnlinePayTypeNone).Updates(map[string]interface{}{
			"pay_channel": userPayChannel,
			"pay_type":    userPayType,
			"status":      types.OnlinePayStatusPaying,
			"updated_by":  operatorId,
			"pay_time":    time.Now().Local(),
		}).Error
		if err != nil {
			return errors.Join(err, errors.New("update order online pay status error"))
		}
		// get user pay channel
		userPayChannelService, err := ops.getPayChannelInstance(sUserPayChannel.OnlinePayChannel, userPayType)
		if err != nil {
			return errors.Join(err, errors.New("get user pay channel instance error"))
		}
		// Create order online pay to online pay service
		var req, resp interface{}
		tokenId, req, resp, err = userPayChannelService.CreateOrderOnlinePay(tx, &o, userPayChannel, userPayType, amount, userIp)
		global.GVA_LOG.Debug("service.pay.payService.CreateOrderOnlinePay", zap.Any("o", o), zap.String("orderNo", orderNo), zap.Any("userPayChannel", userPayChannel), zap.Any("userPayType", userPayType), zap.String("userIp", userIp), zap.String("tokenId", tokenId), zap.Any("req", req), zap.Any("resp", resp), zap.Any("err", err))
		if err != nil {
			return errors.Join(err, errors.New("create order online pay error"))
		}

		// Create order online pay record
		ooprId, err := ops.CreateOrderOnlinePayRecord(tx, &o, oop.ID, userPayChannel, userPayType, amount, userIp)
		if err != nil {
			return errors.Join(err, errors.New("create order online pay record error"))
		}

		// update order online pay add tradeNo
		err = tx.Model(&payModel.OrderOnlinePay{}).Where("id = ? AND order_no = ? AND status = ? AND pay_channel = ? AND pay_type = ?", oop.ID, orderNo, types.OnlinePayStatusPaying, userPayChannel, userPayType).Updates(map[string]interface{}{
			"trade_no":   tokenId,
			"updated_by": operatorId,
		}).Error
		if err != nil {
			return errors.Join(err, errors.New("update order online pay trade no error"))
		}
		// rollbackPoint := fmt.Sprintf("%s_%d_%d_%d_%s", orderNo, userPayChannel, userPayType, amount, userIp)
		// tx = tx.SavePoint(rollbackPoint)
		// update order online pay record
		err = ops.UpdateTradeNoForOrderOnlinePayRecordAfterCreatePayOrder(tx, oop.ID, ooprId, o.OrderNo, userPayChannel, userPayType, tokenId, req, resp, types.OnlinePayStatusPaying)
		if err != nil {
			return errors.Join(err, errors.New("update order online pay record error"))
		}

		return nil

	})
	if err != nil {
		return "", err
	}
	return tokenId, nil

}

// GetOrderOnlinePayByOrderNo retrieves the OrderOnlinePay model from the database based on the provided order number.
//
// Parameters:
// - orderNo: the order number to search for.
//
// Returns:
// - payModel.OrderOnlinePay: the OrderOnlinePay model found in the database.
// - error: an error if the model was not found or if there was an issue querying the database.
func (ops *OnlinePayService) GetOrderOnlinePayByOrderNo(orderNo string) (payModel.OrderOnlinePay, error) {
	var orderOnlinePay payModel.OrderOnlinePay
	err := global.GVA_DB.Where("order_no = ?", orderNo).First(&orderOnlinePay).Error
	if err != nil {
		return orderOnlinePay, err
	}
	return orderOnlinePay, nil
}

func (ops *OnlinePayService) CancelOrderOnlinePay(orderNo string) error {
	return nil
}

func (ops *OnlinePayService) confirmOrderOnlinePay(tx *gorm.DB, r payReq.OnlinePayCallbackRequest, operatorId uint) error {
	// 1. check order_online_pay 【done】
	// 2. update order status to complete/awaiting delivery 【done】
	//  2.1 add order record 【done】
	// 	2.2 update order goods status to ... 【done】
	//  2.3 if order subtype is types.Presentation(压货订单),增加云仓库存, 增加云仓流水 【done】
	//  2.4 if order subtype is types.Ordinary(普通订单),修改订单状态，并发送库房 【done】
	// 3. add franchisee rebate , add recharge record 【done】
	// 4. update order online pay status, add order online pay record 【done】

	// get and check order_online_pay
	global.GVA_LOG.Debug("service.pay.payService.confirmOrderOnlinePay", zap.Any("r", r))
	oop := payModel.OrderOnlinePay{}
	err := tx.Set("gorm:query_option", "FOR UPDATE").Model(&oop).Where("order_no = ? AND status = ? AND pay_channel = ? AND pay_type = ? AND trade_no like ?", r.OrderNo, types.OnlinePayStatusPaying, r.PayChannel, r.PayType, r.TradeNo+"_%").First(&oop).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.First(&oop)", zap.Any("r", r), zap.Any("err", err))
		return errors.New("order online pay not found")
	} else if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.First(&oop)", zap.Any("r", r), zap.Any("err", err))
		return errors.Join(err, errors.New("get order online pay error"))
	}

	// update order status
	o := ordersModel.Order{}
	orderDestStatus := types.OrderAwaitingDelivery
	err = tx.Set("gorm:query_option", "FOR UPDATE").Model(&o).Where("order_no = ? AND payment_type = ? AND amount = ? AND status = ?", r.OrderNo, types.PaymentTypeOnlinePay, r.Amount, types.OrderStatusAwaitingPayment).First(&o).Error
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.First(&o)", zap.Any("r", r), zap.Any("err", err))
		return errors.Join(err, errors.New("get order error"))
	}
	if o.OrderSubType == types.Presentation {
		orderDestStatus = types.OrderCompleted
	}

	updates := struct {
		Status types.OrderStatus
	}{
		Status: orderDestStatus,
	}

	err = tx.Model(&o).Clauses(clause.Returning{}).Where("order_no = ? AND payment_type = ? AND amount = ? AND status = ?", r.OrderNo, types.PaymentTypeOnlinePay, r.Amount, types.OrderStatusAwaitingPayment).
		Updates(map[string]interface{}{
			"status": orderDestStatus},
		).Error
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.Updates(&o)", zap.Any("r", r), zap.Any("updates", updates), zap.Any("err", err))
		return errors.Join(err, errors.New("update order status error"))
	}
	if o.Status != orderDestStatus {
		o.Status = orderDestStatus
	}
	
	// Push notification for order status change after successful payment
	if err = utils.EnqueueOrderStatusChange(o.OrderNo, o.ID, int8(types.OrderStatusAwaitingPayment), int8(orderDestStatus), operatorId, fmt.Sprintf("Order status changed from %s to %s after successful payment", types.OrderStatusAwaitingPayment, orderDestStatus)); err != nil {
		global.GVA_LOG.Error("推送订单到通知服务失败", zap.Error(err), zap.Any("orderNo", o.OrderNo))
		// Continue despite notification failure - don't return error
	}
	// update order record
	orJson, err := json.Marshal(ordersModel.OrderRecordExtra{
		Req:    r,
		Result: o,
		Stage:  types.OrderStatusPaymentSuccess.StringEn(),
	})
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.MarshalJson", zap.Any("r", r), zap.Any("err", err))
		return errors.Join(err, errors.New("marshal order record error"))
	}
	var or []ordersModel.OrderRecord
	or = append(or, ordersModel.OrderRecord{
		GVA_MODEL:   global.GVA_MODEL{},
		OrderID:     o.ID,
		OrderNo:     o.OrderNo,
		OrderStatus: types.OrderStatusPaymentSuccess,
		Remark:      r.PayChannel.String() + " " + r.PayType.String() + r.Result.String(),
		Extra:       orJson,
		OperatorID:  operatorId,
	})
	or = append(or, ordersModel.OrderRecord{
		GVA_MODEL:   global.GVA_MODEL{},
		OrderID:     o.ID,
		OrderNo:     o.OrderNo,
		OrderStatus: orderDestStatus,
		Remark:      "",
		OperatorID:  operatorId,
	})
	global.GVA_LOG.Debug("service.pay.payService.confirmOrderOnlinePay.Create(or)", zap.Any("r", r), zap.Any("or", or))
	err = tx.Create(or).Error
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.Create(or)", zap.Any("r", r), zap.Any("err", err))
		return errors.Join(err, errors.New("create order record error"))
	}

	// 2.2 update order goods status to ...
	var ogs []ordersModel.OrderGoods
	// 2.2.1 get order goods
	err = tx.Model(ogs).Where("order_id = ? AND status = ?", o.ID, types.OrderGoodsStatusAwaitingPayment).Find(&ogs).Error
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.Find ogs", zap.Any("r", r), zap.Any("err", err))
		return errors.Join(err, errors.New("get order goods error in confirmOrderOnlinePay"))
	}
	if len(ogs) == 0 {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay", zap.Any("r", r), zap.Any("err", err))
		return errors.New("order goods not found")
	}
	err = tx.Model(&ogs).Clauses(clause.Returning{}).Where("order_id = ? AND status = ?", o.ID, types.OrderGoodsStatusAwaitingPayment).Updates(map[string]interface{}{
		"status": types.AwaitingDelivery,
	}).Error
	global.GVA_LOG.Debug("service.pay.payService.confirmOrderOnlinePay.ogs", zap.Any("r", r), zap.Any("ogs", ogs))
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay", zap.Any("r", r), zap.Any("err", err))
		return errors.Join(err, errors.New("update order goods status error"))
	}
	if len(ogs) == 0 {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay", zap.Any("r", r), zap.Any("err", err))
		return errors.New("order goods updates status not found")
	}

	if o.OrderSubType == types.Presentation && o.PaymentType == types.PaymentTypeOnlinePay {
		// @TODO:
		// 2.3 if order sub type is types.Presentation(压货订单),增加云仓库存, 增加云仓流水
		cw := cloudWarehouseModel.CloudWarehouse{
			GVA_MODEL:    global.GVA_MODEL{},
			FranchiseeId: o.FranchiseeId,
			OrderNo:      o.OrderNo,
		}
		if err = tx.Create(&cw).Error; err != nil {
			global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.Create cloudWarehouse ", zap.Any("r", r), zap.Any("cw", cw), zap.Any("err", err))
			return fmt.Errorf("CloudWarehouse create failed, err:=%w", err)
		}
		subcws := make([]*cloudWarehouseModel.SubCloudWarehouse, 0, len(ogs))
		cloudWarehouseRecords := make([]cloudWarehouseModel.CloudWarehouseRecord, 0, len(ogs))

		for _, og := range ogs {
			scw := cloudWarehouseModel.SubCloudWarehouse{
				GVA_MODEL:        global.GVA_MODEL{},
				CloudWarehouseId: cw.ID,
				ProductId:        og.ProductId,
			}
			if og.Quantity > 0 {
				scw.Inventory = og.Quantity
				if og.IsGift && og.UnitPrice == 0 {
					scw.UnitPrice = 0
				} else {
					scw.UnitPrice = og.UnitPrice
				}
			} else if og.PointsQuantity > 0 {
				scw.Inventory = og.PointsQuantity
				scw.UnitPrice = og.PointsPrice
			}
			subcws = append(subcws, &scw)
			cwr := cloudWarehouseModel.CloudWarehouseRecord{
				FranchiseeId:     o.FranchiseeId,
				CloudWarehouseId: cw.ID,
				ProductId:        og.ProductId,
				Inventory:        scw.Inventory,
				Pieces:           scw.Inventory,
				TradeNo:          o.OrderNo,
				Remark:           o.Remark,
				OperatorID:       operatorId,
			}
			cloudWarehouseRecords = append(cloudWarehouseRecords, cwr)
		}

		if err = tx.Create(subcws).Error; err != nil {
			global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.Create subCloudWarehouses", zap.Any("r", r), zap.Any("subcws", subcws), zap.Any("err", err))
			return fmt.Errorf("SubCloudWarehouse create failed, err:=%w", err)
		}
		if err = tx.Create(cloudWarehouseRecords).Error; err != nil {
			global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.Create cloudWarehouseRecords", zap.Any("r", r), zap.Any("cloudWarehouseRecords", cloudWarehouseRecords), zap.Any("err", err))
			return fmt.Errorf("CloudWarehouseRecord create failed, err:=%w", err)
		}

		// Push to notification service for CloudWarehouse OnlinePay orders
		if err = utils.EnqueueOrderStatusChange(o.OrderNo, o.ID, int8(o.Status), int8(o.Status), operatorId, "CloudWarehouse created after online payment"); err != nil {
			global.GVA_LOG.Error("推送订单到通知服务失败", zap.Error(err), zap.Any("orderNo", o.OrderNo))
			// Continue despite notification failure - don't return error
		}
	} else if o.OrderSubType == types.Ordinary {
		// 2.4 if order sub type is types.Ordinary(普通订单),修改订单状态并发送库房
		// @TODO:
		// 分配订单至指定库房
		warehouseService := &bigwarehouse.WarehouseService{}
		re, err := warehouseService.DispatchOrderToBigWarehouseWithinTx(tx, []uint{o.ID})
		if err != nil {
			global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.DispatchOrderToBigWarehouse err:", zap.Error(err), zap.Any("orderId", o.ID))
			//return err
		} else {
			global.GVA_LOG.Debug("service.pay.payService.confirmOrderOnlinePay.DispatchOrderToBigWarehouse re:", zap.Any("re", re))
		}
		
		// Push to notification service for Ordinary orders after dispatching to BigWarehouse
		if err = utils.EnqueueOrderStatusChange(o.OrderNo, o.ID, int8(o.Status), int8(o.Status), operatorId, "Order dispatched to warehouse after online payment"); err != nil {
			global.GVA_LOG.Error("推送订单到通知服务失败", zap.Error(err), zap.Any("orderNo", o.OrderNo))
			// Continue despite notification failure - don't return error
		}
	}

	// 3. update franchisee rebeat
	// 3.1 get sum of order_goods reteat
	reteatSum := 0
	err = tx.Model(&ordersModel.OrderGoods{}).Where("order_id = ?", o.ID).Select("SUM(quantity * rebate) as reteat_sum").Row().Scan(&reteatSum)
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.GetRebateSumOfOrderGoods.Scan err:", zap.Error(err), zap.Any("orderId", o.ID))
		return errors.Join(err, errors.New("get sum of order goods rebate error"))
	}
	global.GVA_LOG.Info("service.pay.payService.confirmOrderOnlinePay.GetRebateSumOfOrderGoods.Updates", zap.Any("reteatSum", reteatSum), zap.Any("oop", oop))
	var rrs []franchiseeModel.RechargeRecord
	if oop.Status == types.OnlinePayStatusPaying {
		// 3.2. add rebate to franchisee.point and update franchisee point
		f := franchiseeModel.Franchisee{}
		err = tx.Set("gorm:query_option", "FOR UPDATE").Where("id = ?", o.FranchiseeId).First(&f).Error
		if err != nil {
			global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.AddRebateToFranchisee.First err:", zap.Error(err), zap.Any("orderId", o.ID))
			return errors.Join(err, errors.New("add rebate to franchisee rebated points, get franchisee error"))
		}
		// 3. add franchisee recharge record for user paid and the rebate
		if reteatSum > 0 {
			global.GVA_LOG.Info("service.pay.payService.confirmOrderOnlinePay.AddRebateToFranchisee.Updates", zap.Any("f", f))
			err = tx.Model(&f).Where("id = ?", o.FranchiseeId).Updates(map[string]interface{}{
				"points": gorm.Expr("points + ?", oop.Rebate),
			}).Error
			if err != nil {
				global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.AddRebateToFranchisee.Updates err:", zap.Error(err), zap.Any("orderId", o.ID))
				return errors.Join(err, errors.New("add rebate to franchisee rebated points error"))
			}
			if reteatSum == int(oop.Rebate) {
				rrs = append(rrs, franchiseeModel.RechargeRecord{
					GVA_MODEL:    global.GVA_MODEL{},
					FranchiseeId: uint(o.FranchiseeId),
					RechargeType: utils.RechargeTypePtr(types.RechargePoints),
					OperateType:  utils.OperateTypePtr(types.OrderRebate),
					AccountType:  utils.AccountTypePtr(types.GeneralAccount),
					AccountID:    utils.UintPtr(0),
					Amount:       int(oop.Rebate),
					Balance:      f.Points + reteatSum,
					SerialNo:     r.TradeNo,
					TradeNo:      o.OrderNo,
					OperatorID:   operatorId,
				})
			} else {
				global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.AddRebateToFranchisee.Updates err:", zap.Any("orderId", o.ID), zap.Any("reteatSum", reteatSum), zap.Any("oop.Rebate", oop.Rebate))
				return errors.New("add rebate to franchisee rebated points error")
			}
		}

		rrs = append(rrs, franchiseeModel.RechargeRecord{
			GVA_MODEL:    global.GVA_MODEL{},
			FranchiseeId: uint(o.FranchiseeId),
			RechargeType: utils.RechargeTypePtr(types.RechargeOnlinpay),
			OperateType:  utils.OperateTypePtr(types.OperateTypeOnlinePay),
			AccountType:  utils.AccountTypePtr(types.AccountTypeOnlinePay),
			AccountID:    utils.UintPtr(0),
			Amount:       int(oop.Amount),
			Balance:      f.Balance, // 不改变用户余额
			SerialNo:     r.TradeNo,
			TradeNo:      o.OrderNo,
			OperatorID:   operatorId,
		})
		err = tx.Create(rrs).Error
		if err != nil {
			global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.CreateRechargeRecord.Updates err:", zap.Error(err), zap.Any("orderId", o.ID))
			return errors.Join(err, errors.New("add franchisee recharge record for the rebeat error"))
		}
	}
	// update order_online_pay status
	err = tx.Model(&oop).Clauses(clause.Returning{}).Where("order_no = ? AND status = ? AND pay_channel = ? AND pay_type = ?", r.OrderNo, types.OnlinePayStatusPaying, r.PayChannel, r.PayType).Updates(map[string]interface{}{
		"status":    types.OnlinePayStatusSuccess,
		"paid_time": time.Now().Local(),
	}).Error
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.UpdateOrderOnlinePay.Updates err:", zap.Error(err), zap.Any("orderId", o.ID))
		return errors.Join(err, errors.New("update order online pay status error"))
	}
	if oop.Status != types.OnlinePayStatusSuccess {
		oop.Status = types.OnlinePayStatusSuccess
	}
	if oop.PaidTime == nil {
		ooppt := time.Now().Local()
		oop.PaidTime = &ooppt
	}

	// add order_online_pay_record status
	ooprExtra := payModel.OrderOnlinePayRecordExtra{
		Req:    r,
		Result: oop,
		Stage:  types.OnlinePayStatusSuccess,
	}
	extraJson, err := json.Marshal(ooprExtra)
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.UpdateOrderOnlinePay.JsonMarshal err:", zap.Error(err), zap.Any("orderId", o.ID))
		return errors.Join(err, errors.New("marshal order online pay record extra error"))
	}
	oopr := &payModel.OrderOnlinePayRecord{
		GVA_MODEL:        global.GVA_MODEL{},
		OrderOnlinePayId: oop.ID,
		OrderNo:          r.OrderNo,
		Amount:           r.Amount,
		PayChannel:       r.PayChannel,
		PayType:          r.PayType,
		UserIP:           r.CallBackIP,
		Status:           types.OnlinePayStatusSuccess,
		Extra:            extraJson,
	}
	err = tx.Create(oopr).Error
	if err != nil {
		global.GVA_LOG.Error("service.pay.payService.confirmOrderOnlinePay.UpdateOrderOnlinePay.Create err:", zap.Error(err), zap.Any("oopr", oopr), zap.Any("orderId", o.ID))
		return errors.Join(err, errors.New("add order online pay record status error"))
	}
	return nil
} // update order online pay status to paid or canceled

func (ops *OnlinePayService) ConfirmOrderOnlinePayViaCallBack(r payReq.OnlinePayCallbackRequest, req interface{}) error {
	global.GVA_LOG.Debug("ConfirmOrderOnlinePayViaCallBack", zap.Any("r", r), zap.Any("req", req))
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := ops.confirmOrderOnlinePay(tx, r, 0); err != nil {
			return err
		}
		return nil
	})
	if err == nil {
		return nil
	}
	global.GVA_LOG.Error("ConfirmOrderOnlinePayViaCallBack", zap.Any("r", r), zap.Any("req", req), zap.Error(err))
	return errors.Join(err, errors.New("confirm order online pay via callback error"))
}

func (ops *OnlinePayService) CreateOrderOnlinePayRecord(tx *gorm.DB, o *ordersModel.Order, oopId uint, payChannel types.OnlinePayChannel, payType types.OnlinePayType, amount uint, userIp string) (uint, error) {
	// create order online pay record
	record := &payModel.OrderOnlinePayRecord{
		GVA_MODEL:        global.GVA_MODEL{},
		OrderOnlinePayId: oopId,
		OrderNo:          o.OrderNo,
		Amount:           amount,
		PayChannel:       payChannel,
		PayType:          payType,
		UserIP:           userIp,
		Status:           types.OnlinePayStatusPending,
		Extra:            nil,
	}

	err := tx.Create(record).Error
	if err != nil {
		return 0, errors.Join(err, errors.New("create order online pay record error"))
	}
	return record.ID, nil
}

func (ops *OnlinePayService) UpdateTradeNoForOrderOnlinePayRecordAfterCreatePayOrder(tx *gorm.DB, oopId, ooprId uint, orderNo string, payChannel types.OnlinePayChannel, payType types.OnlinePayType, tradeNo string, req, resp interface{}, status types.OnlinePayStatus) error {
	extra := &payModel.OrderOnlinePayRecordExtra{
		Req:    req,
		Result: resp,
		Stage:  status,
	}
	extraJson, err := json.Marshal(extra)
	if err != nil {
		return errors.Join(err, errors.New("marshal extra error"))
	}
	return tx.Model(&payModel.OrderOnlinePayRecord{}).Where("id= ? AND order_online_pay_id = ? AND order_no = ? AND pay_channel = ? AND pay_type = ? AND status = ?", ooprId, oopId, orderNo, payChannel, payType, types.OnlinePayStatusPending).Updates(map[string]interface{}{
		"trade_no": tradeNo,
		"status":   status,
		"extra":    extraJson,
	}).Error
}

func (ops *OnlinePayService) OnlinePayCallbackHeepay(req interface{}, reqIp string) error {
	global.GVA_LOG.Info("OnlinePayCallback", zap.Any("req", req), zap.String("reqIp", reqIp))
	if req == nil {
		global.GVA_LOG.Error("service.OnlinePayCallback req is nil")
		return errors.New("req is nil")
	}
	userPayChannel := payChannelServiceMap[types.OnlinePayChannelHeepay]
	var reqCallBackIface interface{}
	var ok bool
	var err error

	if ok, reqCallBackIface, err = userPayChannel.CallBackSign(req); !ok || err != nil || reqCallBackIface == nil {
		global.GVA_LOG.Error("service.OnlinePayCallback CallBackSign error", zap.Any("req", req), zap.String("reqIp", reqIp), zap.Error(err), zap.Bool("ok", ok), zap.Any("reqCallBackIface", reqCallBackIface))
		return errors.Join(err, errors.New("callback sign error"))
	}

	if reqCallBack, ok := reqCallBackIface.(heepay.HeepayCallbackRequest); !ok {
		global.GVA_LOG.Error("service.OnlinePayCallback reqCallBackIface is not heepayCallbackRequest", zap.Any("req", req), zap.String("reqIp", reqIp), zap.Any("reqCallBackIface", reqCallBackIface), zap.Bool("ok", ok), zap.Any("type", reflect.TypeOf(reqCallBackIface)))
		return errors.New("req is not heepayCallbackRequest")
	} else {
		o := &ordersModel.Order{}
		err = global.GVA_DB.Where("order_no = ?", reqCallBack.AgentBillId).First(o).Error
		if err != nil {
			global.GVA_LOG.Error("service.OnlinePayCallback get order error", zap.Any("req", req), zap.String("reqIp", reqIp))
			return errors.Join(err, errors.New("get order error"))
		}
		if o.Status != types.OrderStatusAwaitingPayment {
			global.GVA_LOG.Error("service.OnlinePayCallback order status is not awaiting payment", zap.Any("req", req), zap.String("reqIp", reqIp))
			return errors.New("order status is not awaiting payment")
		}
		payamt, err := strconv.ParseFloat(reqCallBack.PayAmt, 64)
		if err != nil {
			global.GVA_LOG.Error("service.OnlinePayCallback parse order amount error", zap.Any("req", req), zap.String("reqIp", reqIp))
			return errors.Join(err, errors.New("parse order amount error"))
		}
		if o.Amount != int(payamt*100) {
			global.GVA_LOG.Error("service.OnlinePayCallback order amount is not equal to callback amount", zap.Any("req", req), zap.String("reqIp", reqIp))
			return errors.New("order amount is not equal to callback amount")
		}
		var callbackReq payReq.OnlinePayCallbackRequest
		if reqCallBack.Result == utilHeepayClient.HeepayWechatMiniProgramPayResultSuccess {
			callbackReq.Result = types.OnlinePayStatusSuccess
		} else {
			global.GVA_LOG.Error("service.OnlinePayCallback callback result is not success", zap.Any("req", req), zap.String("reqIp", reqIp))
			callbackReq.Result = types.OnlinePayStatusFailed
		}
		callbackReq.OrderNo = reqCallBack.AgentBillId
		callbackReq.TradeNo = reqCallBack.JnetBillNo
		if reqCallBack.PayType == utilHeepayClient.HeepayWechatMiniProgramPayType {
			callbackReq.PayType = types.OnlinePayTypeWechatIndirect
		}
		callbackReq.TradeNo = reqCallBack.JnetBillNo
		callbackReq.Amount = uint(payamt * 100)
		callbackReq.PayChannel = types.OnlinePayChannelHeepay
		global.GVA_LOG.Info("OnlinePayCallback", zap.Any("callbackReq", callbackReq))
		return ops.ConfirmOrderOnlinePayViaCallBack(callbackReq, reqCallBack)
	}
}

func (ops *OnlinePayService) RefundOnlinePayOrder(tx *gorm.DB, orderNo string, amount uint, userIp string, operatorId uint) error {
	// 1. Get and Check
	// 	1.1. get and check order and order record
	//  1.2. get and check orderOnlinePay
	//  1.3. get and check orderOnlineRecord
	// 2. Refunding
	//  2.1. update orderOnlinePay status to refunding
	//  2.2 create orderOnlineRecord
	//  2.4. create order record
	//  2.5. update order to refunding, remove bigwarehouse_order or update status to refunding
	//  2.6. update order goods to refunding
	//  2.7. deduct cloudwarehouse inventory , remove sub cloudwarehouse , add clousewarehouse record
	//  2.8. call refund api
	// 3. Refund
	//  3.1  add orderOnlineRecord
	//  3.2. update orderOnlinePay to refunding
	//  3.3. add order record[ignore]
	//  3.4. update order to refunded [ignore]
	//  3.5. update franchisee point rebeat [ignore]
	//  3.6  add recharge record for deduct franchisee point [ignore]

	//  1.2. get and check orderOnlinePay
	oop := payModel.OrderOnlinePay{}
	err := global.GVA_DB.Set("gorm:query_option", "FOR UPDATE").Model(&oop).Where("order_no = ? AND status = ?", orderNo, types.OnlinePayStatusSuccess).Last(&oop).Error
	global.GVA_LOG.Info("service.pay.RefundOnlinePayOrder oop", zap.Any("oop", oop), zap.Any("orderNo", orderNo), zap.Any("amount", amount), zap.Any("userIp", userIp), zap.Any("operatorId", operatorId), zap.Any("err", err))
	if err != nil {
		return errors.Join(err, errors.New("get order online pay error"))
	}
	if oop.Amount != amount {
		return errors.New("order online pay amount is not equal to order goods amount, can not refund")
	}
	//  1.3. get and check orderOnlineRecord
	// @TODO： check whether this is necessary or not??
	oopr := payModel.OrderOnlinePayRecord{}
	err = global.GVA_DB.Model(&oopr).Where("order_no = ? ", orderNo).Last(&oopr).Error
	global.GVA_LOG.Info("service.pay.RefundOnlinePayOrder oopr", zap.Any("oopr", oopr), zap.Any("orderNo", orderNo), zap.Any("amount", amount), zap.Any("userIp", userIp), zap.Any("operatorId", operatorId), zap.Any("err", err))
	if err != nil {
		return errors.Join(err, errors.New("get order online record error"))
	}

	// 2. Refunding
	//  2.1. call pay channel to refund
	payChannelService, ok := payChannelServiceMap[oop.PayChannel]
	if !ok {
		return errors.New("pay channel service is not found")
	}
	if payChannelService == nil {
		return errors.New("pay channel service is not found")
	}
	req, resp, respStr, err := payChannelService.RefundOrderOnlinePay(orderNo, int(amount))
	global.GVA_LOG.Info("service.pay.RefundOnlinePayOrder", zap.Any("req", req), zap.Any("resp", resp), zap.Any("respStr", respStr), zap.Any("err", err))
	if err != nil {
		return errors.Join(err, errors.New("call refund api error: "+orderNo+" return error:"+respStr))
	}
	tx.SavePoint("sp1")
	extra := payModel.OrderOnlinePayRecordExtra{
		Req:    req,
		Result: resp,
		Stage:  types.OnlinePayStutusRefunding,
	}
	extraJson, err := json.Marshal(extra)
	if err != nil {
		return errors.Join(err, errors.New("refund error"))
	}

	oopr_ := payModel.OrderOnlinePayRecord{
		OrderNo:    orderNo,
		PayType:    oop.PayType,
		PayChannel: oop.PayChannel,
		Status:     types.OnlinePayStutusRefunding,
		Amount:     oop.Amount,
		UserIP:     userIp,
		CreatedBy:  operatorId,
		// OpenId:     oop.OpenId,
		Extra: extraJson,
	}
	if err := global.GVA_DB.Create(&oopr_).Error; err != nil {
		return errors.Join(err, errors.New("create order online record error"))
	}
	tx.SavePoint("sp2")
	//  2.2. update orderOnlinePay

	err = global.GVA_DB.Model(&oop).Where("order_no = ? AND status = ?", orderNo, types.OnlinePayStatusSuccess).Update("status", types.OnlinePayStutusRefunding).Error
	if err != nil {
		tx.RollbackTo("sp2")
		return errors.Join(err, errors.New("update order online pay error"))
	}
	tx.SavePoint("sp3")
	// //  2.3. create orderOnlineRecord
	// //  2.4. create order record
	// or := ordersModel.OrderRecord{
	// 	OrderID:     o.ID,
	// 	OrderNo:     o.OrderNo,
	// 	OrderStatus: types.OrderStatusRefunding,
	// 	OperatorID:  operatorId,
	// 	Extra:       extraJson, // @TODO: change extra to OrderRecordExtra
	// }
	// if err := global.GVA_DB.Create(&or).Error; err != nil {
	// 	return errors.Join(err, errors.New("create order record error"))
	// }
	// //  2.5. update order to refunding
	// err = global.GVA_DB.Model(&o).Where("order_no = ?", orderNo).Update("status", types.OrderStatusRefunding).Error
	// if err != nil {
	// 	return errors.Join(err, errors.New("update order status error"))
	// }
	return err
}

func refundCheckSign(req *utilHeepayClient.HeepayRefundCallbackRequest) bool {
	global.GVA_LOG.Info("service.pay.refundCheckSign", zap.Any("req", req))
	return true
}

func (ops *OnlinePayService) RefundOrderOnlinePayHeepayCallBack(req *utilHeepayClient.HeepayRefundCallbackRequest, callBackIp string) error {
	// 3. Refund
	//  3.0. check sign
	//  3.1  add orderOnlinePayRecord
	//  3.2. update orderOnlinePay to refunded
	//  3.3. create orderRecord
	//  3.4. update order to refunded
	if req == nil {
		return errors.New("req is nil")
	}
	// 3.0 check sign
	if refundCheckSign(req) == false {
		return errors.New("sign check failed")
	}
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// get order and oop
		var o ordersModel.Order
		err := tx.Set("gorm:query_option", "FOR UPDATE").Where("order_no = ?", req.AgentBillId).First(&o).Error
		global.GVA_LOG.Info("service.pay.RefundOrderOnlinePayHeepayCallBack", zap.Any("req", req), zap.Any("o", o), zap.Any("err", err))
		if err != nil {
			return errors.Join(err, errors.New("get order error: "+req.AgentBillId))
		}
		if o.Status != types.OrderStatusRefunding {
			return errors.New("order status not match, record status: " + o.Status.String() + " req status: " + types.OrderStatusRefunding.String())
		}
		var oop payModel.OrderOnlinePay
		err = tx.Set("gorm:query_option", "FOR UPDATE").Where("order_no = ?", req.AgentBillId).First(&oop).Error
		global.GVA_LOG.Info("service.pay.RefundOrderOnlinePayHeepayCallBack", zap.Any("req", req), zap.Any("oop", oop), zap.Any("err", err))
		if err != nil {
			return errors.Join(err, errors.New("get order online record error: "+req.AgentBillId))
		}
		if oop.Status != types.OnlinePayStutusRefunding {
			return errors.New("order online record status not match, record status: " + oop.Status.String() + " req status: " + types.OnlinePayStutusRefunding.String())
		}
		if oop.Amount != uint(req.RefundAmt.Mul(decimal.NewFromInt(100)).BigInt().Uint64()) {
			return errors.New("order online record amount not match, record amount: " + strconv.Itoa(int(oop.Amount)) + " req amount: " + strconv.Itoa(int(req.RefundAmt.Mul(decimal.NewFromInt(100)).BigInt().Uint64())))
		}
		arr := strings.Split(oop.TradeNo, "_")
		if len(arr) != 2 {
			return errors.New("hy bill format error , hy bill no not match, record hyBillNo: " + req.HyBillNo + " req hyBillNo: " + oop.TradeNo)
		}
		if req.HyBillNo != arr[0] {
			return errors.New("hy bill no not match, record hyBillNo: " + req.HyBillNo + " req hyBillNo: " + oop.TradeNo)
		}

		// 3.2. update orderOnlinePay to refunded
		err = tx.Model(&oop).Clauses(clause.Returning{}).Where("order_no = ?", req.AgentBillId).Update("status", types.OnlinePayStutusRefunded).Error
		if err != nil {
			return errors.Join(err, errors.New("update order online record status error: "+req.AgentBillId))
		}
		// 3.1  add orderOnlinePayRecord
		callbackExtra := payModel.OrderOnlinePayRecordExtra{
			Req:    req,
			Result: oop,
			Stage:  types.OnlinePayStutusRefunded,
		}
		extraJson, _ := json.Marshal(callbackExtra)

		oopr := payModel.OrderOnlinePayRecord{
			GVA_MODEL:        global.GVA_MODEL{},
			OrderOnlinePayId: oop.ID,
			OrderNo:          req.AgentBillId,
			Amount:           oop.Amount,
			PayType:          oop.PayType,
			PayChannel:       oop.PayChannel,
			TradeNo:          req.HyBillNo,
			Status:           types.OnlinePayStutusRefunded,
			UserIP:           callBackIp,
			CreatedBy:        0,
			Extra:            extraJson,
		}

		err = tx.Create(&oopr).Error
		if err != nil {
			return errors.Join(err, errors.New("add order online record error: "+req.AgentBillId))
		}
		// 3.4. update order to refunded
		err = tx.Model(&o).Clauses(clause.Returning{}).Where("order_no = ?", req.AgentBillId).Update("status", types.OrderRefund).Error
		if err != nil {
			return errors.Join(err, errors.New("update order status error: "+req.AgentBillId))
		}

		// 4. add order record
		oExtra := ordersModel.OrderRecordExtra{
			Req:    req,
			Result: o,
			Stage:  types.OrderRefund.StringEn(),
		}
		extraJson, _ = json.Marshal(oExtra)
		or := ordersModel.OrderRecord{
			GVA_MODEL:   global.GVA_MODEL{},
			OrderID:     o.ID,
			OrderNo:     o.OrderNo,
			OrderStatus: types.OrderRefund,
			OperatorID:  0,
			Extra:       extraJson,
		}

		err = tx.Create(&or).Error
		if err != nil {
			return errors.Join(err, errors.New("add order record error: "+req.AgentBillId))
		}
		return nil

	})

	return err

}

func (ops *OnlinePayService) GetOrderOnlinePay(orderNo string) (*payModelResp.OrderOnlinePayResp, error) {
	// 1. get orderOnlinePay
	var oopr payModelResp.OrderOnlinePayResp
	err := global.GVA_DB.Preload("OopRecords").Model(payModel.OrderOnlinePay{}).Where("order_no = ?", orderNo).Last(&oopr).Error
	if err != nil {
		return nil, err
	}
	return &oopr, nil
}

func (ops *OnlinePayService) HeepayAllotTradeCallBack(heepayAllotReq heepay.HeepayAllotTradeCallbakReq, callbackIp string) error {
	global.GVA_LOG.Debug("service.PayService.HeepayallotTradeCallBack HeepayAllotTradeCallBack", zap.Any("heepayAllotReq", heepayAllotReq))

	// 1. get encrypted data and decrypt to plain text
	plainText, err := heepay.HeepayAllotCallBackDecrypt(&heepayAllotReq)
	if err != nil {
		global.GVA_LOG.Error("service.PayService.HeepayallotTradeCallBack HeepayAllotCallBackDecrypt error", zap.Error(err), zap.Any("heepayAllotReq", heepayAllotReq))
		return err
	}
	global.GVA_LOG.Debug("service.PayService.HeepayallotTradeCallBack plainText", zap.Any("plainText", plainText))

	// 2. transform plain text to heepay.HeepayAllotTradeData
	heepayAllotData, err := heepay.TransformToHeepayAllotTradeDa(plainText)
	heepayAllotData.EncryptData = heepayAllotReq.EncryptData
	heepayAllotData.Sign = heepayAllotReq.Sign

	if err != nil {
		global.GVA_LOG.Error("service.PayService.HeepayallotTradeCallBack TransformToHeepayAllotTradeDa error", zap.Error(err), zap.Any("heepayAllotReq", heepayAllotReq), zap.Any("plainText", plainText))
		return errors.Join(err, errors.New("TransformToHeepayAllotTradeDa error"))
	}
	global.GVA_LOG.Debug("service.PayService.HeepayallotTradeCallBack heepayAllotData", zap.Any("heepayAllotData", heepayAllotData))

	// 3. insert into heepayallotTrade table
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		//  3.1. insert into heepayallotTradeOrder
		var heepayAllotOrder payReq.HyAllotOrderCallbackCreate
		heepayAllotOrder.HyBillNo = heepayAllotData.HyBillNo
		heepayAllotOrder.AgentId = heepayAllotData.AgentId
		heepayAllotOrder.RefAgentId = heepayAllotData.RefAgentId
		heepayAllotOrder.AllotAmount = decimal.NewNullDecimal(heepayAllotData.AllotAmt)
		heepayAllotOrder.RealAmount = decimal.NewNullDecimal(heepayAllotData.RealAmt)
		heepayAllotOrder.TradeAmount = decimal.NewNullDecimal(heepayAllotData.TradeAmt)
		heepayAllotOrder.Balance = decimal.NewNullDecimal(heepayAllotData.AllotAmt)
		heepayAllotOrder.TradeTime, err = time.ParseInLocation("20060102150405", heepayAllotData.TradeTime, time.Local)
		heepayAllotOrder.Status = types.AllotOrderCallBackStatusPending.ToPtr()

		if err != nil {
			return errors.Join(err, errors.New("time.ParseInLocation error"))
		}

		err := tx.Model(payModel.HyAllotOrderCallback{}).Create(
			&payModel.HyAllotOrderCallback{
				GVA_MODEL:                  global.GVA_MODEL{},
				HyAllotOrderCallbackCreate: heepayAllotOrder,
			}).Error
		if err != nil {
			return errors.Join(err, errors.New("create heepayallotTradeOrder error"))
		}

		//  3.2. create heepayallotTradeRecord
		var heepayAllotRecord payReq.HyAllotOrderCallbackRecordCreate
		heepayAllotRecord.HyAllotNo = heepayAllotData.HyBillNo
		extra := payModel.HyAllotOrderCallbackRecordReceiveCallbackExtra{
			EncryptData: heepayAllotData.EncryptData,
			Sign:        heepayAllotData.Sign,
			IP:          callbackIp,
		}

		heepayAllotRecord.Extra, _ = json.Marshal(extra)

		err = tx.Model(payModel.HyAllotOrderCallbackRecord{}).Create(
			&payModel.HyAllotOrderCallbackRecord{
				GVA_MODEL:                        global.GVA_MODEL{},
				HyAllotOrderCallbackRecordCreate: heepayAllotRecord,
			}).Error
		if err != nil {
			return errors.Join(err, errors.New("create heepayallotTradeRecord error"))
		}

		return nil
	})

	if err != nil {
		global.GVA_LOG.Error("service.PayService.HeepayallotTradeCallBack Transaction error", zap.Error(err), zap.Any("heepayAllotReq", heepayAllotReq), zap.Any("heepayAllotData", heepayAllotData), zap.String("callbackIp", callbackIp))
		return err
	}
	return nil
}

// OnlinePayUnpaidOrderClose
func (ops *OnlinePayService) OnlinePayUnpaidOrderClose(k config.OrderUnpaidTimerDetail) error {
	global.GVA_LOG.Info("OnlinePayUnpaidOrderClose start")
	duration, _ := time.ParseDuration(k.InterVal)

	err := global.GVA_DB.Model(&ordersModel.Order{}).
		Where(k.CompareField+" < ? AND "+k.CompareField+" > ? AND payment_type = ? AND status = ?", time.Now().Add(-duration), time.Now().Add(-duration*2*24*10), types.PaymentTypeOnlinePay, types.OrderStatusAwaitingPayment).
		Update("status", types.OrderStatusClosed).Error

	if err != nil {
		global.GVA_LOG.Error("OnlinePayUnpaidOrderClose Transaction error", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("OnlinePayUnpaidOrderClose done")

	return nil
}
