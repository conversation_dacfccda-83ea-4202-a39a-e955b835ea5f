package franchisees

import (
	"errors"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/system"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/plugin/tenant"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
)

type FranchiseeMultiTenantService struct{}

// FranchiseeTenant 加盟商租户信息
type FranchiseeTenant struct {
	TenantID       uint   `json:"tenantId"`
	TenantCode     string `json:"tenantCode"`
	TenantName     string `json:"tenantName"`
	FranchiseeID   uint   `json:"franchiseeId"`
	FranchiseeName string `json:"franchiseeName"`
	FranchiseeCode string `json:"franchiseeCode"`
	IsCurrent      bool   `json:"isCurrent"`
}

// MultiTenantLoginResponse 多租户登录响应
type MultiTenantLoginResponse struct {
	NeedTenantSelection bool               `json:"needTenantSelection"`
	AvailableTenants    []FranchiseeTenant `json:"availableTenants"`
	TempToken           string             `json:"tempToken"`
}

// GetUserFranchisees 获取用户的加盟商租户列表
func (s *FranchiseeMultiTenantService) GetUserFranchisees(userID uint) ([]FranchiseeTenant, error) {
	var franchisees []franchisees.Franchisee
	var tenants []FranchiseeTenant

	// 查询用户在所有租户中的加盟商身份
	err := global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("user_id = ?", userID).
		Find(&franchisees).Error
	if err != nil {
		global.GVA_LOG.Error("查询用户加盟商失败", zap.Error(err), zap.Uint("userID", userID))
		return nil, err
	}

	// 获取租户信息
	for _, franchisee := range franchisees {
		var tenantInfo system.Tenant
		err := global.GVA_DB.Scopes(tenant.SkipTenant).
			Where("id = ?", franchisee.TenantID).
			First(&tenantInfo).Error
		if err != nil {
			global.GVA_LOG.Warn("获取租户信息失败", zap.Error(err), zap.Uint("tenantID", franchisee.TenantID))
			continue
		}

		tenants = append(tenants, FranchiseeTenant{
			TenantID:       tenantInfo.ID,
			TenantCode:     tenantInfo.Code,
			TenantName:     tenantInfo.Name,
			FranchiseeID:   franchisee.ID,
			FranchiseeName: franchisee.Name,
			FranchiseeCode: franchisee.Code,
			IsCurrent:      false,
		})
	}

	return tenants, nil
}

// AuthenticateUser 验证用户凭据
func (s *FranchiseeMultiTenantService) AuthenticateUser(username, password string) (*system.SysUser, error) {
	var user system.SysUser
	err := global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("username = ?", username).
		First(&user).Error
	if err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	return &user, nil
}

// HandleSingleTenantLogin 处理单租户登录
func (s *FranchiseeMultiTenantService) HandleSingleTenantLogin(user *system.SysUser, franchisee *franchisees.Franchisee) (string, error) {
	// 获取租户信息
	var tenantInfo system.Tenant
	err := global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("id = ?", franchisee.TenantID).
		First(&tenantInfo).Error
	if err != nil {
		return "", errors.New("租户信息获取失败")
	}

	// 生成JWT令牌
	token, err := s.GenerateFranchiseeToken(user, franchisee, &tenantInfo)
	if err != nil {
		return "", err
	}

	return token, nil
}

// HandleMultiTenantLogin 处理多租户登录
func (s *FranchiseeMultiTenantService) HandleMultiTenantLogin(user *system.SysUser) (*MultiTenantLoginResponse, error) {
	// 获取用户的所有租户
	tenants, err := s.GetUserFranchisees(user.ID)
	if err != nil {
		return nil, err
	}

	// 生成临时令牌（不包含租户信息）
	tempToken, err := s.GenerateTempToken(user)
	if err != nil {
		return nil, err
	}

	return &MultiTenantLoginResponse{
		NeedTenantSelection: true,
		AvailableTenants:    tenants,
		TempToken:           tempToken,
	}, nil
}

// ConfirmTenantLogin 确认租户登录
func (s *FranchiseeMultiTenantService) ConfirmTenantLogin(tempToken string, tenantID uint) (string, error) {
	// 解析临时令牌
	j := utils.NewJWT()
	claims, err := j.ParseToken(tempToken)
	if err != nil {
		return "", errors.New("临时令牌无效")
	}

	// 获取用户信息
	var user system.SysUser
	err = global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("id = ?", claims.UserID).
		First(&user).Error
	if err != nil {
		return "", errors.New("用户信息获取失败")
	}

	// 获取用户在指定租户中的加盟商信息
	var franchisee franchisees.Franchisee
	err = global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("user_id = ? AND tenant_id = ?", user.ID, tenantID).
		First(&franchisee).Error
	if err != nil {
		return "", errors.New("您在该租户中没有加盟商身份")
	}

	// 获取租户信息
	var tenantInfo system.Tenant
	err = global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("id = ?", tenantID).
		First(&tenantInfo).Error
	if err != nil {
		return "", errors.New("租户信息获取失败")
	}

	// 生成最终令牌
	token, err := s.GenerateFranchiseeToken(&user, &franchisee, &tenantInfo)
	if err != nil {
		return "", err
	}

	return token, nil
}

// SwitchTenant 切换租户
func (s *FranchiseeMultiTenantService) SwitchTenant(userID, tenantID uint) (string, error) {
	// 获取用户信息
	var user system.SysUser
	err := global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return "", errors.New("用户信息获取失败")
	}

	// 获取用户在指定租户中的加盟商信息
	var franchisee franchisees.Franchisee
	err = global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		First(&franchisee).Error
	if err != nil {
		return "", errors.New("您在该租户中没有加盟商身份")
	}

	// 获取租户信息
	var tenantInfo system.Tenant
	err = global.GVA_DB.Scopes(tenant.SkipTenant).
		Where("id = ?", tenantID).
		First(&tenantInfo).Error
	if err != nil {
		return "", errors.New("租户信息获取失败")
	}

	// 生成新的JWT令牌
	token, err := s.GenerateFranchiseeToken(&user, &franchisee, &tenantInfo)
	if err != nil {
		return "", err
	}

	return token, nil
}

// GenerateFranchiseeToken 生成包含加盟商信息的JWT令牌
func (s *FranchiseeMultiTenantService) GenerateFranchiseeToken(user *system.SysUser, franchisee *franchisees.Franchisee, tenant *system.Tenant) (string, error) {
	j := utils.NewJWT()

	// 解析配置中的时间
	bf, _ := utils.ParseDuration(global.GVA_CONFIG.JWT.BufferTime)
	ep, _ := utils.ParseDuration(global.GVA_CONFIG.JWT.ExpiresTime)

	claims := systemReq.CustomClaims{
		BaseClaims: systemReq.BaseClaims{
			UUID:         user.UUID,
			ID:           user.ID,
			Username:     user.Username,
			NickName:     user.NickName,
			AuthorityId:  user.AuthorityId,
			UserType:     systemReq.UserTypeNormal,
			IsSuperAdmin: false,
		},
		BufferTime:   int64(bf / time.Second),
		UserID:       user.ID,
		TenantID:     tenant.ID,
		FranchiseeID: franchisee.ID,
		TenantCode:   tenant.Code,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(ep)),
			Issuer:    global.GVA_CONFIG.JWT.Issuer,
		},
	}

	token, err := j.CreateToken(claims)
	if err != nil {
		global.GVA_LOG.Error("生成JWT令牌失败", zap.Error(err))
		return "", errors.New("令牌生成失败")
	}

	return token, nil
}

// GenerateTempToken 生成临时令牌（用于租户选择）
func (s *FranchiseeMultiTenantService) GenerateTempToken(user *system.SysUser) (string, error) {
	j := utils.NewJWT()

	// 解析配置中的时间
	bf, _ := utils.ParseDuration(global.GVA_CONFIG.JWT.BufferTime)

	claims := systemReq.CustomClaims{
		BaseClaims: systemReq.BaseClaims{
			UUID:         user.UUID,
			ID:           user.ID,
			Username:     user.Username,
			NickName:     user.NickName,
			AuthorityId:  user.AuthorityId,
			UserType:     systemReq.UserTypeNormal,
			IsSuperAdmin: false,
		},
		BufferTime: int64(bf / time.Second),
		UserID:     user.ID,
		// 临时令牌不包含租户和加盟商信息
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(15 * time.Minute)), // 临时令牌15分钟过期
			Issuer:    global.GVA_CONFIG.JWT.Issuer,
		},
	}

	token, err := j.CreateToken(claims)
	if err != nil {
		global.GVA_LOG.Error("生成临时JWT令牌失败", zap.Error(err))
		return "", errors.New("临时令牌生成失败")
	}

	return token, nil
}
