package franchisees

import (
	"encoding/json"
	"errors"
	"math"
	"strings"
	"time"

	"github.com/samber/lo"

	"fmt"
	"sort"
	"strconv"

	appRes "github.com/OSQianXing/guanpu-server/model/app/response"

	pkgerrors "github.com/pkg/errors"
	"github.com/shopspring/decimal"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/account"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	commonResp "github.com/OSQianXing/guanpu-server/model/common/response"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	distributionModel "github.com/OSQianXing/guanpu-server/model/franchisees/distribution"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
	franchiseesRes "github.com/OSQianXing/guanpu-server/model/franchisees/response"
	distributionResp "github.com/OSQianXing/guanpu-server/model/franchisees/response/distribution"
	systemModel "github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/model/warehouse"
	distributionSrv "github.com/OSQianXing/guanpu-server/service/franchisees/distribution"
	systemSrv "github.com/OSQianXing/guanpu-server/service/system"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/gofrs/uuid"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type FranchiseeService struct {
}

// CreateFranchisee 创建Franchisee记录

func (franchiseeService *FranchiseeService) CreateFranchisee(franchisee *franchisees.Franchisee) (fid uint, err error) {
	tel, err := utils.CheckTel(franchisee.Tel)
	if err != nil {
		return 0, err
	}

	franchisee.Tel = tel

	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var u systemModel.SysUser
		err := tx.Where("username = ?", franchisee.Tel).First(&u).Error
		if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		if err == nil {
			return pkgerrors.New("用户已存在")
		}

		user := &systemModel.SysUser{Username: franchisee.Tel, NickName: franchisee.Name, Phone: franchisee.Tel}
		user.Password = utils.BcryptHash("123456")
		user.UUID = uuid.Must(uuid.NewV4())

		err = tx.FirstOrCreate(&user, user).Error
		if err != nil {
			return err
		}

		franchisee.UserID = user.ID
		err = tx.Create(&franchisee).Error
		if err != nil {
			if utils.IsDuplicateError(err) {
				return errors.New("编号已存在")
			}
			return err
		}

		//修改 franchisee_link 表中的客户关系
		if _, err := franchiseeService.FranchiseeLinkCreateLinkForSomeone(*franchisee, uint(franchisee.InviterID)); err != nil {
			return pkgerrors.New("创建团队关系失败")
		}
		// 增加团队关系表
		if err = franchiseeService.FranchiseeMemberLinkAddNewOne(franchisee.ID, uint(franchisee.InviterID)); err != nil {
			return err
		}

		// 处理经理绑定逻辑
		var fts []franchisees.FranchiseeTeam = make([]franchisees.FranchiseeTeam, 0, 2)

		if franchisee.MarketLeadID != 0 {
			fts = append(fts, franchisees.FranchiseeTeam{
				FranchiseeId: franchisee.ID,
				Type:         types.MarketLead,
				UserId:       uint(franchisee.MarketLeadID),
			})
		}
		if franchisee.SupervisionLeadID != 0 {
			fts = append(fts, franchisees.FranchiseeTeam{
				FranchiseeId: franchisee.ID,
				Type:         types.SupervisionLead,
				UserId:       uint(franchisee.SupervisionLeadID),
			})
		}
		//{
		//{
		//	FranchiseeId: franchisee.ID,
		//	Type:         types.MarketLead,
		//	UserId:       uint(franchisee.MarketLeadID),
		//},
		//{
		//	FranchiseeId: franchisee.ID,
		//	Type:         types.SupervisionLead,
		//	UserId:       uint(franchisee.SupervisionLeadID),
		//},
		//}
		if len(fts) > 0 {
			err = tx.Create(&fts).Error
			if err != nil {
				return err
			}
		}

		// 查询所选加盟商分类是否关联了专项账户
		categoryIDJSON, err := json.Marshal(*franchisee.FCategoryId)
		if err != nil {
			return err
		}

		var eas []account.ExclusiveAccount
		err = tx.Where("is_banned = 0 and JSON_CONTAINS(f_category_ids, ?)", categoryIDJSON).Find(&eas).Error
		if err != nil {
			return err
		}

		// 创建加盟商分类相关联的加盟商账户
		var fAccounts []franchisees.FranchiseeAccount
		for _, ea := range eas {
			fAccounts = append(fAccounts, franchisees.FranchiseeAccount{
				FranchiseeId:         franchisee.ID,
				ExclusiveAccountId:   ea.ID,
				ExclusiveAccountName: ea.Name,
			})
		}

		if len(fAccounts) > 0 {
			err = tx.Create(&fAccounts).Error
			if err != nil {
				return err
			}
		}

		return err
	})

	return franchisee.ID, err
}

// DeleteFranchisee 删除Franchisee记录

func (franchiseeService *FranchiseeService) DeleteFranchisee(franchisee franchisees.Franchisee) (err error) {
	err = global.GVA_DB.Delete(&franchisee).Error
	return err
}

// DeleteFranchiseeByIds 批量删除Franchisee记录

func (franchiseeService *FranchiseeService) DeleteFranchiseeByIds(ids request.IdsReq) (err error) {
	err = global.GVA_DB.Delete(&[]franchisees.Franchisee{}, "id in ?", ids.Ids).Error
	return err
}

// UpdateFranchisee 更新Franchisee记录

func (franchiseeService *FranchiseeService) UpdateFranchisee(franchisee franchisees.Franchisee) (err error) {
	var f *franchisees.Franchisee
	err = global.GVA_DB.Where("id = ?", franchisee.ID).First(&f).Error
	if err != nil {
		return err
	}
	if franchisee.InviterID != f.InviterID {
		//check if franchisee.InviterID is the member of f

		var fmemberids []uint
		fmemberids, err := franchiseeService.GetFranchiseeMembersByFranchiseeIdViaMemberLink(f.ID)
		if err != nil {
			global.GVA_LOG.Error("service.franchiseeService.UpdateFranchisee: ", zap.Error(err))
			return err
		}
		if lo.Contains(fmemberids, uint(franchisee.InviterID)) {
			return errors.New("提交的邀请人是当前加盟商的团队成员，请修改")
		}
		//TODO: 更新 FranchiseeLink 表
		global.GVA_LOG.Info("service.franchiseeService.UpdateFranchisee: ", zap.String("franchisee.InviterID", strconv.Itoa(int(franchisee.InviterID))), zap.String("f.InviterID", strconv.Itoa(int(f.InviterID))))
		err = franchiseeService.FranchiseeLinkUpdateInviter(*f, uint(franchisee.InviterID))
		if err != nil {
			global.GVA_LOG.Error("service.franchiseeService.UpdateFranchisee: ", zap.Error(err))
			return err
		}
		err = franchiseeService.FranchiseeMemberLinkUpdateInviter(f.ID, uint(f.InviterID), uint(franchisee.InviterID))
		if err != nil {
			global.GVA_LOG.Error("service.franchiseeService.UpdateFranchisee: ", zap.Error(err))
			return err
		}
	}
	if franchisee.FCategoryId != f.FCategoryId {
		// 更新加盟商分类，更新加盟商 franchisee_link 表
		if err := franchiseeService.updateLinkForFranchiseeCategory(franchisee); err != nil {
			global.GVA_LOG.Error("service.franchiseeService.UpdateFranchisee.updateLinkForFranchiseeCategory: ", zap.Error(err))
			return err
		}
	}
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if f.Tel != franchisee.Tel {
			tel, err := utils.CheckTel(franchisee.Tel)
			if err != nil {
				return err
			}

			franchisee.Tel = tel

			// 更新User.Username
			var u systemModel.SysUser
			err = tx.Where("id = ?", franchisee.UserID).First(&u).Error
			if err != nil {
				return err
			}

			u.Username = franchisee.Tel
			u.Phone = franchisee.Tel
			err = tx.Save(&u).Error
			if err != nil {
				return err
			}
		}

		disSrv := &distributionSrv.DistributionConfigService{}
		dconf, err := disSrv.GetDistributionConfig()
		if err != nil {
			global.GVA_LOG.Error("Error in service.UpdateFranchisee.GetDistributionConfig", zap.Error(err))
			return err
		}
		// 查询判断加盟商关联经理是否变更
		var fm franchisees.FranchiseeTeam
		err = tx.Where("franchisee_id = ? and user_id = ? and type = ?", franchisee.ID, franchisee.MarketLeadID, types.TeamType(types.MarketLead)).First(&fm).Error
		if err != nil {
			if utils.ErrorIsDBNotFound(err) {
				if franchisee.MarketLeadID > 0 && *franchisee.FCategoryId == dconf.TopFCategoryId {
					customers, err := franchiseeService.GetCustomers(franchisee, true)
					if err != nil {
						global.GVA_LOG.Error("Error in service.UpdateFranchisee.GetCustomers", zap.Error(err))
						return err
					}
					var idList = make([]uint, 0, len(customers.CustomersIDList)+1)
					idList = append(idList, uint(franchisee.ID))
					idList = append(idList, customers.CustomersIDList...)
					// 删除原有关联
					err = tx.Where("franchisee_id in (?) and type = ? ", idList, types.MarketLead).Delete(&franchisees.FranchiseeTeam{}).Error
					if err != nil {
						return err
					}

					var fms []franchisees.FranchiseeTeam = make([]franchisees.FranchiseeTeam, 0)
					for _, v := range customers.CustomersIDList {
						fms = append(fms, franchisees.FranchiseeTeam{
							FranchiseeId: v,
							Type:         types.MarketLead,
							UserId:       uint(franchisee.MarketLeadID),
						})
					}
					fms = append(fms, franchisees.FranchiseeTeam{
						FranchiseeId: uint(franchisee.ID),
						Type:         types.MarketLead,
						UserId:       uint(franchisee.MarketLeadID),
					})
					err = tx.Create(&fms).Error
					if err != nil {
						global.GVA_LOG.Error("Error in service.UpdateFranchisee.AddMaketLead.Create", zap.Error(err))
						return err
					}

				} else if franchisee.MarketLeadID > 0 {
					// 新增
					// @todo： 如果当前是一级分销加盟商，则将其所有客户都加入到对应的 team
					err = tx.Create(&franchisees.FranchiseeTeam{
						FranchiseeId: franchisee.ID,
						Type:         types.MarketLead,
						UserId:       uint(franchisee.MarketLeadID),
					}).Error
					if err != nil {
						return err
					}
					// 删除原先的关联
					// @todo： 如果当前是一级分销加盟商，则将其所有客户都删除
					err = tx.Where("franchisee_id = ? and type = ? and user_id != ?", franchisee.ID, types.MarketLead, franchisee.MarketLeadID).Delete(&franchisees.FranchiseeTeam{}).Error
					if err != nil {
						return err
					}
				}
			} else {
				return err
			}
		}

		var fs franchisees.FranchiseeTeam
		err = tx.Where("franchisee_id = ? and user_id = ? and type = ?", franchisee.ID, franchisee.SupervisionLeadID, types.SupervisionLead).First(&fs).Error
		if err != nil {
			if utils.ErrorIsDBNotFound(err) {
				if franchisee.SupervisionLeadID > 0 && *franchisee.FCategoryId == dconf.TopFCategoryId {
					customers, err := franchiseeService.GetCustomers(franchisee, true)
					if err != nil {
						global.GVA_LOG.Error("Error in service.UpdateFranchisee.GetCustomers", zap.Error(err))
						return err
					}
					var idList = make([]uint, 0)
					idList = append(idList, uint(franchisee.ID))
					idList = append(idList, customers.CustomersIDList...)
					err = tx.Where("franchisee_id in (?) and type = ? ", idList, types.SupervisionLead).Delete(&franchisees.FranchiseeTeam{}).Error
					if err != nil {
						return err
					}

					var fms []franchisees.FranchiseeTeam = make([]franchisees.FranchiseeTeam, 0)
					for _, v := range customers.CustomersIDList {
						fms = append(fms, franchisees.FranchiseeTeam{
							FranchiseeId: v,
							Type:         types.SupervisionLead,
							UserId:       uint(franchisee.SupervisionLeadID),
						})
					}
					fms = append(fms, franchisees.FranchiseeTeam{
						FranchiseeId: uint(franchisee.ID),
						Type:         types.SupervisionLead,
						UserId:       uint(franchisee.SupervisionLeadID),
					})
					err = tx.Create(&fms).Error
					if err != nil {
						global.GVA_LOG.Error("Error in service.UpdateFranchisee.AddMaketLead.Create", zap.Error(err))
						return err
					}

				} else if franchisee.SupervisionLeadID > 0 {
					// 新增
					err = tx.Create(&franchisees.FranchiseeTeam{
						FranchiseeId: franchisee.ID,
						Type:         types.SupervisionLead,
						UserId:       uint(franchisee.SupervisionLeadID),
					}).Error
					if err != nil {
						return err
					}
					// 删除原先的关联
					err = tx.Where("franchisee_id = ? and type = ? and user_id != ?", franchisee.ID, types.SupervisionLead, franchisee.SupervisionLeadID).Delete(&franchisees.FranchiseeTeam{}).Error
					if err != nil {
						return err
					}
				}
			} else {
				return err
			}
		}

		if *f.FCategoryId != *franchisee.FCategoryId {
			// 查询所选加盟商分类是否关联了专项账户
			categoryIDJSON, err := json.Marshal(*f.FCategoryId)
			if err != nil {
				return err
			}
			var oldEas []account.ExclusiveAccount
			err = tx.Where("is_banned = 0 and JSON_CONTAINS(f_category_ids, ?)", categoryIDJSON).Find(&oldEas).Error
			if err != nil {
				return err
			}

			categoryIDJSON1, err := json.Marshal(*franchisee.FCategoryId)
			if err != nil {
				return err
			}

			var newEas []account.ExclusiveAccount
			err = tx.Where("is_banned = 0 and JSON_CONTAINS(f_category_ids, ?)", categoryIDJSON1).Find(&newEas).Error
			if err != nil {
				return err
			}

			reduced, added := FindDiffElements(oldEas, newEas)
			if len(reduced) > 0 {
				// (删除改为禁用）加盟商分类相关联的加盟商账户
				var fAccountsIds []uint
				// 查询加盟商分类账户余额不为0 则报错
				var fAccounts []franchisees.FranchiseeAccount
				err = tx.Where("franchisee_id = ? AND exclusive_account_id in (?)", f.ID, reduced).Find(&fAccounts).Error
				if err != nil {
					return err
				}

				for _, fAccount := range fAccounts {
					if fAccount.Balance != 0 {
						return pkgerrors.New(commonResp.AccountBalanceNotZero)
					}

					fAccountsIds = append(fAccountsIds, fAccount.ID)
				}

				// 禁用加盟商分类相关联的加盟商账户
				err = tx.Model(&franchisees.FranchiseeAccount{}).Where("id in ?", fAccountsIds).Update("is_banned", true).Error
				if err != nil {
					return err
				}
			}

			if len(added) > 0 {
				// 创建加盟商分类相关联的加盟商账户
				var fAccounts = make([]franchisees.FranchiseeAccount, 0)
				for _, add := range added {
					// 查询专项账户
					var exclusiveAccount account.ExclusiveAccount
					err = tx.Where("id = ?", add).First(&exclusiveAccount).Error
					if err != nil {
						return err
					}

					// 创建时先查询是否已存在，处于禁用状态则启用
					var fAccount franchisees.FranchiseeAccount
					err = tx.Where("franchisee_id = ? AND exclusive_account_id = ?", f.ID, add).First(&fAccount).Error
					if err != nil {
						// 如果未找到则创建
						if utils.ErrorIsDBNotFound(err) {
							fAccounts = append(fAccounts, franchisees.FranchiseeAccount{
								FranchiseeId:         f.ID,
								ExclusiveAccountId:   exclusiveAccount.ID,
								ExclusiveAccountName: exclusiveAccount.Name,
							})
							continue
						}
						return err
					}

					// 如果已存在且处于禁用状态则启用
					if fAccount.IsBanned {
						err = tx.Model(&franchisees.FranchiseeAccount{}).Where("id = ?", fAccount.ID).Update("is_banned", false).Error
						if err != nil {
							return err
						}
					}
				}

				if len(fAccounts) > 0 {
					err = tx.Create(&fAccounts).Error
					if err != nil {
						return err
					}
				}
			}
		}

		err = tx.Save(&franchisee).Error
		if utils.IsDuplicateError(err) {
			return pkgerrors.New("编号已存在")
		}

		return err
	})

	return err
}

func FindDiffElements(oldEas, newEas []account.ExclusiveAccount) ([]uint, []uint) {
	reduced := make([]uint, 0)
	added := make([]uint, 0)
	for _, origElement := range oldEas {
		found := false

		for _, updatedElement := range newEas {
			if origElement.ID == updatedElement.ID {
				found = true
				break
			}
		}

		if !found {
			reduced = append(reduced, origElement.ID)
		}
	}

	for _, updatedElement := range newEas {
		found := false

		for _, origElement := range oldEas {
			if updatedElement.ID == origElement.ID {
				found = true
				break
			}
		}

		if !found {
			added = append(added, updatedElement.ID)
		}
	}

	return reduced, added
}

// GetFranchisee 根据id获取Franchisee记录

func (franchiseeService *FranchiseeService) GetFranchisee(id uint) (franchisee franchisees.Franchisee, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&franchisee).Error
	return
}

// GetFranchiseeBase
func (franchiseeService *FranchiseeService) GetFranchiseeBase(id uint) (frList franchiseesRes.FranchiseeBase, err error) {
	var fs franchisees.Franchisee
	var u systemModel.SysUser
	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Where("id = ?", id).First(&fs).Error
	if err != nil {
		return
	}
	var ft []franchisees.FranchiseeTeam
	err = global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).Where("franchisee_id = ?", id).Find(&ft).Error
	if err != nil {
		return
	}
	if len(ft) <= 0 {
		//如果本人还没有设置，则查找他的所在的加盟商客户团队长的督导/市场经理
		topFranchisee, err1 := franchiseeService.GetUpTopFranchisee(fs.ID)
		if err1 != nil {
			global.GVA_LOG.Error("获取上级加盟商失败", zap.Error(err1))
			//return frList, errors.Join(err, err1)
		}
		err1 = global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).Where("franchisee_id = ?", topFranchisee.ID).Find(&ft).Error
		if err1 != nil {
			//return frList, errors.Join(err, err1)
		}
	}
	for _, t := range ft {
		if t.Type == types.SupervisionLead {
			fs.SupervisionLeadID = int(t.UserId)
		}
		if t.Type == types.MarketLead {
			fs.MarketLeadID = int(t.UserId)
		}
	}
	err = global.GVA_DB.Model(&systemModel.SysUser{}).Where("id = ?", fs.UserID).First(&u).Error
	if err != nil {
		return
	}
	if fs.InviterID == 0 {
		frList.Inviter = &franchisees.Franchisee{}
	} else {
		inviter, err := franchiseeService.GetFranchisee(uint(fs.InviterID))
		if err != nil {
			return frList, err
		}
		frList.Inviter = &inviter
	}

	frList.Franchisee = fs
	frList.Enable = u.Enable
	return
}

// GetFranchiseeWithCategoryInfo 根据id获取Franchisee记录和分类信息
func (franchiseeService *FranchiseeService) GetFranchiseeWithCategoryInfo(id uint) (franchisee franchiseesRes.FranchiseeWithCategory, err error) {

	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id = ?", id).First(&franchisee).Error
	if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("error in service.franchiseeService.GetFranchiseeWithCategoryInfo",
			zap.Error(err),
			zap.Any("id", id))
		return
	}
	return
}

// GetFranchiseeInfoList 分页获取Franchisee记录

func (franchiseeService *FranchiseeService) GetFranchiseeInfoList(info franchiseesReq.FranchiseeSearch) (list []franchiseesRes.FranchiseeList, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var getLessInfo = info.LessInfo
	// 创建db
	db := global.GVA_DB.Model(&franchisees.Franchisee{})
	var fs []franchisees.Franchisee

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}

	if info.ID != 0 {
		db = db.Where("id = ?", info.ID)
	}

	if info.Name != "" {
		db = db.Where("name like ?", "%"+info.Name+"%")
	}

	if info.Code != "" {
		db = db.Where("code = ?", info.Code)
	}

	if info.Province != "" {
		db = db.Where("province = ?", info.Province)
	}

	if info.City != "" {
		db = db.Where("city = ?", info.City)
	}

	if info.FCategoryId != nil {
		db = db.Where("f_category_id = ?", info.FCategoryId)
	}

	if info.InviterID != 0 {
		db = db.Where("inviter_id = ?", info.InviterID)
	}

	// 查询关联的加盟商账户余额大于0的加盟商ID
	if info.ExclusiveAccountID != nil {
		getLessInfo = false
		var fIds []uint
		err = global.GVA_DB.Model(&franchisees.FranchiseeAccounts{}).Where("balance > 0 and exclusive_account_id = ?", info.ExclusiveAccountID).Pluck("franchisee_id", &fIds).Error
		if err != nil {
			return
		}

		if len(fIds) > 0 {
			db = db.Where("id in ?", fIds)
		} else {
			return
		}
	}

	if info.ExclusiveAccountName != nil {
		getLessInfo = false
		var fIds []uint
		err = global.GVA_DB.Model(&franchisees.FranchiseeAccounts{}).Where("exclusive_account_name = ?", info.ExclusiveAccountName).Pluck("franchisee_id", &fIds).Error
		if err != nil {
			return
		}

		if len(fIds) > 0 {
			db = db.Where("id in ?", fIds)
		} else {
			return
		}
	}

	if info.MarketLeadID != nil {
		var fIds []uint
		err = global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).Where("user_id = ?", info.MarketLeadID).Pluck("franchisee_id", &fIds).Error
		if err != nil {
			return
		}

		if len(fIds) > 0 {
			db = db.Where("id in ?", fIds)
		} else {
			return
		}
	}

	if info.SupervisionLeadID != nil {
		var fIds []uint
		err = global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).Where("user_id = ?", info.SupervisionLeadID).Pluck("franchisee_id", &fIds).Error
		if err != nil {
			return
		}

		if len(fIds) > 0 {
			db = db.Where("id in ?", fIds)
		} else {
			return
		}
	}

	if info.IsMarketLeadOthers {
		subQuery := global.GVA_DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Model(&franchisees.FranchiseeTeam{}).
				Where("type = ?", int8(types.MarketLead)).
				Select("franchisee_id").Find(&franchisees.FranchiseeTeam{})
		})
		db = db.Where(fmt.Sprintf("id not in(%s)", subQuery))
	}

	if info.IsSupervisionLeadOthers {
		subQuery := global.GVA_DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Model(&franchisees.FranchiseeTeam{}).
				Where("type = ?", int8(types.SupervisionLead)).
				Select("franchisee_id").Find(&franchisees.FranchiseeTeam{})
		})
		db = db.Where(fmt.Sprintf("id not in(%s)", subQuery))
	}

	if (info.ParentFranchiseeID != nil) && (*info.ParentFranchiseeID != 0) { // 作为父级加盟商，查找自己的客户统计信息
		customers, err := franchiseeService.GetCustomers(franchisees.Franchisee{GVA_MODEL: global.GVA_MODEL{ID: *info.ParentFranchiseeID}}, false)
		if err != nil {
			global.GVA_LOG.Error("error in services.franchiseeService.GetFranchiseeInfoList.GetCustomers", zap.Error(err), zap.Any("info", info))
		} else {
			//customers.CustomersIDList = append(customers.CustomersIDList, *info.ParentFranchiseeID)
			customers.CustomersIDList = append([]uint{*info.ParentFranchiseeID}, customers.CustomersIDList...)
			if customers.CustomersIDList != nil && len(customers.CustomersIDList) > 0 {
				db = db.Where("id in ?", customers.CustomersIDList)
			}
		}

	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	if limit == 0 {
		limit = 10
	}
	err = db.Limit(limit).Order("created_at DESC").Offset(offset).Find(&fs).Error
	franchiseeList := make([]franchiseesRes.FranchiseeList, 0, len(fs))
	//if !getLessInfo {
	var teamCache map[uint]franchiseesRes.FranchiseeWithCategory = make(map[uint]franchiseesRes.FranchiseeWithCategory)
	//}
	for _, v := range fs {
		var (
			fc              franchisees.FranchiseeCategory
			u               systemModel.SysUser
			inviter         franchiseesRes.FranchiseeWithCategory
			teamLead        franchiseesRes.FranchiseeWithCategory
			inviterTeamLead franchiseesRes.FranchiseeWithCategory
		)
		if v.InviterID == 0 { // 自主注册
			inviter = franchiseesRes.FranchiseeWithCategory{}
		} else {
			// 查询邀请人
			if !getLessInfo {
				inviter, err = franchiseeService.GetFranchiseeWithCategoryInfo(uint(v.InviterID))
				if err != nil {
					inviter = franchiseesRes.FranchiseeWithCategory{}
				}
				if inviter.ID == 0 {
					inviterTeamLead = franchiseesRes.FranchiseeWithCategory{}
				} else {
					if it, ok := teamCache[inviter.ID]; ok {
						inviterTeamLead = it
					} else {
						inviterTeamLead, err = franchiseeService.GetUpTopFranchisee(inviter.ID)
						if err != nil {
							inviterTeamLead = franchiseesRes.FranchiseeWithCategory{}
						}
						teamCache[inviter.ID] = inviterTeamLead
					}
				}

			}
		}

		if !getLessInfo {
			var teamLeadt franchiseesRes.FranchiseeWithCategory
			if it, ok := teamCache[v.ID]; ok {
				teamLeadt = it
			} else {
				teamLeadt, err = franchiseeService.GetUpTopFranchisee(v.ID)
				if err != nil {
					global.GVA_LOG.Warn("error in services.franchiseeService.GetFranchiseeInfoList.GetUpTopFranchisee", zap.Error(err), zap.Any("v.ID", v.ID))
					//teamLead = franchiseesRes.FranchiseeWithCategory{}
				}
				teamCache[v.ID] = teamLeadt
			}

			err = global.GVA_DB.Where("id = ?", v.FCategoryId).First(&fc).Error
			if err != nil {
				if utils.ErrorIsDBNotFound(err) {
					continue
				}

				return nil, 0, err
			}
			teamLead = teamLeadt
		}

		// 查询加盟商专属账户
		var accounts franchisees.FranchiseeAccounts
		if !getLessInfo {
			err = global.GVA_DB.Where("franchisee_id = ?", v.ID).Find(&accounts).Error
			if err != nil {
				return nil, 0, err
			}
		}

		// 查询云仓库存
		var (
			totalInventory          *int
			cloudWarehouseInventory int
		)
		if !getLessInfo {
			err = global.GVA_DB.Model(&warehouse.SubCloudWarehouse{}).
				Select("SUM(inventory) as total_inventory").
				Joins("JOIN cloud_warehouse ON sub_cloud_warehouse.cloud_warehouse_id = cloud_warehouse.id").
				Where("cloud_warehouse.franchisee_id = ?", v.ID).
				Scan(&totalInventory).Error
			if err != nil {
				return nil, 0, err
			}
			if totalInventory != nil {
				cloudWarehouseInventory = *totalInventory
			}
		}

		// 查询加盟商经理
		var marketLeads []franchisees.FranchiseeTeam
		err = global.GVA_DB.Where("franchisee_id = ?", v.ID).
			Where("user_id > 0").Find(&marketLeads).Error
		if err != nil {
			return nil, 0, err
		}
		sysUserSrv := &systemSrv.UserService{}
		for _, marketLead := range marketLeads {
			if marketLead.Type == types.MarketLead {
				v.MarketLeadID = int(marketLead.UserId)
				if !getLessInfo {
					v.MarketLead, err = sysUserSrv.FindUserById(int(marketLead.UserId))
					if err != nil {
						global.GVA_LOG.Error("error in services.franchiseeService.GetFranchiseeInfoList.FindUserById", zap.Error(err), zap.Any("info", info))
						// continue
					}
				}
			}

			if marketLead.Type == types.SupervisionLead {
				v.SupervisionLeadID = int(marketLead.UserId)
				if !getLessInfo {
					v.SupervisionLead, err = sysUserSrv.FindUserById(int(marketLead.UserId))
					if err != nil {
						global.GVA_LOG.Error("error in services.franchiseeService.GetFranchiseeInfoList.FindUserById", zap.Error(err), zap.Any("info", info))
						// continue
					}
				}
			}
		}

		v.Tel = utils.MaskTel(v.Tel)

		err = global.GVA_DB.Where("id = ?", v.UserID).First(&u).Error
		if err != nil && !utils.ErrorIsDBNotFound(err) {
			return nil, 0, err
		}

		franchiseeList = append(franchiseeList, franchiseesRes.FranchiseeList{
			Franchisee:                    v,
			CategoryName:                  fc.Name,
			FranchiseeAccounts:            accounts,
			FranchiseeAccountTotalBalance: accounts.SumBalance(),
			CloudWarehouseInventory:       cloudWarehouseInventory,
			Enable:                        u.Enable,
			Inviter:                       &inviter,
			TeamLeadInfo:                  &teamLead,
			InviterTeamLeadInfo:           &inviterTeamLead,
		})
	}

	return franchiseeList, total, err
}

func (franchiseeService *FranchiseeService) GetFranchiseeInfo(franchiseeID int) (*franchiseesRes.FranchiseeBrief, error) {
	var (
		franchisee franchisees.Franchisee
		invite     franchisees.Franchisee
		out        franchiseesRes.FranchiseeBrief
	)
	err := global.GVA_DB.Unscoped().First(&franchisee, "id =?", franchiseeID).Error
	if err != nil {
		return nil, err
	}

	// 查询加盟商专属账户
	var accounts []*franchisees.FranchiseeAccount
	err = global.GVA_DB.Where("franchisee_id = ?", franchiseeID).Find(&accounts).Error
	if err != nil {
		return nil, err
	}

	if len(accounts) == 0 {
		// 查询所选加盟商分类是否关联了专项账户
		categoryIDJSON, err := json.Marshal(*franchisee.FCategoryId)
		if err != nil {
			return nil, err
		}

		var eas []account.ExclusiveAccount
		err = global.GVA_DB.Where("is_banned = 0 and JSON_CONTAINS(f_category_ids, ?)", categoryIDJSON).Find(&eas).Error
		if err != nil {
			return nil, err
		}

		// 创建加盟商分类相关联的加盟商账户
		var fAccounts []*franchisees.FranchiseeAccount
		for _, ea := range eas {
			fAccounts = append(fAccounts, &franchisees.FranchiseeAccount{
				FranchiseeId:         franchisee.ID,
				ExclusiveAccountId:   ea.ID,
				ExclusiveAccountName: ea.Name,
			})
		}

		if len(fAccounts) > 0 {
			err = global.GVA_DB.Create(&fAccounts).Error
			if err != nil {
				return nil, err
			}

			accounts = fAccounts
		}
	}

	for _, v := range accounts {
		// 查询专属账户是否禁用(如果禁用属于全局禁用，则钱包都处于禁用状态)
		var a account.ExclusiveAccount
		err = global.GVA_DB.Where("id = ?", v.ExclusiveAccountId).First(&a).Error
		if err != nil {
			return nil, err
		}

		if a.IsBanned {
			v.IsBanned = true
		}
	}

	out.FranchiseeExclusiveAccount = accounts

	if franchisee.InviterID > 0 {
		err := global.GVA_DB.Unscoped().First(&invite, "id =?", franchisee.InviterID).Error
		if err != nil {
			return nil, err
		}
		out.Inviter = invite.Name
	}
	out.ID = franchisee.ID
	out.Name = franchisee.Name
	out.Tel = utils.MaskTel(franchisee.Tel)
	out.Addr = franchisee.Province + franchisee.City + franchisee.County + franchisee.Area + franchisee.Address
	out.Balance = franchisee.Balance
	out.Points = franchisee.Points
	begin, end := utils.GetTodayBeginAndEnd()
	err = global.GVA_DB.Model(&franchisees.RechargeRecord{}).Preload("Operator").
		Where("franchisee_id = ? and created_at between ? and ? and deleted_at IS NULL", franchiseeID, begin, end).
		Select(
			"SUM(CASE WHEN recharge_type = 1 AND operate_type in(0,3,4,5) THEN amount ELSE 0 END) AS daily_points_recharge," +
				"SUM(CASE WHEN recharge_type = 1 AND operate_type in(1,2,6) THEN amount ELSE 0 END) AS daily_points_consume," +
				"SUM(CASE WHEN recharge_type = 0 AND operate_type in(0,3,4) THEN amount ELSE 0 END) AS daily_balance_recharge," +
				"SUM(CASE WHEN recharge_type = 0 AND operate_type in(1,2) THEN amount ELSE 0 END) AS daily_balance_consume").
		Scan(&out).Error

	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (franchiseeService *FranchiseeService) BanedFranchisee(franchiseeIDList request.IdsReq) error {
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, v := range franchiseeIDList.Ids {
			var f franchisees.Franchisee
			err := tx.Model(&franchisees.Franchisee{}).Where("id = ?", v).First(&f).Error
			if err != nil {
				return err
			}
			var ff franchisees.Franchisee
			err = tx.Model(&franchisees.Franchisee{}).Where("inviter_id = ?", v).First(&ff).Error
			if err != nil && err != gorm.ErrRecordNotFound {
				return err
			} else if err != nil && err == gorm.ErrRecordNotFound {
			} else {
				return errors.New("存在直接下级加盟商，无法冻结")
			}
			err = franchiseeService.FranchiseeLinkRemoveLinkForFranchisee(uint(v))
			if err != nil {
				return errors.Join(err, errors.New("franchiseeService.BanedFranchisee.FranchiseeLinkRemoveLinkForFranchisee"))
			}
			err = franchiseeService.FranchiseeMemberLinkRemoveSomeOne(uint(v))
			if err != nil {
				return errors.Join(err, errors.New("franchiseeService.BanedFranchisee.FranchiseeMemberLinkRemoveSomeOne"))
			}

			// 更新user表enable为2冻结状态
			if err = tx.Model(&systemModel.SysUser{}).Where("id = ?", f.UserID).Update("enable", 2).Error; err != nil {
				return err
			}
		}
		return nil
	})

	return err
}

func (franchiseeService *FranchiseeService) ExportFranchiseeInfoList(info franchiseesReq.FranchiseeSearch) (*excelize.File, error) {
	db := global.GVA_DB.Table("franchisee as f").Where("f.deleted_at IS NULL")
	var fs []*franchiseesRes.ExportFranchisee

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("f.created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}

	if info.ID != 0 {
		db = db.Where("f.id = ?", info.ID)
	}

	if info.Name != "" {
		db = db.Where("f.name like ?", "%"+info.Name+"%")
	}

	if info.Code != "" {
		db = db.Where("f.code = ?", info.Code)
	}

	if info.Province != "" {
		db = db.Where("f.province = ?", info.Province)
	}

	if info.City != "" {
		db = db.Where("f.city = ?", info.City)
	}

	if info.FCategoryId != nil {
		db = db.Where("f.f_category_id = ?", info.FCategoryId)
	}
	err := db.Joins("left join franchisee_category as fc on f.f_category_id = fc.id").
		Joins("left join franchisee as f2 on f2.id = f.inviter_id").
		Joins("left join franchisee_category as fc2 on f2.f_category_id = fc2.id").
		Select("f.*,f.id as franchisee_id,fc.name as franchisee_category_name,f2.id as inviter_id,f2.name as inviter_name,fc2.name as inviter_category_name,f2.tel as inviter_tel").
		Find(&fs).Error
	if err != nil {
		return nil, err
	}

	disSrv := &distributionSrv.DistributionConfigService{}
	disConf, _ := disSrv.GetDistributionConfig()

	var teamCache map[uint]franchiseesRes.FranchiseeWithCategory = make(map[uint]franchiseesRes.FranchiseeWithCategory)
	for i, v := range fs {
		// 专属账户余额
		var accounts []*franchisees.FranchiseeAccount
		err = global.GVA_DB.Model(franchisees.FranchiseeAccount{}).
			Where("franchisee_id = ?", v.FranchiseeID).
			Find(&accounts).Error
		if err != nil {
			return nil, err
		}
		// 加上通户
		accounts = append(accounts, &franchisees.FranchiseeAccount{
			ExclusiveAccountName: "通用账户",
			Balance:              v.Balance,
			ExclusiveAccountId:   0,
		})
		accounts = append(accounts, &franchisees.FranchiseeAccount{
			ExclusiveAccountName: "积分",
			Balance:              v.Points,
			ExclusiveAccountId:   65535,
		})

		fs[i].FranchiseeAccounts = accounts
		u := systemModel.SysUser{}
		err = global.GVA_DB.Model(systemModel.SysUser{}).Where("id = ?", v.UserId).First(&u).Error
		if err != nil {
			return nil, err
		}
		fs[i].Enable = u.Enable

		// 查询加盟商经理
		var marketLeads []franchisees.FranchiseeTeam
		err = global.GVA_DB.Where("franchisee_id = ? and deleted_at IS NULL", v.FranchiseeID).Find(&marketLeads).Error
		if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Warn("error in services.franchiseeService.GetFranchiseeInfoList", zap.Error(err), zap.Any("info", v.FranchiseeID))
		} else if pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			v.MarketLead = &systemModel.SysUser{
				Username: "无",
				NickName: "无",
			}
			v.SupervisionLead = &systemModel.SysUser{
				Username: "无",
				NickName: "无",
			}
		} else {
			hasMarketLead, hasSupervisionLead := false, false
			sysUserSrv := &systemSrv.UserService{}
			for _, marketLead := range marketLeads {
				if marketLead.Type == types.MarketLead {
					v.MarketLead, err = sysUserSrv.FindUserById(int(marketLead.UserId))
					if err != nil {
						global.GVA_LOG.Error("error in services.franchiseeService.GetFranchiseeInfoList.FindUserById", zap.Error(err), zap.Any("info", info))
						// continue
					}
					hasMarketLead = true
				}

				if marketLead.Type == types.SupervisionLead {
					v.SupervisionLead, err = sysUserSrv.FindUserById(int(marketLead.UserId))
					if err != nil {
						global.GVA_LOG.Error("error in services.franchiseeService.GetFranchiseeInfoList.FindUserById", zap.Error(err), zap.Any("info", info))
						// continue
					}
					hasSupervisionLead = true
				}
			}
			if !hasMarketLead {
				v.MarketLead = &systemModel.SysUser{
					Username: "无",
					NickName: "无",
				}
			}
			if !hasSupervisionLead {
				v.SupervisionLead = &systemModel.SysUser{
					Username: "无",
					NickName: "无",
				}
			}
		}

		// 查询团队
		if teaminfo, ok := teamCache[v.FranchiseeID]; ok {
			fs[i].TeamLeadInfo = &teaminfo
		} else {
			teamInfo, err := franchiseeService.GetUpTopFranchiseeV2ViaFranchiseeLink(v.FranchiseeID)
			if err != nil {
				global.GVA_LOG.Error("service.franchisee.ExportFranchiseeInfoList.GetUpTopFranchisee", zap.Any("err", err))
			}
			if (teamInfo.FCategoryId != nil) && (*teamInfo.FCategoryId == disConf.TopFCategoryId) {
				fs[i].TeamLeadInfo = &teamInfo
				teamCache[v.FranchiseeID] = teamInfo
			} else {
				teamInfo := &franchiseesRes.FranchiseeWithCategory{}
				fs[i].TeamLeadInfo = teamInfo
				teamCache[v.FranchiseeID] = *teamInfo
			}
		}
		if v.InviterID == 0 {
			fs[i].InviterTeamLeadInfo = &franchiseesRes.FranchiseeWithCategory{
				Franchisee: franchisees.Franchisee{
					Name: v.Name,
				},
				FrCategory: &franchisees.FranchiseeCategory{
					Name: v.FranchiseeCategoryName,
				},
			}
		} else {
			if inviterTeamLeadInfo, ok := teamCache[v.InviterID]; ok {
				fs[i].InviterTeamLeadInfo = &inviterTeamLeadInfo
			} else {
				inviterTeamInfo, err := franchiseeService.GetUpTopFranchiseeV2ViaFranchiseeLink(v.InviterID)
				if err != nil {
					global.GVA_LOG.Error("service.franchisee.ExportFranchiseeInfoList.GetUpTopFranchisee", zap.Any("err", err))
				}
				if (inviterTeamInfo.FCategoryId != nil) && (*inviterTeamInfo.FCategoryId == disConf.TopFCategoryId) {
					fs[i].InviterTeamLeadInfo = &inviterTeamInfo
					teamCache[v.InviterID] = inviterTeamInfo
				} else {
					inviterTeamInfo := &franchiseesRes.FranchiseeWithCategory{}
					fs[i].InviterTeamLeadInfo = inviterTeamInfo
					teamCache[v.InviterID] = *inviterTeamInfo
				}
			}
		}
	}
	global.GVA_LOG.Debug("service.franchisee.ExportFranchiseeInfoList", zap.Any("fs", fs))
	return franchiseeService.generateExcel(fs)
}
func (franchiseeService *FranchiseeService) generateExcel(in []*franchiseesRes.ExportFranchisee) (*excelize.File, error) {
	f := excelize.NewFile()
	oldSheetName := f.GetSheetName(0)
	sheetName := "加盟商列表"

	// 修改第一个 sheet 的名称
	if err := f.SetSheetName(oldSheetName, sheetName); err != nil {
		global.GVA_LOG.Error("SetSheetName err!", zap.Error(err))
		return nil, err
	}
	// 设置表头
	header := []string{
		"加盟商名称",
		"加盟商编码",
		"联系人",
		"账号",
		"加盟商分类",
		"所在城市/地区",
		"所在片区",
		"归属团队",
		"归属团队账号",
		"邀请人",
		"邀请人分类",
		"邀请人所在团队",
		"邀请人所在团队账号",
		"市场经理",
		"督导经理",
		"备注",
		"状态",
		"是否老加盟商",
		"创建时间"}
	allAccounts := getAllAccountsInfo(in)
	for _, v := range allAccounts.FranchiseeAccount {
		header = append(header, v.ExclusiveAccountName)
	}
	if err := f.SetSheetRow(sheetName, "A1", &header); err != nil {
		global.GVA_LOG.Error("SetSheetRow err!", zap.Error(err))
		return nil, err
	}
	// 设置表内容
	for i, v := range in {
		row := []interface{}{
			v.Name,
			v.Code,
			v.Linkman,
			v.Tel,
			v.FranchiseeCategoryName,
			v.Province + " " + v.City + " " + v.County + " ",
			v.Area,
			v.TeamLeadInfo.Name,
			v.TeamLeadInfo.Tel,
			v.InviterName + "/" + v.InviterTel,
			v.InviterCategoryName,
			v.InviterTeamLeadInfo.Name,
			v.InviterTeamLeadInfo.Tel,
			v.MarketLead.Username + "/" + v.MarketLead.NickName,
			v.SupervisionLead.Username + "/" + v.SupervisionLead.NickName,
			v.Remark,
			utils.FranchiseeEnableStatusString(v.Enable),
			utils.BoolToChinese(v.IsOldfranchisee),
			v.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		for _, a := range allAccounts.FranchiseeAccount {
			var balance = float64(0)
			for _, b := range v.FranchiseeAccounts {
				if a.ExclusiveAccountId == b.ExclusiveAccountId {
					balance = float64(b.Balance)
					break
				}
			}
			if balance > 0 && a.ExclusiveAccountId != 65535 {
				// balanceCell := fmt.Sprintf("%.2f", balance/100)
				balanceCell, _ := decimal.NewFromFloat(balance / 100).Round(2).Float64()
				row = append(row, balanceCell)
			} else if a.ExclusiveAccountId == 65535 {
				// balanceCell := fmt.Sprintf("%.2f", balance)
				balanceCell, _ := decimal.NewFromFloat(balance).Round(2).Float64()
				row = append(row, balanceCell)
			} else {
				row = append(row, "")
			}
		}
		if err := f.SetSheetRow(sheetName, "A"+strconv.Itoa(i+2), &row); err != nil {
			global.GVA_LOG.Error("SetSheetRow err!", zap.Error(err))
			return nil, err
		}
	}
	return f, nil
}

func getAllAccountsInfo(in []*franchiseesRes.ExportFranchisee) franchiseesReq.AccountsInfo {
	var out franchiseesReq.AccountsInfo
	var accountsHeaderMap = make(map[uint]*franchisees.FranchiseeAccountInfo) // key:专属账户ID value:专属账户
	for _, v := range in {
		for _, a := range v.FranchiseeAccounts {
			if _, ok := accountsHeaderMap[a.ExclusiveAccountId]; !ok {
				j := &franchisees.FranchiseeAccountInfo{
					ExclusiveAccountId:   a.ExclusiveAccountId,
					ExclusiveAccountName: a.ExclusiveAccountName,
				}
				accountsHeaderMap[a.ExclusiveAccountId] = j
				out.FranchiseeAccount = append(out.FranchiseeAccount, j)
			}
		}
	}
	sort.Slice(out.FranchiseeAccount, func(i, j int) bool {
		return out.FranchiseeAccount[i].ExclusiveAccountId < out.FranchiseeAccount[j].ExclusiveAccountId
	})
	return out
}

// GetFranchiseeStatistics 获取加盟商统计
func (franchiseeService *FranchiseeService) GetFranchiseeStatistics() (*franchiseesRes.FranchiseeStatistics, error) {
	var f franchiseesRes.FranchiseeStatistics
	t := time.Now()
	thisMonthBegin := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, time.Local)
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).Count(&f.Total).Error
	if err != nil {
		global.GVA_LOG.Error("service.franchisee.GetFranchiseeStatistics err:", zap.Error(err))
	}
	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Where("created_at > ?", time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)).Where("deleted_at is null").Count(&f.TodayNew).Error

	if err != nil {
		global.GVA_LOG.Error("service.franchisee.GetFranchiseeStatistics err:", zap.Error(err))
	}
	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Where("created_at > ?", time.Date(thisMonthBegin.Year(), thisMonthBegin.Month(), thisMonthBegin.Day(), 0, 0, 0, 0, time.Local)).Where("deleted_at is null").Count(&f.MonthNew).Error
	if err != nil {
		global.GVA_LOG.Error("service.franchisee.GetFranchiseeStatistics err:", zap.Error(err))
	}
	err = global.GVA_DB.Model(&franchisees.FranchiseeStandby{}).Where("result is null").Count(&f.Standby).Error
	return &f, err
}

func (franchiseeService *FranchiseeService) GetFranchiseeInviter(id uint) (*franchisees.Franchisee, error) {
	var f *franchisees.Franchisee
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).Unscoped().Where("id = ? and deleted_at is null", id).First(&f).Error
	return f, err
}

// GetAllFranchiseeWithCategory /**
func (franchiseeService *FranchiseeService) GetAllFranchiseeWithCategory(cid uint) (*[]franchisees.Franchisee, error) {
	var f []franchisees.Franchisee
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).Where("f_category_id = ? and deleted_at is null", cid).Find(&f).Error
	return &f, err
}

func (franchiseeService *FranchiseeService) GetFranchiseeByTel(tel string) (*franchisees.Franchisee, error) {
	var f franchisees.Franchisee
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).Where("tel = ?", tel).First(&f).Error
	return &f, err
}

/**
 * getUpTopFranchisee  获取我所在团队的上游一级分类加盟商
 * @param my *franchisees.Franchisee
 * @param topCategoryID uint 一级分销加盟商分类ID
 */

func (franchiseeService *FranchiseeService) getUpTopFranchisee(franchiseeId uint, topCategoryID uint) (franchiseesRes.FranchiseeWithCategory, error) {

	global.GVA_LOG.Debug("service.getUpTopFranchisee param:", zap.Any("franchiseeId", franchiseeId), zap.Any("topCategoryID", topCategoryID))
	my, err := franchiseeService.GetFranchiseeWithCategoryInfo(franchiseeId)
	if err != nil {
		global.GVA_LOG.Error("Error in service.getUpTopFranchisee", zap.Error(err), zap.Any("my", my))
		return franchiseesRes.FranchiseeWithCategory{}, err
	}
	if *my.FCategoryId == int(topCategoryID) {
		if err != nil {
			global.GVA_LOG.Error("Error in service.getUpTopFranchisee", zap.Error(err), zap.Any("my", my))
			return franchiseesRes.FranchiseeWithCategory{}, err
		}
		return my, nil
	}
	for my.ID != 0 && *my.FCategoryId != int(topCategoryID) {
		if my.InviterID == 0 { // 邀请人ID 是 0，当前加盟商是自助注册
			break
		}
		if my.ID == uint(my.InviterID) {
			global.GVA_LOG.Error("Error in service.getUpTopFranchisee", zap.Error(err), zap.Any("my", my))
			return franchiseesRes.FranchiseeWithCategory{}, pkgerrors.New("上游中没有一级分销加盟商")
		}
		myInviter, err := franchiseeService.GetFranchiseeWithCategoryInfo(uint(my.InviterID))
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Error("Error in service.getUpTopFranchisee", zap.Error(err), zap.Any("my", my))
			return franchiseesRes.FranchiseeWithCategory{}, err
		}
		if *myInviter.FCategoryId != int(topCategoryID) {
			my = myInviter
		} else {
			return myInviter, nil
		}
	}
	return franchiseesRes.FranchiseeWithCategory{}, pkgerrors.New("上游中没有一级分销加盟商")
}

// GetUpTopFranchisee  获取我所在团队的上游一级分类加盟商
func (franchiseeService *FranchiseeService) GetUpTopFranchisee(ID uint) (franchiseesRes.FranchiseeWithCategory, error) {
	disSrv := &distributionSrv.DistributionConfigService{}
	disConf, err := disSrv.GetDistributionConfig()
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetUpTopFranchisee", zap.Error(err))
		return franchiseesRes.FranchiseeWithCategory{}, err
	}
	return franchiseeService.getUpTopFranchisee(ID, uint(disConf.TopFCategoryId))
}

// GetDistributionConfigForUser 获取用户是否具有分销分享的权限
func (franchiseeService *FranchiseeService) GetDistributionConfigForUser(id uint) (distributionConfigForUser distributionResp.DistributionConfigForUser, err error) {
	var dc distributionModel.DistributionConfig
	distributionConfigService := distributionSrv.DistributionConfigService{}
	dc, err = distributionConfigService.GetDistributionConfig()
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetDistributionConfigForUser", zap.Error(err))
		return
	}
	if dc.InviteEnable == nil || !*dc.InviteEnable {
		return
	} else {
		distributionConfigForUser.InviteEnable = *dc.InviteEnable
	}
	if dc.InviteFCategoryIds == nil {
		return
	} else {

		var useInfo franchisees.Franchisee
		useInfo, err1 := franchiseeService.GetFranchisee(id)
		if err1 != nil {
			global.GVA_LOG.Error("Error in service.GetDistributionConfigForUser", zap.Error(err1))
			return
		}
		var inviteFCategoryIds []uint
		err1 = json.Unmarshal([]byte(dc.InviteFCategoryIds.String()), &inviteFCategoryIds)
		global.GVA_LOG.Info("inviteFCategoryIds", zap.Any("inviteFCategoryIds", inviteFCategoryIds))
		if err1 != nil {
			global.GVA_LOG.Error("Error in service.GetDistributionConfigForUser", zap.Error(err1))
			return
		}
		for _, v := range inviteFCategoryIds {
			if v == uint(*useInfo.FCategoryId) {
				distributionConfigForUser.CanInvite = true
				break
			}
		}
		return

	}
}

// ApproveFranchisee 审批加盟商
func (franchiseeService *FranchiseeService) ApproveFranchisee(userId uint, approveReq franchiseesReq.FranchiseeStandbyApproveReq) (err error) {
	standbyService := FranchiseeStandbyService{}
	var fid uint
	if approveReq.Result == false {
		// 审批不通过
		err = standbyService.MarkApprove(userId, approveReq)
		if err != nil {
			return err
		}
		return
	} else if approveReq.Result == true {
		// 审批通过，创建新的加盟商
		approveReq.FR.ID = 0
		approveReq.FR.CreatedAt = time.Now()
		approveReq.FR.UpdatedAt = time.Now()

		fid, err = franchiseeService.CreateFranchisee(&approveReq.FR)
		approveReq.FR.ID = fid
	}
	err = standbyService.MarkApprove(userId, approveReq)
	if err != nil {
		global.GVA_LOG.Error("Error in service.ApproveFranchisee", zap.Any("req", approveReq), zap.Error(pkgerrors.WithStack(err)))
		return
	}
	return
}

// GetTopFranchisee 获取一级分销加盟商
func (franchiseeService *FranchiseeService) GetTopFranchisee() ([]franchiseesRes.FranchiseeCustomerUnit, error) {
	distributionConfigSrv := &distributionSrv.DistributionConfigService{}
	disConf, _ := distributionConfigSrv.GetDistributionConfig()
	if disConf.TopFCategoryId == 0 {
		return nil, pkgerrors.New("未配置一级分销加盟商分类")
	}
	var fList []franchiseesRes.FranchiseeCustomerUnit
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("f_category_id = ?", disConf.TopFCategoryId).Find(&fList).Error
	if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("Error in service.GetTopFranchisee", zap.Error(err))
		return nil, err
	} else if pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return fList, nil
}

// GetTopFranchiseeWithCustomerCount 获取一级分销加盟商及客户数量
func (franchiseeService *FranchiseeService) GetTopFranchiseeWithCustomerCount() ([]franchiseesRes.FranchiseeCustomerCount, error) {
	topFList := make([]franchiseesRes.FranchiseeCustomerUnit, 0)
	topFList, err := franchiseeService.GetTopFranchisee()
	global.GVA_LOG.Info("topFList:", zap.Any("topFList", topFList))
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetTopFranchiseeWithCustomerCount", zap.Error(err))
		return nil, err
	}
	topCustomers := make([]franchiseesRes.FranchiseeCustomerCount, 0)
	for _, v := range topFList {
		global.GVA_LOG.Info(strings.Repeat("-", 20), zap.Any("v", v))
		customers, err := franchiseeService.GetCustomers(v.Franchisee, true)
		global.GVA_LOG.Info("customers:", zap.Any("customers", customers))
		if err != nil {
			global.GVA_LOG.Error("Error in service.GetTopFranchiseeWithCustomerCount", zap.Error(err))
			continue
		}
		topCustomers = append(topCustomers, *customers)
	}
	return topCustomers, nil
}

// GetDirectCustomers 获取直接邀请的客户[包含非一级分销加盟商]
func (franchiseeService *FranchiseeService) GetDirectCustomers(franchiseeId uint) ([]franchiseesRes.FranchiseeCustomer, error) {
	distributionService := &distributionSrv.DistributionConfigService{}
	disConf, _ := distributionService.GetDistributionConfig()
	if disConf.TopFCategoryId == 0 {
		return nil, pkgerrors.New("未配置一级分销加盟商分类")
	}
	var fList []franchiseesRes.FranchiseeCustomerUnit
	var customerList []franchiseesRes.FranchiseeCustomer
	var err error
	if franchiseeId == 0 {
		fList, err = franchiseeService.GetTopFranchiseeV2(disConf, true)
	} else {
		fList, err = franchiseeService.getDirectCustomers(franchiseeId, disConf)
	}

	if len(fList) == 0 || err != nil {
		return customerList, nil
	}
	for k := range fList {
		dr, err := franchiseeService.getDirectCustomers(fList[k].ID, disConf)
		global.GVA_LOG.Debug("dr:", zap.Any("dr", dr), zap.Error(err))
		if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			//TODO: 无法获取直接邀请的客户
			global.GVA_LOG.Error("Error in service.franchisee.GetDirectCustomers", zap.Error(err))
			continue
		}

		customerList = append(customerList, franchiseesRes.FranchiseeCustomer{
			FranchiseeCustomerUnit: fList[k],
			DirectCustomersCount:   len(dr),
			DirectCustomers:        dr,
		})
	}
	return customerList, nil
}

func (franchiseeService *FranchiseeService) getDirectCustomers(franchiseeId uint, disConf distributionModel.DistributionConfig) ([]franchiseesRes.FranchiseeCustomerUnit, error) {
	var fList []franchiseesRes.FranchiseeCustomerUnit
	if err := global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id != ? and franchisee.inviter_id = ? and franchisee.f_category_id != ?", franchiseeId, franchiseeId, disConf.TopFCategoryId).Find(&fList).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("Error in service.GetDirectCustomers", zap.Error(err))
		return nil, err
	} else if pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		return fList, err
	}
	return fList, nil
}

//// batchGetDirectCustomers 批量获取直接邀请的客户, not finished
//func (franchiseeService *FranchiseeService) batchGetDirectCustomers(franchiseeIds []uint, disConf distributionModel.DistributionConfig) (map[uint][]franchiseesRes.FranchiseeCustomerUnit, error) {
//	var customerMapList map[uint][]franchiseesRes.FranchiseeCustomerUnit = make(map[uint][]franchiseesRes.FranchiseeCustomerUnit)
//	if len(franchiseeIds) == 0 {
//		return customerMapList, nil
//	}
//	var sliceStringMap []map[string]interface{}
//	if err := global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id != inviter_id and f_category_id = ?", disConf.TopFCategoryId).Where("inviter_id in ?", franchiseeIds).Find(&sliceStringMap).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
//		global.GVA_LOG.Error("Error in service.GetDirectCustomers", zap.Error(err))
//		return nil, err
//	}
//	global.GVA_LOG.Debug("service.franchiseeService.batchGetDirectCustomers:", zap.Any("sliceStringMap", sliceStringMap))
//
//	return nil, nil
//}
//
//// BatchGetDirectCustomers 批量获取直接邀请的客户,not finished
//func (franchiseeService *FranchiseeService) BatchGetDirectCustomers(franchiseeIds []uint) (map[uint][]franchiseesRes.FranchiseeCustomerUnit, error) {
//
//	distributionService := &distributionSrv.DistributionConfigService{}
//	disConf, _ := distributionService.GetDistributionConfig()
//	if disConf.TopFCategoryId == int(0) {
//		return nil, errors.New("未配置一级分销加盟商分类")
//	}
//
//	return franchiseeService.batchGetDirectCustomers(franchiseeIds, disConf)
//}

// GetDirectMember 获取直接邀请的加盟商
func (franchiseeService *FranchiseeService) GetDirectMember(franchiseeId uint) ([]franchisees.Franchisee, error) {
	var fList []franchisees.Franchisee
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Joins("LEFT JOIN sys_users on franchisee.user_id = sys_users.id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id != ? and franchisee.inviter_id = ?", franchiseeId, franchiseeId).
		Find(&fList).Error
	if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("Error in service.FranchiseeService.GetDirectMember", zap.String("error", err.Error()))
		return nil, err
	} else if pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return fList, nil
}

// GetDirectMemberIds 获取直接邀请的加盟商ID列表
func (franchiseeService *FranchiseeService) GetDirectMemberIds(franchiseeId uint) ([]uint, error) {
	var fList []uint
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Joins("LEFT JOIN sys_users on franchisee.user_id = sys_users.id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id != ? and franchisee.inviter_id = ?", franchiseeId, franchiseeId).
		Pluck("franchisee.id", &fList).Error
	if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("Error in service.FranchiseeService.GetDirectMemberIds", zap.Error(err))
		return nil, err
	} else if pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return fList, nil
}

// GetMemebersIDs
func (franchiseeService *FranchiseeService) GetMemebersIDs(franchiseeId uint) ([]uint, error) {
	return franchiseeService.LoopGetMemberIds(franchiseeId)
}
func (franchiseeService *FranchiseeService) LoopGetMemberIds(id uint) ([]uint, error) {
	directMemberIds, err := franchiseeService.GetDirectMemberIds(id)
	if err != nil {
		return nil, err
	}
	if len(directMemberIds) == 0 {
		return []uint{}, nil
	}
	var idList []uint = make([]uint, 0, len(directMemberIds))
	idList = append(idList, directMemberIds...)
	for k := range directMemberIds {
		memberIds, err := franchiseeService.LoopGetMemberIds(directMemberIds[k])
		if err != nil {
			return nil, err
		}
		if len(memberIds) == 0 {
			continue
		}
		idList = append(idList, memberIds...)
	}

	return idList, nil
}

// GetCustomers 获取当前分销商客户
func (franchiseeService *FranchiseeService) GetCustomers(fr franchisees.Franchisee, checkSelf bool) (*franchiseesRes.FranchiseeCustomerCount, error) {
	distributionConfigSrv := &distributionSrv.DistributionConfigService{}
	disConf, err := distributionConfigSrv.GetDistributionConfig()
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetCustomers", zap.Error(err))
		return nil, err
	}
	global.GVA_LOG.Debug("disConf", zap.Any("disConf", disConf))
	if disConf.TopFCategoryId == 0 {
		return nil, pkgerrors.New("未配置一级分销加盟商分类")
	}
	global.GVA_LOG.Info("disConf:", zap.Any("disConf", disConf), zap.Any("fr", fr))
	//if checkSelf && (fr.FCategoryId == nil || *(fr.FCategoryId) != disConf.TopFCategoryId) {
	//	return nil, errors.New("当前非一级分销加盟商，不具有客户")
	//}
	customer, err := franchiseeService.LoopGetCustomer(&franchiseesRes.FranchiseeCustomerCount{FR: &fr, CustomerCount: 0, CustomersIDList: []uint{}}, disConf.TopFCategoryId)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetCustomers", zap.Error(err))
		return nil, err
	}
	return customer, nil
}

func (franchiseeService *FranchiseeService) LoopGetCustomer(
	customer *franchiseesRes.FranchiseeCustomerCount,
	topFCategoryId int) (*franchiseesRes.FranchiseeCustomerCount, error) {
	if customer.FR == nil {
		return customer, nil
	}
	directMember, err := franchiseeService.GetDirectMember(customer.FR.ID)
	if err != nil {
		return customer, err
	}
	if len(directMember) > 0 {
		customer.DirectCustomers = make([]*franchiseesRes.FranchiseeCustomerCount, 0, len(directMember))
		customer.CustomersIDList = make([]uint, 0, len(directMember))
	}
	for k := range directMember {
		if *directMember[k].FCategoryId == topFCategoryId {
			continue
		}
		customer.DirectCustomers = append(customer.DirectCustomers, &franchiseesRes.FranchiseeCustomerCount{FR: &directMember[k], CustomerCount: 0, CustomersIDList: []uint{}})
		customer.CustomerCount++
		customer.CustomersIDList = append(customer.CustomersIDList, directMember[k].ID)
	}
	for k := range customer.DirectCustomers {
		reMember, err := franchiseeService.LoopGetCustomer(customer.DirectCustomers[k], topFCategoryId)
		if err != nil {
			continue
		}
		customer.CustomerCount += reMember.CustomerCount
		customer.CustomersIDList = append(customer.CustomersIDList, reMember.CustomersIDList...)
		customer.DirectCustomers[k].CustomerCount = reMember.CustomerCount
		//customer.DirectCustomers[k].DirectCustomers = reMember.DirectCustomers
	}
	return customer, nil
}

// LoopGetMember 循环获取邀请的团队成员下线
func (franchiseeService *FranchiseeService) LoopGetMember(member *franchiseesRes.FranchiseeCustomerCount, traceMap map[uint]struct{}) (*franchiseesRes.FranchiseeCustomerCount, error) {
	if member.FR == nil {
		return member, nil
	}
	franchiseeId := member.FR.ID
	directMember, err := franchiseeService.GetDirectMember(franchiseeId)
	global.GVA_LOG.Info("directMember:", zap.Any("directMember", directMember))
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetMembers", zap.Error(err))
		return member, err
	}
	if len(directMember) > 0 {
		member.DirectCustomers = make([]*franchiseesRes.FranchiseeCustomerCount, 0, len(directMember))
		member.CustomersIDList = make([]uint, 0, len(directMember))
	}
	for k := range directMember {
		// 去除自己
		if directMember[k].ID == member.FR.ID {
			continue
		}
		if _, ok := traceMap[directMember[k].ID]; ok {
			// 过滤已经获取过的
			continue
		}
		traceMap[directMember[k].ID] = struct{}{} // 标记已经获取

		member.DirectCustomers = append(member.DirectCustomers, &franchiseesRes.FranchiseeCustomerCount{FR: &directMember[k], CustomerCount: 0, CustomersIDList: []uint{}})
		member.CustomerCount++
		member.CustomersIDList = append(member.CustomersIDList, directMember[k].ID)
	}
	for k := range member.DirectCustomers {
		reMember, err := franchiseeService.LoopGetMember(member.DirectCustomers[k], traceMap)
		if err != nil {
			continue
		}
		member.CustomerCount += reMember.CustomerCount
		member.CustomersIDList = append(member.CustomersIDList, reMember.CustomersIDList...)
		member.DirectCustomers[k].CustomerCount = reMember.CustomerCount
	}

	return member, nil
}

// GetMembers 获取邀请的加盟商
func (franchiseeService *FranchiseeService) GetMembers(fr franchisees.Franchisee) (*franchiseesRes.FranchiseeCustomerCount, error) {
	customer := &franchiseesRes.FranchiseeCustomerCount{
		CustomerCount:   0,
		CustomersIDList: []uint{},
		FR:              &fr,
	}
	var traceIDmap = make(map[uint]struct{}, math.MaxInt)
	traceIDmap[fr.ID] = struct{}{}
	customer, err := franchiseeService.LoopGetMember(customer, traceIDmap)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetMembers", zap.Error(err))
		return nil, err
	}
	return customer, nil
}

// GetMyTopFranchisee 获取我邀请的一级分销商
func (franchiseeService *FranchiseeService) GetMyTopFranchisee(fr franchisees.Franchisee) ([]franchisees.Franchisee, error) {
	distributionConfigSrv := &distributionSrv.DistributionConfigService{}
	disConf, _ := distributionConfigSrv.GetDistributionConfig()
	if disConf.TopFCategoryId == 0 {
		return nil, pkgerrors.New("未配置一级分销加盟商分类")
	}

	var fList []franchisees.Franchisee
	if err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id != ? and franchisee.inviter_id = ? and franchisee.f_category_id = ?", fr.ID, fr.ID, disConf.TopFCategoryId).
		Find(&fList).Error; err != nil {
		if !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Error("Error in service.GetMyTopFranchisee", zap.Error(err))
			return nil, err
		}
		return nil, err
	}
	return fList, nil
}

// GetMyTopFranchiseeIDs 获取我邀请的一级分销商ID列表
func (franchiseeService *FranchiseeService) GetMyTopFranchiseeIDs(franchiseeId uint) ([]uint, error) {
	distributionConfigSrv := &distributionSrv.DistributionConfigService{}
	disConf, _ := distributionConfigSrv.GetDistributionConfig()
	if disConf.TopFCategoryId == 0 {
		return nil, pkgerrors.New("未配置一级分销加盟商分类")
	}
	var flist []uint
	if err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id != ? and franchisee.inviter_id = ? and franchisee.f_category_id = ?", franchiseeId, franchiseeId, disConf.TopFCategoryId).Pluck("franchisee.id", &flist).Error; err != nil {
		if !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Error("Error in service.GetMyTopFranchiseeIDs", zap.Error(err))
			return nil, err
		}
		return nil, err
	}
	return flist, nil
}

// BatchGetMyTopFranchiseeIDs 批量获取参数加盟商 ID 列表中邀请的一级分销商ID列表
func (franchiseeService *FranchiseeService) BatchGetMyTopFranchiseeIDs(franchiseeIds []uint) ([]uint, error) {
	distributionConfigSrv := &distributionSrv.DistributionConfigService{}
	disConf, _ := distributionConfigSrv.GetDistributionConfig()
	if disConf.TopFCategoryId == 0 {
		return nil, pkgerrors.New("未配置一级分销加盟商分类")
	}
	var fList []uint
	if err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id not in (?) and franchisee.inviter_id in (?) and franchisee.f_category_id = ?", franchiseeIds, franchiseeIds, disConf.TopFCategoryId).Pluck("franchisee.id", &fList).Error; err != nil {
		if !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Error("Error in service.BatchGetMyTopFranchiseeIDs", zap.Error(err), zap.Any("franchiseeIds", franchiseeIds))
		}
		return nil, err
	}
	return fList, nil
}

// GetTopFranchiseeV2 获取一级分销加盟商+无邀请人(自主注册)加盟商
func (franchiseeService *FranchiseeService) GetTopFranchiseeV2(disConf distributionModel.DistributionConfig, includeNonInviter bool) ([]franchiseesRes.FranchiseeCustomerUnit, error) {
	var fList []franchiseesRes.FranchiseeCustomerUnit
	err := global.GVA_DB.Model(&franchisees.Franchisee{}).
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
		Where("sys_users.enable = ?", 1).
		Preload("FrCategory").
		Where("franchisee.f_category_id = ?", disConf.TopFCategoryId).
		Find(&fList).Error

	if err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("Error in service.GetTopFranchiseeV2", zap.Error(err))
		return nil, err
	} else if pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if !includeNonInviter {
		return fList, nil
	}
	var nonInviterList []franchiseesRes.FranchiseeCustomerUnit
	if err := global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").
		Joins("LEFT JOIN sys_users ON sys_users.id = franchisee.user_id").
		Where("sys_users.enable = ?", 1).
		Where("franchisee.id != ? and franchisee.inviter_id = ? and franchisee.f_category_id <> ?", franchisees.DefaultInviterID, franchisees.DefaultInviterID, disConf.TopFCategoryId).Find(&nonInviterList).Error; err != nil && !pkgerrors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("Error in service.GetTopFranchiseeV2", zap.Error(err))
	}
	fList = append(fList, nonInviterList...)
	return fList, nil
}

func (franchiseeService *FranchiseeService) GetTodayNewFranchisee(franchiseeIDs []uint) (todayNewFranchisees []uint, err error) {
	todayBegin, todayEnd := utils.GetTodayBeginAndEnd()
	if err = global.GVA_DB.Model(&franchisees.Franchisee{}).Where("id in ?", franchiseeIDs).Where("created_at >= ? and created_at <= ?", todayBegin, todayEnd).Pluck("id", &todayNewFranchisees).Error; err != nil {
		global.GVA_LOG.Error("Error in service.GetTodayNewFranchisee", zap.Error(err))
		return
	}
	return
}

func (franchiseeService *FranchiseeService) GetMonthNewFranchisee(franchiseeIDs []uint) (monthNewFranchisees []uint, err error) {
	monthBegin, monthEnd := utils.GetMonthBeginAndEnd()
	if err = global.GVA_DB.Model(&franchisees.Franchisee{}).Where("id in ?", franchiseeIDs).Where("created_at >= ? and created_at <= ?", monthBegin, monthEnd).Pluck("id", &monthNewFranchisees).Error; err != nil {
		global.GVA_LOG.Error("Error in service.GetMonthNewFranchisee", zap.Error(err))
		return
	}
	return
}

// GetFranchiseeCustomerOverview 获取加盟商客户概况
func (franchiseeService *FranchiseeService) GetFranchiseeCustomerOverview(franchisee franchisees.Franchisee) (p *franchiseesRes.FranchiseeCustomerOverview, err error) {
	p = new(franchiseesRes.FranchiseeCustomerOverview)
	var customersIDs []uint
	disSrv := &distributionSrv.DistributionConfigService{}
	disconf, err := disSrv.GetDistributionConfig()
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview.GetDistributionConfig", zap.Error(err))
		return nil, errors.Join(err, errors.New("error in service.GetFranchiseeCustomerOverview.GetDistributionConfig"))
	}
	if disconf.TopFCategoryId == *franchisee.FCategoryId {
		customersIDs, err = franchiseeService.GetCustomerIDsViaFranchiseeLink(franchisee.ID)
		if err != nil {
			global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview.GetTopCustomerIDsViaFranchiseeLink", zap.Error(err), zap.Any("franchiseeID", franchisee.ID))
			return nil, errors.Join(err, errors.New("error in service.GetFranchiseeCustomerOverview.GetTopCustomerIDsViaFranchiseeLink"))
		}
	} else {
		customersIDs, err = franchiseeService.GetCustomersIDsForNonTopViaFranchiseeLink(franchisee.ID)
		if err != nil {
			global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview.GetCustomersIDsForNonTopViaFranchiseeLink", zap.Error(err), zap.Any("franchiseeID", franchisee.ID))
			return nil, errors.Join(err, errors.New("error in service.GetFranchiseeCustomerOverview.GetCustomersIDsForNonTopViaFranchiseeLink"))
		}
	}

	global.GVA_LOG.Debug("customersIDs", zap.Any("customersIDs", customersIDs))
	customersIDs = append(customersIDs, franchisee.ID)
	customersIDs = lo.Uniq(customersIDs)
	p.TotalCustomer = len(customersIDs)
	todayNew, err := franchiseeService.GetTodayNewFranchisee(customersIDs)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview", zap.Error(err))
		p.TodayNewCustomer = 0
	} else {
		p.TodayNewCustomer = len(todayNew)
	}
	monthNew, err := franchiseeService.GetMonthNewFranchisee(customersIDs)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview", zap.Error(err))
		p.MonthNewCustomer = 0
	} else {
		p.MonthNewCustomer = len(monthNew)
	}

	customersList, err := franchiseeService.GetDirectCustomers(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview", zap.Error(err))
		return nil, err
	}
	cf, err := franchiseeService.GetFranchiseeWithCategoryInfo(franchisee.ID)
	if err != nil {
		global.GVA_LOG.Error("Error in service.GetFranchiseeCustomerOverview", zap.Error(err))
		//return nil, err
	}
	cfc := &franchiseesRes.FranchiseeCustomer{
		FranchiseeCustomerUnit: franchiseesRes.FranchiseeCustomerUnit{
			Franchisee: cf.Franchisee,
			FrCategory: cf.FrCategory,
		},
		DirectCustomersCount: len(customersList),
	}
	p.DirectCustomers = append([]franchiseesRes.FranchiseeCustomer{*cfc}, customersList...)

	return
}

// searchFranchiseeWithinIDsAndKeywords 根据关键字在加盟商列表中搜索
func (franchiseeService *FranchiseeService) searchFranchiseeWithinIDsAndKeywords(franchiseeIds []uint, keyword string, limit int) (f []franchiseesRes.FranchiseeCustomer, err error) {
	var u []franchiseesRes.FranchiseeCustomerUnit
	// check if keyword is empty or not ,if the keyword is digit number, search throw tel column ,if the keyword is not digit number, search throw name column
	if keyword == "" {
		err = global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id in ?", franchiseeIds).Limit(limit).Find(&u).Error
		if err != nil {
			global.GVA_LOG.Error("Error in service.FranchiseeService.searchFranchiseeWithinIDsAndKeywords", zap.Error(err))
			return
		}
	}

	if utils.IsDigit(keyword) {
		err = global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id in ?", franchiseeIds).Where("tel like ?", "%"+keyword+"%").Limit(limit).Find(&u).Error

	} else {
		err = global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id in ?", franchiseeIds).Where("name like ?", "%"+keyword+"%").Limit(limit).Find(&u).Error
	}

	if err != nil {
		global.GVA_LOG.Error("Error in service.FranchiseeService.searchFranchiseeWithinIDsAndKeywords", zap.Error(err))
		return nil, err
	}
	disSrv := distributionSrv.DistributionConfigService{}
	disConf, _ := disSrv.GetDistributionConfig()
	for k := range u {
		dr, err := franchiseeService.getDirectCustomers(u[k].ID, disConf)
		if err != nil {
			//TODO: 无法获取直接邀请的客户
			global.GVA_LOG.Error("Error in service.franchisee.GetDirectCustomers", zap.Error(err))
		}

		f = append(f, franchiseesRes.FranchiseeCustomer{
			FranchiseeCustomerUnit: u[k],
			DirectCustomersCount:   len(dr),
			DirectCustomers:        dr,
		})
	}
	return
}

// GetFranchiseeSearchCustomer 获取加盟商搜索客户
func (franchiseeService *FranchiseeService) GetFranchiseeSearchCustomer(franchisee franchisees.Franchisee, keyword string, limit int) (p []franchiseesRes.FranchiseeCustomer, err error) {
	customers, err := franchiseeService.GetCustomers(franchisee, false)
	if err != nil {
		global.GVA_LOG.Error("Error in service.FranchiseeService.GetFranchiseeSearchCustomer", zap.Error(err),
			zap.Any("franchisee", franchisee))
		return nil, err
	}
	global.GVA_LOG.Info("customers:", zap.Any("customers", customers))

	p, err = franchiseeService.searchFranchiseeWithinIDsAndKeywords(customers.CustomersIDList, keyword, limit)
	return p, err
}

func (franchiseeService *FranchiseeService) GetFranchiseeBaseList(req franchiseesReq.FranchiseeBaseListReq) (list []franchiseesRes.FranchiseeWithCategory, total int64, err error) {
	global.GVA_LOG.Debug("GetFranchiseeBaseList", zap.Any("req", req))
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := global.GVA_DB.Model(&franchisees.Franchisee{})

	if req.Keyword != "" {
		if utils.IsDigit(req.Keyword) {
			db = db.Where("name like ? or tel like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		} else {
			db = db.Where("name like ? ", "%"+req.Keyword+"%")
		}
	}
	if req.StartCreatedAt != nil {
		db = db.Where("created_at >= ?", req.StartCreatedAt)
	}
	if req.EndCreatedAt != nil {
		db = db.Where("created_at <= ?", req.EndCreatedAt)
	}
	if req.MarketLeadID != nil {
		var fIds []uint
		err = global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).Where("user_id = ?", req.MarketLeadID).Pluck("franchisee_id", &fIds).Error
		if err != nil {
			return
		}

		if len(fIds) > 0 {
			db = db.Where("id in ?", fIds)
		} else {
			return
		}
	}

	if req.SupervisionLeadID != nil {
		var fIds []uint
		err = global.GVA_DB.Model(&franchisees.FranchiseeTeam{}).Where("user_id = ?", req.SupervisionLeadID).Pluck("franchisee_id", &fIds).Error
		if err != nil {
			return
		}

		if len(fIds) > 0 {
			db = db.Where("id in ?", fIds)
		} else {
			return
		}
	}

	if req.IsMarketLeadOthers {
		subQuery := global.GVA_DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Model(&franchisees.FranchiseeTeam{}).
				Where("type = ?", int8(types.MarketLead)).
				Select("franchisee_id").Find(&franchisees.FranchiseeTeam{})
		})
		db = db.Where(fmt.Sprintf("id not in(%s)", subQuery))
	}

	if req.IsSupervisionLeadOthers {
		subQuery := global.GVA_DB.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Model(&franchisees.FranchiseeTeam{}).
				Where("type = ?", int8(types.SupervisionLead)).
				Select("franchisee_id").Find(&franchisees.FranchiseeTeam{})
		})
		db = db.Where(fmt.Sprintf("id not in(%s)", subQuery))
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	db = db.Preload("FrCategory").Limit(limit).Offset(offset)
	if req.Keyword != "" {
		if utils.IsDigit(req.Keyword) {
			db = db.Where("name like ? or tel like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		} else {
			db = db.Where("name like ? ", "%"+req.Keyword+"%")
		}
	}
	err = db.Order("name asc").Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, err
}

// GetFranchiseeCustomerUnit 获取加盟商信息
func (franchiseeService *FranchiseeService) GetFranchiseeCustomerUnit(id uint) (f franchiseesRes.FranchiseeCustomerUnit, err error) {
	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id = ?", id).First(&f).Error
	return f, err
}

// GetFranchiseeAllCustomer 获取所有客户(包括一级分销加盟商和非一级分销加盟商)
func (franchiseeService *FranchiseeService) GetFranchiseeAllCustomer(f franchisees.Franchisee) (customers []franchiseesRes.FranchiseeCustomerUnit, err error) {
	c, err := franchiseeService.GetCustomers(f, false)
	if err != nil {
		global.GVA_LOG.Error("Error in service.FranchiseeService.GetFranchiseeAllCustomer", zap.Error(err))
		return nil, err
	}
	global.GVA_LOG.Debug("service.FranchiseeService.GetFranchiseeAllCustomer.GetCustomers", zap.Any("customers", c))
	//total = int64(c.CustomerCount)
	//customers = make([]franchiseesRes.FranchiseeCustomerUnit, 0)

	// for k := range c.CustomersIDList {
	// 	global.GVA_LOG.Debug("service.FranchiseeService.GetFranchiseeAllCustomer.GetCustomersIDList", zap.Any("customersIDList", c.CustomersIDList[k]))
	// 	customer, err := franchiseeService.GetDirectCustomers(c.CustomersIDList[k])
	// 	if err != nil {
	// 		global.GVA_LOG.Error("Error in service.FranchiseeService.GetFranchiseeAllCustomer", zap.Error(err))
	// 		continue
	// 	}
	// 	kUnit, err := franchiseeService.GetFranchiseeCustomerUnit(c.CustomersIDList[k])
	// 	if err != nil {
	// 		global.GVA_LOG.Error("Error in service.FranchiseeService.GetFranchiseeAllCustomer", zap.Error(err))
	// 		continue
	// 	}
	// 	kInfo := franchiseesRes.FranchiseeCustomer{
	// 		FranchiseeCustomerUnit: kUnit,
	// 		DirectCustomersCount:   len(customer),
	// 	}
	// 	global.GVA_LOG.Debug("service.FranchiseeService.GetFranchiseeAllCustomer.GetDirectCustomers", zap.Any("customers", customer), zap.Error(err))
	// 	customers = append(customers, kInfo)
	// 	//customers = append(customers, customer...)
	// }
	customers, err = franchiseeService.BatchGetFranchiseeCustomerUnit(c.CustomersIDList)
	global.GVA_LOG.Debug("service.FranchiseeService.GetFranchiseeAllCustomer all customers", zap.Any("customers", customers))
	return customers, nil
}

// BatchGetFranchiseeCustomerUnit 批量获取加盟商信息
func (franchiseeService *FranchiseeService) BatchGetFranchiseeCustomerUnit(franchiseeIds []uint) (f []franchiseesRes.FranchiseeCustomerUnit, err error) {
	f = make([]franchiseesRes.FranchiseeCustomerUnit, 0)
	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Preload("FrCategory").Where("id in ?", franchiseeIds).Order("name asc").Find(&f).Error
	return f, err
}

// UnbanedFranchisee updates the enable status of franchisee(s) in the database.
//
// franchiseeIDList: a struct containing a list of franchisee IDs to be unbanned.
// err: an error that occurred during the transaction, if any.
func (franchiseeService *FranchiseeService) UnbanedFranchisee(franchiseeIDList request.IdsReq) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for _, v := range franchiseeIDList.Ids {
			var f franchisees.Franchisee
			err := tx.Model(&franchisees.Franchisee{}).Where("id = ?", v).First(&f).Error
			if err != nil {
				return err
			}

			// 更新user表enable为2冻结状态
			if err = tx.Model(&systemModel.SysUser{}).Where("id = ?", f.UserID).Update("enable", 1).Error; err != nil {
				return err
			}
			// 添加到客户团队
			_, err = franchiseeService.FranchiseeLinkCreateLinkForSomeone(f, uint(f.InviterID))
			if err != nil {
				return errors.Join(err, errors.New("franchiseeService.UnbanedFranchisee.FranchiseeLinkCreateLinkForSomeone"))
			}
			err = franchiseeService.FranchiseeMemberLinkAddNewOne(uint(v), uint(f.InviterID))
			if err != nil {
				return errors.Join(err, errors.New("franchiseeService.UnbanedFranchisee.FranchiseeMemberLinkAddNewOne"))
			}
			if err != nil {
				global.GVA_LOG.Error("Error in service.FranchiseeService.UnbanedFranchisee", zap.Error(err), zap.Any("f", f))
				return err
			}
		}

		return nil
	})

	return err
}

// GetFranchiseeAllCustomerV2 获取所有客户分页版本
func (franchiseeService *FranchiseeService) GetFranchiseeAllCustomerV2(f franchisees.Franchisee, info request.PageInfo) (customers []franchiseesRes.FranchiseeCustomerUnit, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	c, err := franchiseeService.GetCustomers(f, false)
	if err != nil {
		global.GVA_LOG.Error("Error in service.FranchiseeService.GetFranchiseeAllCustomer", zap.Error(err))
		return nil, 0, err
	}
	global.GVA_LOG.Debug("service.FranchiseeService.GetFranchiseeAllCustomer.GetCustomers", zap.Any("customers", c))
	total = int64(c.CustomerCount) + 1
	if int64(offset) >= total {
		return []franchiseesRes.FranchiseeCustomerUnit{}, total, nil
	}
	if int64(offset+limit) > total {
		limit = int(total) - offset
	}

	//customers = make([]franchiseesRes.FranchiseeCustomerUnit, limit)
	customersIdList := append([]uint{f.ID}, c.CustomersIDList...)
	customers, err = franchiseeService.BatchGetFranchiseeCustomerUnit(customersIdList[offset : offset+limit])
	global.GVA_LOG.Debug("service.FranchiseeService.GetFranchiseeAllCustomer all customers", zap.Any("customers", customers))
	return customers, total, nil
}

// SimpleUpdateFranchisee 简单更新加盟商信息
func (franchiseeService *FranchiseeService) SimpleUpdateFranchisee(f franchisees.Franchisee, updateColumns []string) (err error) {
	err = global.GVA_DB.Model(&franchisees.Franchisee{}).Unscoped().Where("id = ?", f.ID).Select(updateColumns).Updates(&f).Error
	return
}

func (franchiseeService *FranchiseeService) ResetFranchiseePassword(franchisee_id uint) (err error) {
	f, err := franchiseeService.GetFranchisee(franchisee_id)
	if err != nil {
		return err
	}
	userSrv := &systemSrv.UserService{}
	err = userSrv.ResetPassword(f.UserID)
	if err != nil {
		return err
	}
	return nil

}

func (franchiseeService *FranchiseeService) GetCustomerInviterInfo(f franchisees.Franchisee, req franchiseesReq.FranchiseeIdReq) (*franchiseesRes.FranchiseeCustomerUnit, error) {
	customers, err := franchiseeService.GetCustomers(f, true)
	if err != nil {
		return nil, err
	}
	if f.ID == req.ID {
		customerUnit, err := franchiseeService.GetFranchiseeCustomerUnit(uint(f.InviterID))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return &franchiseesRes.FranchiseeCustomerUnit{}, nil
			}
			return nil, err
		}
		return &customerUnit, nil
	}
	for _, v := range customers.CustomersIDList {
		if v == req.ID {
			var customerF franchisees.Franchisee
			customerF, err = franchiseeService.GetFranchisee(req.ID)
			if err != nil {
				return nil, err
			}
			customerUnit, err := franchiseeService.GetFranchiseeCustomerUnit(uint(customerF.InviterID))
			if err != nil {
				return nil, err
			}
			return &customerUnit, nil
		}
	}
	return nil, errors.New("没有找到该客户")
}

// GetFranchiseeProfile 获取加盟商信息
func (franchiseeService *FranchiseeService) GetFranchiseeProfile(franchisee franchisees.Franchisee) (*appRes.FranchiseeProfileRes, error) {
	franchiseeInfo, err := franchiseeService.GetFranchiseeWithCategoryInfo(franchisee.ID)
	if err != nil {
		return nil, err
	}
	var inviter franchisees.Franchisee
	if franchiseeInfo.InviterID != 0 {
		inviter, err = franchiseeService.GetFranchisee(uint(franchiseeInfo.InviterID))
		if err != nil {
			return nil, err
		}
	}
	dconfSrv := &distributionSrv.DistributionConfigService{}
	dconf, err := dconfSrv.GetDistributionConfig()
	if err != nil {
		return nil, errors.Join(err, errors.New("failed to get distribution config in service.FranchiseeService.GetFranchiseeProfile"))
	}
	var customersIDs []uint
	if dconf.TopFCategoryId == *franchiseeInfo.FCategoryId {
		customersIDs, err = franchiseeService.GetCustomerIDsViaFranchiseeLink(franchiseeInfo.ID)
		if err != nil {
			return nil, errors.Join(err, errors.New("failed to get customer list in service.FranchiseeService.GetFranchiseeProfile"))
		}
	} else {
		customersIDs, err = franchiseeService.GetCustomersIDsForNonTopViaFranchiseeLink(franchisee.ID)
		if err != nil {
			return nil, errors.Join(err, errors.New("failed to get customer list in service.FranchiseeService.GetFranchiseeProfile"))
		}
	}
	directCustomers, err := franchiseeService.getDirectCustomers(franchisee.ID, dconf)
	if err != nil {
		return nil, errors.Join(err, errors.New("failed to get direct customers in service.FranchiseeService.GetFranchiseeProfile"))
	}

	return &appRes.FranchiseeProfileRes{
		FranchiseeWithCategory: franchiseeInfo,
		Inviter:                &inviter,
		DirectCustomerCount:    len(directCustomers),
		CustomerCount:          len(customersIDs),
	}, nil
}
