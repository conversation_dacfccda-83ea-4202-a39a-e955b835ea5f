package franchisees

import (
	"context"
	"errors"

	"github.com/OSQianXing/guanpu-server/model/system"
	"github.com/OSQianXing/guanpu-server/types"
	"gorm.io/gorm"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	powerwechat "github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	franchiseesReq "github.com/OSQianXing/guanpu-server/model/franchisees/request"
)

type FranchiseeWxInfoService struct {
	miniProgramSrv *miniProgram.MiniProgram
}

// CreateFranchiseeWxInfo 创建FranchiseeWxInfo记录

func (franchiseeWxInfoService *FranchiseeWxInfoService) CreateFranchiseeWxInfo(franchiseeWxInfo *franchisees.FranchiseeWxInfo) (err error) {
	err = global.GVA_DB.Create(franchiseeWxInfo).Error
	return err
}

// DeleteFranchiseeWxInfo 删除FranchiseeWxInfo记录

func (franchiseeWxInfoService *FranchiseeWxInfoService) DeleteFranchiseeWxInfo(franchiseeWxInfo franchisees.FranchiseeWxInfo) (err error) {
	err = global.GVA_DB.Delete(&franchiseeWxInfo).Error
	return err
}

// DeleteFranchiseeWxInfoByIds 批量删除FranchiseeWxInfo记录

func (franchiseeWxInfoService *FranchiseeWxInfoService) DeleteFranchiseeWxInfoByIds(ids request.IdsReq) (err error) {
	err = global.GVA_DB.Delete(&[]franchisees.FranchiseeWxInfo{}, "id in ?", ids.Ids).Error
	return err
}

// UpdateFranchiseeWxInfo 更新FranchiseeWxInfo记录

func (franchiseeWxInfoService *FranchiseeWxInfoService) UpdateFranchiseeWxInfo(franchiseeWxInfo franchisees.FranchiseeWxInfo) (err error) {
	err = global.GVA_DB.Save(&franchiseeWxInfo).Error
	return err
}

// GetFranchiseeWxInfo 根据id获取FranchiseeWxInfo记录

func (franchiseeWxInfoService *FranchiseeWxInfoService) GetFranchiseeWxInfo(franchiseeId uint) (franchiseeWxInfo franchisees.FranchiseeWxInfo, err error) {
	err = global.GVA_DB.Model(&franchisees.FranchiseeWxInfo{}).Where("franchisee_id = ?", franchiseeId).First(&franchiseeWxInfo).Error
	return
}

// GetFranchiseeWxInfoInfoList 分页获取FranchiseeWxInfo记录

func (franchiseeWxInfoService *FranchiseeWxInfoService) GetFranchiseeWxInfoInfoList(info franchiseesReq.FranchiseeWxInfoSearch) (list []franchisees.FranchiseeWxInfo, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&franchisees.FranchiseeWxInfo{})
	var franchiseeWxInfos []franchisees.FranchiseeWxInfo
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Find(&franchiseeWxInfos).Error
	return franchiseeWxInfos, total, err
}

// FranchiseeWxMiniProgramBind 小程序登陆
func (franchiseeWxInfoService *FranchiseeWxInfoService) FranchiseeWxMiniProgramBind(code string, userId uint) (err error) {

	f, err := dao.Franchisee.GetFranchiseeByUserID(userId)
	if err != nil {
		return errors.Join(err, errors.New("获取用户信息失败"))
	}
	checkWxInfo := &franchisees.FranchiseeWxInfo{}
	err = global.GVA_DB.Model(&franchisees.FranchiseeWxInfo{}).Where("franchisee_id = ? AND wx_type = ?", f.ID, types.WxTypeMiniProgram).First(checkWxInfo).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.Join(err, errors.New("获取小程序绑定信息失败"))
	} else if err == nil {
		return errors.New("已绑定小程序,无需重新绑定")
	}

	if franchiseeWxInfoService.miniProgramSrv == nil {
		miniProgramSrv, err := powerwechat.NewMiniProgram(&powerwechat.UserConfig{
			AppID:     global.GVA_CONFIG.Wechat.MiniAppID,
			Secret:    global.GVA_CONFIG.Wechat.MiniAppSecret,
			HttpDebug: global.GVA_CONFIG.Wechat.HttpDebug,
			Debug:     global.GVA_CONFIG.Wechat.Debug,
		})

		if err != nil {
			return errors.Join(err, errors.New("初始化后台小程序服务失败"))
		}
		franchiseeWxInfoService.miniProgramSrv = miniProgramSrv
	}
	res, err := franchiseeWxInfoService.miniProgramSrv.Auth.Session(context.Background(), code)
	if err != nil {
		return errors.Join(err, errors.New("授权失败"))
	}

	err = global.GVA_DB.Model(&franchisees.FranchiseeWxInfo{}).Create(&franchisees.FranchiseeWxInfo{
		GVA_MODEL:    global.GVA_MODEL{},
		FranchiseeId: f.ID,
		OpenId:       &res.OpenID,
		UnionId:      &res.UnionID,
		SessionKey:   &res.SessionKey,
		WxType:       types.WxTypeMiniProgram,
	}).Error

	if err != nil {
		return errors.Join(err, errors.New("绑定小程序失败"))
	}

	return
}

// FranchiseeWxMiniProgramLogin 小程序登陆
func (franchiseeWxInfoService *FranchiseeWxInfoService) FranchiseeWxMiniProgramLogin(code string) (user *system.SysUser, err error) {
	if franchiseeWxInfoService.miniProgramSrv == nil {
		miniProgramSrv, err := powerwechat.NewMiniProgram(&powerwechat.UserConfig{
			AppID:     global.GVA_CONFIG.Wechat.MiniAppID,
			Secret:    global.GVA_CONFIG.Wechat.MiniAppSecret,
			HttpDebug: global.GVA_CONFIG.Wechat.HttpDebug,
			Debug:     global.GVA_CONFIG.Wechat.Debug,
		})
		if err != nil {
			return nil, err
		}
		franchiseeWxInfoService.miniProgramSrv = miniProgramSrv
	}
	res, err := franchiseeWxInfoService.miniProgramSrv.Auth.Session(context.Background(), code)
	if err != nil {
		return nil, err
	}
	fwxInfo := franchisees.FranchiseeWxInfo{}
	err = global.GVA_DB.Model(&franchisees.FranchiseeWxInfo{}).Where("open_id = ? AND wx_type = ?", res.OpenID, "miniProgram").First(&fwxInfo).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Join(err, errors.New("service.FranchiseeWxInfoService.FranchiseeWxMiniProgramLogin.First.FranchiseeWxInfo"))
	} else if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("用户尚未绑定微信小程序")
	}
	f, err := dao.Franchisee.GetFranchiseeByFranchiseeID(int(fwxInfo.FranchiseeId))
	if err != nil {
		return nil, errors.Join(err, errors.New("service.FranchiseeWxInfoService.FranchiseeWxMiniProgramLogin.GetFranchiseeByFranchiseeID"))
	}
	err = global.GVA_DB.Model(&system.SysUser{}).Preload("Authorities").Preload("Authority").Where("id = ?", f.UserID).First(&user).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Join(err, errors.New("service.FranchiseeWxInfoService.FranchiseeWxMiniProgramLogin.First.SysUser"))
	} else if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("用户不合法")
	}

	return user, nil
}
