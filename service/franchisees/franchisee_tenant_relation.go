package franchisees

import (
	"errors"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type FranchiseeTenantRelationService struct{}

// AddFranchiseeToTenant 将加盟商添加到租户
func (ftrService *FranchiseeTenantRelationService) AddFranchiseeToTenant(franchiseeID, tenantID uint, role string, isDefault bool) error {
	// 检查是否已存在关联
	var existing franchisees.FranchiseeTenantRelation
	err := global.GVA_DB.Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).First(&existing).Error
	if err == nil {
		return errors.New("加盟商已属于该租户")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 如果设置为默认租户，先取消其他默认设置
	if isDefault {
		err = global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
			Where("franchisee_id = ? AND is_default = ?", franchiseeID, true).
			Update("is_default", false).Error
		if err != nil {
			return err
		}
	}

	// 创建新的关联
	relation := franchisees.FranchiseeTenantRelation{
		FranchiseeID: franchiseeID,
		TenantID:     tenantID,
		Status:       franchisees.StatusActive,
		Role:         role,
		IsDefault:    isDefault,
	}

	return global.GVA_DB.Create(&relation).Error
}

// RemoveFranchiseeFromTenant 从租户中移除加盟商
func (ftrService *FranchiseeTenantRelationService) RemoveFranchiseeFromTenant(franchiseeID, tenantID uint) error {
	return global.GVA_DB.Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).
		Delete(&franchisees.FranchiseeTenantRelation{}).Error
}

// GetFranchiseesByTenant 获取租户下的所有加盟商
func (ftrService *FranchiseeTenantRelationService) GetFranchiseesByTenant(tenantID uint) ([]franchisees.Franchisee, error) {
	var franchiseeList []franchisees.Franchisee
	
	err := global.GVA_DB.Table("franchisee").
		Joins("INNER JOIN franchisee_tenant_relation ON franchisee.id = franchisee_tenant_relation.franchisee_id").
		Where("franchisee_tenant_relation.tenant_id = ? AND franchisee_tenant_relation.status = ?", tenantID, franchisees.StatusActive).
		Find(&franchiseeList).Error
	
	return franchiseeList, err
}

// GetTenantsByFranchisee 获取加盟商所属的所有租户
func (ftrService *FranchiseeTenantRelationService) GetTenantsByFranchisee(franchiseeID uint) ([]franchisees.FranchiseeTenantRelation, error) {
	var relations []franchisees.FranchiseeTenantRelation
	
	err := global.GVA_DB.Where("franchisee_id = ? AND status = ?", franchiseeID, franchisees.StatusActive).
		Find(&relations).Error
	
	return relations, err
}

// GetFranchiseeDefaultTenant 获取加盟商的默认租户
func (ftrService *FranchiseeTenantRelationService) GetFranchiseeDefaultTenant(franchiseeID uint) (*franchisees.FranchiseeTenantRelation, error) {
	var relation franchisees.FranchiseeTenantRelation
	
	err := global.GVA_DB.Where("franchisee_id = ? AND is_default = ? AND status = ?", 
		franchiseeID, true, franchisees.StatusActive).First(&relation).Error
	
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("加盟商没有默认租户")
	}
	
	return &relation, err
}

// SetFranchiseeDefaultTenant 设置加盟商的默认租户
func (ftrService *FranchiseeTenantRelationService) SetFranchiseeDefaultTenant(franchiseeID, tenantID uint) error {
	// 先取消所有默认设置
	err := global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
		Where("franchisee_id = ?", franchiseeID).
		Update("is_default", false).Error
	if err != nil {
		return err
	}

	// 设置新的默认租户
	return global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
		Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).
		Update("is_default", true).Error
}

// FranchiseeBelongsToTenant 检查加盟商是否属于指定租户
func (ftrService *FranchiseeTenantRelationService) FranchiseeBelongsToTenant(franchiseeID, tenantID uint) bool {
	var count int64
	global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
		Where("franchisee_id = ? AND tenant_id = ? AND status = ?", franchiseeID, tenantID, franchisees.StatusActive).
		Count(&count)
	return count > 0
}

// GetFranchiseesByTenantWithPagination 分页获取租户下的加盟商
func (ftrService *FranchiseeTenantRelationService) GetFranchiseesByTenantWithPagination(tenantID uint, page, pageSize int) ([]franchisees.Franchisee, int64, error) {
	var franchiseeList []franchisees.Franchisee
	var total int64
	
	offset := (page - 1) * pageSize
	
	// 计算总数
	err := global.GVA_DB.Table("franchisee").
		Joins("INNER JOIN franchisee_tenant_relation ON franchisee.id = franchisee_tenant_relation.franchisee_id").
		Where("franchisee_tenant_relation.tenant_id = ? AND franchisee_tenant_relation.status = ?", tenantID, franchisees.StatusActive).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	err = global.GVA_DB.Table("franchisee").
		Joins("INNER JOIN franchisee_tenant_relation ON franchisee.id = franchisee_tenant_relation.franchisee_id").
		Where("franchisee_tenant_relation.tenant_id = ? AND franchisee_tenant_relation.status = ?", tenantID, franchisees.StatusActive).
		Offset(offset).Limit(pageSize).
		Find(&franchiseeList).Error
	
	return franchiseeList, total, err
}

// UpdateFranchiseeTenantRole 更新加盟商在租户中的角色
func (ftrService *FranchiseeTenantRelationService) UpdateFranchiseeTenantRole(franchiseeID, tenantID uint, role string) error {
	return global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
		Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).
		Update("role", role).Error
}

// DeactivateFranchiseeInTenant 在租户中停用加盟商（不删除关联）
func (ftrService *FranchiseeTenantRelationService) DeactivateFranchiseeInTenant(franchiseeID, tenantID uint) error {
	return global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
		Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).
		Update("status", franchisees.StatusInactive).Error
}

// ActivateFranchiseeInTenant 在租户中激活加盟商
func (ftrService *FranchiseeTenantRelationService) ActivateFranchiseeInTenant(franchiseeID, tenantID uint) error {
	return global.GVA_DB.Model(&franchisees.FranchiseeTenantRelation{}).
		Where("franchisee_id = ? AND tenant_id = ?", franchiseeID, tenantID).
		Update("status", franchisees.StatusActive).Error
}

// GetFranchiseeByTelAndTenant 根据手机号和租户获取加盟商信息
func (ftrService *FranchiseeTenantRelationService) GetFranchiseeByTelAndTenant(tel string, tenantID uint) (*franchisees.Franchisee, error) {
	var franchisee franchisees.Franchisee
	
	err := global.GVA_DB.Table("franchisee").
		Joins("INNER JOIN franchisee_tenant_relation ON franchisee.id = franchisee_tenant_relation.franchisee_id").
		Where("franchisee.tel = ? AND franchisee_tenant_relation.tenant_id = ? AND franchisee_tenant_relation.status = ?", 
			tel, tenantID, franchisees.StatusActive).
		First(&franchisee).Error
	
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("在指定租户中未找到该手机号对应的加盟商")
	}
	
	return &franchisee, err
}

// LogFranchiseeTenantOperation 记录加盟商租户操作日志
func (ftrService *FranchiseeTenantRelationService) LogFranchiseeTenantOperation(franchiseeID, tenantID uint, operation, details string) {
	global.GVA_LOG.Info("加盟商租户操作",
		zap.Uint("franchiseeID", franchiseeID),
		zap.Uint("tenantID", tenantID),
		zap.String("operation", operation),
		zap.String("details", details))
}
