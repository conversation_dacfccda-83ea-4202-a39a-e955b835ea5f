package system

import (
	"errors"
	"time"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/system"
	systemReq "github.com/OSQianXing/guanpu-server/model/system/request"
	"github.com/OSQianXing/guanpu-server/utils"
	"gorm.io/gorm"
)

type TenantService struct{}

// CreateTenant 创建租户
func (tenantService *TenantService) CreateTenant(tenant system.Tenant) (err error) {
	var existTenant system.Tenant
	if !errors.Is(global.GVA_DB.Where("code = ?", tenant.Code).First(&existTenant).Error, gorm.ErrRecordNotFound) {
		return errors.New("租户编码已存在")
	}

	// 设置默认状态
	if tenant.Status == nil {
		status := true
		tenant.Status = &status
	}

	return global.GVA_DB.Create(&tenant).Error
}

// DeleteTenant 删除租户
func (tenantService *TenantService) DeleteTenant(id uint) (err error) {
	var tenant system.Tenant
	err = global.GVA_DB.Where("id = ?", id).First(&tenant).Error
	if err != nil {
		return err
	}

	// 检查是否有关联的用户
	var count int64
	err = global.GVA_DB.Model(&system.UserTenantRelation{}).Where("tenant_id = ?", id).Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("该租户下存在关联用户，无法删除")
	}

	return global.GVA_DB.Delete(&tenant).Error
}

// UpdateTenant 更新租户
func (tenantService *TenantService) UpdateTenant(tenant system.Tenant) (err error) {
	var oldTenant system.Tenant
	err = global.GVA_DB.Where("id = ?", tenant.ID).First(&oldTenant).Error
	if err != nil {
		return err
	}

	if tenant.Code != oldTenant.Code {
		var existTenant system.Tenant
		if !errors.Is(global.GVA_DB.Where("code = ?", tenant.Code).First(&existTenant).Error, gorm.ErrRecordNotFound) {
			return errors.New("租户编码已存在")
		}
	}

	return global.GVA_DB.Save(&tenant).Error
}

// GetTenant 获取租户信息
func (tenantService *TenantService) GetTenant(id uint) (tenant system.Tenant, err error) {
	err = global.GVA_DB.Where("id = ?", id).Preload("AppConfig").First(&tenant).Error
	return
}

// GetTenantByCode 根据租户代码获取租户信息
func (tenantService *TenantService) GetTenantByCode(code string) (tenant system.Tenant, err error) {
	err = global.GVA_DB.Where("code = ?", code).Preload("AppConfig").First(&tenant).Error
	return
}

// GetTenantList 获取租户列表
func (tenantService *TenantService) GetTenantList(info request.PageInfo) (list []system.Tenant, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := global.GVA_DB.Model(&system.Tenant{})
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Offset(offset).Preload("AppConfig").Find(&list).Error
	return
}

// AddUserToTenant 添加用户到租户
func (tenantService *TenantService) AddUserToTenant(userID, tenantID uint, role string) error {
	var relation system.UserTenantRelation
	err := global.GVA_DB.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&relation).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 检查是否是用户的第一个租户，如果是则设为默认
		var count int64
		err = global.GVA_DB.Model(&system.UserTenantRelation{}).Where("user_id = ?", userID).Count(&count).Error
		if err != nil {
			return err
		}

		isDefault := count == 0
		status := true
		now := time.Now()

		relation = system.UserTenantRelation{
			UserID:    userID,
			TenantID:  tenantID,
			IsDefault: isDefault,
			Role:      role,
			Status:    &status,
			JoinTime:  &now,
		}

		return global.GVA_DB.Create(&relation).Error
	} else if err != nil {
		return err
	}

	// 已存在关联，更新角色
	relation.Role = role
	return global.GVA_DB.Save(&relation).Error
}

// RemoveUserFromTenant 从租户中移除用户
func (tenantService *TenantService) RemoveUserFromTenant(userID, tenantID uint) error {
	var relation system.UserTenantRelation
	err := global.GVA_DB.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&relation).Error
	if err != nil {
		return err
	}

	// 如果是默认租户，需要重新设置默认租户
	if relation.IsDefault {
		var newDefault system.UserTenantRelation
		err = global.GVA_DB.Where("user_id = ? AND tenant_id != ?", userID, tenantID).First(&newDefault).Error
		if err == nil {
			newDefault.IsDefault = true
			if err = global.GVA_DB.Save(&newDefault).Error; err != nil {
				return err
			}
		}
	}

	return global.GVA_DB.Delete(&relation).Error
}

// SetDefaultTenant 设置默认租户
func (tenantService *TenantService) SetDefaultTenant(userID, tenantID uint) error {
	// 检查用户是否属于该租户
	var relation system.UserTenantRelation
	err := global.GVA_DB.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&relation).Error
	if err != nil {
		return err
	}

	// 取消原默认租户
	err = global.GVA_DB.Model(&system.UserTenantRelation{}).Where("user_id = ? AND is_default = ?", userID, true).Update("is_default", false).Error
	if err != nil {
		return err
	}

	// 设置新默认租户
	relation.IsDefault = true
	return global.GVA_DB.Save(&relation).Error
}

// GetUserTenants 获取用户关联的所有租户
func (tenantService *TenantService) GetUserTenants(userID uint) (tenants []system.Tenant, err error) {
	var relations []system.UserTenantRelation
	err = global.GVA_DB.Where("user_id = ?", userID).Find(&relations).Error
	if err != nil {
		return
	}

	var tenantIDs []uint
	for _, relation := range relations {
		tenantIDs = append(tenantIDs, relation.TenantID)
	}

	if len(tenantIDs) > 0 {
		err = global.GVA_DB.Where("id IN ?", tenantIDs).Preload("AppConfig").Find(&tenants).Error
	}

	return
}

// GetDefaultTenant 获取用户默认租户
func (tenantService *TenantService) GetDefaultTenant(userID uint) (tenant system.Tenant, err error) {
	var relation system.UserTenantRelation
	err = global.GVA_DB.Where("user_id = ? AND is_default = ?", userID, true).First(&relation).Error
	if err != nil {
		return
	}

	err = global.GVA_DB.Where("id = ?", relation.TenantID).Preload("AppConfig").First(&tenant).Error
	return
}

// GenerateTenantToken 生成包含租户信息的JWT令牌
func (tenantService *TenantService) GenerateTenantToken(userID, tenantID uint) (string, error) {
	var user system.SysUser
	err := global.GVA_DB.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return "", err
	}

	// 检查用户是否属于该租户
	var relation system.UserTenantRelation
	err = global.GVA_DB.Where("user_id = ? AND tenant_id = ?", userID, tenantID).First(&relation).Error
	if err != nil {
		return "", err
	}

	// 确定用户类型和权限
	userType := systemReq.UserTypeNormal
	isSuperAdmin := false
	var managedTenants []uint

	// 根据权限ID判断用户类型
	switch user.AuthorityId {
	case 888: // 超级管理员权限ID
		userType = systemReq.UserTypeSuperAdmin
		isSuperAdmin = true
	case 777: // 系统管理员权限ID
		userType = systemReq.UserTypeSystemAdmin
	default:
		if relation.Role == "admin" {
			userType = systemReq.UserTypeTenantAdmin
		}
	}

	// 获取管理的租户列表（对于管理员用户）
	if userType >= systemReq.UserTypeTenantAdmin {
		var relations []system.UserTenantRelation
		err = global.GVA_DB.Where("user_id = ? AND role IN ?", userID, []string{"admin", "manager"}).Find(&relations).Error
		if err == nil {
			for _, rel := range relations {
				managedTenants = append(managedTenants, rel.TenantID)
			}
		}
	}

	j := utils.NewJWT()
	claims := systemReq.CustomClaims{
		BaseClaims: systemReq.BaseClaims{
			ID:             user.ID,
			UUID:           user.UUID,
			Username:       user.Username,
			NickName:       user.NickName,
			AuthorityId:    user.AuthorityId,
			UserType:       userType,
			IsSuperAdmin:   isSuperAdmin,
			ManagedTenants: managedTenants,
		},
		BufferTime: 60 * 60 * 24, // 默认缓冲时间为1天
		TenantID:   tenantID,
	}

	token, err := j.CreateToken(claims)
	if err != nil {
		return "", err
	}

	return token, nil
}
