package system

import (
	"time"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/system"
	"gorm.io/gorm"
)

type SuperAdminLogService struct{}

// CreateSuperAdminLog 创建超级管理员操作日志
func CreateSuperAdminLog(log *system.SuperAdminOperationLog) error {
	return global.GVA_DB.Create(log).Error
}

// GetSuperAdminLogs 获取操作日志
func (s *SuperAdminLogService) GetSuperAdminLogs(page, pageSize int, filters map[string]interface{}) (logs []system.SuperAdminLog, total int64, err error) {
	db := global.GVA_DB.Model(&system.SuperAdminLog{})
	
	for key, value := range filters {
		db = db.Where(key+" = ?", value)
	}
	
	err = db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	err = db.Scopes(Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&logs).Error
		
	return logs, total, err
}

// GetSuperAdminLogStats 获取日志统计
func (s *SuperAdminLogService) GetSuperAdminLogStats(days int) (stats system.SuperAdminLogStats, err error) {
	startTime := time.Now().AddDate(0, 0, -days)
	
	err = global.GVA_DB.Model(&system.SuperAdminLog{}).
		Select("operation_type, count(*) as total").
		Where("created_at >= ?", startTime).
		Group("operation_type").
		Scan(&stats.OperationTypeStats).Error
	if err != nil {
		return stats, err
	}
	
	err = global.GVA_DB.Model(&system.SuperAdminLog{}).
		Select("user_id, count(*) as total").
		Where("created_at >= ?", startTime).
		Group("user_id").
		Order("total DESC").
		Limit(10).
		Scan(&stats.TopUsers).Error
		
	return stats, err
}

func Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}
		
		switch {
		case pageSize > 100:
			pageSize = 100
		case pageSize <= 0:
			pageSize = 10
		}
		
		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}