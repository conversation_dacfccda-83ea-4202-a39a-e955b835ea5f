package orders

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm/clause"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/common"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/orders"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/model/orders/response"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"gorm.io/gorm"
)

type OrderDeliveryService struct {
}

// CreateOrderDelivery 创建OrderDelivery记录

func (orderDeliveryService *OrderDeliveryService) CreateOrderDelivery(orderDelivery *ordersReq.OrderDeliveryCreate, userName string, operatorId uint) (id uint, err error) {
	var orderDeliveryModel orders.OrderDelivery
	orderDeliveryModel.OrderNo = orderDelivery.OrderNo
	orderDeliveryModel.DeliveryNo = utils.GenDeliveryNo(orderDelivery.FranchiseeId, utils.SequenceGuanpuSerialNo)
	orderDeliveryModel.FranchiseeId = orderDelivery.FranchiseeId
	orderDeliveryModel.GoodsDetail = orderDelivery.GoodsDetail
	orderDeliveryModel.LogisticsNo = orderDelivery.LogisticsNo
	orderDeliveryModel.LogisticsId = &orderDelivery.LogisticsId // TODO: check why orderDeveliveryModel.LogisticsId is *int
	orderDeliveryModel.Remark = &orderDelivery.Remark
	orderDeliveryModel.Consignor = userName

	allHasDelivered, err := orderDeliveryService.CheckDeliveryWithCreatedModel(orderDelivery.OrderNo, &orderDeliveryModel)
	if err != nil {
		return 0, err
	}
	orderStatus := types.OrderPartDelivery
	if allHasDelivered {
		orderStatus = types.OrderDelivery
	}

	o := &orders.Order{}
	err = global.GVA_DB.Model(orders.Order{}).Where("order_no = ?", orderDelivery.OrderNo).First(o).Error
	if err != nil {
		return 0, errors.Join(err, errors.New("订单不存在"))
	}

	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		err = tx.Create(&orderDeliveryModel).Error
		if err != nil {
			return errors.Join(err, errors.New("创建发货单失败"))
		}

		err = tx.Model(orders.Order{}).Where("order_no = ?", orderDelivery.OrderNo).Update("status", orderStatus).Error
		if err != nil {
			return errors.Join(err, errors.New("更新订单状态失败:"+orderDelivery.OrderNo))
		}

		// Send order status change notification
		if err = utils.EnqueueOrderStatusChange(orderDelivery.OrderNo, o.ID, int8(types.OrderAwaitingDelivery), int8(orderStatus), operatorId, fmt.Sprintf("Order status changed from %s to %s", types.OrderAwaitingDelivery, orderStatus)); err != nil {
			global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
		}

		reqJson, _ := json.Marshal(orders.OrderRecordExtra{
			Req:    orderDelivery,
			Result: orderDeliveryModel,
			Stage:  orderStatus.StringEn(),
		})
		err = tx.Create(&orders.OrderRecord{
			OrderID:     o.ID,
			OrderNo:     orderDelivery.OrderNo,
			OrderStatus: types.OrderStocking,
			OperatorID:  operatorId,
			Extra:       reqJson,
			Remark:      orderDelivery.Remark,
		}).Error
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return 0, err
	}

	return orderDeliveryModel.ID, nil
}

func (orderDeliveryService *OrderDeliveryService) BatchStocking(orderStocking *ordersReq.BatchStocking, userName string, operatorId uint) (odMap map[string]string, err error) {
	//TODO: add mysql read lock to every order to prevent concurrent modification and finialize release lock
	var (
		ods      []*orders.OrderDelivery
		osMap    = make(map[string]bool)
		orderNos []string
		ors      []*orders.OrderRecord
	)

	odMap = make(map[string]string)
	for _, os := range orderStocking.OrderStockings {
		if os.FranchiseeId == 0 || os.OrderNo == "" || os.LogisticsId == 0 {
			return nil, fmt.Errorf("参数错误")
		}
		if _, ok := osMap[os.OrderNo]; ok {
			return nil, fmt.Errorf("订单号重复: %s", os.OrderNo)
		}
		osMap[os.OrderNo] = true
		orderNos = append(orderNos, os.OrderNo)

		// 校验订单是否已发货
		var od orders.OrderDelivery
		err = global.GVA_DB.Where("order_no = ?", os.OrderNo).First(&od).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if od.ID != 0 {
			return nil, fmt.Errorf("订单已操作过发货,请核对: %s", os.OrderNo)
		}

		// 组装GoodsDetail
		var (
			goodsDetail = make(orders.GoodsDetails, 0)
			ogs         []*orders.OrderGoods
			o           orders.Order
		)
		err = global.GVA_DB.Where("order_no = ?", os.OrderNo).First(&o).Error
		if err != nil {
			return nil, err
		}

		err = global.GVA_DB.Where("order_id = ?", o.ID).Find(&ogs).Error
		if err != nil {
			return nil, err
		}
		for _, og := range ogs {
			quantity := og.GetQuantity(o.PaymentType)
			goodsDetail = append(goodsDetail, &orders.GoodsDetail{
				GoodsId:  int(og.ID),
				GoodsNum: quantity,
			})
		}

		odSt := &orders.OrderDelivery{
			OrderNo:      os.OrderNo,
			DeliveryNo:   utils.GenDeliveryNo(os.FranchiseeId, utils.SequenceGuanpuSerialNo),
			FranchiseeId: os.FranchiseeId,
			GoodsDetail:  &goodsDetail,
			LogisticsId:  &os.LogisticsId,
			Consignor:    userName,
			Remark:       &o.Remark,
		}
		reqJson, _ := json.Marshal(orders.OrderRecordExtra{
			Req:    orderStocking,
			Result: odSt,
			Stage:  types.OrderStocking.StringEn(),
		})
		ors = append(ors, &orders.OrderRecord{
			OrderID:     o.ID,
			OrderNo:     os.OrderNo,
			OrderStatus: types.OrderStocking,
			OperatorID:  operatorId,
			Extra:       reqJson,
			Remark:      o.Remark,
		})
		ods = append(ods, odSt)
		odMap[os.OrderNo] = odSt.DeliveryNo
	}

	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		err = tx.Create(&ods).Error
		if err != nil {
			return err
		}

		err = tx.Model(orders.Order{}).Where("order_no in (?)", orderNos).Updates(map[string]interface{}{"status": types.OrderDelivery}).Error
		if err != nil {
			return err
		}

		// Send order status change notification for each order
		for _, orderNo := range orderNos {
			o := orders.Order{}
			err = tx.Where("order_no = ?", orderNo).First(&o).Error
			if err != nil {
				return err
			}
			if err = utils.EnqueueOrderStatusChange(orderNo, o.ID, int8(types.OrderAwaitingDelivery), int8(types.OrderDelivery), operatorId, fmt.Sprintf("Order status changed from %s to %s", types.OrderAwaitingDelivery, types.OrderDelivery)); err != nil {
				global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
			}
		}

		err = tx.Create(&ors).Error
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return odMap, nil
}

func (orderDeliveryService *OrderDeliveryService) BatchConfirmDeliveryByDeliveryNos(ids request.NosReq, operatorId uint) (err error) {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		var ods []*orders.OrderDelivery
		err = tx.Model(&orders.OrderDelivery{}).Where("delivery_no in (?)", ids.Nos).Find(&ods).Error
		if err != nil {
			return err
		}

		// 检查发货单状态
		for _, od := range ods {
			if od.Status != types.DeliveryStatusStocking {
				return errors.New("发货单状态异常，非备货状态：" + od.Status.String())
			}
		}

		// 修改发货单状态
		err = tx.Model(&orders.OrderDelivery{}).Where("delivery_no in (?)", ids.Nos).Update("status", types.DeliveryStatusDelivery).Error
		if err != nil {
			return err
		}

		ors := make([]*orders.OrderRecord, 0)
		for _, od := range ods {
			// Get the order ID from the database
			var order orders.Order
			if err = tx.Where("order_no = ?", od.OrderNo).First(&order).Error; err != nil {
				return fmt.Errorf("failed to get order: %w", err)
			}
			// check order status
			if order.Status != types.OrderDelivery && order.Status != types.OrderPartDelivery {
				return errors.New("订单状态异常，非发货/部分发货状态：" + order.Status.String())
			}

			reqJson, _ := json.Marshal(orders.OrderRecordExtra{
				Req:   od,
				Stage: types.OrderDelivery.StringEn(),
			})
			ors = append(ors, &orders.OrderRecord{
				OrderID:     order.ID,
				OrderNo:     order.OrderNo,
				OrderStatus: types.OrderDelivery,
				OperatorID:  operatorId,
				Extra:       reqJson,
			})
		}

		// 创建订单记录
		err = tx.Create(&ors).Error
		if err != nil {
			return err
		}

		// Send order status change notification for each delivery
		for _, od := range ods {
			// Get the order ID from the database again to ensure we have it in scope
			var order orders.Order
			if err = tx.Where("order_no = ?", od.OrderNo).First(&order).Error; err != nil {
				return fmt.Errorf("failed to get order: %w", err)
			}
			if err = utils.EnqueueOrderStatusChange(od.OrderNo, order.ID, int8(types.DeliveryStatusStocking), int8(types.DeliveryStatusDelivery), operatorId, fmt.Sprintf("Order delivery status changed from %s to %s", types.DeliveryStatusStocking, types.DeliveryStatusDelivery)); err != nil {
				global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
			}
		}

		return nil
	})
}

func (orderDeliveryService *OrderDeliveryService) CheckDeliveryWithCreatedModel(orderNo string, createModel *orders.OrderDelivery) (hasDeliveried bool, err error) {
	allGoodsDetail, err := orderDeliveryService.GetAllGoodsDetailByOrderNo(orderNo)
	if err != nil {
		return false, err
	}
	// 查已发货的商品
	var orderDeliverys []*orders.OrderDelivery
	err = global.GVA_DB.Where("order_no = ?", orderNo).Find(&orderDeliverys).Error
	if err != nil {
		return false, err
	}
	// 减去已发货的商品
	for _, orderDelivery := range orderDeliverys {
		if orderDelivery.GoodsDetail == nil {
			continue
		}
		for _, d := range *orderDelivery.GoodsDetail {
			if allCount, ok := allGoodsDetail[uint(d.GoodsId)]; ok {
				allGoodsDetail[uint(d.GoodsId)] = allCount - d.GoodsNum
			}
		}
	}
	// 校验本次发货的商品
	if createModel == nil || createModel.GoodsDetail == nil || len(*createModel.GoodsDetail) == 0 {
		return false, errors.New("本次没有发货商品")
	}

	for _, goodsDetail := range *createModel.GoodsDetail {
		if goodsDetail.GoodsNum <= 0 {
			return false, errors.New("发货商品数量必须大于0")
		}
		remainDeliveryCount, ok := allGoodsDetail[uint(goodsDetail.GoodsId)]
		if !ok {
			return false, errors.New("发货商品不在订单中")
		}
		if goodsDetail.GoodsNum > remainDeliveryCount {
			return false, errors.New("发货商品数量超过订单中的数量")
		}
		remainDeliveryCount = remainDeliveryCount - goodsDetail.GoodsNum
		allGoodsDetail[uint(goodsDetail.GoodsId)] = remainDeliveryCount
	}
	// 校验是否全部发货
	allGoodsDeliveried := true
	for _, remainCount := range allGoodsDetail {
		if remainCount != 0 {
			allGoodsDeliveried = false
			break
		}
	}
	return allGoodsDeliveried, nil
}

// GetAllGoodsDetailByOrderNo 根据订单号获取商品详情 map[orderGoods.id]商品数量
func (orderDeliveryService *OrderDeliveryService) GetAllGoodsDetailByOrderNo(orderNo string) (goodsDetail map[uint]int, err error) {
	var orderModel orders.Order
	err = global.GVA_DB.Where("order_no = ?", orderNo).First(&orderModel).Error
	if err != nil {
		return nil, err
	}

	if orderModel.OrderSubType == types.Presentation {
		return nil, errors.New("压货订单不支持备货")
	}

	if orderModel.Status == types.OrderRefund {
		return nil, errors.New("订单已退款")
	}

	var orderGoods []*orders.OrderGoods
	err = global.GVA_DB.Where("order_id = ?", orderModel.ID).Find(&orderGoods).Error
	if err != nil {
		return nil, err
	}
	if len(orderGoods) == 0 {
		return nil, nil
	}
	// 用map存储商品id和商品数量
	var orderGoodsMap = make(map[uint]int)
	for _, orderGood := range orderGoods {
		orderGoodsMap[orderGood.ID] = orderGood.GetQuantity(orderModel.PaymentType)
	}
	return orderGoodsMap, nil
}

// DeleteOrderDelivery 删除OrderDelivery记录

func (orderDeliveryService *OrderDeliveryService) DeleteOrderDelivery(orderDelivery orders.OrderDelivery) (err error) {
	err = global.GVA_DB.Delete(&orderDelivery).Error
	return err
}

// DeleteOrderDeliveryByIds 批量删除OrderDelivery记录

func (orderDeliveryService *OrderDeliveryService) DeleteOrderDeliveryByIds(ids request.IdsReq) (err error) {
	err = global.GVA_DB.Delete(&[]orders.OrderDelivery{}, "id in ?", ids.Ids).Error
	return err
}

// UpdateOrderDelivery 更新OrderDelivery记录
func (orderDeliveryService *OrderDeliveryService) UpdateOrderDelivery(req ordersReq.OrderDeliveryUpdate, operatorId uint) (err error) {
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 更新物流单号
		err := tx.Model(&orders.OrderDelivery{}).Where("id = ?", req.ID).Update("logistics_no", req.LogisticsNo).Error
		if err != nil {
			return err
		}

		// 查询发货单
		var orderDelivery orders.OrderDelivery
		err = tx.Where("id = ?", req.ID).First(&orderDelivery).Error
		if err != nil {
			return err
		}

		o := orders.Order{}
		err = tx.Model(&o).Where("order_no = ?", orderDelivery.OrderNo).First(&o).Error
		if err != nil {
			return errors.Join(err, errors.New("订单不存在:"+orderDelivery.OrderNo))
		}

		// 如果是已发货状态，更新为备货中
		if orderDelivery.Status == types.DeliveryStatusDelivery {
			od := orders.OrderDelivery{}
			err = tx.Model(&od).Clauses(clause.Returning{}).Where("id = ?", req.ID).Update("status", types.DeliveryStatusStocking).Error
			if err != nil {
				return errors.Join(err, errors.New("更新发货单状态失败:"+orderDelivery.OrderNo))
			}

			reqJson, _ := json.Marshal(orders.OrderRecordExtra{
				Req:    req,
				Result: od,
				Stage:  types.OrderStocking.StringEn(),
			})
			err = tx.Model(&orders.OrderRecord{}).Create(&orders.OrderRecord{
				OrderID:     o.ID,
				OrderNo:     o.OrderNo,
				OrderStatus: types.OrderStocking,
				OperatorID:  operatorId,
				Extra:       reqJson,
			}).Error
			if err != nil {
				return errors.Join(err, errors.New("更新订单创建订单记录失败:"+orderDelivery.OrderNo))
			}

			// Send order status change notification
			if err = utils.EnqueueOrderStatusChange(orderDelivery.OrderNo, o.ID, int8(types.DeliveryStatusDelivery), int8(types.DeliveryStatusStocking), operatorId, fmt.Sprintf("Order delivery status changed from %s to %s", types.DeliveryStatusDelivery, types.DeliveryStatusStocking)); err != nil {
				global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
			}
		}

		// 如果是备货中状态，更新为已发货
		if orderDelivery.Status == types.DeliveryStatusStocking {
			od := orders.OrderDelivery{}
			err = tx.Model(&od).Clauses(clause.Returning{}).Where("id = ?", req.ID).Update("status", types.DeliveryStatusDelivery).Error
			if err != nil {
				return err
			}
			reqJson, _ := json.Marshal(orders.OrderRecordExtra{
				Req:    req,
				Result: od,
				Stage:  types.OrderDelivery.StringEn(),
			})
			err = tx.Model(&orders.OrderRecord{}).Create(&orders.OrderRecord{
				OrderID:     o.ID,
				OrderNo:     o.OrderNo,
				OrderStatus: types.OrderDelivery,
				OperatorID:  operatorId,
				Extra:       reqJson,
			}).Error
			if err != nil {
				return errors.Join(err, errors.New("更新订单创建订单记录失败:"+orderDelivery.OrderNo))
			}

			// Send order status change notification
			if err = utils.EnqueueOrderStatusChange(orderDelivery.OrderNo, o.ID, int8(types.DeliveryStatusStocking), int8(types.DeliveryStatusDelivery), operatorId, fmt.Sprintf("Order delivery status changed from %s to %s", types.DeliveryStatusStocking, types.DeliveryStatusDelivery)); err != nil {
				global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
			}
		}
		return nil
	})
	return
}

func (orderDeliveryService *OrderDeliveryService) GetOrderDeliveryListByDeliveryNos(nos request.NosReq) (odl response.OrderDeliveryList, err error) {
	var ods []orders.OrderDelivery
	err = global.GVA_DB.Where("delivery_no in (?)", nos.Nos).Find(&ods).Error
	if err != nil {
		return odl, err
	}

	if len(ods) != len(nos.Nos) {
		return odl, errors.New("发货单号不存在")
	}

	var odlItem = make([]response.OrderDeliveryListItem, 0, len(ods))
	var productMap = make(map[uint]*products.Product)
	var productCountMap = make(map[uint]int)                       // 商品数量统计
	var consigneeProductCountMap = make(map[string]int)            // 收货人Tel商品数量统计
	var consigneeOrderMap = make(map[string][]string)              // 收货人Tel订单统计
	var consigneeMap = make(map[string]*response.ConsigneeSummary) // 收货人电话号码映射

	for _, od := range ods {
		odInfo, err := orderDeliveryService.GetOrderDelivery(od.ID)
		if err != nil {
			return odl, err
		}

		// 查询加盟商/订单/物流信息
		var (
			f franchisees.Franchisee
			o orders.Order
			l common.LogisticsTemplate
		)
		err = global.GVA_DB.Unscoped().Where("id = ?", od.FranchiseeId).First(&f).Error
		if err != nil {
			return odl, err
		}
		err = global.GVA_DB.Where("order_no = ?", od.OrderNo).First(&o).Error
		if err != nil {
			return odl, err
		}
		err = global.GVA_DB.Where("id = ?", od.LogisticsId).First(&l).Error
		if err != nil {
			return odl, err
		}

		var applyAreas []string
		err = global.GVA_DB.Model(&common.LogisticsApplyArea{}).Where("template_id = ?", l.ID).Pluck("area", &applyAreas).Error
		if err != nil {
			return odl, err
		}
		l.ApplyAreas = applyAreas
		odlItem = append(odlItem, response.OrderDeliveryListItem{
			OrderDelivery: odInfo,
			Franchisee:    f,
			Order:         o,
			Logistics:     l,
		})
		// delivery summary
		if _, ok := consigneeOrderMap[o.AddressSnapshot.Tel]; !ok { // 不存在收货人的订单
			consigneeOrderMap[o.AddressSnapshot.Tel] = make([]string, 0)
			consigneeOrderMap[o.AddressSnapshot.Tel] = append(consigneeOrderMap[o.AddressSnapshot.Tel], o.OrderNo)
		} else {
			consigneeOrderMap[o.AddressSnapshot.Tel] = append(consigneeOrderMap[o.AddressSnapshot.Tel], o.OrderNo)
		}

		if _, ok := consigneeMap[o.AddressSnapshot.Tel]; !ok {
			consigneeMap[o.AddressSnapshot.Tel] = &response.ConsigneeSummary{
				ConsigneeName:  o.AddressSnapshot.Consignee,
				ConsigneeTel:   o.AddressSnapshot.Tel,
				ConsigneeAddr:  o.AddressSnapshot,
				OrderNos:       nil,
				TotalNum:       0,
				FranchiseeName: f.Name,
				FranchiseeTel:  f.Tel,
			}
		} else {

		}

		goods := *odInfo.GoodsDetail
		for _, g := range goods {
			if *g.Product.IsCombination {
				// get combination detail
				combinationP, err := dao.Product.GetCombinationProductDetial(int(g.Product.ID))
				if err != nil {
					global.GVA_LOG.Error("error in service.orderDeliveryService.GetOrderDeliveryListByDeliveryNos", zap.String("error", err.Error()), zap.Uint("productId", g.Product.ID))
					return response.OrderDeliveryList{}, err
				}
				for _, cp := range combinationP {
					if _, ok := productMap[cp.Product.ID]; !ok {
						productMap[cp.Product.ID] = cp.Product
					}
					// get product count
					if _, ok := productCountMap[cp.Product.ID]; !ok {
						productCountMap[cp.Product.ID] = cp.Quantity * g.GoodsNum
					} else {
						productCountMap[cp.Product.ID] += cp.Quantity * g.GoodsNum
					}
					if _, ok := consigneeProductCountMap[o.AddressSnapshot.Tel]; !ok {
						consigneeProductCountMap[o.AddressSnapshot.Tel] = cp.Quantity * g.GoodsNum
					} else {
						consigneeProductCountMap[o.AddressSnapshot.Tel] += cp.Quantity * g.GoodsNum
					}
				}
			} else {
				if _, ok := productMap[g.Product.ID]; !ok {
					productMap[g.Product.ID] = &g.Product
				}
				// get product count
				if _, ok := productCountMap[g.Product.ID]; !ok {
					productCountMap[g.Product.ID] = g.GoodsNum
				} else {
					productCountMap[g.Product.ID] += g.GoodsNum
				}
				if _, ok := consigneeProductCountMap[o.AddressSnapshot.Tel]; !ok {
					consigneeProductCountMap[o.AddressSnapshot.Tel] = g.GoodsNum
				} else {
					consigneeProductCountMap[o.AddressSnapshot.Tel] += g.GoodsNum
				}
			}
			global.GVA_LOG.Debug("maps", zap.Any("productCountMap", productCountMap), zap.Any("consigneeProductCountMap", consigneeProductCountMap), zap.Any("consigneeOrderMap", consigneeOrderMap), zap.Any("consigneeMap", consigneeMap), zap.Any("productMap", productMap))
		}
	}
	var productCount = make([]*response.ProductCount, 0)
	var ConsigneeSummarys = make([]response.ConsigneeSummary, 0)
	for p, c := range productCountMap {
		productCount = append(productCount, &response.ProductCount{
			Product:  *productMap[p],
			Quantity: c,
		})
	}
	for c := range consigneeMap {
		consigneeMap[c].TotalNum = consigneeProductCountMap[c]
		consigneeMap[c].OrderNos = consigneeOrderMap[c]
		ConsigneeSummarys = append(ConsigneeSummarys, *consigneeMap[c])
	}

	odl.Items = odlItem
	odl.Summarys = response.DeliverySummary{
		ConsigneeSummary: ConsigneeSummarys,
		ProductsSummary:  productCount,
	}

	return odl, nil
}

// GetOrderDelivery 根据id获取OrderDelivery记录
func (orderDeliveryService *OrderDeliveryService) GetOrderDelivery(id uint) (orderDelivery orders.OrderDelivery, err error) {
	err = global.GVA_DB.Preload("OrderRecords").Preload("OrderRecords.Operator").Where("id = ?", id).First(&orderDelivery).Error
	if err != nil {
		return
	}
	var o orders.Order
	err = global.GVA_DB.Where("order_no = ?", orderDelivery.OrderNo).First(&o).Error
	if err != nil {
		return
	}
	for j, goodsDetail := range *orderDelivery.GoodsDetail {
		var (
			product    products.Product
			orderGoods orders.OrderGoods
		)
		err = global.GVA_DB.
			Table("order_goods as og").
			Joins("left join product as p on p.id = og.product_id").
			Where("og.id = ?", goodsDetail.GoodsId).
			Select("p.*").
			First(&product).Error
		if err != nil {
			return
		}
		(*orderDelivery.GoodsDetail)[j].Product = product

		if utils.BoolIsTrue(product.IsCombination) {
			// 组合商品信息
			combination, err := dao.Product.GetCombinationProductDetial(int(product.ID))
			if err != nil {
				return orders.OrderDelivery{}, err
			}
			(*orderDelivery.GoodsDetail)[j].CombinationDetail = combination
		}
		err = global.GVA_DB.Where("id = ?", goodsDetail.GoodsId).First(&orderGoods).Error
		if err != nil {
			return
		}
		(*orderDelivery.GoodsDetail)[j].Cost = &orderGoods.UnitPrice
		(*orderDelivery.GoodsDetail)[j].ExchangePrice = &orderGoods.PointsPrice
		(*orderDelivery.GoodsDetail)[j].TotalNum = orderGoods.GetQuantity(o.PaymentType)
	}
	if orderDelivery.LogisticsId != nil && *orderDelivery.LogisticsId == 0 {
		return
	}
	var logisticsTemplate common.LogisticsTemplate
	err = global.GVA_DB.Unscoped().Where("id = ?", orderDelivery.LogisticsId).First(&logisticsTemplate).Error
	if err != nil {
		return
	}
	orderDelivery.ExpressCompany = logisticsTemplate.Name
	return
}

// GetOrderDeliveryInfoList 分页获取OrderDelivery记录
func (orderDeliveryService *OrderDeliveryService) GetOrderDeliveryInfoList(info ordersReq.OrderDeliverySearch) (list []response.OrderDelivery, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&orders.OrderDelivery{})
	db = db.Joins("LEFT JOIN `orders` as o on o.order_no = order_delivery.order_no and o.franchisee_id = order_delivery.franchisee_id")

	var orderDeliverys []response.OrderDelivery
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("order_delivery.created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.StartDeliveryAt != nil && info.EndDeliveryAt != nil {
		db = db.Joins("OrderRecords").Where("OrderRecords.order_status = ? and OrderRecords.created_at BETWEEN ? AND ?", types.OrderDelivery, info.StartDeliveryAt, info.EndDeliveryAt)
	}
	if info.BigWarehouseId != nil && *info.BigWarehouseId != 0 {
		db = db.Joins("LEFT JOIN big_warehouse_order ON big_warehouse_order.order_no = order_delivery.order_no ").Where("big_warehouse_order.big_warehouse_id = ?", info.BigWarehouseId)
	}
	if info.DeliveryNo != "" {
		db = db.Where("order_delivery.delivery_no = ?", info.DeliveryNo)
	}
	if info.OrderNo != "" {
		db = db.Where("order_delivery.order_no = ?", info.OrderNo)
	}
	if info.FranchiseeId != 0 {
		db = db.Where("order_delivery.franchisee_id = ?", info.FranchiseeId)
	}
	if info.FranchiseTel != "" {
		var franchiseeIds []uint
		err := global.GVA_DB.Model(&franchisees.Franchisee{}).Where("tel = ?", info.FranchiseTel).Pluck("id", &franchiseeIds).Error
		if err != nil {
			return nil, 0, err
		}
		if len(franchiseeIds) > 0 {
			db = db.Where("order_delivery.franchisee_id in ?", franchiseeIds)
		}
	}
	if info.ConsigneeTel != nil && *info.ConsigneeTel != "" {
		//db = db.Joins("LEFT JOIN franchisee_address as fa on fa.franchisee_id = order_delivery.franchisee_id").Where(" fa.tel = ? ", *info.ConsigneeTel)
		db = db.Where(datatypes.JSONQuery("o.address_snapshot").Equals(*info.ConsigneeTel, "tel"))
	}
	if info.ConsigneeName != nil && *info.ConsigneeName != "" {
		//db = db.Joins("LEFT JOIN franchisee_address as fa on fa.franchisee_id = order_delivery.franchisee_id").Where(" fa.consignee = ? ", *info.ConsigneeName)
		db = db.Where(datatypes.JSONQuery("o.address_snapshot").Equals(*info.ConsigneeName, "consignee"))
	}

	if info.Status != nil {
		db = db.Where("order_delivery.status = ?", int8(*info.Status))
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit == 0 {
		limit = 10
	}
	err = db.Preload("Order").Preload("OrderRecords").Preload("BigWarehouseOrder.BigWarehouse").Preload("BigWarehouseOrder").
		Select("order_delivery.id,order_delivery.created_at,order_delivery.delivery_no,order_delivery.order_no,order_delivery.franchisee_id,order_delivery.status,order_delivery.goods_detail,order_delivery.logistics_no,order_delivery.logistics_id,order_delivery.consignor,order_delivery.remark,o.address_snapshot->>'$.consignee' as consignee_name, o.address_snapshot->>'$.tel' as consignee_tel,CONCAT_WS(' ',o.address_snapshot->>'$.province',o.address_snapshot->>'$.city',o.address_snapshot->>'$.county',o.address_snapshot->>'$.address') as order_address,order_delivery.created_at as stocking_time," +
			"CASE order_delivery.status WHEN 4 THEN order_delivery.updated_at ELSE order_delivery.created_at END as delivery_time").
		Group("order_delivery.id,o.address_snapshot").
		Limit(limit).Order("order_delivery.created_at DESC").Offset(offset).Find(&orderDeliverys).Error
	// 补充加盟商信息
	for i, orderDelivery := range orderDeliverys {
		var franchiseeModel franchisees.Franchisee
		err = global.GVA_DB.Unscoped().Where("id = ?", orderDelivery.FranchiseeId).First(&franchiseeModel).Error
		if err != nil {
			if utils.ErrorIsDBNotFound(err) {
				continue
			} else {
				return nil, 0, err
			}
		}
		orderDeliverys[i].Franchisee = franchiseeModel
		for _, v1 := range *orderDeliverys[i].GoodsDetail {
			var og orders.OrderGoods
			err = global.GVA_DB.Where("id = ?", v1.GoodsId).First(&og).Error
			if err != nil {
				if utils.ErrorIsDBNotFound(err) {
					continue
				}

				return nil, 0, err
			}

			// 查询商品名称
			p, err := dao.Product.FirstProduct(og.ProductId)
			if err != nil {
				if utils.ErrorIsDBNotFound(err) {
					continue
				}
				return nil, 0, err
			}
			v1.Product = *p
		}
	}
	return orderDeliverys, total, err
}

func (orderDeliveryService *OrderDeliveryService) ExportOrderDelivery(pageInfo ordersReq.OrderDeliverySearch) (*excelize.File, error) {
	db := global.GVA_DB.Table("order_delivery as od")
	// 如果有条件搜索 下方会自动创建搜索语句
	if pageInfo.StartCreatedAt != nil && pageInfo.EndCreatedAt != nil {
		// 时间范围不能超过 3 个月
		if pageInfo.EndCreatedAt.Sub(*pageInfo.StartCreatedAt).Hours() > 24*184 {
			return nil, errors.New("时间范围不能超过 184 天")
		}
		db = db.Where("od.created_at BETWEEN ? AND ?", pageInfo.StartCreatedAt.Local(), pageInfo.EndCreatedAt.Local())
	}

	if pageInfo.BigWarehouseId != nil && *pageInfo.BigWarehouseId != 0 {
		db = db.Joins("LEFT JOIN big_warehouse_order ON big_warehouse_order.order_no = od.order_no ").Where("big_warehouse_order.big_warehouse_id = ?", pageInfo.BigWarehouseId)
	}
	if pageInfo.StartDeliveryAt != nil && pageInfo.EndDeliveryAt != nil {
		db = db.Joins("LEFT JOIN order_record as OrderRecords on OrderRecords.order_no = od.order_no ").Where("OrderRecords.order_status = ? and OrderRecords.created_at BETWEEN ? AND ?", types.OrderDelivery, pageInfo.StartDeliveryAt.Local(), pageInfo.EndDeliveryAt.Local())
	}

	if pageInfo.StartDeliveryAt == nil && pageInfo.EndDeliveryAt == nil && pageInfo.StartCreatedAt == nil && pageInfo.EndCreatedAt == nil {
		now := time.Now().Local()
		begin := now.AddDate(0, -3, 0)
		db = db.Where("od.created_at BETWEEN ? AND ?", begin, now)
	}
	if pageInfo.DeliveryNo != "" {
		db = db.Where("od.delivery_no = ?", pageInfo.DeliveryNo)
	}
	if pageInfo.OrderNo != "" {
		db = db.Where("od.order_no = ?", pageInfo.OrderNo)
	}
	if pageInfo.FranchiseeId != 0 {
		db = db.Where("od.franchisee_id = ?", pageInfo.FranchiseeId)
	}
	if pageInfo.FranchiseTel != "" {
		var franchiseeIds []uint
		err := global.GVA_DB.Model(&franchisees.Franchisee{}).Where("tel = ?", pageInfo.FranchiseTel).Pluck("id", &franchiseeIds).Error
		if err != nil {
			return nil, err
		}
		if len(franchiseeIds) > 0 {
			db = db.Where("od.franchisee_id in ?", franchiseeIds)
		}
	}
	if pageInfo.Status != nil {
		db = db.Where("od.status = ?", int8(*pageInfo.Status))
	}
	var orderDeliverys []*response.ExcelOrderDelivery
	err := db.Preload("OrderRecords").Preload("BigWarehouseOrder.BigWarehouse").Preload("BigWarehouseOrder").Model(&orders.OrderDelivery{}).Select("od.delivery_no, od.status,o.order_no, o.payment_type, f.name as franchisee_name, u.username as franchisee_account," +
		"od.goods_detail, o.address_snapshot, od.remark, o.created_at as order_time, " +
		"od.created_at as stocking_time," +
		"CASE od.status WHEN 4 THEN od.updated_at ELSE od.created_at  END as delivery_time," +
		"CASE od.status WHEN 3 THEN od.updated_at ELSE NULL  END as receive_time," +
		" od.logistics_no,od.goods_detail," +
		"lt.express_company,lt.linkman,lt.tel").
		Joins("left join `" + orders.Order{}.TableName() + "` as o on o.order_no = od.order_no").
		Joins("left join franchisee as f on f.id = od.franchisee_id").
		Joins("left join sys_users as u on u.id = f.user_id").
		Joins("left join logistics_template as lt on lt.id = od.logistics_id").
		Group("od.id,o.address_snapshot,o.payment_type,o.payment_type,o.created_at").
		Find(&orderDeliverys).Error
	if err != nil {
		return nil, err
	}
	// 补充信息
	for i, orderDelivery := range orderDeliverys {
		ogIDs := orderDelivery.GoodsDetail.GetGoodsIds()
		err := global.GVA_DB.Table("order_goods as og").
			Joins("left join product as p on p.id = og.product_id").
			Where("og.id in ?", ogIDs).
			Select("og.id as goods_id,p.name as product_name,p.special_mall_id ," +
				"p.id as product_id,p.is_combination, if(og.quantity>0,og.quantity,og.points_quantity) as purchase_quantity ").
			Find(&orderDeliverys[i].ExportProductDetail).Error
		if err != nil {
			return nil, err
		}

		for _, productDetail := range orderDeliverys[i].ExportProductDetail {
			var specialMallName string
			err = global.GVA_DB.Where("id = ?", productDetail.SpecialMallID).Model(&products.SpecialMall{}).Select("name as specail_mall_name").Scan(&specialMallName).Error
			if err != nil {
				return nil, err
			}
			productDetail.SpecailMallName = specialMallName
			if !productDetail.IsCombination {
				continue
			}
			// 组合商品信息
			combination, err := dao.Product.GetCombinationProductDetial(productDetail.ProductID)
			if err != nil {
				return nil, err
			}
			productDetail.CombinationDetail = combination
		}
	}
	global.GVA_LOG.Debug("orderDeliverys", zap.Any("orderDeliverys", orderDeliverys))
	return orderDeliveryService.ExportExcel(orderDeliverys)
}

// ExportExcel 生成excel
func (orderDeliveryService *OrderDeliveryService) ExportExcel(orderDeliverys []*response.ExcelOrderDelivery) (*excelize.File, error) {
	// 创建excel
	f := excelize.NewFile()
	// 创建sheet
	sheetName := "发货单"
	// 修改第一个 sheet 的名称
	if err := f.SetSheetName(f.GetSheetName(0), sheetName); err != nil {
		global.GVA_LOG.Error("SetSheetName err!", zap.Error(err))
		return nil, err
	}
	header := []interface{}{"发货单号", "状态", "订单号", "支付方式", "加盟商名称", "加盟商账号", "商品名称", "专区名称", "组合商品明细", "购买数量", "发货件数", "收货人", "收货电话", "收货地址", "备注", "下单时间", "备货时间", "发货时间", "物流信息", "物流单号", "仓库"}

	if err := f.SetSheetRow(sheetName, "A1", &header); err != nil {
		return nil, err
	}
	line := 2
	for _, orderDelivery := range orderDeliverys {
		for _, productDetail := range orderDelivery.ExportProductDetail {
			var logisticsInfo string
			if orderDelivery.ExpressCompany != "" {
				logisticsInfo = fmt.Sprintf("%s (%s %s)", orderDelivery.ExpressCompany, orderDelivery.ExpressLinkman, orderDelivery.ExpressTel)
			}
			var orderDeliveryTime string
			if orderDelivery.Status > 0 {
				// orderDeliveryTime = orderDelivery.DeliveryTime.Format("2006-01-02 15:04:05")
				orderDeliveryTime = utils.TimeStringOrZero(&orderDelivery.DeliveryTime)
			}
			// ormap := utils.FormatOrderRecords(orderDelivery.OrderRecords)
			ormap := utils.FormatOrderRecordsWithDeliverNo(orderDelivery.OrderRecords, orderDelivery.DeliveryNo)
			global.GVA_LOG.Debug("orderDelivery", zap.Any("orderDelivery", orderDelivery))
			global.GVA_LOG.Debug("ormap", zap.Any("ormap", ormap))
			var bigWarehouseString string
			if orderDelivery.BigWarehouseOrder != nil && orderDelivery.BigWarehouseOrder.BigWarehouse != nil {
				bigWarehouseString = orderDelivery.BigWarehouseOrder.BigWarehouse.Name + "/" + orderDelivery.BigWarehouseOrder.BigWarehouse.Code
			} else {
				bigWarehouseString = "-"
			}

			row := []interface{}{
				orderDelivery.DeliveryNo,                       // 发货单号
				orderDelivery.Status,                           // 状态
				orderDelivery.OrderNo,                          // 订单号
				orderDelivery.PaymentType,                      // 支付方式
				orderDelivery.FranchiseeName,                   // 加盟商名称
				orderDelivery.FranchiseeAccount,                // 加盟商账号
				productDetail.ProductName,                      // 商品名称
				productDetail.SpecailMallName,                  // 专区名称
				productDetail.CombinationProductDetailString(), // 组合商品明细
				productDetail.PurchaseQuantity,                 // 购买数量
				orderDelivery.GoodsDetail.GetGoodsNumByGoodsId(productDetail.GoodsID), // 发货件数
				orderDelivery.AddressSnapshot.Consignee,                               // 收货人
				orderDelivery.AddressSnapshot.Tel,                                     // 收货电话
				fmt.Sprintf("%s %s %s", orderDelivery.AddressSnapshot.Province, orderDelivery.AddressSnapshot.City, orderDelivery.AddressSnapshot.Address), // 收货地址
				orderDelivery.Remark,                             // 备注
				utils.TimeStringOrZero(&orderDelivery.OrderTime), // 下单时间
				utils.ReturnFirstOrSecond(utils.PrintOrderRecordTime(ormap, types.OrderStocking), utils.TimeStringOrZero(&orderDelivery.StockingTime)), // 备货时间
				utils.ReturnFirstOrSecond(utils.PrintOrderRecordTime(ormap, types.OrderDelivery), orderDeliveryTime),                                   // 发货时间
				logisticsInfo,             // 物流信息
				orderDelivery.LogisticsNo, // 物流单号
				bigWarehouseString,        // 仓库
			}
			if err := f.SetSheetRow(sheetName, fmt.Sprintf("A%d", line), &row); err != nil {
				return nil, err
			}
			line++
		}
	}
	// 列宽度
	if err := f.SetColWidth(sheetName, "A", "N", 20); err != nil {
		global.GVA_LOG.Error("SetColWidth err!", zap.Error(err))
		return nil, err
	}
	return f, nil
}
