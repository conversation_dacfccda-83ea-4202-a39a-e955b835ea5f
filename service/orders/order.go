package orders

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/OSQianXing/guanpu-server/service/bigwarehouse"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
	"gorm.io/gorm/clause"

	"github.com/OSQianXing/guanpu-server/dao"
	"github.com/OSQianXing/guanpu-server/model/account"
	"github.com/OSQianXing/guanpu-server/model/warehouse"

	"go.uber.org/zap"

	"strings"

	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/model/app"
	commonModel "github.com/OSQianXing/guanpu-server/model/common"
	"github.com/OSQianXing/guanpu-server/model/common/request"
	"github.com/OSQianXing/guanpu-server/model/franchisees"
	"github.com/OSQianXing/guanpu-server/model/orders"
	ordersReq "github.com/OSQianXing/guanpu-server/model/orders/request"
	"github.com/OSQianXing/guanpu-server/model/orders/response"
	payModel "github.com/OSQianXing/guanpu-server/model/pay"
	payResp "github.com/OSQianXing/guanpu-server/model/pay/response"
	"github.com/OSQianXing/guanpu-server/model/products"
	"github.com/OSQianXing/guanpu-server/service/common"
	payService "github.com/OSQianXing/guanpu-server/service/pay"
	"github.com/OSQianXing/guanpu-server/types"
	"github.com/OSQianXing/guanpu-server/utils"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

type OrderService struct {
}

var bigWarehouseService = &bigwarehouse.WarehouseService{}

type accountConsume struct {
	AccountID uint
	Amount    int
	SmID      int
}

// CreateOrder 创建Order记录

func (orderService *OrderService) CreateOrder(orderReq *ordersReq.CreateOrderRequest, operatorCreateID uint, userIp string) (orderNo string, err error) {

	var (
		f                        franchisees.Franchisee
		rc                       = make([]*franchisees.RechargeRecord, 0)
		order                    *orders.Order
		cw                       *warehouse.CloudWarehouse
		orderGoods               = make([]*orders.OrderGoods, 0, len(orderReq.Products))
		cloudRecords             = make([]*warehouse.CloudWarehouseRecord, 0, len(orderReq.Products))
		subcws                   = make([]*warehouse.SubCloudWarehouse, 0, len(orderReq.Products))
		totalBalance             int                             // 消费总金额
		totalPoints              int                             // 消费总积分
		totalRebate              int                             // 总返利积分
		productRebateMap         = make(map[uint]int)            // 商品返利积分
		productCombinationMap    = make(map[uint]bool)           // 是否组合
		productAccountConsumeMap = make(map[uint]accountConsume) // 商品对应加盟商专项账户扣款金额
		accountConsumeMap        = make(map[uint]accountConsume) // 加盟商专项账户扣款金额
		accountBalanceMap        = make(map[uint]int)            // 加盟商专项账户余额
		generalAccountConsume    int
		orderGoodsStatus         types.OrderGoodsStatus = types.AwaitingDelivery // 订单商品状态
	)
	// 校验一下赠品
	// 赠品规则
	err = orderService.checkGift(orderReq)
	if err != nil {
		return "", err
	}
	global.GVA_LOG.Info("orderReq", zap.Any("orderReq", orderReq))
	if orderReq.PaymentType == types.ZeroPayOrder && orderReq.OrderType == types.ZeroOrder {
		// 零元订单
		global.GVA_LOG.Info("zero pay order", zap.Any("orderReq", orderReq))
	} else if (orderReq.PaymentType == types.CloudPickUp && orderReq.OrderSubType != types.PickUp) || (orderReq.PaymentType != types.CloudPickUp && orderReq.OrderSubType == types.PickUp) {
		return "", errors.New("订单类型异常")
	}

	// 创建订单
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if orderReq.PaymentType != types.PaymentTypeNone && orderReq.PaymentType != types.PaymentTypeOnlinePay {
			// create order_online_pay record for paying check

		}
		if orderReq.PaymentType != types.PaymentTypeOnlinePay && orderReq.PaymentType != types.PaymentTypeNone {
			if err := tx.Set("gorm:query_option", "FOR UPDATE").
				Where("id = ?", orderReq.FranchiseeId).
				First(&f).Error; err != nil {
				return errors.Join(err, errors.New("Get franchisee and lock err: "+strconv.Itoa(int(orderReq.FranchiseeId))))
			}
		} else {
			// just for online pay
			if err := tx.Where("id = ?", orderReq.FranchiseeId).First(&f).Error; err != nil {
				return errors.Join(err, errors.New("Get franchisee err : "+strconv.Itoa(int(orderReq.FranchiseeId))))
			}
		}

		// 标记通用账户余额，用于校验余额下单时，余额是否足够
		generalAccountBalance := f.Balance

		generalAccountBalance, generalAccountConsume, totalBalance, totalPoints, totalRebate, err2, done := orderService.computeOrderProductsConsume(tx, orderReq, f, productCombinationMap, err, accountBalanceMap, generalAccountBalance, productAccountConsumeMap, generalAccountConsume, totalBalance, totalPoints, productRebateMap, totalRebate)
		if done {
			return err2
		}

		if (orderReq.PaymentType != types.ZeroPayOrder) && (orderReq.PaymentType != types.InitFranchiseePresent) && (orderReq.PaymentType != types.PaymentTypeOnlinePay) && (orderReq.PaymentType != types.PaymentTypeNone) && (orderReq.PaymentType != types.CloudPickUp) {
			// 积分下单金额校验
			if orderReq.OrderSubType != types.PickUp && totalPoints > 0 && totalPoints > f.Points {
				return fmt.Errorf("积分不足，请充值后下单")
			}

			if orderReq.OrderSubType != types.PickUp && generalAccountBalance < 0 {
				return fmt.Errorf("余额不足，请充值后下单")
			}
		}
		var orderStatus types.OrderStatus
		if orderReq.PaymentType == types.PaymentTypeOnlinePay || orderReq.PaymentType == types.PaymentTypeNone {
			orderStatus = types.OrderStatusAwaitingPayment
		} else {
			orderStatus = types.OrderAwaitingDelivery
		}

		// 创建order
		var fa app.FranchiseeAddress
		err = global.GVA_DB.Where("id = ?", orderReq.AddressId).First(&fa).Error
		if err != nil && !utils.ErrorIsDBNotFound(err) {
			return err
		}

		if fa.FranchiseeId != int(orderReq.FranchiseeId) {
			return fmt.Errorf("address not found")
		}

		order = &orders.Order{
			TenantID:        orderReq.TenantID,
			OrderNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
			FranchiseeId:    int(orderReq.FranchiseeId),
			AddressSnapshot: &fa,
			PaymentType:     orderReq.PaymentType,
			OrderType:       orderReq.OrderType,
			OrderSubType:    orderReq.OrderSubType,
			Remark:          orderReq.Remark,
			Status:          orderStatus, // TODO: mark as orderStatusWaitingPayment if paymentType = types.PaymentTypeOnlyPay
			Amount:          totalBalance,
			Points:          totalPoints,
			ShipmentType:    orderReq.ShipmentType,

			Franchisee: nil,
		}
		orderNo = order.OrderNo

		// 换货单逻辑
		if orderReq.ReturnOrderNos != nil {
			order.ReturnOrderNos = orderReq.ReturnOrderNos

			// 反向关联退货单
			for _, returnNo := range *orderReq.ReturnOrderNos {
				var returnOrder orders.OrderReturn
				if err = tx.Where("return_no = ?", returnNo).First(&returnOrder).Error; err != nil {
					return fmt.Errorf("return order not found, err:=%w", err)
				}

				returnOrder.ExchangeOrderNo = order.OrderNo
				if err = tx.Save(&returnOrder).Error; err != nil {
					return fmt.Errorf("return order update failed, err:=%w", err)
				}
			}
			order.Status = types.OrderAwaitingDelivery
		}

		// 压货单逻辑
		if orderReq.OrderSubType == types.Presentation {
			if orderReq.PaymentType != types.PaymentTypeOnlinePay && orderReq.PaymentType != types.PaymentTypeNone { // 如果是压货订单且不是在线支付，需要创建CloudWarehouse，对于在线支付压货单，要支付后创建CloudWarehouse
				cw = &warehouse.CloudWarehouse{
					FranchiseeId: int(orderReq.FranchiseeId),
					OrderNo:      order.OrderNo,
				}
				if err = tx.Create(&cw).Error; err != nil {
					return fmt.Errorf("CloudWarehouse create failed, err:=%w", err)
				}
			}
		}
		if order.PaymentType == types.PaymentTypeOnlinePay || order.PaymentType == types.PaymentTypeNone {
			order.Status = types.OrderStatusAwaitingPayment
		} else {
			if orderReq.OrderSubType == types.Presentation {
				order.Status = types.OrderCompleted
			} else {
				order.Status = types.OrderAwaitingDelivery
			}
		}

		if err = tx.Create(&order).Error; err != nil {
			return fmt.Errorf("order create failed, err:=%w", err)
		}
		orderReqJson, _ := json.Marshal(orders.OrderRecordExtra{
			Req:    orderReq,
			Result: order,
			Stage:  order.Status.StringEn(),
		})

		if err = tx.Create(&orders.OrderRecord{
			OrderID:     order.ID,
			OrderNo:     order.OrderNo,
			OrderStatus: order.Status,
			OperatorID:  operatorCreateID,
			Extra:       orderReqJson,
		}).Error; err != nil {
			return errors.Join(err, errors.New("创建订单操作记录失败)"))
		}
		if orderReq.PaymentType == types.PaymentTypeOnlinePay {
			payService := &payService.OnlinePayService{}
			if _, err = payService.CreateOrderOnlinePayWithinTx(tx, order.OrderNo, types.OnlinePayChannelNone, types.OnlinePayTypeNone, uint(order.Amount), uint(totalRebate), operatorCreateID, userIp, orderReqJson); err != nil {
				return errors.Join(err, errors.New("create order online pay error"))
			}
			orderGoodsStatus = types.OrderGoodsStatusAwaitingPayment
		} else {
			orderGoodsStatus = types.AwaitingDelivery
		}

		// 创建goods
		for _, product := range orderReq.Products {
			var orderGood *orders.OrderGoods
			if orderReq.PaymentType == types.ZeroPayOrder || orderReq.PaymentType == types.InitFranchiseePresent { // 如果是零元订单, same as not zero pay order
				orderGood = &orders.OrderGoods{
					OrderId:        order.ID,
					GoodNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					ProductId:      int(product.ProductId),
					UnitPrice:      product.UnitPrice,
					Quantity:       product.Quantity,
					PointsPrice:    product.PointsPrice,
					PointsQuantity: product.PointsQuantity,
					Status:         orderGoodsStatus,
					Rebate:         0,
					IsCombination:  productCombinationMap[product.ProductId],
					AccountType:    0,
					AccountId:      0,
					AccountPayed:   0,
					SpecialMallID:  0,
				}
			} else {
				accountType := types.GeneralAccount
				if orderReq.PaymentType == types.PointsBalance {
					productRebateMap[product.ProductId] = 0
					productAccountConsumeMap[product.ProductId] = accountConsume{
						AccountID: 0,
						Amount:    product.PointsPrice * product.PointsQuantity,
						SmID:      0,
					}
				}
				if productAccountConsumeMap[product.ProductId].AccountID > 0 {
					accountType = types.ExclusiveAccount
				}
				if orderReq.PaymentType == types.PaymentTypeOnlinePay {
					accountType = types.AccountTypeOnlinePay
				}
				orderGood = &orders.OrderGoods{
					OrderId:        order.ID,
					GoodNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					ProductId:      int(product.ProductId),
					UnitPrice:      product.UnitPrice,
					Quantity:       product.Quantity,
					PointsPrice:    product.PointsPrice,
					PointsQuantity: product.PointsQuantity,
					Status:         orderGoodsStatus,
					Rebate:         productRebateMap[product.ProductId],
					IsCombination:  productCombinationMap[product.ProductId],
					AccountType:    accountType,
					AccountId:      productAccountConsumeMap[product.ProductId].AccountID,
					AccountPayed:   productAccountConsumeMap[product.ProductId].Amount,
					SpecialMallID:  productAccountConsumeMap[product.ProductId].SmID,
				}

			}
			orderGoods = append(orderGoods, orderGood)

			// 提货单逻辑
			if orderReq.OrderSubType == types.PickUp {
				if product.CloudBatchOrderNo == nil {
					return fmt.Errorf("提货单商品批次单号不能为空")
				}

				// 查询云仓商品现有库存
				var cloudWarehouse warehouse.CloudWarehouse
				if err = tx.Where("franchisee_id = ? AND order_no = ?", orderReq.FranchiseeId, product.CloudBatchOrderNo).First(&cloudWarehouse).Error; err != nil {
					return fmt.Errorf("提货单商品批次单号异常, err:=%w", err)
				}

				// 查询子云仓库存
				var subCloudWarehouse warehouse.SubCloudWarehouse
				if err = tx.Set("gorm:query_option", "FOR UPDATE").Where("cloud_warehouse_id = ? AND product_id = ? and inventory >= ?", cloudWarehouse.ID, product.ProductId, product.Quantity).First(&subCloudWarehouse).Error; err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						return fmt.Errorf("提货单商品库存不足")
					}
					return fmt.Errorf("提货单商品异常, err:=%w", err)
				}

				if subCloudWarehouse.Inventory < product.Quantity {
					return fmt.Errorf("该云仓商品库存不足")
				}

				var q int
				if product.Quantity > 0 {
					q = product.Quantity
				} else {
					q = product.PointsQuantity
				}

				cloudRecord := &warehouse.CloudWarehouseRecord{
					FranchiseeId:     int(orderReq.FranchiseeId),
					CloudWarehouseId: cloudWarehouse.ID,
					Type:             types.OutCloud,
					ProductId:        int(product.ProductId),
					Inventory:        subCloudWarehouse.Inventory - q,
					Pieces:           q,
					TradeNo:          order.OrderNo,
					Remark:           orderReq.Remark,
					OperatorID:       operatorCreateID,
				}
				cloudRecords = append(cloudRecords, cloudRecord)

				// 减少库存
				if err = tx.Model(&warehouse.SubCloudWarehouse{}).Where("id =?", subCloudWarehouse.ID).Updates(map[string]interface{}{
					"inventory": gorm.Expr("inventory - ?", q),
				}).Error; err != nil {
					return fmt.Errorf("SubCloudWarehouse pickup failed, err:=%w", err)
				}
			}

			// 压货单逻辑
			if orderReq.OrderSubType == types.Presentation && (orderReq.PaymentType != types.PaymentTypeOnlinePay && orderReq.PaymentType != types.PaymentTypeNone) {
				scw := &warehouse.SubCloudWarehouse{
					CloudWarehouseId: cw.ID,
					ProductId:        int(product.ProductId),
				}

				if product.Quantity > 0 {
					scw.Inventory = product.Quantity
					scw.UnitPrice = product.UnitPrice
				} else {
					scw.Inventory = product.PointsQuantity
					scw.PointsPrice = product.PointsPrice
				}

				cloudRecord := &warehouse.CloudWarehouseRecord{
					FranchiseeId:     int(orderReq.FranchiseeId),
					CloudWarehouseId: cw.ID,
					Type:             types.InCloud,
					ProductId:        int(product.ProductId),
					Inventory:        scw.Inventory,
					Pieces:           scw.Inventory,
					TradeNo:          order.OrderNo,
					Remark:           orderReq.Remark,
					OperatorID:       operatorCreateID,
				}
				cloudRecords = append(cloudRecords, cloudRecord)
				subcws = append(subcws, scw)
			}

			if order.OrderType == types.Franchisee {
				// 删除购物车 userID
				err = orderService.CleanCart(orderReq.FranchiseeId, product.ProductId, tx)
				if err != nil {
					return err
				}
			}
		}

		for _, gift := range orderReq.Gifts {
			if gift.Quantity == 0 {
				return fmt.Errorf("gift quantity error")
			}
			if orderReq.PaymentType == types.ZeroPayOrder || orderReq.PaymentType == types.InitFranchiseePresent { // 如果是零元订单, same as not zero pay order
				orderGoods = append(orderGoods, &orders.OrderGoods{
					OrderId:        order.ID,
					GoodNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					ProductId:      int(gift.ProductId),
					UnitPrice:      0,
					Quantity:       gift.Quantity,
					PointsPrice:    0,
					PointsQuantity: 0,
					Rebate:         0,
					Status:         types.AwaitingDelivery,
					IsGift:         true,
				})
			} else {
				if orderReq.PaymentType == types.PaymentTypeOnlinePay || orderReq.PaymentType == types.PaymentTypeNone {
					orderGoodsStatus = types.OrderGoodsStatusAwaitingPayment
				} else {
					orderGoodsStatus = types.AwaitingDelivery
				}
				orderGoods = append(orderGoods, &orders.OrderGoods{
					OrderId:        order.ID,
					GoodNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					ProductId:      int(gift.ProductId),
					UnitPrice:      0,
					Quantity:       gift.Quantity,
					PointsPrice:    0,
					PointsQuantity: 0,
					Rebate:         0,
					Status:         orderGoodsStatus,
					IsGift:         true,
				})
			}

			// 压货单逻辑
			if orderReq.OrderSubType == types.Presentation && (orderReq.PaymentType != types.PaymentTypeOnlinePay && orderReq.PaymentType != types.PaymentTypeNone) {
				scw := &warehouse.SubCloudWarehouse{
					CloudWarehouseId: cw.ID,
					ProductId:        int(gift.ProductId),
					Inventory:        gift.Quantity,
					UnitPrice:        0,
					PointsPrice:      0,
				}

				cloudRecord := &warehouse.CloudWarehouseRecord{
					FranchiseeId:     int(orderReq.FranchiseeId),
					CloudWarehouseId: cw.ID,
					Type:             types.InCloud,
					ProductId:        int(gift.ProductId),
					Inventory:        gift.Quantity,
					Pieces:           gift.Quantity,
					TradeNo:          order.OrderNo,
					Remark:           orderReq.Remark,
					OperatorID:       operatorCreateID,
				}
				cloudRecords = append(cloudRecords, cloudRecord)
				subcws = append(subcws, scw)
			}

			if order.OrderType == types.Franchisee {
				// 删除购物车
				err = orderService.CleanCart(orderReq.FranchiseeId, gift.ProductId, tx)
				if err != nil {
					return err
				}
			}
		}

		if err = tx.Create(&orderGoods).Error; err != nil {
			return fmt.Errorf("orderGoods create failed, err:=%w", err)
		}

		if len(cloudRecords) > 0 {
			if err = tx.Create(&cloudRecords).Error; err != nil {
				return fmt.Errorf("cloudRecords create failed, err:=%w", err)
			}
		}

		if len(subcws) > 0 {
			if err = tx.Create(&subcws).Error; err != nil {
				return fmt.Errorf("subcws create failed, err:=%w", err)
			}
		}

		// 线下支付或者提货订单不操作金额变更,零元订单也不需要支付，不需要操作资金变更
		if orderReq.PaymentType == types.OfflineReceipt || orderReq.PaymentType == types.CloudPickUp || orderReq.PaymentType == types.ZeroPayOrder || orderReq.PaymentType == types.InitFranchiseePresent || orderReq.PaymentType == types.PaymentTypeOnlinePay || orderReq.PaymentType == types.PaymentTypeNone {
			return nil
		} else if orderReq.PaymentType == types.AccountBalance || orderReq.PaymentType == types.PointsBalance {
			// 创建余额变更记录
			if totalPoints > 0 {
				rc = append(rc, &franchisees.RechargeRecord{
					FranchiseeId: orderReq.FranchiseeId,
					RechargeType: utils.RechargeTypePtr(types.RechargePoints),
					OperateType:  utils.OperateTypePtr(types.MallConsume),
					AccountType:  utils.AccountTypePtr(types.GeneralAccount),
					AccountID:    utils.UintPtr(0),
					Amount:       totalPoints,
					Balance:      f.Points - totalPoints,
					SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					TradeNo:      order.OrderNo,
					OperatorID:   operatorCreateID,
				})

				if err = tx.Model(&franchisees.Franchisee{}).Where("id =?", orderReq.FranchiseeId).Updates(map[string]interface{}{
					"points": gorm.Expr("points - ?", totalPoints),
				}).Error; err != nil {
					return fmt.Errorf("franchisee consume failed, err:=%w", err)
				}
			}

			if totalBalance > 0 {
				if generalAccountConsume > 0 {
					rc = append(rc, &franchisees.RechargeRecord{
						FranchiseeId: orderReq.FranchiseeId,
						RechargeType: utils.RechargeTypePtr(types.RechargeAmount),
						OperateType:  utils.OperateTypePtr(types.MallConsume),    // types.MallConsume,
						AccountType:  utils.AccountTypePtr(types.GeneralAccount), //types.GeneralAccount,
						AccountID:    utils.UintPtr(0),
						Amount:       generalAccountConsume,
						Balance:      f.Balance - generalAccountConsume,
						SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
						TradeNo:      order.OrderNo,
						OperatorID:   operatorCreateID,
					})

					if err = tx.Model(&franchisees.Franchisee{}).Where("id =?", orderReq.FranchiseeId).Updates(map[string]interface{}{
						"balance": gorm.Expr("balance - ?", generalAccountConsume),
					}).Error; err != nil {
						return fmt.Errorf("franchisee consume failed, err:=%w", err)
					}
				}

				// 记录时相同加盟商专户的消费金额合并
				for _, consume := range productAccountConsumeMap {
					if consume.AccountID == 0 {
						continue
					}

					if _, ok := accountConsumeMap[consume.AccountID]; ok {
						amount := accountConsumeMap[consume.AccountID].Amount + consume.Amount
						accountConsumeMap[consume.AccountID] = accountConsume{
							AccountID: consume.AccountID,
							Amount:    amount,
						}
					} else {
						accountConsumeMap[consume.AccountID] = accountConsume{
							AccountID: consume.AccountID,
							Amount:    consume.Amount,
						}
					}
				}

				for _, consume := range accountConsumeMap {
					if consume.Amount == 0 {
						continue
					}
					rc = append(rc, &franchisees.RechargeRecord{
						FranchiseeId: orderReq.FranchiseeId,
						RechargeType: utils.RechargeTypePtr(types.RechargeAmount),  //types.RechargeAmount,
						OperateType:  utils.OperateTypePtr(types.MallConsume),      //types.MallConsume,
						AccountType:  utils.AccountTypePtr(types.ExclusiveAccount), //types.ExclusiveAccount,
						AccountID:    &consume.AccountID,
						Amount:       consume.Amount,
						Balance:      accountBalanceMap[consume.AccountID],
						SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
						TradeNo:      order.OrderNo,
						OperatorID:   operatorCreateID,
					})

					if err = tx.Model(&franchisees.FranchiseeAccount{}).Where("id =?", consume.AccountID).Updates(map[string]interface{}{
						"balance": gorm.Expr("balance - ?", consume.Amount),
					}).Error; err != nil {
						return fmt.Errorf("franchiseeAccount consume failed, err:=%w", err)
					}
				}
			}

			if totalRebate > 0 {
				rc = append(rc, &franchisees.RechargeRecord{
					FranchiseeId: orderReq.FranchiseeId,
					RechargeType: utils.RechargeTypePtr(types.RechargePoints),
					OperateType:  utils.OperateTypePtr(types.OrderRebate),
					AccountType:  utils.AccountTypePtr(types.GeneralAccount),
					AccountID:    utils.UintPtr(0),
					Amount:       totalRebate,
					Balance:      f.Points + totalRebate,
					SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					TradeNo:      order.OrderNo,
					OperatorID:   operatorCreateID,
				})

				if err = tx.Model(&franchisees.Franchisee{}).Where("id =?", orderReq.FranchiseeId).Updates(map[string]interface{}{
					"points": gorm.Expr("points + ?", totalRebate),
				}).Error; err != nil {
					return fmt.Errorf("franchisee consume failed, err:=%w", err)
				}
			}

			if len(rc) == 0 {
				return nil
			}
			if err = tx.Create(&rc).Error; err != nil {
				return fmt.Errorf("consume rechargeRecord failed, err:=%w", err)
			}
			// Send order status change notification
			if err = utils.EnqueueOrderStatusChange(order.OrderNo, order.ID, 0, int8(order.Status), operatorCreateID, "Order created"); err != nil {
				global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
			}
		}

		return nil
	})
	//  非在线支付订单，且已经支付 或 无需支付，分配订单至指定库房
	if err == nil && (order.PaymentType != types.PaymentTypeOnlinePay && order.PaymentType != types.PaymentTypeNone) && (order.OrderSubType != types.Presentation) {
		// 分配订单至指定库房
		warehouseService := &bigwarehouse.WarehouseService{}
		re, err := warehouseService.DispatchOrderToBigWarehouse([]uint{order.ID})
		if err != nil {
			global.GVA_LOG.Error("service.OrderService.CreateOrder.DispatchOrderToBigWarehouse err:", zap.Error(err), zap.Any("orderId", order.ID))
			//return err
		} else {
			global.GVA_LOG.Debug("service.OrderService.CreateOrder.DispatchOrderToBigWarehouse re:", zap.Any("re", re))
		}

	}

	if err == nil {
		return order.OrderNo, nil
	}

	return "", err
}

func (orderService *OrderService) computeOrderProductsConsume(tx *gorm.DB, orderReq *ordersReq.CreateOrderRequest, f franchisees.Franchisee, productCombinationMap map[uint]bool, err error, accountBalanceMap map[uint]int, generalAccountBalance int, productAccountConsumeMap map[uint]accountConsume, generalAccountConsume int, totalBalance int, totalPoints int, productRebateMap map[uint]int, totalRebate int) (int, int, int, int, int, error, bool) {
	// 下单商品校验及数据准备
	for _, v := range orderReq.Products {
		if v.Quantity == 0 && v.PointsQuantity == 0 {
			return 0, 0, 0, 0, 0, fmt.Errorf("product quantity failed"), true
		}

		var p products.Product
		if err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id =?", v.ProductId).First(&p).Error; err != nil {
			return 0, 0, 0, 0, 0, err, true
		}

		// 检验 商品 是否是上架可售卖状态
		if !p.OnSale && orderReq.OrderType == types.Franchisee && orderReq.OrderSubType != types.PickUp {
			return 0, 0, 0, 0, 0, fmt.Errorf("商品已下架：" + p.Name), true
		}

		//  校验 商品 是否 与老客户 兼容
		global.GVA_LOG.Debug("service.order.CreateOrder", zap.Any("p", p), zap.Any("f", f), zap.Any("orderReq", orderReq))
		if (*p.OldFranchiseeCanBuy == false) && f.IsOldFranchisee && (orderReq.PaymentType != types.ZeroPayOrder) {
			return 0, 0, 0, 0, 0, fmt.Errorf("当前用户不能购买该产品"), true
		}

		// 校验当支付方式为积分支付时，限制只能积分兑换
		if orderReq.PaymentType == types.PointsBalance && v.Quantity > 0 {
			return 0, 0, 0, 0, 0, fmt.Errorf("payment_type abnormal"), true
		}

		// 校验当支付方式为余额支付时，限制只能余额支付
		if (orderReq.PaymentType == types.AccountBalance || orderReq.PaymentType == types.PaymentTypeOnlinePay) && (v.Quantity <= 0 || v.UnitPrice <= 0) {
			return 0, 0, 0, 0, 0, fmt.Errorf("payment_type abnormal"), true
		}

		global.GVA_LOG.Debug("orderReq", zap.Any("orderReq", orderReq),
			zap.Any("v", v),
			zap.Any("p", p),
			zap.Any("cost", p.Cost),
			zap.Any("pointsPrice", p.ExchangePrice),
			zap.Any("pointsQuantity", v.PointsQuantity),
			zap.Any("unitPrice", v.UnitPrice),
			zap.Any("quantity", v.Quantity),
			zap.Any("pointsQuantity", v.PointsQuantity),
			zap.Any("pointsPrice", v.PointsPrice),
			zap.Any("unitPrice", v.UnitPrice),
			zap.Any("quantity", v.Quantity),
			zap.Any("pointsQuantity", v.PointsQuantity),
			zap.Any("pointsPrice", v.PointsPrice),
			zap.Any("orderReq.OrderSubType != types.PickUp && *orderReq.PaymentType != types.ZeroPayOrder",
				orderReq.OrderSubType != types.PickUp && orderReq.PaymentType != types.ZeroPayOrder),
			zap.Any("v.Quantity > 0 && (v.UnitPrice != *p.Cost || v.UnitPrice <= 0)",
				v.Quantity > 0 && (v.UnitPrice != *p.Cost || v.UnitPrice <= 0)),
			zap.Any("v.PointsQuantity > 0 && (v.PointsPrice != *p.ExchangePrice || v.PointsPrice <= 0)", v.PointsQuantity > 0 && (v.PointsPrice != *p.ExchangePrice || v.PointsPrice <= 0)),
		)
		// 校验单价是否正确,如果非提货订单或非零元订单，校验单价是否正确，并正确匹配支付类型以及订单类型、订单子类型的匹配关系
		if (orderReq.PaymentType != types.ZeroPayOrder) && (orderReq.PaymentType != types.InitFranchiseePresent) {
			if orderReq.OrderSubType != types.PickUp && (((orderReq.PaymentType == types.AccountBalance || orderReq.PaymentType == types.PaymentTypeOnlinePay || orderReq.PaymentType == types.PaymentTypeNone) && (v.Quantity <= 0 || v.UnitPrice != *p.Cost || v.UnitPrice <= 0)) || ((orderReq.PaymentType == types.PointsBalance) && (v.PointsQuantity <= 0 || v.PointsPrice != *p.ExchangePrice || v.PointsPrice <= 0))) {
				return 0, 0, 0, 0, 0, fmt.Errorf("product price abnormal"), true
			}
			// 校验商品支持的加盟商分类
			if (orderReq.PaymentType != types.CloudPickUp || orderReq.OrderSubType != types.PickUp) && !p.IsContainsFranchiseeCategory(*f.FCategoryId) {
				return 0, 0, 0, 0, 0, fmt.Errorf("存在当前加盟商分类不支持的商品: %s", p.Name), true
			}
		}

		// 商品是否组合
		productCombinationMap[p.ID] = *p.IsCombination

		if (orderReq.PaymentType != types.ZeroPayOrder) && (orderReq.PaymentType != types.InitFranchiseePresent) && (orderReq.PaymentType != types.PaymentTypeOnlinePay) { // 如果不是零元订单,也不是加盟赠送订单,也不是在线支付，
			// 商品属于某专区时，需查询加盟商是否专区对应的专属账户，有则标记加盟商专属账户余额
			if p.SpecialMallID > 0 && v.Quantity > 0 {
				var sm products.SpecialMall
				if err := tx.Where("id = ? and enable = 1", p.SpecialMallID).First(&sm).Error; err != nil {
					return 0, 0, 0, 0, 0, err, true
				}

				// 查询校验专属账户是否被禁用
				var ea account.ExclusiveAccount
				if err := tx.Where("id = ? ", sm.WalletID).First(&ea).Error; err != nil {
					return 0, 0, 0, 0, 0, err, true
				}

				// 根据sm.WalletID和加盟商ID 查询加盟商专属账户(未查到则依然扣除通用账户的钱)
				var fa franchisees.FranchiseeAccount
				if err := tx.Where("is_banned = 0 and franchisee_id = ? AND exclusive_account_id = ?", f.ID, sm.WalletID).First(&fa).Error; err != nil && !utils.ErrorIsDBNotFound(err) {
					return 0, 0, 0, 0, 0, err, true
				}

				if err == nil && !ea.IsBanned {
					// 需标记加盟商专属账户余额，每次计算时使用
					if _, ok := accountBalanceMap[fa.ID]; !ok {
						accountBalanceMap[fa.ID] = fa.Balance
					}

					if accountBalanceMap[fa.ID] > 0 {
						// goods金额大于专属账户金额+通用账户剩余金额 则余额不足
						if v.UnitPrice*v.Quantity-(accountBalanceMap[fa.ID]+generalAccountBalance) > 0 {
							return 0, 0, 0, 0, 0, fmt.Errorf("余额不足，请充值后下单"), true
						} else if v.UnitPrice*v.Quantity-accountBalanceMap[fa.ID] > 0 { // goods金额大于专属账户金额 但通用账户够支付，通用账户金额减去差值用于后续遍历计算
							// 此时使用全部加盟商专户余额，剩余的钱用通户支付
							generalAccountBalance -= v.UnitPrice*v.Quantity - accountBalanceMap[fa.ID]
							productAccountConsumeMap[v.ProductId] = accountConsume{
								AccountID: fa.ID,
								Amount:    accountBalanceMap[fa.ID],
								SmID:      p.SpecialMallID,
							}
							generalAccountConsume += v.UnitPrice*v.Quantity - accountBalanceMap[fa.ID]
							accountBalanceMap[fa.ID] = 0
						} else {
							// 此时专户钱够付全部goods
							productAccountConsumeMap[v.ProductId] = accountConsume{
								AccountID: fa.ID,
								Amount:    v.UnitPrice * v.Quantity,
								SmID:      p.SpecialMallID,
							}

							accountBalanceMap[fa.ID] = accountBalanceMap[fa.ID] - v.UnitPrice*v.Quantity
						}
					} else {
						// 专属账户剩余金额0，使用通用账户金额
						generalAccountBalance -= v.UnitPrice * v.Quantity
						generalAccountConsume += v.UnitPrice * v.Quantity
						productAccountConsumeMap[v.ProductId] = accountConsume{SmID: p.SpecialMallID}
					}
				} else {
					// 未查询到专属账户，使用通用账户金额
					generalAccountBalance -= v.UnitPrice * v.Quantity
					generalAccountConsume += v.UnitPrice * v.Quantity
					productAccountConsumeMap[v.ProductId] = accountConsume{SmID: p.SpecialMallID}
				}
			} else if p.SpecialMallID == 0 && v.Quantity > 0 {
				// 商品不属于专属账户，使用通用账户金额
				generalAccountBalance -= v.UnitPrice * v.Quantity
				generalAccountConsume += v.UnitPrice * v.Quantity
				productAccountConsumeMap[v.ProductId] = accountConsume{SmID: p.SpecialMallID}
			}
		} else if orderReq.PaymentType == types.PaymentTypeOnlinePay {
			// generalAccountBalance -= v.UnitPrice * v.Quantity
			// generalAccountConsume += v.UnitPrice * v.Quantity
			productAccountConsumeMap[v.ProductId] = accountConsume{
				AccountID: 0,
				Amount:    0,
				SmID:      p.SpecialMallID,
			}
		}

		// 计算本次下单商品总金额
		if (orderReq.PaymentType != types.ZeroPayOrder) && (orderReq.PaymentType != types.InitFranchiseePresent) {
			totalBalance += v.UnitPrice * v.Quantity
			totalPoints += v.PointsPrice * v.PointsQuantity
		} else {
			totalBalance += 0
			totalPoints += 0
		}

		if (orderReq.PaymentType != types.ZeroPayOrder) && (orderReq.PaymentType != types.InitFranchiseePresent) { // 如果是零元订单
			// 标记商品返利积分
			if v.Quantity > 0 {
				productRebateMap[v.ProductId] = *p.Rebate
				totalRebate += *p.Rebate * v.Quantity
			} else {
				productRebateMap[v.ProductId] = 0
			}
		}
	}
	return generalAccountBalance, generalAccountConsume, totalBalance, totalPoints, totalRebate, nil, false
}

func (orderService *OrderService) checkGift(orderReq *ordersReq.CreateOrderRequest) error {
	if len(orderReq.Gifts) == 0 {
		return nil
	}
	var freeGiftMap = make(map[int]*commonModel.FreeGift)
	for _, product := range orderReq.Products {
		quantity := product.Quantity
		if product.PointsQuantity > 0 {
			quantity = product.PointsQuantity
		}
		freeGift, err := common.ProductGiftRuleServiceApp.GetGifts(int(product.ProductId), quantity, orderReq.PaymentType.ToProductType())
		if err != nil {
			return err
		}
		if freeGift == nil {
			continue
		}
		if _, ok := freeGiftMap[freeGift.ProductID]; ok {
			freeGiftMap[freeGift.ProductID].Quantity += freeGift.Quantity
		} else {
			freeGiftMap[freeGift.ProductID] = freeGift
		}
	}
	var reqGifts = make(map[int]int, 0)
	for _, gift := range orderReq.Gifts {
		reqGifts[int(gift.ProductId)] += gift.Quantity
	}
	for productID, quantity := range reqGifts {
		f, ok := freeGiftMap[productID]
		if !ok {
			return fmt.Errorf("gift  is illegal")
		}
		if f.Quantity < quantity {
			return fmt.Errorf("赠品数量超限")
		}
	}
	return nil
}

// CreateOnlinePayOrder 创建在线支付订单

// func (orderService *OrderService) CreateOnlinePayOrder(orderReq ordersReq.CreateOrderRequest, operatorCreateID uint, userIp string) (orderNo string, err error) {
// 	var (
// 		f  franchisees.Franchisee
// 		ff []franchisees.Franchisee
// 	)
// 	if orderReq.PaymentType != types.PaymentTypeOnlinePay && orderReq.PaymentType != types.PaymentTypeNone {
// 		return "", errors.New("不支持的支付方式")
// 	}

// 	if err = orderService.checkGift(&orderReq); err != nil {
// 		return
// 	}

// 	err = global.GVA_DB.Create(&order).Error
// 	return order, err
// }

// PayOrder 支付订单

func (orderService *OrderService) PayOrder(orderNo string) (err error) {
	return
}

// DeleteOrder 删除Order记录

func (orderService *OrderService) DeleteOrder(order orders.Order, operatorDelete uint) (err error) {
	err = global.GVA_DB.Delete(&order).Error
	return err
}

// DeleteOrderByIds 批量删除Order记录

func (orderService *OrderService) DeleteOrderByIds(ids request.IdsReq) (err error) {
	err = global.GVA_DB.Delete(&[]orders.Order{}, "id in ?", ids.Ids).Error
	return err
}

func (orderService *OrderService) UpdateOrder(orderReq ordersReq.UpdateOrder, operatorUpdate uint, userIp string) (err error) {
	type accountRefund struct {
		Amount  int
		Balance int
	}

	var (
		ffrom                  types.OrderStatus
		from                   []types.OrderStatus
		tto                    types.OrderStatus
		to                     []types.OrderStatus
		f                      franchisees.Franchisee
		rc                     = make([]*franchisees.RechargeRecord, 0)
		order                  orders.Order
		delivery               orders.OrderDelivery
		ogs                    []*orders.OrderGoods
		oop                    payModel.OrderOnlinePay
		totalReturnRebate      int                            // 总扣减返利积分
		accountRefundMap       = make(map[uint]accountRefund) // 加盟商专项账户退款金额
		generalAccountRefund   int                            // 加盟商通用账户退款金额
		onlinePayAccountRefund uint                           // 加盟商在线支付退款金额
		cloudRecords           = make([]*warehouse.CloudWarehouseRecord, 0)
	)

	switch orderReq.OrderOperate {
	case types.GoodsDelivery:
		from = []types.OrderStatus{types.OrderAwaitingDelivery}
		to = []types.OrderStatus{types.OrderDelivery}
	case types.GoodsReturn:
		from = []types.OrderStatus{types.OrderDelivery}
		to = []types.OrderStatus{types.OrderReturn}
	case types.GoodsRefund:
		from = []types.OrderStatus{types.OrderAwaitingDelivery, types.OrderCompleted, types.OrderStocking, types.OrderDelivery, types.OrderStatusPaymentSuccess} // 允许云仓退款，订单发货但发货单备货状态退款
		to = []types.OrderStatus{types.OrderRefund, types.OrderCloudReturn, types.OrderStatusRefunding}
	}

	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		tx.Begin()
		if err := tx.Set("gorm:query_option", "FOR UPDATE").Unscoped().Where("id =?", orderReq.FranchiseeId).First(&f).Error; err != nil {
			return err
		}

		// TenantID will be applied by GORM plugin from context
		if err = tx.Where("order_no = ?", orderReq.OrderNo).First(&order).Error; err != nil {
			return err
		}

		if order.OrderType == types.SystemImport {
			return fmt.Errorf("系统导入订单不允许退款")
		}

		if _, ok := lo.Find(from, func(item types.OrderStatus) bool {
			return item == order.Status
		}); !ok {
			return fmt.Errorf("订单状态不允许退款")
		}
		if order.Status == types.OrderCompleted && order.OrderSubType != types.Presentation {
			// 已经完毕的订单，只有云仓压货订单可以退款
			return errors.New("只有云仓压货订单可以退款")
		}
		if order.Status == types.OrderDelivery {
			// 查询发货单
			err = global.GVA_DB.Model(&orders.OrderDelivery{}).Where("order_no = ? and status = ?", order.OrderNo, types.DeliveryStatusStocking).First(&delivery).Error
			if err != nil {
				if !utils.ErrorIsDBNotFound(err) {
					return errors.Join(err, errors.New("查询发货单失败"))
				}
				global.GVA_LOG.Error("service.UpdateOrder err:", zap.Error(err), zap.Any("order", order))
				return errors.Join(err, errors.New("备货状态查询发货单失败"))
			}
			var deliveryCount int64
			err = global.GVA_DB.Model(&orders.OrderDelivery{}).Where("order_no = ?", order.OrderNo).Count(&deliveryCount).Error
			if err != nil {
				return errors.Join(err, errors.New("查询发货单数量失败"))
			}
			if deliveryCount > 1 {
				return errors.New("已有多个发货单，不允许退款")
			}
		}

		ffrom = from[0]
		tto = to[0]
		// 云仓压货订单退款
		if (order.PaymentType == types.AccountBalance || order.PaymentType == types.PointsBalance || order.PaymentType == types.PaymentTypeOnlinePay) && order.OrderSubType == types.Presentation && order.Status == types.OrderCompleted {
			// 检查是否已发货
			// 如果已发货，不允许退款
			global.GVA_LOG.Debug("service.order.UpdateOrder",
				zap.Any("order", order),
				zap.Any("orderReq", orderReq))
			isWhole := dao.CloudWarehouse.ISCloudWarehouseOrderWhole(order.OrderNo)
			if !isWhole {
				return fmt.Errorf("该压货单已提货，不允许退款")
			}
		} else if (order.PaymentType == types.AccountBalance || order.PaymentType == types.PointsBalance || order.PaymentType == types.ZeroPayOrder || order.PaymentType == types.PaymentTypeOnlinePay) && (order.Status == types.OrderAwaitingDelivery || order.Status == types.OrderStatusPaymentSuccess || (order.Status == types.OrderDelivery && delivery.Status == types.DeliveryStatusStocking)) {
			// 普通订单状态为待发货/已发货，但是发货单为备货状态 ********
		} else if (order.PaymentType == types.CloudPickUp) && (order.Status == types.OrderAwaitingDelivery || (order.Status == types.OrderDelivery && delivery.Status == types.DeliveryStatusStocking)) {
			//提货订单，且订单状态为待发货状态 ********
			//提货订单，且订单状态为发货状态，且发货单为备货状态 ********
		} else {
			return fmt.Errorf("订单状态不允许退款")
		}

		if err = tx.Where("order_id = ?", order.ID).Find(&ogs).Error; err != nil {
			return err
		}
		//end of check order and order delivery status

		//begin do refund
		if (order.PaymentType == types.AccountBalance || order.PaymentType == types.PointsBalance || order.PaymentType == types.PaymentTypeOnlinePay) && order.OrderSubType == types.Presentation {
			//处理云仓压货订单，1.清空 scw 库存，2.创建云仓流水
			ffrom = from[1]
			var cw warehouse.CloudWarehouse
			if err = tx.Where("order_no = ?", order.OrderNo).First(&cw).Error; err != nil {
				return err
			}
			if order.Remark == "" {
				order.Remark = "云仓退单退款"
			}
			// for loop ogs , 清空cw.id 、 ogs.product_id , inventory 对应的云仓库存
			for _, v := range ogs {
				if err = tx.Model(&warehouse.SubCloudWarehouse{}).
					Where("cloud_warehouse_id = ?", cw.ID).
					Where("product_id = ?", v.ProductId).
					Where("inventory = ?", v.Quantity+v.PointsQuantity).
					Update("inventory", 0).Error; err != nil {
					return err
				}
				cloudRecord := &warehouse.CloudWarehouseRecord{
					FranchiseeId:     cw.FranchiseeId,
					Type:             types.RefundCloud,
					CloudWarehouseId: cw.ID,
					ProductId:        v.ProductId,
					Inventory:        (v.Quantity + v.PointsQuantity) - (v.Quantity + v.PointsQuantity), // 置零
					Pieces:           v.Quantity + v.PointsQuantity,
					TradeNo:          cw.OrderNo,
					Remark:           order.Remark,
					OperatorID:       operatorUpdate,
				}
				cloudRecords = append(cloudRecords, cloudRecord)
			}
			if len(cloudRecords) > 0 {
				if err = tx.Create(cloudRecords).Error; err != nil {
					return err
				}
			}
			//尚未退款，留在后面
		}
		if order.PaymentType == types.CloudPickUp && order.OrderSubType == types.PickUp && (order.Status == types.OrderAwaitingDelivery || (order.Status == types.OrderDelivery && delivery.Status == types.DeliveryStatusStocking)) {
			// 提货订单，且订单状态等于待发货状态 ********
			// 提货订单，且订单状态为发货状态，且发货单为备货状态 ********
			// 1. 查找 cloud_warehouse_record 记录，根据 trade_no
			// 2. 查找 sub_cloud_warehouse 对应的商品，更新 inventory
			// 3. 创建云仓流水
			// 4. 如果订单是发货状态发货单为备货状态，更新发货单状态为 云仓退货 ********
			//productIds := make([]uint, 0)
			if order.Status == types.OrderAwaitingDelivery {
				tto = to[1] // 云仓退货
			} else if order.Status == types.OrderDelivery {
				ffrom = from[3]
				tto = to[1] // 云仓退货
			} else {
				return errors.Join(errors.New("order status is not allow refund"), errors.New("云仓提货订单非待发货/备货状态"))
			}
			var pickupCloudsRecords []warehouse.CloudWarehouseRecord
			err := tx.Model(&warehouse.CloudWarehouseRecord{}).Where("franchisee_id = ? and type = ? and trade_no = ?", order.FranchiseeId, types.OutCloud, order.OrderNo).Find(&pickupCloudsRecords).Error
			if err != nil {
				return errors.Join(err, errors.New("没有找到云仓提货订单流水记录"))
			}
			// compare ogs with pickup_clouds_records for product_id and quantity
			if len(pickupCloudsRecords) != len(ogs) || len(pickupCloudsRecords) == 0 {
				//TODO: 云仓流水记录与订单 order_goods 不匹配
				return errors.New("云仓流水记录与订单 order_goods 不匹配")

			}
			var ogspMap = make(map[int]int)
			var cwrpMap = make(map[int]int)
			for k := range ogs {
				if _, ok := ogspMap[ogs[k].ProductId]; !ok {
					ogspMap[ogs[k].ProductId] = ogs[k].Quantity + ogs[k].PointsQuantity
				} else {
					ogspMap[ogs[k].ProductId] += ogs[k].Quantity + ogs[k].PointsQuantity
				}
			}
			for k := range pickupCloudsRecords {
				if _, ok := cwrpMap[pickupCloudsRecords[k].ProductId]; !ok {
					cwrpMap[pickupCloudsRecords[k].ProductId] = pickupCloudsRecords[k].Pieces
				} else {
					cwrpMap[pickupCloudsRecords[k].ProductId] += pickupCloudsRecords[k].Pieces
				}
			}
			for k, v := range ogspMap {
				if _, ok := cwrpMap[k]; !ok || cwrpMap[k] != v {
					return errors.New("cloud_warehouse_record quantity is not equal to order_goods quantity")
				}
			}
			//for _, v := range ogs {
			//	for _, cloudRecord := range pickupCloudsRecords {
			//		if v.ProductId == cloudRecord.ProductId {
			//			if v.Quantity == cloudRecord.Pieces {
			//				continue
			//			} else {
			//				return fmt.Errorf("cloud_warehouse_record quantity is not equal to order_goods quantity")
			//			}
			//		}
			//	}
			//}

			//根据云仓流水记录，创建退仓的云仓流水，并更新库存
			for k := range pickupCloudsRecords {
				cr := pickupCloudsRecords[k]
				var scw warehouse.SubCloudWarehouse
				if err = tx.Where("cloud_warehouse_id = ?", cr.CloudWarehouseId).Where("product_id = ?", cr.ProductId).First(&scw).Error; err != nil {
					return errors.Join(err, errors.New("没有找到子仓的商品记录"))
				}
				if err = tx.Model(&warehouse.SubCloudWarehouse{}).
					Where("cloud_warehouse_id = ?", cr.CloudWarehouseId).
					Where("product_id = ?", cr.ProductId).
					Where("inventory = ?", scw.Inventory).
					Update("inventory", scw.Inventory+cr.Pieces).Error; err != nil {
					return err
				}
				cr.CreatedAt = time.Now().Local()
				cr.UpdatedAt = time.Now().Local()
				cr.Type = types.ReturnCloud
				cr.Inventory = scw.Inventory + cr.Pieces
				cr.ID = 0
				cr.OperatorID = operatorUpdate
				cloudRecords = append(cloudRecords, &cr)
			}
			if len(cloudRecords) > 0 {
				if err = tx.Create(cloudRecords).Error; err != nil {
					return err
				}
			}
		}

		// 普通订单余额支付，分析订单商品扣款情况
		if order.PaymentType == types.AccountBalance {
			for _, v := range ogs {
				if !v.IsGift && v.Quantity > 0 {
					totalReturnRebate += v.Rebate * v.Quantity

					// 计算专户和通户退款金额
					if v.AccountId != 0 {
						// 查询加盟商专户名称
						var a franchisees.FranchiseeAccount
						if err = tx.Where("id = ?", v.AccountId).First(&a).Error; err != nil {
							// 查不到报错，删除逻辑已改为禁用
							return err
						}

						// 如果accountId已存在，则累加金额
						if _, ok := accountRefundMap[v.AccountId]; ok {
							amount := accountRefundMap[v.AccountId].Amount + v.AccountPayed
							accountRefundMap[v.AccountId] = accountRefund{
								Amount:  amount,
								Balance: a.Balance,
							}
						} else {
							accountRefundMap[v.AccountId] = accountRefund{
								Amount:  v.AccountPayed,
								Balance: a.Balance,
							}
						}
					}

					generalAccountRefund += v.UnitPrice*v.Quantity - v.AccountPayed
				}
			}
		}
		// 在线支付,分析商品扣款情况，获取 order_online_pay 记录
		if order.PaymentType == types.PaymentTypeOnlinePay {
			for _, v := range ogs {
				if !v.IsGift && v.Quantity > 0 {
					totalReturnRebate += v.Rebate * v.Quantity
				}
				if v.AccountType == types.AccountTypeOnlinePay {
					onlinePayAccountRefund += uint(v.UnitPrice * v.Quantity)
				}
			}
			// get order_online_pay
			if err = tx.Where("order_no", order.OrderNo).First(&oop).Error; err != nil {
				return errors.Join(err, errors.New("get order online pay record error, can not refund"))
			}
			if oop.Amount != onlinePayAccountRefund {
				return errors.New("order online pay amount is not equal to order goods amount")
			}
			if oop.Status != types.OnlinePayStatusSuccess {
				return errors.New("order online pay status is not success, can not refund")
			}
			if oop.TradeNo == "" {
				return errors.New("order online pay tradeNo is empty, can not refund")
			}
		}

		// 积分支付，退回
		if order.Points > 0 && order.PaymentType == types.PointsBalance {
			rc = append(rc, &franchisees.RechargeRecord{
				FranchiseeId: orderReq.FranchiseeId,
				RechargeType: utils.RechargeTypePtr(types.RechargePoints),
				OperateType:  utils.OperateTypePtr(types.MallRefund),
				Amount:       order.Points,
				Balance:      f.Points + order.Points,
				SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
				TradeNo:      order.OrderNo,
				OperatorID:   operatorUpdate,
			})

			// 更新金额及创建变更记录
			if err = tx.Model(&franchisees.Franchisee{}).Where("id =?", orderReq.FranchiseeId).Updates(map[string]interface{}{
				"points": gorm.Expr("points + ?", order.Points),
			}).Error; err != nil {
				return fmt.Errorf("franchisee refund failed, err:=%w", err)
			}
		}

		// 余额支付，退回
		if order.Amount > 0 && order.PaymentType == types.AccountBalance {
			if generalAccountRefund > 0 {
				rc = append(rc, &franchisees.RechargeRecord{
					FranchiseeId: orderReq.FranchiseeId,
					RechargeType: utils.RechargeTypePtr(types.RechargeAmount), //types.RechargeAmount,
					OperateType:  utils.OperateTypePtr(types.MallRefund),      //types.MallRefund,
					AccountType:  utils.AccountTypePtr(types.GeneralAccount),  // types.GeneralAccount,
					AccountID:    utils.UintPtr(0),                            //0,
					Amount:       generalAccountRefund,
					Balance:      f.Balance + generalAccountRefund,
					SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					TradeNo:      order.OrderNo,
					OperatorID:   operatorUpdate,
				})

				// 更新金额及创建变更记录
				if err = tx.Model(&franchisees.Franchisee{}).Where("id =?", orderReq.FranchiseeId).Updates(map[string]interface{}{
					"balance": gorm.Expr("balance + ?", generalAccountRefund),
				}).Error; err != nil {
					return fmt.Errorf("franchisee refund failed, err:=%w", err)
				}
			}

			for k, v := range accountRefundMap {
				rc = append(rc, &franchisees.RechargeRecord{
					FranchiseeId: orderReq.FranchiseeId,
					RechargeType: utils.RechargeTypePtr(types.RechargeAmount),  //types.RechargeAmount,
					OperateType:  utils.OperateTypePtr(types.MallRefund),       //types.MallRefund,
					AccountType:  utils.AccountTypePtr(types.ExclusiveAccount), //types.ExclusiveAccount,
					AccountID:    &k,
					Amount:       v.Amount,
					Balance:      v.Balance + v.Amount,
					SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
					TradeNo:      order.OrderNo,
					OperatorID:   operatorUpdate,
				})

				// 更新金额及创建变更记录
				if err = tx.Model(&franchisees.FranchiseeAccount{}).Where("id =?", k).Updates(map[string]interface{}{
					"balance": gorm.Expr("balance + ?", v.Amount),
				}).Error; err != nil {
					return fmt.Errorf("franchisee account refund failed, err:=%w", err)
				}
			}
		}

		if totalReturnRebate > 0 && order.PaymentType != types.ZeroPayOrder {
			if f.Points-totalReturnRebate < 0 {
				return fmt.Errorf("积分不足")
			}

			rc = append(rc, &franchisees.RechargeRecord{
				FranchiseeId: orderReq.FranchiseeId,
				RechargeType: utils.RechargeTypePtr(types.RechargePoints),
				OperateType:  utils.OperateTypePtr(types.ReturnRefundRebate),
				Amount:       totalReturnRebate,
				Balance:      f.Points - totalReturnRebate,
				SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
				TradeNo:      order.OrderNo,
				OperatorID:   operatorUpdate,
			})

			if err = tx.Model(&franchisees.Franchisee{}).Where("id =?", orderReq.FranchiseeId).Updates(map[string]interface{}{
				"points": gorm.Expr("points - ?", totalReturnRebate),
			}).Error; err != nil {
				return fmt.Errorf("franchisee refund failed, err:=%w", err)
			}
		}
		// 在线支付，退回
		if order.PaymentType == types.PaymentTypeOnlinePay && onlinePayAccountRefund > 0 {
			rc = append(rc, &franchisees.RechargeRecord{
				FranchiseeId: orderReq.FranchiseeId,
				RechargeType: utils.RechargeTypePtr(types.RechargeOnlinpay),
				OperateType:  utils.OperateTypePtr(types.MallRefund),
				AccountType:  utils.AccountTypePtr(types.AccountTypeOnlinePay),
				AccountID:    utils.UintPtr(0),
				Amount:       int(onlinePayAccountRefund),
				Balance:      f.Balance,
				SerialNo:     utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
				TradeNo:      order.OrderNo,
				OperatorID:   operatorUpdate,
			})
			tto = to[2] // 在线支付，状态为 “退款中”。需要等待 支付服务商返回 callback 确认退款成功才能修改为 已退款
			if order.Status == types.OrderStatusPaymentSuccess {
				ffrom = from[4]
			}
			payServiceInstance := &payService.OnlinePayService{}
			err := payServiceInstance.RefundOnlinePayOrder(tx, order.OrderNo, onlinePayAccountRefund, userIp, operatorUpdate)
			if err != nil {
				return fmt.Errorf("online pay refund failed, err:=%w", err)
			}
		}
		if len(rc) > 0 {
			if err = tx.Create(&rc).Error; err != nil {
				return err
			}
		}
		if order.Status == types.OrderDelivery && delivery.Status == types.DeliveryStatusStocking {
			ffrom = from[3]
			if order.PaymentType == types.CloudPickUp {
				tto = to[1]
			} else if order.PaymentType != types.PaymentTypeOnlinePay {
				tto = to[0]
			}
			// 提货单已经备货
			if err = tx.Model(&orders.OrderDelivery{}).Where("id = ?", delivery.ID).Update("status", types.DeliveryStatusReturned).Error; err != nil {
				return errors.Join(err, errors.New("更新发货单状态失败:"+delivery.OrderNo))
			}
			// @todo: remove bigwarehouse order
		}

		var o orders.Order
		err = tx.Model(&o).Clauses(clause.Returning{}).Where("order_no = ? and status = ?", orderReq.OrderNo, ffrom).Update("status", tto).Error
		if err != nil {
			return errors.Join(err, errors.New("更新订单状态失败:"+orderReq.OrderNo))
		}
		reqJson, _ := json.Marshal(orders.OrderRecordExtra{
			Req:    orderReq,
			Result: o,
			Stage:  tto.StringEn(),
		})
		err = tx.Create(&orders.OrderRecord{
			OrderID:     order.ID,
			OrderNo:     order.OrderNo,
			OrderStatus: tto,
			OperatorID:  operatorUpdate,
			Extra:       reqJson,
			Remark:      order.Remark,
		}).Error
		if err != nil {
			return errors.Join(err, errors.New("更新订单创建订单记录失败:"+order.OrderNo))
		}
		// Send order status change notification
		if err = utils.EnqueueOrderStatusChange(orderReq.OrderNo, order.ID, int8(ffrom), int8(tto), operatorUpdate, fmt.Sprintf("Order status changed from %s to %s", ffrom, tto)); err != nil {
			global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
		}

		return nil
	})

	return err
}

// GetOrder 根据id获取Order记录

func (orderService *OrderService) GetOrder(id uint) (detail response.OrderDetail, err error) {
	var (
		order            orders.Order
		ps               = make([]response.ProductDetail, 0)
		deliveryProducts = make([]response.ProductDetail, 0)
		deliveryNos      = make([]string, 0)
		onlinePayInfo    *payResp.OrderOnlinePayResp
	)

	err = global.GVA_DB.Preload("OrderRemarks").Preload("OrderRemarks.CreateUser").Preload("OrderRecords").Preload("OrderRecords.Operator").Preload("OrderOnlinePay").Where("id = ?", id).First(&order).Error
	if err != nil {
		if utils.ErrorIsDBNotFound(err) {
			err = nil
		}
		return
	}
	if order.PaymentType == types.PaymentTypeOnlinePay {
		paySrv := payService.OnlinePayService{}
		onlinePayInfo, err = paySrv.GetOrderOnlinePay(order.OrderNo)
		if err == nil {
			detail.OnlinePayInfo = onlinePayInfo
		} else {
			return detail, errors.Join(err, errors.New("获取在线支付信息失败"))
		}
	}

	var orderGoods []*response.OrderGoods
	err = global.GVA_DB.
		Model(&response.OrderGoods{}).
		Joins("left join product on product.id = order_goods.product_id").
		Select("order_goods.*, product.code as product_code, product.name as product_name, product.image_addr,product.is_gift").
		Where("order_goods.order_id = ?", id).Find(&orderGoods).Error
	if err != nil {
		return
	}

	// temp
	for _, good := range orderGoods {
		good.Status = int(order.Status)
		c, gErr := dao.Product.GetCombinationProductDetial(good.ProductId)
		if gErr != nil {
			err = gErr
			return
		}
		good.CombinationDetail = c
		// 获取商品所属分区
		if good.SpecialMallID != 0 {
			var sm products.SpecialMall
			err = global.GVA_DB.Model(products.SpecialMall{}).Unscoped().Where("id = ?", good.SpecialMallID).First(&sm).Error
			if err != nil {
				return
			}
			good.SpecialMall = &sm
		}
	}
	// 补充账户支付信息
	walletPay, err := dao.Order.GetWalletPayedDetail(int(order.ID))
	if err != nil {
		return
	}
	detail.WalletPay = walletPay

	var franchisee franchisees.Franchisee
	err = global.GVA_DB.Unscoped().Where("id = ?", order.FranchiseeId).First(&franchisee).Error
	if err != nil && !utils.ErrorIsDBNotFound(err) {
		return
	}
	detail.Order = &order
	detail.ProductInfo = orderGoods
	detail.Franchisee = &franchisee

	if order.Status == types.OrderAwaitingDelivery || order.Status == types.OrderReturn || order.Status == types.OrderDelivery ||
		order.Status == types.OrderCompleted || order.Status == types.OrderRefund || order.Status == types.OrderCloudReturn || order.Status == types.OrderStatusAwaitingPayment || order.Status == types.OrderStatusPaying || order.Status == types.OrderStatusPaymentSuccess || order.Status == types.OrderStatusPaymentFailed || order.Status == types.OrderStatusRefunding || order.Status == types.OrderStatusClosed {
		for _, og := range orderGoods {
			p, cErr := dao.Product.FirstProduct(og.ProductId)
			if cErr != nil {
				err = cErr
				return
			}

			quantity := og.GetQuantity(order.PaymentType)

			if order.Status == types.OrderDelivery || order.Status == types.OrderCompleted {
				deliveryProducts = append(deliveryProducts, response.ProductDetail{
					OrderGoods:    og,
					Product:       *p,
					Quantity:      quantity,
					TotalQuantity: quantity,
					GoodsID:       og.ID,
				})
			} else {
				ps = append(ps, response.ProductDetail{
					OrderGoods:    og,
					Product:       *p,
					Quantity:      quantity,
					TotalQuantity: quantity,
					GoodsID:       og.ID,
				})
			}
		}
	} else if order.Status == types.OrderPartDelivery {
		var (
			dOrderGoods   []*response.OrderGoods
			ods           []*orders.OrderDelivery
			odgMap        = make(map[int]int)
			ogMap         = make(map[int]int)
			dOgIds, ogIds []int
		)

		for _, og := range orderGoods {
			ogMap[int(og.ID)] = og.GetQuantity(order.PaymentType)
		}

		err = global.GVA_DB.Where("order_no", order.OrderNo).Find(&ods).Error
		if err != nil {
			return
		}

		for _, od := range ods {
			for _, odg := range *od.GoodsDetail {
				if _, ok := odgMap[odg.GoodsId]; !ok {
					odgMap[odg.GoodsId] = odg.GoodsNum
				} else {
					odgMap[odg.GoodsId] += odg.GoodsNum
				}
			}
		}

		for i := range odgMap {
			dOgIds = append(dOgIds, i)
		}
		err = global.GVA_DB.
			Model(&response.OrderGoods{}).
			Joins("left join product on product.id = order_goods.product_id").
			Select("order_goods.*, product.name as product_name, product.image_addr").
			Where("order_goods.id  in (?)", dOgIds).Find(&dOrderGoods).Error
		if err != nil {
			return
		}

		for _, og := range dOrderGoods {
			p, cErr := dao.Product.FirstProduct(og.ProductId)
			if cErr != nil {
				err = cErr
				return
			}
			deliveryProducts = append(deliveryProducts, response.ProductDetail{
				OrderGoods:    og,
				Product:       *p,
				TotalQuantity: og.GetQuantity(order.PaymentType),
				Quantity:      odgMap[int(og.ID)],
				GoodsID:       og.ID,
			})
		}

		var notOgs []*response.OrderGoods
		notDeliveryMap := calNotDeliveryMap(ogMap, odgMap)
		for i := range notDeliveryMap {
			ogIds = append(ogIds, i)
		}
		err = global.GVA_DB.
			Model(&response.OrderGoods{}).
			Joins("left join product on product.id = order_goods.product_id").
			Select("order_goods.*, product.name as product_name, product.image_addr").
			Where("order_goods.id  in (?)", ogIds).Find(&notOgs).Error
		if err != nil {
			return
		}
		for _, og := range notOgs {
			p, cErr := dao.Product.FirstProduct(og.ProductId)
			if cErr != nil {
				err = cErr
				return
			}

			ps = append(ps, response.ProductDetail{
				OrderGoods:    og,
				Product:       *p,
				TotalQuantity: og.GetQuantity(order.PaymentType),
				Quantity:      notDeliveryMap[int(og.ID)],
				GoodsID:       og.ID,
			})
		}
	} else {
		err = errors.New("订单状态异常")
		return
	}

	var ods []*orders.OrderDelivery
	err = global.GVA_DB.Where("order_no", order.OrderNo).Find(&ods).Error
	if err != nil {
		return
	}

	for _, od := range ods {
		deliveryNos = append(deliveryNos, od.DeliveryNo)
	}

	if order.Status == types.OrderDelivery {
		// 查询发货单
		var delivery orders.OrderDelivery
		err = global.GVA_DB.Model(&orders.OrderDelivery{}).Where("order_no = ? and status = ?", order.OrderNo, types.DeliveryStatusStocking).First(&delivery).Error
		if err != nil {
			if !utils.ErrorIsDBNotFound(err) {
				return
			}
		} else {
			order.Status = types.OrderStocking
		}
	}

	detail.DeliveryNos = deliveryNos
	detail.Products = ps
	detail.DeliveryProducts = deliveryProducts
	return detail, nil
}

func calNotDeliveryMap(ogMap map[int]int, odgMap map[int]int) map[int]int {
	notDeliveryMap := make(map[int]int)

	for k, v := range ogMap {
		if odgMap[k] == 0 {
			notDeliveryMap[k] = v
		} else {
			notShippedQty := v - odgMap[k]
			if notShippedQty > 0 {
				notDeliveryMap[k] = notShippedQty
			}
		}
	}

	return notDeliveryMap
}

// GetOrderInfoList 分页获取Order记录
func (orderService *OrderService) GetOrderInfoList(info ordersReq.OrderSearch) (list []orders.Order, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&orders.Order{}).Preload("OrderOnlinePay")
	var os []orders.Order

	// 租户隔离将由GORM插件自动处理
	// if info.TenantID > 0 {
	// 	db = db.Where("tenant_id = ?", info.TenantID)
	// } else {
	// 	// 如果没有租户ID，返回空结果（安全考虑）
	// 	return nil, 0, errors.New("租户ID不能为空")
	// }

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("`orders`.created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.StartDeliveryAt != nil && info.EndDeliveryAt != nil {
		var osFilter []string
		err := global.GVA_DB.Model(&orders.OrderRecord{}).Where("order_status = ? AND created_at BETWEEN ? AND ?", types.OrderDelivery, info.StartDeliveryAt, info.EndDeliveryAt).Distinct("order_no").Pluck("order_no", &osFilter).Error
		if err != nil {
			return nil, 0, err
		}
		// db = db.Joins("OrderRecords").Where("OrderRecords.order_status = ? and OrderRecords.created_at BETWEEN ? AND ?", types.OrderDelivery, info.StartDeliveryAt, info.EndDeliveryAt)
		db = db.Where("`orders`.order_no in ?", osFilter)
	}
	// 订单号
	if info.OrderNo != "" {
		db = db.Where("`order`.order_no = ?", info.OrderNo)
	}
	// 支付方式
	if info.PaymentType != nil {
		db = db.Where("payment_type = ?", info.PaymentType)
	}
	if info.ShipmentType != nil {
		db = db.Where("shipment_type = ?", info.ShipmentType)
	}
	// 订单状态
	if info.Status != nil {
		if *info.Status == types.OrderStocking {
			var (
				orderNoList []string
			)
			err := global.GVA_DB.Model(&orders.OrderDelivery{}).Where("status = ?", types.DeliveryStatusStocking).Pluck("order_no", &orderNoList).Error
			if err != nil {
				return nil, 0, err
			}
			if len(orderNoList) == 0 {
				return nil, 0, nil
			}
			db = db.Where("`order`.status = ? and order_no in ?", types.OrderDelivery, orderNoList)
		} else {
			db = db.Where("`order`.status = ?", info.Status)
		}
	} else {
		db = db.Where("`order`.status != ? && `order`.status != ?", types.OrderStatusAwaitingPayment, types.OrderStatusClosed)
	}

	if info.NeedDelivery != nil {
		if *info.NeedDelivery {
			db = db.Where("`order`.status in(?)", []types.OrderStatus{types.OrderAwaitingDelivery, types.OrderPartDelivery})
		}
	}
	// 订单类型
	if info.OrderType != nil {
		db = db.Where("order_type = ?", info.OrderType)
	}
	if info.OrderSubType != nil {
		db = db.Where("order_sub_type = ?", info.OrderSubType)
	}
	if info.MinAmount > 0 {
		db = db.Where("amount >= ?", info.MinAmount)
	}
	if info.MaxAmount > 0 {
		db = db.Where("amount <= ?", info.MaxAmount)
	}
	// 购买人姓名
	if info.FranchiseName != "" {
		var franchiseeIds []uint
		err := global.GVA_DB.Unscoped().Model(&franchisees.Franchisee{}).
			Where("name like ?", "%"+info.FranchiseName+"%").Pluck("id", &franchiseeIds).Error
		if err != nil {
			return nil, 0, err
		}
		if len(franchiseeIds) == 0 {
			return nil, 0, nil
		}
		db = db.Where("franchisee_id in ?", franchiseeIds)
	}
	// 购买人手机号
	if info.FranchiseTel != "" {
		var franchiseeIds []uint
		err := global.GVA_DB.Unscoped().Model(&franchisees.Franchisee{}).Where("tel = ?", info.FranchiseTel).Pluck("id", &franchiseeIds).Error
		if err != nil {
			return nil, 0, err
		}
		if len(franchiseeIds) == 0 {
			return nil, 0, nil
		}
		db = db.Where("franchisee_id in ?", franchiseeIds)
	}
	if info.ConsigneeTel != nil && *info.ConsigneeTel != "" {
		db = db.Where(datatypes.JSONQuery("address_snapshot").Equals(*info.ConsigneeTel, "tel"))
	}
	if info.ConsigneeName != nil && *info.ConsigneeName != "" {
		db = db.Where(datatypes.JSONQuery("address_snapshot").Equals(*info.ConsigneeName, "consignee"))
	}
	// 商品编号
	if info.ProductCode != "" {
		var productIds []uint
		err := global.GVA_DB.Unscoped().Model(&products.Product{}).Unscoped().Where("code = ?", info.ProductCode).Pluck("id", &productIds).Error
		if err != nil {
			return nil, 0, err
		}
		if len(productIds) > 0 {
			var (
				orderIDList []uint
			)
			err = global.GVA_DB.Model(&orders.OrderGoods{}).Where("product_id in ?", productIds).Pluck("order_id", &orderIDList).Error
			if err != nil {
				return nil, 0, err
			}
			if len(orderIDList) == 0 {
				return nil, 0, nil
			}
			db = db.Where("`order`.id in ?", orderIDList)
		}
	}
	if info.ProductName != "" {
		var productIds []uint
		err := global.GVA_DB.Unscoped().Model(&products.Product{}).Unscoped().Where("name like ?", "%"+info.ProductName+"%").Pluck("id", &productIds).Error
		if err != nil {
			return nil, 0, err
		}
		if len(productIds) > 0 {
			var (
				orderIDList []uint
			)
			err = global.GVA_DB.Model(&orders.OrderGoods{}).Where("order_goods.product_id in ?", productIds).Pluck("order_id", &orderIDList).Error
			if err != nil {
				return nil, 0, err
			}
			if len(orderIDList) == 0 {
				return nil, 0, nil
			}
			db = db.Where("`"+orders.Order{}.TableName()+"`.id in ?", orderIDList)
		}
	}
	if info.BigWarehouseId != nil && *info.BigWarehouseId > 0 {
		db = db.Joins("BigWarehouseOrder").Where("BigWarehouseOrder.big_warehouse_id = ?", info.BigWarehouseId)
	}
	global.GVA_LOG.Info("info.Area", zap.Any("info.Area", info.Area))
	// 构建查询条件
	if len(info.Area) > 0 {
		areaCondition := ""
		for _, v := range info.Area {
			oneArea := strings.Split(v, "|")
			if len(oneArea) == 1 {
				areaCondition = areaCondition + "JSON_UNQUOTE(address_snapshot->'$.province') = '" + oneArea[0] + "' or "
			} else if len(oneArea) == 2 {
				areaCondition = areaCondition + "(JSON_UNQUOTE(address_snapshot->'$.province') = '" + oneArea[0] + "' and JSON_UNQUOTE(address_snapshot->'$.city') = '" + oneArea[1] + "') or "
			} else if len(oneArea) == 3 {
				areaCondition = areaCondition + "(JSON_UNQUOTE(address_snapshot->'$.province') = '" + oneArea[0] + "' and JSON_UNQUOTE(address_snapshot->'$.city') = '" + oneArea[1] + "' and JSON_UNQUOTE(address_snapshot->'$.county') = '" + oneArea[2] + "') or "
			}
		}
		if len(areaCondition) > 4 {
			areaCondition = areaCondition[:len(areaCondition)-4]
			global.GVA_LOG.Debug("areaCondition", zap.String("areaCondition", areaCondition))
			db = db.Where(areaCondition)
		}
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit == 0 {
		limit = 10
	}
	err = db.Preload("OrderRecords.Operator").Preload("OrderRecords").Preload("BigWarehouseOrder.BigWarehouse").Preload("BigWarehouseOrder").Limit(limit).Offset(offset).Order("created_at desc").Find(&os).Error
	for i, o := range os {
		var franchisee franchisees.Franchisee
		err = global.GVA_DB.Model(&franchisees.Franchisee{}).Where("id = ?", o.FranchiseeId).Unscoped().First(&franchisee).Error
		if err != nil && !utils.ErrorIsDBNotFound(err) {
			return nil, 0, err
		}
		os[i].Franchisee = &franchisee

		if o.Status == types.OrderDelivery {
			// 查询发货单
			var delivery orders.OrderDelivery
			err = global.GVA_DB.Model(&orders.OrderDelivery{}).Where("order_no = ? and status = ?", o.OrderNo, types.DeliveryStatusStocking).First(&delivery).Error
			if err != nil {
				if !utils.ErrorIsDBNotFound(err) {
					return nil, 0, err
				}
			} else {
				os[i].Status = types.OrderStocking
			}
		}

		var (
			orderGoods []*response.OrderGoods
			ogMap      = make(map[int]int)
		)
		err = global.GVA_DB.
			Model(&response.OrderGoods{}).
			Joins("left join product on product.id = order_goods.product_id").
			Select("order_goods.*, product.name as product_name, product.image_addr").
			Where("order_goods.order_id = ?", o.ID).Find(&orderGoods).Error
		if err != nil {
			return
		}

		for _, og := range orderGoods {
			ogMap[int(og.ID)] = og.GetQuantity(o.PaymentType)
		}

		ogs := make([]*orders.ProductDetail, 0, len(orderGoods))
		for _, og := range orderGoods {
			c, cErr := dao.Product.GetCombinationProductDetial(og.ProductId)
			if cErr != nil {
				return nil, 0, cErr
			}

			quantity := og.GetQuantity(o.PaymentType)
			if o.Status == types.OrderDelivery || o.Status == types.OrderPartDelivery || o.Status == types.OrderCompleted {
				var (
					ods    []*orders.OrderDelivery
					odgMap = make(map[int]int)
				)

				cErr := global.GVA_DB.Where("order_no", o.OrderNo).Find(&ods).Error
				if cErr != nil {
					err = cErr
					return
				}

				for _, od := range ods {
					for _, odg := range *od.GoodsDetail {
						if _, ok := odgMap[odg.GoodsId]; !ok {
							odgMap[odg.GoodsId] = odg.GoodsNum
						} else {
							odgMap[odg.GoodsId] += odg.GoodsNum
						}
					}
				}

				notDeliveryMap := calNotDeliveryMap(ogMap, odgMap)

				ogs = append(ogs, &orders.ProductDetail{
					Name:                     og.ProductName,
					AwaitingDeliveryQuantity: notDeliveryMap[int(og.ID)],
					TotalQuantity:            quantity,
					CombinationDetail:        c,
				})

				continue
			}

			ogs = append(ogs, &orders.ProductDetail{
				Name:                     og.ProductName,
				AwaitingDeliveryQuantity: quantity,
				TotalQuantity:            quantity,
				CombinationDetail:        c,
			})
		}

		os[i].GoodsDetail = ogs
	}
	return os, total, err
}

func (orderService *OrderService) CleanCart(franchiseeId uint, productID uint, tx *gorm.DB) error {
	db := global.GVA_DB
	if tx != nil {
		db = tx
	}
	var franchisee franchisees.Franchisee
	err := db.Unscoped().Where("id = ?", franchiseeId).First(&franchisee).Error
	if err != nil {
		return err
	}
	// 删除购物车
	if err = db.Where("product_id", productID).
		Where("user_id = ?", franchisee.UserID).
		Delete(&app.Cart{}).Error; err != nil {
		return fmt.Errorf("cart delete failed, err:=%w", err)
	}
	return nil
}

func (orderService *OrderService) ExportOrderInfoList(info ordersReq.OrderSearch) (*excelize.File, error) {
	//
	db := global.GVA_DB.Preload("OrderRecords").Preload("OrderRecords.Operator").Preload("OrderRemarks").Preload("OrderRemarks.CreateUser").Preload("BigWarehouseOrder.BigWarehouse").Preload("BigWarehouseOrder").Preload("OrderOnlinePay").Table("`" + orders.Order{}.TableName() + "` as o")

	// 租户隔离将由GORM插件自动处理
	// if info.TenantID > 0 {
	// 	db = db.Where("o.tenant_id = ?", info.TenantID)
	// } else {
	// 	// 如果没有租户ID，返回空结果（安全考虑）
	// 	return nil, errors.New("租户ID不能为空")
	// }

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		// 时间范围不能超过 3 个月
		if info.EndCreatedAt.Sub(*info.StartCreatedAt).Hours() > 24*184 {
			return nil, errors.New("时间范围不能超过 184 天")
		}
		db = db.Where("o.created_at BETWEEN ? AND ?", info.StartCreatedAt.Local(), info.EndCreatedAt.Local())
	}

	if info.StartDeliveryAt != nil && info.EndDeliveryAt != nil {
		// db = db.Joins("left join order_record as ORS on ORS.order_no = o.order_no").Where("ORS.order_status = ? and ORS.created_at BETWEEN ? AND ?", types.OrderDelivery, info.StartDeliveryAt.Local(), info.EndDeliveryAt.Local())
		var osFilter []string
		err := global.GVA_DB.Model(&orders.OrderRecord{}).Where("order_status = ? AND created_at BETWEEN ? AND ?", types.OrderDelivery, info.StartDeliveryAt, info.EndDeliveryAt).Distinct("order_no").Pluck("order_no", &osFilter).Error
		if err != nil {
			return nil, err
		}
		db = db.Where("o.order_no in ?", osFilter)
	}

	if info.StartDeliveryAt == nil && info.EndDeliveryAt == nil && info.StartCreatedAt == nil && info.EndCreatedAt == nil {
		db = db.Where("o.created_at BETWEEN ? AND ?", time.Now().AddDate(0, -3, 0).Local(), time.Now().Local())
	}
	// 订单号
	if info.OrderNo != "" {
		db = db.Where("o.order_no = ?", info.OrderNo)
	}
	// 支付方式
	if info.PaymentType != nil {
		db = db.Where("o.payment_type = ?", info.PaymentType)
	}
	// 订单状态
	if info.Status != nil {
		if *info.Status == types.OrderStocking {
			var (
				orderNoList []string
			)
			err := global.GVA_DB.Model(&orders.OrderDelivery{}).Where("status = ?", types.DeliveryStatusStocking).Pluck("order_no", &orderNoList).Error
			if err != nil {
				return nil, err
			}
			if len(orderNoList) == 0 {
				return nil, nil
			}
			db = db.Where("o.status = ? and o.order_no in ?", types.OrderDelivery, orderNoList)
		} else {
			db = db.Where("o.status = ?", info.Status)
		}
	}
	if info.BigWarehouseId != nil && *info.BigWarehouseId > 0 {
		db = db.Joins("BigWarehouseOrder").Where("BigWarehouseOrder.big_warehouse_id = ?", info.BigWarehouseId)
	}
	if info.NeedDelivery != nil {
		if *info.NeedDelivery {
			db = db.Where("o.status in(?)", []int8{int8(types.OrderAwaitingDelivery), int8(types.OrderPartDelivery)})
		}
	}
	// 订单类型
	if info.OrderType != nil {
		db = db.Where("o.order_type = ?", info.OrderType)
	}
	// 订单子类型
	if info.OrderSubType != nil {
		db = db.Where("o.order_sub_type = ?", info.OrderSubType)
	}
	if info.MinAmount > 0 {
		db = db.Where("o.amount >= ?", info.MinAmount)
	}
	if info.MaxAmount > 0 {
		db = db.Where("o.amount <= ?", info.MaxAmount)
	}
	// 购买人姓名
	if info.FranchiseName != "" {
		var franchiseeIds []uint
		err := global.GVA_DB.Unscoped().Model(&franchisees.Franchisee{}).
			Where("name like ?", "%"+info.FranchiseName+"%").Pluck("id", &franchiseeIds).Error
		if err != nil {
			return nil, err
		}
		if len(franchiseeIds) == 0 {
			return nil, nil
		}
		db = db.Where("o.franchisee_id in ?", franchiseeIds)
	}
	// 购买人手机号
	if info.FranchiseTel != "" {
		var franchiseeIds []uint
		err := global.GVA_DB.Unscoped().Model(&franchisees.Franchisee{}).Where("tel = ?", info.FranchiseTel).Pluck("id", &franchiseeIds).Error
		if err != nil {
			return nil, err
		}
		if len(franchiseeIds) == 0 {
			return nil, nil
		}
		db = db.Where("o.franchisee_id in ?", franchiseeIds)
	}
	// 商品编号
	if info.ProductCode != "" {
		var productIds []uint
		err := global.GVA_DB.Unscoped().Model(&products.Product{}).Unscoped().Where("code = ?", info.ProductCode).Pluck("id", &productIds).Error
		if err != nil {
			return nil, err
		}
		if len(productIds) > 0 {
			var (
				orderIDList []uint
			)
			err = global.GVA_DB.Model(&orders.OrderGoods{}).Where("product_id in ?", productIds).Pluck("order_id", &orderIDList).Error
			if err != nil {
				return nil, err
			}
			if len(orderIDList) == 0 {
				return nil, nil
			}
			db = db.Where("o.id in ?", orderIDList)
		}
	}
	var result []*ordersReq.OrderExport
	err := db.Joins("right join order_goods as og on og.order_id = o.id").
		Joins("left join product as p on p.id = og.product_id").
		Joins("left join special_mall sm on sm.id = p.special_mall_id").
		Joins("left join franchisee as f on f.id = o.franchisee_id").
		Joins("left join sys_users as u on u.id = f.user_id").
		Select("o.id,o.order_no,o.order_type,o.order_sub_type,o.payment_type,f.name as franchisee_name," +
			"u.username as franchisee_account,p.id as product_id,p.name as product_name," +
			"o.amount, o.points,o.status,o.address_snapshot,o.remark,o.created_at," +
			"og.id as order_goods_id,og.unit_price as product_unit_price,og.points_price as product_unit_points," +
			"p.is_combination,if(og.quantity>0,og.quantity,og.points_quantity) as product_quantity,sm.name as special_mall_name").
		Find(&result).Error
	if err != nil {
		return nil, err
	}

	var orderMap = make(map[string]struct{}, 0)

	// 购买数量 待发货数量
	for i, v := range result {
		var (
			orderDeliverys []*orders.OrderDelivery
			deliveryCount  int
		)
		// 查询已发货的数量
		var err = global.GVA_DB.Model(&orders.OrderDelivery{}).Where("order_no = ?", v.OrderNo).
			Select("goods_detail").
			Find(&orderDeliverys).Error
		if err != nil {
			return nil, err
		}
		for _, orderDelivery := range orderDeliverys {
			for _, goodsDetail := range *orderDelivery.GoodsDetail {
				if goodsDetail.GoodsId == result[i].OrderGoodsID {
					deliveryCount += goodsDetail.GoodsNum
				}
			}
		}
		result[i].WaitingDeliveryCount = v.ProductQuantity - deliveryCount

		if *v.Status == types.OrderDelivery {
			// 查询发货单
			var delivery orders.OrderDelivery
			err = global.GVA_DB.Model(&orders.OrderDelivery{}).Where("order_no = ? and status = ?", v.OrderNo, types.DeliveryStatusStocking).First(&delivery).Error
			if err != nil {
				if !utils.ErrorIsDBNotFound(err) {
					return nil, err
				}
			} else {
				status := types.OrderStocking
				result[i].Status = &status
			}
		}

		// 组合商品明细
		if result[i].IsCombination {
			c, err := dao.Product.GetCombinationProductDetial(result[i].ProductID)
			if err != nil {
				return nil, err
			}
			result[i].CombinationDetail = c
		}
		if _, ok := orderMap[v.OrderNo]; ok {
			continue
		}
		var walletPay = make([]*response.WalletPay, 0)
		if *v.PaymentType != types.ZeroPayOrder && *v.PaymentType != types.PaymentTypeOnlinePay {
			walletPay, err = dao.Order.GetWalletPayedDetail(int(v.OrderID))
			if err != nil {
				return nil, err
			}
		}
		sort.Slice(walletPay, func(i, j int) bool {
			return walletPay[i].WalletID < walletPay[j].WalletID
		})
		result[i].WalletPay = walletPay
		orderMap[v.OrderNo] = struct{}{}
	}
	global.GVA_LOG.Debug("service.order.ExportOrderInfoList", zap.Any("result", result))
	return generateExcel(result)
}

// 生成Excel文件
func generateExcel(orderList []*ordersReq.OrderExport) (*excelize.File, error) {
	f := excelize.NewFile()
	oldSheetName := f.GetSheetName(0)
	sheetName := "订单列表"

	// 修改第一个 sheet 的名称
	if err := f.SetSheetName(oldSheetName, sheetName); err != nil {
		global.GVA_LOG.Error("SetSheetName err!", zap.Error(err))
		return nil, err
	}
	// 专项账户表头
	specialPay := getAllSpecialAccountPayed(orderList)
	// 设置表头
	var headerMiddle []interface{}
	headerLeft := []interface{}{"订单号", "下单方式", "订单类型", "支付方式", "加盟商名称", "账号", "商品名称", "专区名称", "组合商品明细", "商品单价/积分价", "购买数量", "待发货数量", "单品总金额", "单品总积分", "订单金额", "订单积分", "在线支付"}
	headerRight := []interface{}{"订单状态", "收货人", "收货人电话", "收货地址", "订单备注", "客服备注", "下单时间", "支付时间", "备货时间", "发货时间", "操作人", "仓库"}
	// 专项账户支付,动态列表头
	for _, v := range specialPay.WalletPay {
		headerMiddle = append(headerMiddle, v.WalletName)
	}
	header := append(headerLeft, headerMiddle...)
	header = append(header, headerRight...)
	if err := f.SetSheetRow(sheetName, "A1", &header); err != nil {
		return nil, err
	}

	var orderAmountMap = make(map[string]float64)
	var orderPointsMap = make(map[string]float64)
	// 填充数据
	for i, row := range orderList {
		rowIndex := i + 2
		amount := float64(row.Amount)
		singleTotleAmount := float64(0)
		singleTotlePoints := 0
		var (
			//costStr, amountStr, pointsStr string
			cost, amountUnit, pointsUnit, amountOnlinePay float64
		)
		if row.PaymentType.IsPoints() {
			//pointsStr = fmt.Sprintf("%v", float64(row.Points))
			pointsUnit = float64(row.Points)
			//costStr = fmt.Sprintf("%v", row.ProductUnitPoints)
			cost = float64(row.ProductUnitPoints)
			singleTotlePoints = row.ProductQuantity * int(cost)
		} else if *row.PaymentType == types.CloudPickUp {
			// 提货订单,一个订单中既有积分支付,又有现金支付
			if row.Points > 0 {
				//pointsStr = fmt.Sprintf("%v", row.Points)
				pointsUnit = float64(row.Points)
				//costStr = fmt.Sprintf("%v", row.ProductUnitPoints)
				cost = float64(row.ProductUnitPoints)
				singleTotlePoints = row.ProductQuantity * int(cost)
			}
			if row.Amount > 0 {
				//amountStr = fmt.Sprintf("%.2f", amount/100)
				amountUnit, _ = decimal.NewFromFloat(float64(amount) / 100).Round(2).Float64()
				//costStr = fmt.Sprintf("%.2f", float64(row.ProductUnitPrice)/100)
				cost, _ = decimal.NewFromFloat(float64(row.ProductUnitPrice) / 100).Round(2).Float64()
				singleTotleAmount = float64(row.ProductQuantity) * cost
			}
		} else if *row.PaymentType == types.ZeroPayOrder {
			//amountStr = fmt.Sprintf("%.2f", amount/100)
			amountUnit, _ = decimal.NewFromFloat(float64(amount) / 100).Round(2).Float64()
			//costStr = fmt.Sprintf("%.2f", float64(row.ProductUnitPrice)/100)
			cost, _ = decimal.NewFromFloat(float64(row.ProductUnitPrice) / 100).Round(2).Float64()
		} else if *row.PaymentType == types.PaymentTypeOnlinePay {
			if *row.Status == types.OrderStatusAwaitingPayment || *row.Status == types.OrderStatusClosed {
				amountOnlinePay = 0
			} else {
				amountOnlinePay, _ = decimal.NewFromFloat(float64(amount) / 100).Round(2).Float64()
			}
			amountUnit, _ = decimal.NewFromFloat(float64(amount) / 100).Round(2).Float64()
			cost, _ = decimal.NewFromFloat(float64(row.ProductUnitPrice) / 100).Round(2).Float64()
			singleTotleAmount = float64(row.ProductQuantity) * cost
		} else {
			//costStr = fmt.Sprintf("%.2f", float64(row.ProductUnitPrice)/100)
			cost, _ = decimal.NewFromFloat(float64(row.ProductUnitPrice) / 100).Round(2).Float64()
			//amountStr = fmt.Sprintf("%.2f", amount/100)
			amountUnit, _ = decimal.NewFromFloat(float64(amount) / 100).Round(2).Float64()
			singleTotleAmount = float64(row.ProductQuantity) * cost
		}

		if _, ok := orderAmountMap[row.OrderNo]; ok {
			//amountStr = ""
			amountUnit = 0
			amountOnlinePay = 0
		} else {
			//orderAmountMap[row.OrderNo] = amountStr
			orderAmountMap[row.OrderNo] = amountUnit
		}
		if _, ok := orderPointsMap[row.OrderNo]; ok {
			//pointsStr = ""
			pointsUnit = 0
		} else {
			//orderPointsMap[row.OrderNo] = pointsStr
			orderPointsMap[row.OrderNo] = pointsUnit
		}
		lineLeft := []interface{}{
			row.OrderNo,
			row.OrderType.String(),
			row.OrderSubType.String(),
			row.PaymentType.String(),
			row.FranchiseeName,
			row.FranchiseeAccount,
			row.ProductName,
			row.SpecialMallName,
			row.CombinationDetailString(),
			cost,
			row.ProductQuantity,
			row.WaitingDeliveryCount,
			// float64(row.ProductQuantity) * cost,
			singleTotleAmount,
			// row.ProductQuantity * row.Points,
			singleTotlePoints,
			amountUnit,
			pointsUnit,
			amountOnlinePay,
		}
		// 专项账户支付,动态列
		var lineMiddle []interface{}
		for _, v := range specialPay.WalletPay {
			specialPayedAmount := float64(0)
			specialPointsPayedAmount := float64(0)
			// 在线支付,没有专项账户支付
			// if *row.PaymentType == types.PaymentTypeOnlinePay {
			// 	lineMiddle = append(lineMiddle, 0)
			// 	continue
			// }
			// 专项账户支付
			for _, w := range row.WalletPay {
				if w.WalletID == v.WalletID && w.WalletName == v.WalletName {
					if w.TotalPointsPayed > 0 {
						specialPointsPayedAmount = float64(w.TotalPointsPayed)
						break
					}
					if w.TotalPayed > 0 {
						specialPayedAmount = float64(w.TotalPayed)
						break
					}
				}
			}
			if specialPayedAmount > 0 {
				//lineMiddle = append(lineMiddle, fmt.Sprintf("%.2f", specialPayedAmount/100))
				specialPayedAmount, _ = decimal.NewFromFloat(specialPayedAmount / 100).Round(2).Float64()
				lineMiddle = append(lineMiddle, specialPayedAmount)
			} else if specialPointsPayedAmount > 0 {
				//lineMiddle = append(lineMiddle, fmt.Sprintf("%v", specialPointsPayedAmount))
				lineMiddle = append(lineMiddle, specialPointsPayedAmount)
			} else {
				//lineMiddle = append(lineMiddle, "")
				lineMiddle = append(lineMiddle, 0)
			}
		}
		ormap := utils.FormatOrderRecords(row.OrderRecords)
		lineRight := []interface{}{
			row.Status.String(),
			row.AddressSnapshot.Consignee,
			row.AddressSnapshot.Tel,
			row.AddressSnapshot.Province + row.AddressSnapshot.City + row.AddressSnapshot.County + row.AddressSnapshot.Area + row.AddressSnapshot.Address,
			row.Remark,
			utils.PrintOrderRemarks(row.OrderRemarks),
			row.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if *row.PaymentType == types.PaymentTypeOnlinePay {
			if row.OrderOnlinePay != nil && row.OrderOnlinePay.PaidTime != nil {
				lineRight = append(lineRight, row.OrderOnlinePay.PaidTime.Format("2006-01-02 15:04:05"))
			} else {
				lineRight = append(lineRight, "-")
			}
		} else {
			// append empty data
			lineRight = append(lineRight, "-")
		}
		if row.ProductQuantity-row.WaitingDeliveryCount > 0 {
			lineRight = append(lineRight,
				[]interface{}{
					utils.PrintOrderRecordTime(ormap, types.OrderStocking),
					utils.PrintOrderRecordTime(ormap, types.OrderDelivery)}...)
		} else {
			lineRight = append(lineRight,
				[]interface{}{
					"-", "-"}...)
		}
		// add 操作人
		operator := "-"
		if len(row.OrderRecords) > 0 {
			o := row.OrderRecords[0].Operator
			if o != nil {
				operator = o.Username + "/ " + o.NickName
			}
		}
		lineRight = append(lineRight, operator)
		if row.BigWarehouseOrder != nil && row.BigWarehouseOrder.BigWarehouse != nil {
			lineRight = append(lineRight, row.BigWarehouseOrder.BigWarehouse.Name+"/ "+row.BigWarehouseOrder.BigWarehouse.Code)
		} else {
			lineRight = append(lineRight, "-")
		}

		line := append(lineLeft, lineMiddle...)
		line = append(line, lineRight...)
		if err := f.SetSheetRow(sheetName, "A"+strconv.Itoa(rowIndex), &line); err != nil {
			return nil, err
		}
	}

	// 列宽度
	if err := f.SetColWidth(sheetName, "A", "G", 20); err != nil {
		global.GVA_LOG.Error("SetColWidth err!", zap.Error(err))
		return nil, err
	}

	return f, nil
}

func getAllSpecialAccountPayed(in []*ordersReq.OrderExport) ordersReq.WalletPayInfo {
	walletPay := ordersReq.WalletPayInfo{
		WalletPayMap: make(map[string]*response.WalletPay),
	}
	for _, v := range in {
		for _, w := range v.WalletPay {
			k := fmt.Sprintf("%d_%s", w.WalletID, w.WalletName)
			if _, ok := walletPay.WalletPayMap[k]; !ok {
				walletPay.WalletPayMap[k] = w
				walletPay.WalletPay = append(walletPay.WalletPay, w)
			}
		}
	}
	sort.Slice(walletPay.WalletPay, func(i, j int) bool {
		return walletPay.WalletPay[i].WalletID < walletPay.WalletPay[j].WalletID
	})
	return walletPay
}

// SimpleGetOrder 直接获取 order 记录
func (orderService *OrderService) SimpleGetOrder(orderId uint) (orders.Order, error) {
	var o orders.Order
	err := global.GVA_DB.Model(orders.Order{}).Preload("OrderRemarks").Preload("OrderRemarks.CreateUser").Where("id = ?", orderId).First(&o).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("service.SimpleGetOrder err:", zap.Error(err), zap.Any("orderId", orderId))
		return o, err
	}
	return o, nil
}

func (orderService *OrderService) GetOrderByOrderNo(orderNo string) (orders.Order, error) {
	var o orders.Order
	err := global.GVA_DB.Model(&orders.Order{}).Where("order_no = ?", orderNo).First(&o).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("service.GetOrderByOrderNo err:", zap.Error(err), zap.Any("orderNo", orderNo))
	}
	return o, err
}

func (orderService *OrderService) GetOrderGoods(orderId uint) ([]orders.OrderGoods, error) {
	var og []orders.OrderGoods
	err := global.GVA_DB.Model(&orders.OrderGoods{}).Where("order_id = ?", orderId).Find(&og).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Error("service.GetOrderGoods err:", zap.Error(err), zap.Any("orderId", orderId))
	}
	return og, err
}

// CreateImportOrder 导入订单
func (orderService *OrderService) CreateImportOrder(orderReq *ordersReq.CreateOrderRequest, orderTime *time.Time) (err error) {
	var (
		f                     franchisees.Franchisee
		order                 *orders.Order
		cw                    *warehouse.CloudWarehouse
		orderGoods            = make([]*orders.OrderGoods, 0, len(orderReq.Products))
		cloudRecords          = make([]*warehouse.CloudWarehouseRecord, 0, len(orderReq.Products))
		subcws                = make([]*warehouse.SubCloudWarehouse, 0, len(orderReq.Products))
		totalBalance          int // 消费总金额
		totalPoints           int // 消费总积分
		generalAccountConsume int
		t                     time.Time
	)

	if orderTime == nil {
		t = time.Now().Local()
		orderTime = &t
	}
	err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ?", orderReq.FranchiseeId).First(&f).Error; err != nil {
			return err
		}
		for _, v := range orderReq.Products {
			if v.Quantity == 0 && v.PointsQuantity == 0 {
				return errors.New("商品数量不能为0")
			}
			var p products.Product
			if err := tx.Set("gorm:query_option", "FOR UPDATE").
				Where("id = ?", v.ProductId).First(&p).Error; err != nil {
				return err
			}
			generalAccountConsume += v.UnitPrice * v.Quantity
			totalBalance += v.UnitPrice * v.Quantity
			totalPoints += v.PointsPrice * v.PointsQuantity
		}
		order = &orders.Order{
			OrderNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
			FranchiseeId:    int(orderReq.FranchiseeId),
			AddressSnapshot: nil,
			PaymentType:     orderReq.PaymentType,
			OrderType:       orderReq.OrderType,
			OrderSubType:    orderReq.OrderSubType,
			Remark:          orderReq.Remark,
			Status:          types.OrderAwaitingDelivery,
			Amount:          totalBalance,
			Points:          totalPoints,
			Franchisee:      nil,
			GVA_MODEL: global.GVA_MODEL{
				CreatedAt: *orderTime,
				UpdatedAt: *orderTime,
			},
		}
		if orderReq.OrderSubType == types.Presentation {
			order.Status = types.OrderCompleted
			cw = &warehouse.CloudWarehouse{
				FranchiseeId: int(orderReq.FranchiseeId),
				OrderNo:      order.OrderNo,
			}
			if err = tx.Create(&cw).Error; err != nil {
				return fmt.Errorf("create cloud_warehouse failed, err:=%w", err)
			}
		}
		if err = tx.Create(&order).Error; err != nil {
			return fmt.Errorf("create order failed, err:=%w", err)
		}
		// Send order status change notification
		if err = utils.EnqueueOrderStatusChange(order.OrderNo, order.ID, 0, int8(order.Status), 0, "Order created"); err != nil {
			global.GVA_LOG.Error("Failed to enqueue order status change", zap.Error(err))
		}

		// 创建 goods
		for _, product := range orderReq.Products {
			var orderGood *orders.OrderGoods

			orderGood = &orders.OrderGoods{
				OrderId:        order.ID,
				GoodNo:         utils.GenOrderNo(orderReq.FranchiseeId, utils.SequenceGuanpuSerialNo),
				ProductId:      int(product.ProductId),
				UnitPrice:      product.UnitPrice, // 导入数据不影响业绩
				Quantity:       product.Quantity,
				PointsPrice:    product.PointsPrice,
				PointsQuantity: product.PointsQuantity,
				Status:         types.AwaitingDelivery,
				Rebate:         0,
				IsCombination:  false,
				AccountId:      0,
				AccountPayed:   0,
				SpecialMallID:  0,
				GVA_MODEL: global.GVA_MODEL{
					CreatedAt: *orderTime,
					UpdatedAt: *orderTime,
				},
			}
			if orderReq.OrderSubType == types.Presentation {
				scw := &warehouse.SubCloudWarehouse{
					CloudWarehouseId: cw.ID,
					ProductId:        int(product.ProductId),
					GVA_MODEL: global.GVA_MODEL{
						CreatedAt: *orderTime,
						UpdatedAt: *orderTime,
					},
				}

				if product.Quantity > 0 {
					scw.Inventory = product.Quantity
					scw.UnitPrice = product.UnitPrice
				} else {
					scw.Inventory = product.PointsQuantity
					scw.PointsPrice = product.PointsPrice
				}

				cloudRecord := &warehouse.CloudWarehouseRecord{
					FranchiseeId: int(orderReq.FranchiseeId),
					Type:         types.InCloud,
					ProductId:    int(product.ProductId),
					Inventory:    scw.Inventory,
					Pieces:       scw.Inventory,
					TradeNo:      order.OrderNo,
					Remark:       orderReq.Remark,
					GVA_MODEL: global.GVA_MODEL{
						CreatedAt: *orderTime,
						UpdatedAt: *orderTime,
					},
				}
				cloudRecords = append(cloudRecords, cloudRecord)
				subcws = append(subcws, scw)
				orderGoods = append(orderGoods, orderGood)
			}
		}
		if err = tx.Create(&orderGoods).Error; err != nil {
			return fmt.Errorf("orderGoods create failed, err:=%w", err)
		}

		if len(cloudRecords) > 0 {
			if err = tx.Create(&cloudRecords).Error; err != nil {
				return fmt.Errorf("cloudRecords create failed, err:=%w", err)
			}
		}

		if len(subcws) > 0 {
			if err = tx.Create(&subcws).Error; err != nil {
				return fmt.Errorf("subcws create failed, err:=%w", err)
			}
		}
		return err
	})
	return err
}

func (orderService *OrderService) UpdateOrderAddress(franchiseeID uint, orderAddressUpdateReq *ordersReq.OrderAddressUpdate) error {
	var o orders.Order

	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {

		err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("franchisee_id = ? and order_no = ?", franchiseeID, orderAddressUpdateReq.OrderNo).
			Find(&o).Error

		if err != nil {
			return errors.Join(err, errors.New("UpdateOrderAddress findOrder error"))

		}
		if o.Status != types.OrderAwaitingDelivery {
			return errors.New("订单已经处理中，不可再修改收货地址")
		}
		var fa app.FranchiseeAddress
		err = tx.Where("id = ? and franchisee_id = ?", orderAddressUpdateReq.AddressId, franchiseeID).
			First(&fa).Error
		if err != nil {
			return errors.Join(err, errors.New("获取收货地址失败"))
		}

		o.AddressSnapshot = &fa
		err = tx.Save(o).Error
		if err != nil {
			return errors.Join(err, errors.New("保存收货地址失败"))
		}
		_, err = bigWarehouseService.ReDispatchOrderToBigWarehouse(tx, []uint{o.ID})

		if err != nil {
			return errors.Join(err, errors.New("UpdateOrderAddress bigWarehouseService.ReDispatchOrderToBigWarehouse error"))
		}

		return nil
	})
	return err
}

// UpdateOrderWarehouse 更新订单仓库
func (orderService *OrderService) UpdateOrderWarehouse(orderNo string, orderID uint, warehouseID uint) error {
	var o orders.Order

	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ? and order_no = ?", orderID, orderNo).
			Find(&o).Error

		if err != nil {
			return errors.Join(err, errors.New("UpdateOrderWarehouse findOrder error"))
		}

		if o.Status != types.OrderAwaitingDelivery {
			return errors.New("订单已经处理中，不可再修改仓库")
		}
		global.GVA_LOG.Debug("warehouseID", zap.Any("o", o))
		err = bigWarehouseService.UpdateOrCreateOrderWarehouse(tx, orderID, o.OrderNo, warehouseID)
		if err != nil {
			return errors.Join(err, errors.New("UpdateOrderWarehouse bigWarehouseService.UpdateOrCreateOrderWarehouse error"))
		}
		return nil
	})
	return err
}
