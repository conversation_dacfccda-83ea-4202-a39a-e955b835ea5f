package main

import (
	"context"
	"flag"
	"log"
	"os"

	"github.com/OSQianXing/guanpu-server/core"
	"github.com/OSQianXing/guanpu-server/global"
	"github.com/OSQianXing/guanpu-server/initialize"
	"github.com/OSQianXing/guanpu-server/service/orderNotification"
	"github.com/OSQianXing/guanpu-server/source/system"
	"github.com/google/gops/agent"
	"go.uber.org/zap"
)

//go:generate go env -w GO111MODULE=on
//go:generate go env -w GOPROXY=https://goproxy.cn,direct
//go:generate go mod tidy
//go:generate go mod download

// @title                       Swagger Example API
// @version                     0.0.1
// @description                 This is a sample Server pets
// @securityDefinitions.apikey  ApiKeyAuth
// @in                          header
// @name                        x-token
// @BasePath                    /
func main() {
	// 解析命令行参数
	initData := flag.Bool("init-data", false, "Initialize tenant data")
	flag.Parse()

	// Initialize system monitoring
	if err := agent.Listen(agent.Options{
		ShutdownCleanup: true,
	}); err != nil {
		log.Fatal("Failed to initialize system monitoring:", err)
	}

	// Initialize configuration
	global.GVA_VP = core.Viper()
	if global.GVA_VP == nil {
		log.Fatal("Failed to initialize configuration")
	}

	// Initialize logging
	global.GVA_LOG = core.Zap()
	if global.GVA_LOG == nil {
		log.Fatal("Failed to initialize logger")
	}
	zap.ReplaceGlobals(global.GVA_LOG)

	// Initialize other components
	initialize.OtherInit()

	// Initialize Redis first as it's required by other services
	initialize.Redis()
	if global.GVA_REDIS == nil {
		global.GVA_LOG.Fatal("Failed to initialize Redis")
	}
	defer initialize.CloseRedis() // Ensure Redis connection is closed properly

	// Initialize database
	global.GVA_DB = initialize.Gorm()
	if global.GVA_DB == nil {
		global.GVA_LOG.Fatal("Failed to initialize database")
	} else {
		// Register tables and tenant plugin
		initialize.RegisterTables()

		// Register tenant models for auto-detection
		initialize.RegisterTenantModels()

		// Validate tenant model registration (optional, for debugging)
		if global.GVA_CONFIG.System.Env == "develop" {
			initialize.ValidateTenantModels()
		}

		// Create tenant-related database indexes (production optimization)
		if global.GVA_CONFIG.System.Env == "production" {
			initialize.CreateTenantIndexes()
		}
	}

	// Ensure database connection is closed when the program exits
	db, err := global.GVA_DB.DB()
	if err != nil {
		global.GVA_LOG.Fatal("Failed to get database instance", zap.Error(err))
	}
	defer db.Close()

	// 执行租户数据初始化
	if *initData {
		global.GVA_LOG.Info("Starting tenant data initialization...")

		// 创建租户初始化器
		tenantInit := system.NewTenantInit()

		// 创建上下文并添加数据库实例
		ctx := context.WithValue(context.Background(), "db", global.GVA_DB)

		// 执行初始化
		if _, err := tenantInit.InitializeData(ctx); err != nil {
			global.GVA_LOG.Error("Tenant data initialization failed", zap.Error(err))
			os.Exit(1)
		}

		global.GVA_LOG.Info("Tenant data initialization completed successfully")
		os.Exit(0)
	}

	// Create a root context with cancellation for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Initialize background jobs
	initialize.Timer()
	initialize.ProductAutoJob()
	initialize.UnPayedOrderClose()
	initialize.DBList()

	// Initialize notification system with proper error handling
	metrics := orderNotification.NewNotificationMetrics()
	if metrics == nil {
		global.GVA_LOG.Fatal("Failed to create notification metrics")
	}

	monitor := orderNotification.NewSystemMonitor(metrics)
	if monitor == nil {
		global.GVA_LOG.Fatal("Failed to create system monitor")
	}

	daemon := orderNotification.NewNotificationDaemon(metrics, monitor)
	if daemon == nil {
		global.GVA_LOG.Error("Failed to create notification daemon")
	} else {
		daemon.Start(ctx)
		defer func() {
			daemon.Stop()
			global.GVA_LOG.Info("Notification daemon stopped")
		}()
		global.GVA_LOG.Info("Notification daemon started successfully")
	}

	// Start the server
	core.RunWindowsServer()
}
